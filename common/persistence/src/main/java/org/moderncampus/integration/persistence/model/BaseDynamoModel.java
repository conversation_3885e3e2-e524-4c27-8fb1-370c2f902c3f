package org.moderncampus.integration.persistence.model;

import java.time.Instant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAutoGeneratedTimestampAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.UpdateBehavior;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbUpdateBehavior;

@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public abstract class BaseDynamoModel {

    protected static final String PK = "PK";

    protected static final String SK = "SK";

    public abstract String getPartitionKey();

    public abstract String getSortKey();

    public void setPartitionKey(String partitionKey) {
    }//derived attributes

    public void setSortKey(String sortKey) {
    }

    @Getter(onMethod_ = {
            @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_IF_NOT_EXISTS),
            @DynamoDbAutoGeneratedTimestampAttribute
    })
    Instant createdTimestamp;

    @Getter(onMethod_ = {@DynamoDbAutoGeneratedTimestampAttribute})
    Instant updatedTimestamp;
}
