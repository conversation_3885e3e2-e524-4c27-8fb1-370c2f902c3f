package org.moderncampus.integration.webservice.dto;

import java.util.Map;

import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.exception.ValidationException;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class APIErrorResponse {

    final Object message;

    final Integer status;

    String propertyPath;

    String traceId;

    @Setter
    Map<String, Object> meta;

    public APIErrorResponse(ApplicationException applicationException) {
        this.message = applicationException.getMessage();
        this.status = applicationException.getStatusCode();
        if (applicationException instanceof ValidationException) {
            this.propertyPath = ((ValidationException) applicationException).getPropertyPath();
        }
        this.traceId = applicationException.getTraceId();
        this.meta = applicationException.getMeta();
    }
}
