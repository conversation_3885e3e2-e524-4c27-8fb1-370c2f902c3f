package org.moderncampus.integration.webservice;

import org.springframework.boot.SpringApplication;

public class BaseApplication {

    protected static void setup(SpringApplication application) {
        setSystemProps();

    }

    private static void setSystemProps() {
        //Configure the HTTP Client properties using the legacy HttpURLConnection
        System.setProperty("http.maxConnections", "50");//max idle connections per route in cache
        System.setProperty("http.keepAlive.time.server",
                "180000");//3 minutes after which connection is considered stale and is evicted
    }
}
