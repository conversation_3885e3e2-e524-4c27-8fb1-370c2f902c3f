package org.moderncampus.integration.tenants.service;

import java.util.UUID;

import org.moderncampus.integration.persistence.model.TenantDefinition;
import org.moderncampus.integration.persistence.repository.ITenantDefinitionRepository;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TenantApiTokenService {

    ITenantDefinitionRepository definitionRepository;

    public String createApiToken(String tenantId) throws Exception {
        String token = UUID.randomUUID().toString();
        TenantDefinition definition = new TenantDefinition();
        definition.setApiKey(token);
        definition.setName(tenantId);
        definitionRepository.update(definition);
        return token;
    }

    public void deleteApiToken(String tenantId) throws Exception {
        TenantDefinition definition = definitionRepository.getById(tenantId);
        definition.setApiKey(null);
        definition.setName(tenantId);
        definitionRepository.update(definition, false);
    }

    public boolean isValidToken(String tenantId, String token) {
        return definitionRepository.findById(tenantId).map(TenantDefinition::getApiKey).map(token::equals)
                .orElse(false);
    }

    public String retrieveApiTokenForTenant(String tenantId) {
        return definitionRepository.findById(tenantId).map(TenantDefinition::getApiKey).orElse(null);
    }
}
