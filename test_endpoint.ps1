# Test script for the new instructor endpoint
$headers = @{
    'Accept' = 'application/json'
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
}

$url = 'http://localhost:8080/uapi/integration/v1/instructors/id=8c005e2e-14fd-47f3-a772-eb88705818ef'

Write-Host "Testing endpoint: $url"
Write-Host "Waiting for application to start..."
Start-Sleep -Seconds 30

try {
    $response = Invoke-RestMethod -Uri $url -Method Get -Headers $headers
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error occurred:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
