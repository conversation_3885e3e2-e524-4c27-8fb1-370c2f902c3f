//package org.moderncampus.integration.config.webservice.controller;
//
//import static org.moderncampus.integration.Constants.VERSION_1;
//import static org.moderncampus.integration.webservice.util.Headers.TENANT_ID;
//
//import java.util.Map;
//
//import org.apache.commons.lang3.tuple.Pair;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mockito;
//import org.moderncampus.integration.webservice.admin.service.TenantConfigService;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//
//public class ConfigControllerTest extends BaseControllerTest {
//
//    public final String URL = "/" + VERSION_1 + "/admin/tenants/configs";
//
//    @MockBean
//    private TenantConfigService service;
//
//    @Test
//    void addConfigsTest() throws Exception {
//        var tenantId = "tenantIdTest";
//        var sourceSystem = "sourceSystemTest";
//        var requestBody = Map.of(
//                "config.key.1", "config1Value",
//                "config.key.2", "config2Value",
//                "config.key.3", "config3Value"
//        );
//
//        Mockito.when(service.upsertConfigs(tenantId, sourceSystem, requestBody)).thenReturn(requestBody);
//
//        var request = makeAuthenticatedPostRequest(URL + "/upsert", mapper.writeValueAsString(requestBody))
//                .header(TENANT_ID, tenantId);
//        mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk());
//    }
//
//    @Test
//    void getAllConfigsTest() throws Exception {
//        var tenantId = "tenantIdTest";
//        var configs = Map.of(
//                "config.key.1", "config1Value",
//                "config.key.2", "config2Value",
//                "config.key.3", "config3Value"
//        );
//
//        Mockito.when(service.getAllConfigs(tenantId)).thenReturn(configs);
//
//        var request = makeAuthenticatedGetRequest(URL + "/").header(TENANT_ID, tenantId);
//        mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk());
//    }
//
//    @Test
//    void getConfigTest() throws Exception {
//        var tenantId = "tenantIdTest";
//        var sourceSystem = "sourceSystemTest";
//        var config = Pair.of("config.key.1", "config1Value");
//
//        Mockito.when(service.getConfig(tenantId, sourceSystem, config.getKey())).thenReturn(config);
//
//        var request = makeAuthenticatedGetRequest(URL + "/" + config.getKey()).header(TENANT_ID, tenantId);
//        mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk());
//    }
//}
