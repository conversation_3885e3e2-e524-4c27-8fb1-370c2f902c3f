package org.moderncampus.integration.webservice.admin.controller.tenants;

import static org.moderncampus.integration.Constants.VERSION_1;
import static org.moderncampus.integration.webservice.admin.constants.Constants.SOURCE_SYSTEM;

import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.moderncampus.integration.system.IntegrationSystem;
import org.moderncampus.integration.webservice.admin.dto.ListTenantConfigsResponse;
import org.moderncampus.integration.webservice.admin.security.Authenticated;
import org.moderncampus.integration.webservice.admin.service.TenantConfigService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController("adminTenantsConfigsController")
@RequestMapping("/" + VERSION_1 + "/admin/tenants/{tenantName}/configs")
@Tag(name = "Admin Tenant Config API.", description = "Manages tenant config")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class TenantConfigController {

    TenantConfigService configService;

    @PostMapping("/upsert")
    @ResponseStatus(HttpStatus.OK)
    @ApiResponse(responseCode = "200", description = "Tenant Configurations successfully created or updated.",
            content = {@Content(mediaType = "application/json")})
    @ApiResponse(responseCode = "400", description = "Bad request.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "401", description = "Unauthorized request.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "500", description = "Internal Error or Access Denied", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @Authenticated
    Map<String, String> upsertConfigs(
            @PathVariable String tenantName,
            @RequestHeader(name = SOURCE_SYSTEM, required = true) IntegrationSystem sourceSystem,
            @RequestBody Map<String, String> configs) {
        return configService.upsertConfigs(tenantName, sourceSystem, configs);
    }

    @GetMapping("/")
    @ResponseStatus(HttpStatus.OK)
    @ApiResponse(responseCode = "200", description = "Successful retrieval of all tenant configurations",
            content = {@Content(mediaType = "application/json")})
    @ApiResponse(responseCode = "401", description = "Unauthorized request.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "500", description = "Internal Error or Access Denied", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @Authenticated
    ListTenantConfigsResponse getAllConfigs(@PathVariable String tenantName) {
        return configService.getAllConfigs(tenantName);
    }

    @GetMapping("/{key}")
    @ResponseStatus(HttpStatus.OK)
    @ApiResponse(responseCode = "200", description = "Successful retrieval of tenant config by key",
            content = {@Content(mediaType = "application/json")})
    @ApiResponse(responseCode = "401", description = "Unauthorized request.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "404", description = "Config does not exist for given key.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "500", description = "Internal Error or Access Denied", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @Authenticated
    Pair<String, String> getConfig(@PathVariable String tenantName,
            @RequestHeader(name = SOURCE_SYSTEM, required = true) IntegrationSystem sourceSystem,
            @PathVariable String key) throws Exception {
        return configService.getConfig(tenantName, sourceSystem, key);
    }

    @DeleteMapping("{key}")
    @ApiResponse(responseCode = "200", description = "Successful retrieval of tenant config by key",
            content = {@Content(mediaType = "application/json")})
    @ApiResponse(responseCode = "401", description = "Unauthorized request.", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @ApiResponse(responseCode = "500", description = "Internal Error or Access Denied", content = {
            @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    @Authenticated
    @ResponseStatus(HttpStatus.NO_CONTENT)
    void deleteTenant(@PathVariable String tenantName,
            @RequestHeader(name = SOURCE_SYSTEM, required = true) IntegrationSystem sourceSystem,
            @PathVariable String key)
            throws Exception {
        configService.deleteConfig(tenantName, sourceSystem, key);
    }
}
