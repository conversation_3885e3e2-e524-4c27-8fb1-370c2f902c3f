package org.moderncampus.integration.webservice.admin.controller.users;

import static org.moderncampus.integration.Constants.VERSION_1;

import org.moderncampus.integration.webservice.admin.dto.AuthenticationRequest;
import org.moderncampus.integration.webservice.admin.dto.AuthenticationResponse;
import org.moderncampus.integration.webservice.admin.service.UserAuthService;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@CrossOrigin
@RestController
@RequestMapping("/" + VERSION_1 + "/admin/users")
@Tag(name = "Admin User Auth API.", description = "Manages the authentication for admin users.")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserAuthController {

    UserAuthService service;

    @Operation(summary = "Authenticates user with username + password.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User authenticated.",
                    content = {@Content(mediaType = "application/json",
                            schema = @Schema(implementation = AuthenticationResponse.class))}),
            @ApiResponse(responseCode = "401", description = "User could not be authenticated.",
                    content = @Content)})
    @PostMapping("/authenticate")
    @ResponseStatus(HttpStatus.OK)
    public AuthenticationResponse authenticate(
            @Valid @RequestBody AuthenticationRequest authenticationRequestDto) {
        return service.authenticateUser(authenticationRequestDto);
    }
}
