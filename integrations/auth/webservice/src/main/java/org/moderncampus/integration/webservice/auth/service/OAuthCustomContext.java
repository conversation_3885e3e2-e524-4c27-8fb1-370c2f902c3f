package org.moderncampus.integration.webservice.auth.service;

import java.util.Map;

public class OAuthCustomContext {

    private static final ThreadLocal<Map<String, String>> context = new ThreadLocal<>();

    public static Map<String, String> getContext() {
        return context.get();
    }

    public static void setContext(Map<String, String> ctx) {
        context.set(ctx);
    }

    public static void clear() {
        context.remove();
    }
}