package org.moderncampus.integration.webservice.auth.security;

import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.web.SecurityFilterChain;

import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;

@Configuration
public class AuthorizationServerConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
        return http
                .formLogin(Customizer.withDefaults())
                .build();
    }

    @Bean
    public AuthorizationServerSettings authorizationServerSettings() {
        return AuthorizationServerSettings.builder()
                .authorizationEndpoint("/integration/" + UUID.randomUUID().toString())
                .deviceAuthorizationEndpoint("/integration/" + UUID.randomUUID().toString())
                .deviceVerificationEndpoint("/integration/" + UUID.randomUUID().toString())
                .tokenEndpoint("/integration/auth/oauth2/token")
                .tokenIntrospectionEndpoint("/integration/auth/oauth2/introspect")
                .tokenRevocationEndpoint("/integration/" + UUID.randomUUID().toString())
                .jwkSetEndpoint("/integration/" + UUID.randomUUID().toString()) // or /xapi/.well-known/jwks.json
                .oidcUserInfoEndpoint("/integration/" + UUID.randomUUID().toString())
                .oidcClientRegistrationEndpoint("/integration/" + UUID.randomUUID().toString())
                .oidcLogoutEndpoint("/integration/" + UUID.randomUUID().toString())
                .build();
    }

    @Bean
    public JWKSource<SecurityContext> jwkSource(@Value("${jwt.public.key}") RSAPublicKey rsaPublicKey,
            @Value("${jwt.private.key}") RSAPrivateKey rsaPrivateKey) {
        RSAKey rsaKey = new RSAKey.Builder(rsaPublicKey).privateKey(rsaPrivateKey).keyID(UUID.randomUUID().toString())
                .build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        return ((jwkSelector, securityContext) -> jwkSelector.select(jwkSet));
    }

}
