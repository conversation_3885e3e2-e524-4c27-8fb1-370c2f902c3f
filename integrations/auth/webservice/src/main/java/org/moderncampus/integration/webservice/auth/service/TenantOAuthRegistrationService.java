package org.moderncampus.integration.webservice.auth.service;

import static org.moderncampus.integration.Constants.DEST_SYSTEM_ID;
import static org.moderncampus.integration.Constants.SOURCE_SYSTEM_ID;
import static org.moderncampus.integration.tenants.validator.TenantValidator.BAD_REQUEST_INACTIVE_TENANT;
import static org.moderncampus.integration.tenants.validator.TenantValidator.TENANT_NOT_FOUND;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.exception.EntityNotFoundException;
import org.moderncampus.integration.persistence.repository.OAuthRegistration;
import org.moderncampus.integration.persistence.repository.OAuthRegistrationRepository;
import org.moderncampus.integration.tenants.validator.TenantValidator;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TenantOAuthRegistrationService implements RegisteredClientRepository {

    OAuthRegistrationRepository repository;

    ObjectMapper mapper;

    TenantValidator tenantValidator;

    @Override
    public void save(RegisteredClient registeredClient) {
        OAuthRegistration registration = new OAuthRegistration();
        registration.setId(registeredClient.getClientId());
        registration.setClientId(registeredClient.getClientId());
        registration.setClientSecret(registeredClient.getClientSecret());
        registration.setClientIdIssuedAt(registeredClient.getClientIdIssuedAt());
        registration.setClientSecretExpiresAt(registeredClient.getClientSecretExpiresAt());
        registration.setClientName(registeredClient.getClientName());
        registration.setClientAuthenticationMethods(
                Optional.ofNullable(registeredClient.getClientAuthenticationMethods())
                        .map(methods -> methods.stream().map(
                                ClientAuthenticationMethod::getValue).collect(
                                Collectors.toSet())).orElse(null));
        registration.setScopes(registeredClient.getScopes());
        registration.setAuthorizationGrantTypes(Optional.ofNullable(registeredClient.getAuthorizationGrantTypes())
                .map(grantTypes -> grantTypes.stream().map(
                        AuthorizationGrantType::getValue).collect(Collectors.toSet())).orElse(null));
        repository.save(registration);
    }

    @Override
    public RegisteredClient findById(String id) {
        OAuthRegistration oAuthRegistration = new OAuthRegistration();
        oAuthRegistration.setId(id);
        oAuthRegistration.setClientId(id);
        try {
            OAuthRegistration registration = repository.getById(oAuthRegistration);
            Pair<String, String> integrationSystems = extractIntegrationSystems(registration);
            tenantValidator.validate(registration.getClientName(), integrationSystems.getLeft(),
                    integrationSystems.getRight());
            return toRegisteredClient(registration);
        } catch (EntityNotFoundException e) {
            return null;
        } catch (Exception e) {
            if (e instanceof ApplicationException) {
                String authErrorCode = OAuth2ErrorCodes.INVALID_REQUEST;
                if (e.getMessage().contains(TENANT_NOT_FOUND) || e.getMessage().contains(BAD_REQUEST_INACTIVE_TENANT)) {
                    authErrorCode = OAuth2ErrorCodes.INVALID_CLIENT;
                }
                throw new OAuth2AuthenticationException(new OAuth2Error(authErrorCode, e.getMessage(), null));
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public RegisteredClient findByClientId(String clientId) {
        return findById(clientId);
    }

    private Pair<String, String> extractIntegrationSystems(OAuthRegistration registration) {
        Map<String, String> customSettings = registration.getCustomSettings();
        return Pair.of(customSettings.get(SOURCE_SYSTEM_ID), customSettings.get(DEST_SYSTEM_ID));
    }

    private RegisteredClient toRegisteredClient(OAuthRegistration registration) {
        OAuthCustomContext.setContext(registration.getCustomSettings());
        return RegisteredClient.withId(registration.getId())
                .clientId(registration.getClientId())
                .clientSecret("{bcrypt}" + registration.getClientSecret())
                .clientIdIssuedAt(registration.getClientIdIssuedAt())
                .clientSecretExpiresAt(registration.getClientSecretExpiresAt())
                .clientAuthenticationMethods(methods -> {
                    if (registration.getClientAuthenticationMethods() != null) {
                        methods.addAll(registration.getClientAuthenticationMethods().stream()
                                .map(ClientAuthenticationMethod::new).collect(
                                        Collectors.toSet()));
                    }
                })
                .authorizationGrantTypes(grants -> {
                    if (registration.getAuthorizationGrantTypes() != null) {
                        grants.addAll(
                                registration.getAuthorizationGrantTypes().stream().map(AuthorizationGrantType::new)
                                        .collect(Collectors.toSet()));
                    }
                })
                .scopes(scopes -> {
                    if (registration.getScopes() != null) {
                        scopes.addAll(registration.getScopes());
                    }
                }).tokenSettings(
                        TokenSettings.builder().accessTokenTimeToLive(Duration.ofMinutes(30)).build())
                .build();
    }
}
