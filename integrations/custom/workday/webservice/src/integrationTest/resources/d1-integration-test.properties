spring.profiles.active=dev
workday.integration.auth.clientId=YTU3NmZmNTEtYTJjOC00OTNjLWFkYTEtMTlmZmY5ODc3YjY2
workday.integration.auth.clientSecret=9pptxt9v6l20b7xqdnwrajuvo0x4usdw8fj7cileflrvyvm7sf0990szz9do5o95f3acdk4j82ax81fz7n0puhsce8r4nsuv17t
workday.integration.tenant=moderncampus_dpt1
workday.integration.host=impl-services1.wd12.myworkday.com
workday.integration.version=v41.0
workday.integration.auth.refreshToken=7hnjgo8h3op7z1382zthfui8ggtnwroynhtbcwz5nch7tuqzzs417o3951kkf9y50hid469r7vkpbh01uiucutu66kq25d2wkw9
workday.integration.reportUser=d1integration
workday.integration.mapping.duplicateCheckIntSystemId=INT_Duplicate_Checking_TPS_v3/INT_Duplicate_Checking_TPS_v3/StartHere
workday.integration.mapping.studentIdType=CUSTOM_ID_TYPE-6-6
camel.springboot.route-filter-include-pattern=^.*destiny.*$