package org.moderncampus.integration.workday.webservice.controller.destiny;


import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.hamcrest.Matchers.anEmptyMap;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/d1-integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
class DestinyWorkdayIntegrationControllerTests {

    @Autowired
    MockMvc mockMvc;

    @Autowired
    ObjectMapper mapper;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("destiny.integration.host", () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("destiny.integration.username", () -> "test");
        registry.add("destiny.integration.password", () -> "test");
        registry.add("destiny.integration.useHttp", () -> true);
    }

    private static String getStudentCreateOrUpdatePayload() {
        return """
                {
                      "number" : "X81595943",
                      "addresses" : [ {
                        "type" : "Home:COMMUNICATION_USAGE_BEHAVIOR_TENANTED-6-19",
                        "line1" : "999 Update Street 1",
                        "city" : "Update City",
                        "state" : "CA",
                        "postalCode" : "90210",
                        "country" : "US"
                      } ],
                      "emails" : [ {
                        "type" : "Home",
                        "emailAddress" : "<EMAIL>"
                      } ],
                      "phones" : [ {
                        "type" : "Home:PHONE_DEVICE_TYPE-6-5",
                        "number" : "7845879",
                        "areaCode" : "458"
                      } ]
                    }""";
    }

    private static String getSearchMatchTriggerPayload() {
        return """
                {
                  "correlationId": "7102414_1708980437811",
                  "student": {
                    "number": "X81595912",
                    "firstName": "Liam",
                    "lastName": "Kim",
                    "birthDate": "2000-02-04",
                    "addresses": [
                      {
                        "type": "Home:COMMUNICATION_USAGE_BEHAVIOR_TENANTED-6-19",
                        "line1": "14141",
                        "city": "Toronto",
                        "state": "ON",
                        "postalCode": "M2M 3W5",
                        "country": "CAN"
                      }
                    ],
                    "emails": [
                      {
                        "type": "Home",
                        "emailAddress": "<EMAIL>"
                      }
                    ],
                    "phones": [
                      {
                        "type": "Home:PHONE_DEVICE_TYPE-6-5",
                        "number": "3344124",
                        "areaCode": "123",
                        "countryCode": ""
                      }
                    ]
                  }
                }""";
    }

    private static String getSearchMatchResponsePayload() {
        return """
                <?xml version="1.0" encoding="utf-8"?><result><Event><Integration_Status>Completed with Warnings</Integration_Status><Initiated_DateTime>2024-04-05T14:40:23Z</Initiated_DateTime><Completed_DateTime>2024-04-05T14:40:28Z</Completed_DateTime><Total_Processing_Time_Seconds>5.156</Total_Processing_Time_Seconds><External_Job_ID>4383699_1712328021590</External_Job_ID><Integration_Event_WID>eac4c046c5361001fbd7a688745d0000</Integration_Event_WID><Debug_Mode>false</Debug_Mode><Include_Matriculated_Students>true</Include_Matriculated_Students><Include_Student_Prospects>true</Include_Student_Prospects><Include_Workers>true</Include_Workers><Automatic_Merge_Count>0</Automatic_Merge_Count><Possible_Match_Count>0</Possible_Match_Count><Rules_Processed>1</Rules_Processed><Rules_Skipped>13</Rules_Skipped><Stop_Processing_If_Single_Automatic_Merge_Found>false</Stop_Processing_If_Single_Automatic_Merge_Found></Event><warnings>
                <warning>Rule /WD Test/ 106-Name + DOB + Email + Phone skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 107-Name + DOB + Phone + Address skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 108-Name + DOB + Email + Address skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 109-Name + DOB + Email skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 110-Name + DOB + Phone skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 114-DOB + Email + Phone skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 115-DOB + Phone + Address skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 116-DOB+Email+Address skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 105-National ID skipped. Reason:  Required value(s) for National ID were not submitted.</warning>
                <warning>Rule /WD Test/ 101-Name + National ID + DOB skipped. Reason:  Required value for Date of Birth was not submitted. Required value(s) for National ID were not submitted.</warning>
                <warning>Rule /WD Test/ 102-Name + National ID skipped. Reason:  Required value(s) for National ID were not submitted.</warning>
                <warning>Rule /WD Test/ 103-Name + DOB skipped. Reason:  Required value for Date of Birth was not submitted.</warning>
                <warning>Rule /WD Test/ 104-National ID + DOB skipped. Reason:  Required value for Date of Birth was not submitted. Required value(s) for National ID were not submitted.</warning>
                </warnings><errors/><Matches total="0"/></result>""";
    }

    @Test
    void getInstructors() throws Exception {
        this.mockMvc.perform(get("/integration/v1/destiny/test/workday/instructors")).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void createOrUpdateStudent() throws Exception {
        this.mockMvc.perform(
                        patch("/integration/v1/destiny/test/workday/students/666555444").contentType(MediaType.APPLICATION_JSON)
                                .content(getStudentCreateOrUpdatePayload())).andDo(print()).andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.data", anEmptyMap()))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void triggerSearchMatch() throws Exception {
        this.mockMvc.perform(post("/integration/v1/destiny/test/workday/students/duplicate-check-request").contentType(
                        MediaType.APPLICATION_JSON).content(getSearchMatchTriggerPayload())).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }


    @Test
    void handleSearchMatchResponse() throws Exception {
        wireMockServer.stubFor(WireMock.post(WireMock.urlPathEqualTo("/webservice/InternalViewREST/login"))
                .willReturn(aResponse().withHeader("Content-Type", "application/json;charset=UTF-8")
                        .withBody("""
                                {
                                    "loginResponse": {
                                        "sessionId": "CF41895119D7D0D2DF967EDE8203D4A9"
                                    }
                                }
                                """))

        );
        wireMockServer.stubFor(WireMock.post(WireMock.urlMatching(".*/async/events/.*"))
                .willReturn(aResponse().withHeader("Content-Type", "text/xml;charset=UTF-8")
                        .withBody(""))

        );
        this.mockMvc.perform(post("/integration/v1/workday/test/destiny/students/duplicate-check-response").contentType(
                        MediaType.APPLICATION_XML_VALUE).content(getSearchMatchResponsePayload())).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.data", anEmptyMap()))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }
}