package org.moderncampus.integration.workday.webservice;

import org.moderncampus.integration.webservice.BaseIntegrationApplication;
import org.moderncampus.integration.webservice.config.SharedAppConfiguration;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.servers.Server;

@OpenAPIDefinition(
        servers = @Server(url = "/"),
        info = @Info(title = "Modern Campus Integration Connect Workday APIs", version = "v1")
)
@SecurityScheme(name = "apiToken", type = SecuritySchemeType.APIKEY, paramName = "X-Api-Token", in = SecuritySchemeIn.HEADER)
@SecurityScheme(name = "basicAuth", type = SecuritySchemeType.HTTP,
        scheme = "basic")
@SpringBootApplication
@EnableCaching
@Import(SharedAppConfiguration.class)
public class Application extends BaseIntegrationApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(Application.class);
        doBaseSetup(application);
        application.run(args);
    }

    @Bean
    public GroupedOpenApi curriculogOpenApi() {
        String paths[] = {"/**/curriculog/**"};
        return GroupedOpenApi.builder().group("Curriculog Workday Integration").pathsToMatch(paths)
                .addOpenApiCustomizer(defaultAuthCustomizer()).build();
    }

    @Bean
    public GroupedOpenApi acalogOpenApi() {
        String paths[] = {"/**/acalog/**"};
        return GroupedOpenApi.builder().group("Acalog Workday Integration").pathsToMatch(paths)
                .addOpenApiCustomizer(defaultAuthCustomizer()).build();
    }

    @Bean
    public GroupedOpenApi destinyOpenApi() {
        String paths[] = {"/**/destiny/**"};
        return GroupedOpenApi.builder().group("Destiny Workday Integration").pathsToMatch(paths).build();
    }

    public OpenApiCustomizer defaultAuthCustomizer() {
        return openApi -> openApi.getComponents().getSecuritySchemes().entrySet()
                .removeIf(entry -> !"apiToken".equalsIgnoreCase(entry.getKey()));
    }
}
