package org.moderncampus.integration.workday.webservice.service.destiny;

import static org.moderncampus.integration.workday.workflow.route.identifier.destiny.WorkdayRouteIds.*;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.cewd.CewdInstructor;
import org.moderncampus.integration.dto.cewd.CewdStudent;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntStagedResult;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntegrationResult;
import org.moderncampus.integration.workday.dto.dupsearch.cewd.request.WDStudentDuplicateCheckRequest;
import org.moderncampus.integration.workday.webservice.dto.destiny.StudentUpdateRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class WorkdayIntegrationService extends BaseIntegrationService {

    public IntegrationResponse<CewdInstructor> getInstructors()
            throws Exception {
        return executeRoute(V1_D1_WORKDAY_GET_INSTRUCTORS);
    }

    public IntegrationResponse<ObjectNode> updateStudent(String id, StudentUpdateRequest updateRequest)
            throws Exception {
        updateRequest.setId(id);
        CewdStudent student = new CewdStudent();
        BeanUtils.copyProperties(updateRequest, student);
        return executeRoute(V1_D1_WORKDAY_UPDATE_STUDENT, student, null);
    }

    public IntegrationResponse<DuplicateCheckStudioIntStagedResult> createDuplicateCheckRequest(
            WDStudentDuplicateCheckRequest searchMatchInitRequest)
            throws Exception {
        return executeRoute(V1_D1_WORKDAY_CREATE_DUPLICATE_CHECK_REQ, searchMatchInitRequest, null);
    }

    public IntegrationResponse<ObjectNode> createDuplicateCheckResponse(
            DuplicateCheckStudioIntegrationResult response)
            throws Exception {
        return executeRoute(V1_WORKDAY_D1_SEND_DUPLICATE_CHECK_RESP, response, null);
    }
}
