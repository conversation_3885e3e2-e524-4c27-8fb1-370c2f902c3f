package org.moderncampus.integration.workday.webservice.service.health;

import static org.moderncampus.integration.workday.workflow.route.identifier.common.WorkdayRouteIds.V1_WORKDAY_HEALTH;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.boot.actuate.health.Health;
import org.springframework.stereotype.Service;

@Service
public class HealthEndpointService extends BaseIntegrationService {

    public IntegrationResponse<Health> getHealthStatus()
            throws Exception {
        return getHealthStatus(V1_WORKDAY_HEALTH);
    }
}
