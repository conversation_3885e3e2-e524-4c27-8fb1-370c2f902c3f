package org.moderncampus.integration.workday.webservice.controller.curriculog;

import static org.moderncampus.integration.Constants.*;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.workday.dto.course.CreateWorkdayCourse;
import org.moderncampus.integration.workday.dto.course.UpdateWorkdayCourse;
import org.moderncampus.integration.workday.dto.course.WorkdayCourse;
import org.moderncampus.integration.workday.webservice.service.curriculog.CurriculogWorkdayIntegrationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + CURRICULOG_SYSTEM_ID + "/{schoolId}/"
        + WORKDAY_SYSTEM_ID)
@Tag(name = "Acalog Workday Integration API", description = "Manages integration requests from Acalog to Workday.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class CurriculogWorkdayIntegrationController {

    static final String COURSES = "courses";

    static final String UPDATE_COURSE = COURSES + "/" + "{id}";

    CurriculogWorkdayIntegrationService workdayIntegrationService;

    @Operation(summary = "Create course into Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Course created in Workday"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = COURSES)
    @ResponseStatus(HttpStatus.CREATED)
    public IntegrationResponse<WorkdayCourse> createCourse(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @RequestBody CreateWorkdayCourse ccWorkdayCourse)
            throws Exception {
        return workdayIntegrationService.createCourse(ccWorkdayCourse);
    }

    @Operation(summary = "Update course into Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Course updated in Workday"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_COURSE)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> updateCourse(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @PathVariable String id,
            @RequestBody UpdateWorkdayCourse ccWorkdayCourse)
            throws Exception {
        return workdayIntegrationService.updateCourse(id, ccWorkdayCourse);
    }

}
