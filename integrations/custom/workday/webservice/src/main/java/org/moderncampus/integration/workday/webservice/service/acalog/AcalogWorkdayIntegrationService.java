package org.moderncampus.integration.workday.webservice.service.acalog;

import static org.moderncampus.integration.workday.workflow.route.identifier.acalog.WorkdayRouteIds.V1_ACALOG_WORKDAY_GET_COURSES;

import java.util.List;
import java.util.Set;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.moderncampus.integration.workday.dto.course.WorkdayCourse;
import org.springframework.stereotype.Service;

@Service
public class AcalogWorkdayIntegrationService extends BaseIntegrationService {

    public IntegrationResponse<List<WorkdayCourse>> getCourseList(BasePaginationRequest requestWS)
            throws Exception {
        return searchEntities(requestWS, V1_ACALOG_WORKDAY_GET_COURSES, null,
                Set.of(Constants.PAGE_SIZE, Constants.PAGE_NUM, Constants.TOTAL_PAGES, Constants.TOTAL_SIZE));
    }
}
