package org.moderncampus.integration.workday.webservice.dto.destiny;

import java.util.List;

import org.moderncampus.integration.dto.cewd.CewdAddress;
import org.moderncampus.integration.dto.cewd.CewdEmail;
import org.moderncampus.integration.dto.cewd.CewdPhone;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StudentUpdateRequest {

    @Getter(onMethod_ = {@Schema(hidden = true)})
    String id;

    String number;

    List<CewdAddress> addresses;

    List<CewdPhone> phones;

    List<CewdEmail> emails;
}
