package org.moderncampus.integration.workday.webservice.controller.destiny;

import static org.moderncampus.integration.Constants.*;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.cewd.CewdInstructor;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntStagedResult;
import org.moderncampus.integration.workday.dto.dupsearch.cewd.request.WDStudentDuplicateCheckRequest;
import org.moderncampus.integration.workday.webservice.dto.destiny.StudentUpdateRequest;
import org.moderncampus.integration.workday.webservice.service.destiny.WorkdayIntegrationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + DESTINY_SYSTEM_ID + "/{schoolId}/"
        + WORKDAY_SYSTEM_ID)
@Tag(name = "Destiny Workday Integration API", description = "Manages integration requests from Destiny to Workday.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class DestinyWorkdayIntegrationController {

    static final String INSTRUCTORS = "instructors";

    static final String STUDENTS = "students";

    static final String UPDATE_STUDENT = STUDENTS + "/" + "{id}";

    static final String DUPLICATE_CHECK_REQUEST = STUDENTS + "/" + "duplicate-check-request";

    WorkdayIntegrationService workerIntegrationService;

    @Operation(summary = "Get Instructors From Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Instructors"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = INSTRUCTORS)
    public IntegrationResponse<CewdInstructor> getInstructors(
            @ParameterObject IntegrationRequestContext requestContextWS)
            throws Exception {
        return workerIntegrationService.getInstructors();
    }

    @Operation(summary = "Update Student In Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful Student Update"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_STUDENT)
    public IntegrationResponse<ObjectNode> updateStudent(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @PathVariable String id,
            @RequestBody StudentUpdateRequest updateRequest) throws Exception {
        return workerIntegrationService.updateStudent(id, updateRequest);
    }

    @Operation(summary = "Create Duplicate Check Request In Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created duplicate check request in Workday"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = DUPLICATE_CHECK_REQUEST)
    public IntegrationResponse<DuplicateCheckStudioIntStagedResult> createDuplicateCheckRequest(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @RequestBody WDStudentDuplicateCheckRequest searchMatchInitRequest) throws Exception {
        return workerIntegrationService.createDuplicateCheckRequest(searchMatchInitRequest);
    }
}
