package org.moderncampus.integration.workday.webservice.controller.acalog;

import static org.moderncampus.integration.Constants.*;

import java.util.List;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.workday.dto.course.WorkdayCourse;
import org.moderncampus.integration.workday.webservice.dto.WorkdayAPIPaginationRequest;
import org.moderncampus.integration.workday.webservice.service.acalog.AcalogWorkdayIntegrationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + ACALOG_SYSTEM_ID + "/{schoolId}/"
        + WORKDAY_SYSTEM_ID)
@Tag(name = "Acalog Workday Integration API", description = "Manages integration requests from Acalog to Workday.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class AcalogWorkdayIntegrationController {

    static final String COURSES = "courses";

    AcalogWorkdayIntegrationService workdayIntegrationService;

    @Operation(summary = "Get Courses From Workday")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Courses"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = COURSES)
    public IntegrationResponse<List<WorkdayCourse>> getCourses(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @ParameterObject WorkdayAPIPaginationRequest paginationRequest)
            throws Exception {
        return workdayIntegrationService.getCourseList(paginationRequest);
    }
}
