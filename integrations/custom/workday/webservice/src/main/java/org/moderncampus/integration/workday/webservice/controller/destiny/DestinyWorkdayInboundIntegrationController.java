package org.moderncampus.integration.workday.webservice.controller.destiny;

import static org.moderncampus.integration.Constants.*;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntegrationResult;
import org.moderncampus.integration.workday.webservice.service.destiny.WorkdayIntegrationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + WORKDAY_SYSTEM_ID + "/{schoolId}/"
        + DESTINY_SYSTEM_ID)
@Tag(name = "Workday to Destiny Integration API", description = "Manages integration requests from Workday to Destiny.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "basicAuth")
@Slf4j
public class DestinyWorkdayInboundIntegrationController {

    static final String STUDENTS = "students";

    static final String DUPLICATE_CHECK_RESPONSE = STUDENTS + "/" + "duplicate-check-response";

    WorkdayIntegrationService workerIntegrationService;

    @Operation(summary = "Create response event in the Destiny One system for a corresponding Workday student duplicate check request")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created response event in the Destiny One system"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = DUPLICATE_CHECK_RESPONSE,
            consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public IntegrationResponse<ObjectNode> createDuplicateCheckRequest(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @RequestBody DuplicateCheckStudioIntegrationResult result) throws Exception {
        return workerIntegrationService.createDuplicateCheckResponse(result);
    }
}
