package org.moderncampus.integration.workday.webservice.dto;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Optional;

import org.moderncampus.integration.dto.base.IPaginationConstruct;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.moderncampus.integration.workday.dto.WorkdayAPIPaginationConstruct;
import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkdayAPIPaginationRequest extends BasePaginationRequest {

    @Parameter(in = ParameterIn.QUERY)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    Optional<LocalDate> asOfEffectiveDate = Optional.empty();

    @Parameter(in = ParameterIn.QUERY)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    Optional<OffsetDateTime> asOfEntryMoment = Optional.empty();

    @Override
    protected IPaginationConstruct createPaginationConstruct() {
        return new WorkdayAPIPaginationConstruct();
    }

    @Schema(hidden = true)
    public IPaginationConstruct getPaginationConstruct() {
        WorkdayAPIPaginationConstruct construct = (WorkdayAPIPaginationConstruct) super.getPaginationConstruct();
        construct.setAsOfEffectiveDate(asOfEffectiveDate.orElse(null));
        construct.setAsOfEntryMoment(asOfEntryMoment.orElse(null));
        return construct;
    }
}
