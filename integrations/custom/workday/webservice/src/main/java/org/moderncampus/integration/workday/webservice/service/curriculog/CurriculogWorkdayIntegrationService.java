package org.moderncampus.integration.workday.webservice.service.curriculog;

import static org.moderncampus.integration.workday.workflow.route.identifier.curriculog.WorkdayRouteIds.V1_CURRICULOG_WORKDAY_CREATE_COURSE;
import static org.moderncampus.integration.workday.workflow.route.identifier.curriculog.WorkdayRouteIds.V1_CURRICULOG_WORKDAY_UPDATE_COURSE;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.moderncampus.integration.workday.dto.course.CreateWorkdayCourse;
import org.moderncampus.integration.workday.dto.course.UpdateWorkdayCourse;
import org.moderncampus.integration.workday.dto.course.WorkdayCourse;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class CurriculogWorkdayIntegrationService extends BaseIntegrationService {

    public IntegrationResponse<WorkdayCourse> createCourse(CreateWorkdayCourse createCCWDCourse)
            throws Exception {
        return executeRoute(V1_CURRICULOG_WORKDAY_CREATE_COURSE, createCCWDCourse, null);
    }

    public IntegrationResponse<ObjectNode> updateCourse(String id, UpdateWorkdayCourse updateCCWDCourse)
            throws Exception {
        updateCCWDCourse.setId(id);
        return executeRoute(V1_CURRICULOG_WORKDAY_UPDATE_COURSE, updateCCWDCourse, null);
    }
}
