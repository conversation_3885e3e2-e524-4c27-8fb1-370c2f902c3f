package org.moderncampus.integration.workday.webservice.security;

import static org.moderncampus.integration.Constants.DESTINY_SYSTEM_ID;
import static org.moderncampus.integration.Constants.WORKDAY_SYSTEM_ID;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;
import org.moderncampus.integration.tenants.service.TenantApiTokenService;
import org.moderncampus.integration.webservice.security.BasicAuthNoPreemptiveResponseEntryPoint;
import org.moderncampus.integration.webservice.security.WebSecurityConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;

@Configuration
@Import(WebSecurityConfig.class)
public class CustomWebSecurityConfig {

    @Value("${spring.profiles.active:}")
    private String activeProfiles;

    static final List<String> SUPPORTED_INBOUND_SYSTEM_IDS = List.of(DESTINY_SYSTEM_ID);

    static final String INBOUND_PATH_PATTERN_TEMPLATE = "/integration/**/" + WORKDAY_SYSTEM_ID + "/**/%s/**";

    @Bean
    @Order(1)
    public SecurityFilterChain workdayInboundSecurityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable);

        http.sessionManagement(configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        http
                .securityMatcher(new OrRequestMatcher(
                        SUPPORTED_INBOUND_SYSTEM_IDS.stream().map((inboundSystem -> AntPathRequestMatcher.antMatcher(
                                        String.format(INBOUND_PATH_PATTERN_TEMPLATE, inboundSystem))))
                                .collect(Collectors.toList())))
                .authorizeHttpRequests(
                        auth -> {
                            if (activeProfiles.contains("dev")) {
                                auth.anyRequest().permitAll();
                            } else {
                                auth.anyRequest().authenticated();
                            }
                        })
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .httpBasic(configurer -> configurer.authenticationEntryPoint(
                        new BasicAuthNoPreemptiveResponseEntryPoint()));
        return http.build();
    }

    @Bean
    public UserDetailsService tenantAuthDetailsService(TenantApiTokenService apiTokenService) {
        return username ->
                Optional.ofNullable(apiTokenService.retrieveApiTokenForTenant(username))
                        .map((token) -> new TenantPrincipal(Pair.of(username, token)))
                        .orElseThrow(
                                () -> new UsernameNotFoundException(
                                        "Username does not match an existing tenant name."));
    }

    @Bean
    public PasswordEncoder encoder() {
        return NoOpPasswordEncoder.getInstance();
    }
}
