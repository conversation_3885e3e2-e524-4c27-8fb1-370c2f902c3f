package org.moderncampus.integration.workday.webservice.controller.health;

import static org.moderncampus.integration.Constants.VERSION_1;
import static org.moderncampus.integration.Constants.WORKDAY_SYSTEM_ID;

import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.workday.webservice.service.health.HealthEndpointService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.boot.actuate.health.Health;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + "{sourceSystemId}" + "/{schoolId}/"
        + WORKDAY_SYSTEM_ID)
@Tag(name = "Workday Integration Health API", description = "Health endpoint for the Workday Integration.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class HealthEndpointController {

    HealthEndpointService healthEndpointService;

    @Operation(summary = "Get Workday Health Endpoint Status", hidden = true)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Health Status"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = "health")
    public Health getHealthStatus(
            @ParameterObject IntegrationRequestContext requestContextWS)
            throws Exception {
        return healthEndpointService.getHealthStatus().getData();
    }
}
