spring.config.import=integration-common.properties
# springdoc properties
springdoc.swagger-ui.path=/swagger/workday/
springdoc.api-docs.path=/swagger/workday/docs
# common module properties
customization.transform.classpath=/workflow/build/classes/java/customTransform
# workday workflow module properties
workday.integration.auth.clientId=
workday.integration.auth.clientSecret=
workday.integration.auth.refreshToken=
workday.integration.tenant=
workday.integration.host=
workday.integration.version=v41.0
workday.integration.reportUser=
workday.integration.report.mcd1getinstructors.name=
workday.integration.mapping.duplicateCheckIntSystemId=
workday.integration.mapping.studentIdType=
workday.integration.mapping.nationalIdType=
workday.integration.mapping.address.home=
workday.integration.mapping.address.work=
workday.integration.mapping.phone.home=
workday.integration.mapping.phone.work=
workday.integration.mapping.skipBusinessProcess=true