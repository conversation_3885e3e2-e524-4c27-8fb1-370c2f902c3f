import static org.moderncampus.integration.workday.workflow.route.identifier.destiny.WorkdayRouteIds.V1_D1_WORKDAY_UPDATE_STUDENT;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.test.spring.junit5.CamelSpringBootTest;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.dto.cewd.CewdPerson;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

@CamelSpringBootTest
@EnableAutoConfiguration
@SpringBootTest(classes = {TestConfig.class})
@SpringJUnitWebConfig
public class WorkdayRouteTest {

    @Autowired
    ProducerTemplate producerTemplate;

    @Autowired
    CamelContext context;

    @Test
    public void shouldInjectEndpoint() throws InterruptedException {
        String routeUri = RouteSupport.buildDirectRouteURI(V1_D1_WORKDAY_UPDATE_STUDENT.getId());
        Endpoint endpoint = context.getEndpoint(routeUri);
        CewdPerson person = new CewdPerson();
        person.setId("35432");
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        exchange.getMessage().setBody(person);
        Exchange response = producerTemplate.send(routeUri, exchange);
        System.out.println(response.getMessage());
    }
}
