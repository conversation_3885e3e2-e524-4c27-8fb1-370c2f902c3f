package org.moderncampus.integration.workday.workflow.transform.core

import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.dto.course.WorkdayCourse
import org.moderncampus.integration.workday.dto.course.WorkdayCourse.AcademicUnitData
import org.moderncampus.integration.workday.dto.course.WorkdayCourse.CombinationInstructionalFormat
import org.moderncampus.integration.workday.dto.course.WorkdayCourse.CourseComponentData
import org.moderncampus.integration.workday.dto.course.WorkdayCourse.GradingBasesData
import org.moderncampus.integration.workday.workflow.transform.helper.WDStudentRecordsServiceReqTransformHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

import java.time.LocalDate

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.helper.GroovyXMLSupport.updateNodeCollection

@Component
class CourseToSubmitStudentCourseDataTransform extends BaseTransformer<WorkdayCourse, String> {

    static Logger LOGGER = LoggerFactory.getLogger(CourseToSubmitStudentCourseDataTransform.class)

    @Override
    protected String doTransform(TransformContext context, WorkdayCourse input) {
        def xmlBuilder = new StreamingMarkupBuilder()
        xmlBuilder.encoding = 'UTF-8'
        NodeChild effectiveSnapshotNode = context.getContextProp(LOADED_ENTITY, NodeChild.class)
        def crseDataXml = xmlBuilder.bind { builder -> //builder is of type BaseMarkupBuilder$Document
            "wd:Student_Course_Data"() {
                mapCourseDataNode(builder, input, effectiveSnapshotNode)
            }
        }

        def crseRefXml = xmlBuilder.bind {
            if (effectiveSnapshotNode) {
                mkp.yield effectiveSnapshotNode.parent().parent().'Student_Course_Reference'
            }
        }
        return WDStudentRecordsServiceReqTransformHelper.mapToSubmitStudentCrseRequest(context,
                crseRefXml.toString(),
                crseDataXml.toString(), effectiveSnapshotNode == null)
    }

    private void mapCourseDataNode(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        try {
            if (!effectiveSnapshotNode) {
                builder."wd:Student_Course_Data_Data"() {
                    mapAvailableDates(builder, input, effectiveSnapshotNode)
                }
            } else {
                mapAvailableDates(builder, input, effectiveSnapshotNode)
                builder.mkp.yield effectiveSnapshotNode.parent().'Student_Course_Data_Data'
            }

            if (!effectiveSnapshotNode) {
                builder."wd:Student_Course_Data_Snapshot_Data"() {
                    mapCourseDataSnapShotDataNode(builder, input, effectiveSnapshotNode)
                }
            } else {
                mapCourseDataSnapShotDataNode(builder, input, effectiveSnapshotNode)
                builder.mkp.yield effectiveSnapshotNode
            }
        } catch (Exception e) {
            LOGGER.info("Exception caught during mapping", e)
            throw e
        }
    }

    private void mapAvailableDates(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.firstAvailable || input.lastAvailable) {
            //Need builder's scope here as by default the scope is lexical and referring to the outer class instance. Inside the calling closure
            // however, the scope is resolved to the builder automatically via Groovy's delegation strategy.
            def courseDataDataNode = effectiveSnapshotNode?.parent()?.'Student_Course_Data_Data'
            if (input.firstAvailable) {
                effectiveSnapshotNode ? courseDataDataNode.'First_Available'.replaceBody(input.firstAvailable)
                        : buildFirstAvailableNode(builder, input.firstAvailable)
            }
            if (input.lastAvailable) {
                if (effectiveSnapshotNode) {
                    def lastAvailableNode = courseDataDataNode.'Last_Available'
                    if (lastAvailableNode.isEmpty()) {
                        courseDataDataNode << {
                            buildLastAvailableNode(delegate, input.lastAvailable)
                        }
                    } else {
                        lastAvailableNode.replaceBody(input.lastAvailable)
                    }
                } else {
                    buildLastAvailableNode(builder, input.lastAvailable)
                }
            }
        }
    }

    private void mapCourseDataSnapShotDataNode(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        mapPeriodOfferedRefTypeIds(builder, input, effectiveSnapshotNode)
        mapAcademicUnitData(builder, input, effectiveSnapshotNode)
        mapCourseTitle(builder, input, effectiveSnapshotNode)
        mapAbbreviatedTitle(builder, input, effectiveSnapshotNode)
        mapCIPCodeRefId(builder, input, effectiveSnapshotNode)
        mapDescription(builder, input, effectiveSnapshotNode)
        mapCourseComponents(builder, input, effectiveSnapshotNode)
        mapCombinationInstructionalFormats(builder, input, effectiveSnapshotNode)
        mapGradingBasis(builder, input, effectiveSnapshotNode)
        mapRepeatableFlags(builder, input, effectiveSnapshotNode)
        mapEffectiveDate(builder, input, effectiveSnapshotNode)
        mapSectionOverridesAllowed(builder, input, effectiveSnapshotNode)
        mapAcademicLevelRefId(builder, input, effectiveSnapshotNode)
        mapLocationRefIds(builder, input, effectiveSnapshotNode)
        mapUnitTypeRefId(builder, input, effectiveSnapshotNode)
        mapCourseTagRefIds(builder, input, effectiveSnapshotNode)
        mapMinAndMaxUnits(builder, input, effectiveSnapshotNode)
        mapCourseListingData(builder, input, effectiveSnapshotNode)
        mapCourseContactHours(builder, input, effectiveSnapshotNode)
    }

    private void mapPeriodOfferedRefTypeIds(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.periodsOfferedRefTypeIds, effectiveSnapshotNode, effectiveSnapshotNode ?
                effectiveSnapshotNode.'Periods_Offered_Reference'.groupBy { node ->
                    GPathResult child = node.'*'.find { idNode -> idNode.'@wd:type' == "Academic_Periods_Offered_Type_ID" } as GPathResult
                    return extractNodeText(child)
                } : null, "buildPeriodOfferedRefNode")
    }

    private void mapAcademicUnitData(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.academicUnitData, effectiveSnapshotNode, effectiveSnapshotNode ?
                effectiveSnapshotNode.'Academic_Unit_Data'.groupBy { node ->
                    GPathResult child = node.'Academic_Unit_Reference'.'*'.find { idNode -> idNode.'@wd:type' == "Academic_Unit_ID" } as GPathResult
                    return extractNodeText(child)
                } : null, "buildAcademicUnitDataNode")
    }

    private void mapCourseTitle(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.courseTitle) {
            effectiveSnapshotNode ? effectiveSnapshotNode.'Course_Title'.replaceBody(input.courseTitle) : builder."wd:Course_Title"(input.courseTitle)
        }
    }

    private void mapAbbreviatedTitle(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.abbreviatedTitle) {
            effectiveSnapshotNode ? effectiveSnapshotNode.'Abbreviated_Title'.replaceBody(input.abbreviatedTitle) :
                    builder."wd:Abbreviated_Title"(input.abbreviatedTitle)
        }
    }

    private void mapCIPCodeRefId(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.cipCodeRefId) {
            if (effectiveSnapshotNode) {
                def cipCodeRefNode = effectiveSnapshotNode.'CIP_Code_Reference'
                if (cipCodeRefNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildCipCodeRefNode(delegate, input)
                    }
                } else {
                    cipCodeRefNode.replaceNode {
                        buildCipCodeRefNode(delegate, input)
                    }
                }
            } else {
                buildCipCodeRefNode(builder, input)
            }
        }
    }

    private void mapDescription(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.description) {
            if (effectiveSnapshotNode) {
                def descriptionNode = effectiveSnapshotNode.'Description'
                if (descriptionNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildDescriptionNode(delegate, input.description)
                    }
                } else {
                    descriptionNode.replaceBody(input.description)
                }
            } else {
                buildDescriptionNode(builder, input.description)
            }
        }
    }

    private void mapCourseComponents(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.courseComponents, effectiveSnapshotNode, effectiveSnapshotNode ? effectiveSnapshotNode.
                'Student_Course_Component_Data'.groupBy { node ->
            GPathResult instructionalFormatNode = node.'Instructional_Format_Reference'.'*'.find { idNode ->
                idNode.'@wd:type' ==
                        "Instructional_Format_ID"
            } as GPathResult
            return extractNodeText(instructionalFormatNode)
        } : null, "buildCourseComponentDataNode", { item -> item.refId })
    }

    private void mapCombinationInstructionalFormats(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.combinationInstructionalFormats, effectiveSnapshotNode, effectiveSnapshotNode ?
                effectiveSnapshotNode.'Course_Combination_Instructional_Format_Contact_Hours_Data'.groupBy { node ->
                    GPathResult child = node.'Combination_Instructional_Format_Reference'.'*'.find { idNode ->
                        idNode.'@wd:type' == "Instructional_Format_ID"
                    } as GPathResult
                    return extractNodeText(child)
                } : null, "buildCourseCombinationInstructionalFormatNode",
                { item -> item.refId })
    }

    private void mapGradingBasis(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.allowedGradingBases, effectiveSnapshotNode, effectiveSnapshotNode ?
                effectiveSnapshotNode.'Default_Grading_Bases_Reference'.groupBy { node ->
                    GPathResult child = node.'*'.find { idNode -> idNode.'@wd:type' == "Student_Grading_Basis_ID" } as GPathResult
                    return extractNodeText(child)
                } : null, "buildDefaultGradingBasisModeRefNode")
    }

    private void mapRepeatableFlags(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.repeatable != null) {
            if (effectiveSnapshotNode) {
                def repeatableNode = effectiveSnapshotNode.'Repeatable'
                if (repeatableNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildRepeatableNode(delegate, input.repeatable)
                    }
                } else {
                    repeatableNode.replaceBody(input.repeatable)
                }
            } else {
                buildRepeatableNode(builder, input.repeatable)
            }
        }
        if (input.concurrentlyRepeatable != null) {
            if (effectiveSnapshotNode) {
                def concurrentlyRepeatableNode = effectiveSnapshotNode.'Concurrently_Repeatable'
                if (concurrentlyRepeatableNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildConcurrentlyRepeatableNode(delegate, input.concurrentlyRepeatable)
                    }
                } else {
                    concurrentlyRepeatableNode.replaceBody(input.concurrentlyRepeatable)
                }
            } else {
                buildConcurrentlyRepeatableNode(builder, input.concurrentlyRepeatable)
            }
        }
        if (input.repeatMaxAttempts != null) {
            if (effectiveSnapshotNode) {
                def repeatMaxAttemptsNode = effectiveSnapshotNode.'Repeat_Maximum_Attempts'
                if (repeatMaxAttemptsNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildRepeatMaxAttemptsNode(delegate, input.repeatMaxAttempts)
                    }
                } else {
                    repeatMaxAttemptsNode.replaceBody(input.repeatMaxAttempts)
                }
            } else {
                buildRepeatMaxAttemptsNode(builder, input.repeatMaxAttempts)
            }
        }
        if (input.repeatMaxUnits != null) {
            if (effectiveSnapshotNode) {
                def repeatMaxUnitsNode = effectiveSnapshotNode.'Repeat_Maximum_Units'
                if (repeatMaxUnitsNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildRepeatMaxUnitsNode(delegate, input.repeatMaxUnits)
                    }
                } else {
                    repeatMaxUnitsNode.replaceBody(input.repeatMaxUnits)
                }
            } else {
                buildRepeatMaxUnitsNode(builder, input.repeatMaxUnits)
            }
        }
    }

    private void mapEffectiveDate(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.effectiveDate) {
            effectiveSnapshotNode ? effectiveSnapshotNode.'Effective_Date'.replaceBody(input.effectiveDate) : builder."wd:Effective_Date"(input.
                    effectiveDate)
        }
    }

    private void mapSectionOverridesAllowed(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.sectionOverridesAllowed != null) {
            if (effectiveSnapshotNode) {
                def overridesAllowedNode = effectiveSnapshotNode.'Section_Overrides_Allowed'
                if (overridesAllowedNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildSectionOverridesAllowedNode(delegate, input.sectionOverridesAllowed)
                    }
                } else {
                    overridesAllowedNode.replaceBody(input.sectionOverridesAllowed)
                }
            } else {
                buildSectionOverridesAllowedNode(builder, input.sectionOverridesAllowed)
            }
        }
    }

    private void mapAcademicLevelRefId(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.academicLevelRefId) {
            if (effectiveSnapshotNode) {
                def academicLevelRefNode = effectiveSnapshotNode.'Academic_Level_Reference' as NodeChildren
                if (academicLevelRefNode.isEmpty()) {
                    throw new RuntimeException('Unable to update without a default academic level reference node')
                }
                academicLevelRefNode.replaceNode {
                    buildAcademicLevelRefNode(builder, input.academicLevelRefId)
                }
            } else {
                buildAcademicLevelRefNode(builder, input.academicLevelRefId)
            }
        }
    }

    private void mapLocationRefIds(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.allowedLocationRefIds, effectiveSnapshotNode, effectiveSnapshotNode ? effectiveSnapshotNode.
                'Allowed_Locations_Reference'.groupBy { node ->
            GPathResult child = node.'*'.find { idNode -> idNode.'@wd:type' == "Location_ID" } as GPathResult
            return extractNodeText(child)
        } : null, "buildAllowedLocationRefNode")
    }

    private void mapUnitTypeRefId(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.unitTypeRefId) {
            if (effectiveSnapshotNode) {
                def unitTypeRefNode = effectiveSnapshotNode.'Unit_Type_Reference' as NodeChildren
                if (unitTypeRefNode.isEmpty()) {
                    throw new RuntimeException('Unable to update without a unit type reference node')
                }
                unitTypeRefNode.replaceNode {
                    buildUnitTypeRefNode(builder, input.unitTypeRefId)
                }
            } else {
                buildUnitTypeRefNode(builder, input.unitTypeRefId)
            }
        }
    }

    private void mapCourseTagRefIds(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        updateNodeCollection(this, builder, input.courseTagRefIds, effectiveSnapshotNode, effectiveSnapshotNode ? effectiveSnapshotNode.
                'Course_Tag_Reference'.groupBy { node ->
            GPathResult child = node.'*'.find { idNode -> idNode.'@wd:type' == "Student_Course_Tag_ID" } as GPathResult
            return extractNodeText(child)
        } : null, "buildCourseTagRefNode")
    }

    private void mapMinAndMaxUnits(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.minUnits != null) {
            if (effectiveSnapshotNode) {
                def minUnitsNode = effectiveSnapshotNode.'Minimum_Units'
                if (minUnitsNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildMinUnitsNode(delegate, input.minUnits)
                    }
                } else {
                    minUnitsNode.replaceBody(input.minUnits)
                }
            } else {
                buildMinUnitsNode(builder, input.minUnits)
            }
        }
        if (input.maxUnits != null) {
            if (effectiveSnapshotNode) {
                def maxUnitsNode = effectiveSnapshotNode.'Maximum_Units'
                if (maxUnitsNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildMaxUnitsNode(delegate, input.maxUnits)
                    }
                } else {
                    maxUnitsNode.replaceBody(input.maxUnits)
                }
            } else {
                buildMaxUnitsNode(builder, input.maxUnits)
            }
        }
    }

    private void mapCourseListingData(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.courseListingData && !effectiveSnapshotNode) { //Updates to course listing data is unsupported on the WD APIs
            input.courseListingData.each { listingData ->
                buildStudentCourseListingDataNode(builder, listingData)
            }
        }
    }

    private void mapCourseContactHours(def builder, WorkdayCourse input, NodeChild effectiveSnapshotNode) {
        if (input.contactHours) {
            if (effectiveSnapshotNode) {
                def contactHoursNode = effectiveSnapshotNode.'Contact_Hours'
                if (contactHoursNode.isEmpty()) {
                    effectiveSnapshotNode << {
                        buildContactHoursNode(delegate, input.contactHours)
                    }
                } else {
                    contactHoursNode.replaceBody(input.contactHours)
                }
            } else {
                buildContactHoursNode(builder, input.contactHours)
            }
        }
    }

    private void buildPeriodOfferedRefNode(def builder, String typeId) {
        builder."wd:Periods_Offered_Reference"() {
            "wd:ID"("wd:type": "Academic_Periods_Offered_Type_ID", typeId)
        }
    }

    private void buildAcademicUnitDataNode(def builder, AcademicUnitData academicUnitData) {
        builder."wd:Academic_Unit_Data"() {
            if (academicUnitData.refId) {
                "wd:Academic_Unit_Reference"() {
                    "wd:ID"("wd:type": "Academic_Unit_ID", academicUnitData.refId)
                }
            }
            if (academicUnitData.courseInventoryOwner != null) {
                "wd:Course_Inventory_Owner"(academicUnitData.courseInventoryOwner)
            }
            if (academicUnitData.allowedToOffer != null) {
                "wd:Allowed_to_Offer"(academicUnitData.allowedToOffer)
            }
            if (academicUnitData.defaultOfferPercent) {
                "wd:Default_Offering_Percent"(academicUnitData.defaultOfferPercent)
            }
        }
    }

    private void buildCipCodeRefNode(def builder, WorkdayCourse input) {
        builder."wd:CIP_Code_Reference"() {
            "wd:ID"("wd:type": "Cip_Code_ID", input.cipCodeRefId)
            "wd:ID"("wd:type": "CIP_Code_ID_2020", input.cipCodeRefId)
        }
    }

    private void buildCourseComponentDataNode(def builder, CourseComponentData componentData) {
        builder."wd:Student_Course_Component_Data"() {
            if (componentData.refId) {
                "wd:Instructional_Format_Reference"() {
                    "wd:ID"("wd:type": "Instructional_Format_ID", componentData.refId)
                }
            }

            if (componentData.deliveryModeRefIds) {
                componentData.deliveryModeRefIds.each { refId ->
                    {
                        "wd:Delivery_Mode_Reference"() {
                            "wd:ID"("wd:type": "Delivery_Mode_ID", refId)
                        }
                    }
                }
            }

            if (componentData.controlsGrading != null) {
                "wd:Required_Student_Course_Component"(true)
                "wd:Student_Course_Component_Controls_Grading"(componentData.controlsGrading)
            }

            if (componentData.instructorLoadPercentage != null) {
                "wd:Instructor_Load_Percentage"(componentData.instructorLoadPercentage)
            }

            if (componentData.contactHours != null) {
                "wd:Course_Component_Contact_Hours"(componentData.contactHours)
            }
        }
    }

    private void buildCourseCombinationInstructionalFormatNode(def builder, CombinationInstructionalFormat format) {
        builder."wd:Course_Combination_Instructional_Format_Contact_Hours_Data"() {
            if (format.refId) {
                "wd:Combination_Instructional_Format_Reference"() {
                    "wd:ID"("wd:type": "Instructional_Format_ID", format.refId)
                }
            }
            if (format.contactHours != null) {
                "wd:Combination_Instructional_Format_Contact_Hours"(format.contactHours)
            }
        }
    }

    private void buildDefaultGradingBasisModeRefNode(def builder, GradingBasesData basesData) {
        builder."wd:Default_Grading_Bases_Reference"() {
            if (basesData.parentId) {
                "wd:ID"("wd:type": "Student_Grading_Basis_ID", "wd:parent_type": "Academic_Unit_ID", "wd:parent_id": basesData.
                        parentId, basesData.typeId)
            } else {
                "wd:ID"("wd:type": "Student_Grading_Basis_ID", basesData.typeId)
            }
        }
    }

    private void buildAcademicLevelRefNode(def builder, String academicLevelRefId) {
        builder."wd:Academic_Level_Reference"() {
            "wd:ID"("wd:type": "Academic_Level_ID", academicLevelRefId)
        }
    }

    private void buildAllowedLocationRefNode(def builder, String refId) {
        builder."wd:Allowed_Locations_Reference"() {
            "wd:ID"("wd:type": "Location_ID", refId)
        }
    }

    private void buildUnitTypeRefNode(def builder, String unitTypeRefId) {
        builder."wd:Unit_Type_Reference"() {
            "wd:ID"("wd:type": "Student_Course_Unit_Type_ID", unitTypeRefId)
        }
    }

    private void buildCourseTagRefNode(def builder, String refId) {
        builder."wd:Course_Tag_Reference"() {
            "wd:ID"("wd:type": "Student_Course_Tag_ID", refId)
        }
    }

    private void buildStudentCourseListingDataNode(def builder, WorkdayCourse.CourseListingData courseListingData) {
        builder."wd:Student_Course_Listing_Data"() {
            if (courseListingData.refId) {
                "wd:Course_Subject_Reference"() {
                    "wd:ID"("wd:type": "Course_Subject_ID", courseListingData.refId)
                }
            }
            if (courseListingData.courseNumber != null) {
                "wd:Course_Number"(courseListingData.courseNumber)
            }
        }
    }

    private void buildRepeatableNode(def builder, Boolean repeatable) {
        builder."wd:Repeatable"(repeatable)
    }

    private void buildConcurrentlyRepeatableNode(def builder, Boolean concurrentlyRepeatable) {
        builder."wd:Concurrently_Repeatable"(concurrentlyRepeatable)
    }

    private void buildRepeatMaxAttemptsNode(def builder, BigDecimal repeatMaxAttempts) {
        builder."wd:Repeat_Maximum_Attempts"(repeatMaxAttempts)
    }

    private void buildRepeatMaxUnitsNode(def builder, BigDecimal repeatMaxUnits) {
        builder."wd:Repeat_Maximum_Units"(repeatMaxUnits)
    }

    private void buildSectionOverridesAllowedNode(def builder, Boolean sectionOverridesAllowed) {
        builder."wd:Section_Overrides_Allowed"(sectionOverridesAllowed)
    }

    private void buildMinUnitsNode(def builder, BigDecimal minUnits) {
        builder."wd:Minimum_Units"(minUnits)
    }

    private void buildMaxUnitsNode(def builder, BigDecimal maxUnits) {
        builder."wd:Maximum_Units"(maxUnits)
    }

    private void buildFirstAvailableNode(def builder, LocalDate firstAvailable) {
        builder."wd:First_Available"(firstAvailable)
    }

    private void buildLastAvailableNode(def builder, LocalDate lastAvailable) {
        builder."wd:Last_Available"(lastAvailable)
    }

    private void buildContactHoursNode(def builder, BigDecimal contactHours) {
        builder."wd:Contact_Hours"(contactHours)
    }

    private void buildDescriptionNode(def builder, String description) {
        builder."wd:Description"(description)
    }
}
