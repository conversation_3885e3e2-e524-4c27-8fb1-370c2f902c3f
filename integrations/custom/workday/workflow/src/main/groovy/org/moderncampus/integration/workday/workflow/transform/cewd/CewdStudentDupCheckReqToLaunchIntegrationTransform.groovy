package org.moderncampus.integration.workday.workflow.transform.cewd

import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import groovy.xml.StreamingMarkupBuilder
import org.moderncampus.integration.dto.cewd.CewdAddress
import org.moderncampus.integration.dto.cewd.CewdEmail
import org.moderncampus.integration.dto.cewd.CewdPhone
import org.moderncampus.integration.dto.cewd.CewdStudent
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.dto.dupsearch.cewd.request.WDStudentDuplicateCheckRequest
import org.moderncampus.integration.workday.workflow.config.IWorkdayMappingConfiguration
import org.moderncampus.integration.workday.workflow.transform.helper.WDIntServiceReqTransformHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.LocalDate

@Component
class CewdStudentDupCheckReqToLaunchIntegrationTransform extends
        BaseTransformer<WDStudentDuplicateCheckRequest, String> {

    static final String INTEGRATION_SYSTEM_ID = "Integration_System_ID"
    static final String LAUNCH_PARAMETER_NAME = "Launch_Parameter_Name"
    static final String NAME_COUNTRY_ISO_ALPHA_3_CODE = "Name_Country_ISO_Alpha_3_Code"
    static final String FIRST_NAME = "First_Name"
    static final String MIDDLE_NAME = "Middle_Name"
    static final String LAST_NAME = "Last_Name"
    static final String DATE_OF_BIRTH = "Date_of_Birth"
    static final String ADDRESS_LINE_1 = "Address_Line_1"
    static final String CITY = "City"
    static final String REGION_ISO_3166_2_CODE = "Region_ISO_3166-2_Code"
    static final String POSTAL_CODE = "Postal_Code"
    static final String COUNTRY_ISO_ALPHA_3_CODE = "Country_ISO_Alpha_3_Code"
    static final String E_164_FORMATTED_PHONE_NUMBER = "E164_Formatted_Phone_Number"
    static final String PHONE_NUMBER_EXTENSION = "Phone_Number_Extension"
    static final String EMAIL_ADDRESS = "Email_Address"
    static final String NATIONAL_ID_TYPE = "National_ID_Type"
    static final String NATIONAL_ID = "National_ID_Unformatted"
    static final String INCLUDE_MATRICULATED_STUDENTS = "Include_Matriculated_Students"
    static final String INCLUDE_STUDENT_PROSPECTS = "Include_Student_Prospects"
    static final String INCLUDE_WORKERS = "Include_Workers"
    static final String EXTERNAL_JOB_ID = "External_Job_ID"
    static final String USA = "USA"

    private final IWorkdayMappingConfiguration mappingConfiguration

    @Autowired
    CewdStudentDupCheckReqToLaunchIntegrationTransform(IWorkdayMappingConfiguration mappingConfiguration) {
        this.mappingConfiguration = mappingConfiguration
    }

    @Override
    protected String doTransform(TransformContext context, WDStudentDuplicateCheckRequest input) {
        String integrationSystemId = mappingConfiguration.getDuplicateCheckIntSystemId()
        String nationalIdType = mappingConfiguration.getNationalIdType()
        CewdStudent cewdStudent = input.getStudent()
        CewdAddress cewdAddress = cewdStudent.getAddresses().getFirst()
        CewdPhone cewdPhone = cewdStudent.getPhones().getFirst()
        String phoneNumber = (cewdPhone.getCountryCode() + Optional.ofNullable(cewdPhone.getAreaCode()).orElse("")
                + cewdPhone.getNumber())
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance()
        CewdEmail cewdEmail = cewdStudent.getEmails().getFirst()

        def launchIntegrationReqBuilder = new StreamingMarkupBuilder()
        launchIntegrationReqBuilder.encoding = 'UTF-8'
        def xml = launchIntegrationReqBuilder.bind { builder ->
            createLaunchParamRefNode builder, integrationSystemId
            try {
                createLaunchParamDataNode(builder, integrationSystemId, NAME_COUNTRY_ISO_ALPHA_3_CODE, USA)
                createLaunchParamDataNode(builder, integrationSystemId, FIRST_NAME, input.getStudent().getFirstName())
                createLaunchParamDataNode(builder, integrationSystemId, MIDDLE_NAME, input.getStudent().getMiddleName())
                createLaunchParamDataNode(builder, integrationSystemId, LAST_NAME, input.getStudent().getLastName())
                createLaunchParamDataNode(builder, integrationSystemId, DATE_OF_BIRTH,
                        Optional.ofNullable(input.getStudent().getBirthDate()).map(
                                LocalDate::toString).orElse(null))
                createLaunchParamDataNode(builder, integrationSystemId, ADDRESS_LINE_1, cewdAddress.getLine1())
                createLaunchParamDataNode(builder, integrationSystemId, CITY, cewdAddress.getCity())
                createLaunchParamDataNode(builder, integrationSystemId, REGION_ISO_3166_2_CODE, cewdAddress.getState())
                createLaunchParamDataNode(builder, integrationSystemId, POSTAL_CODE, cewdAddress.getPostalCode())
                createLaunchParamDataNode(builder, integrationSystemId, COUNTRY_ISO_ALPHA_3_CODE, cewdAddress.getCountry())
                createLaunchParamDataNode(builder, integrationSystemId, E_164_FORMATTED_PHONE_NUMBER,
                        phoneUtil.format(phoneUtil.parse(phoneNumber, "US"),
                                PhoneNumberUtil.PhoneNumberFormat.E164))
                createLaunchParamDataNode(builder, integrationSystemId, PHONE_NUMBER_EXTENSION, cewdPhone.getExtension())
                createLaunchParamDataNode(builder, integrationSystemId, EMAIL_ADDRESS, cewdEmail.getEmailAddress())
                createLaunchParamDataNode(builder, integrationSystemId, NATIONAL_ID, input.getStudent().getSocialSecurityNum())
                createLaunchParamDataNode(builder, integrationSystemId, NATIONAL_ID_TYPE, nationalIdType)
                createLaunchParamDataNode(builder, integrationSystemId, INCLUDE_MATRICULATED_STUDENTS, Boolean.TRUE)
                createLaunchParamDataNode(builder, integrationSystemId, INCLUDE_STUDENT_PROSPECTS, Boolean.TRUE)
                createLaunchParamDataNode(builder, integrationSystemId, INCLUDE_WORKERS, Boolean.TRUE)
                createLaunchParamDataNode(builder, integrationSystemId, EXTERNAL_JOB_ID, input.getCorrelationId())
            } catch (NumberParseException e) {
                throw new RuntimeException(e)
            }
        }

        return WDIntServiceReqTransformHelper.mapToLaunchIntegrationEventRequest(context, xml.toString())
    }

    private void createLaunchParamRefNode(def builder, String integrationSystemId) {
        builder."wd:Integration_System_Reference"() {
            "wd:ID"("wd:type": INTEGRATION_SYSTEM_ID, integrationSystemId)
        }
    }

    private void createLaunchParamDataNode(def builder, String integrationSystemId, String paramName,
                                           Object paramValue) {
        builder."wd:Integration_Launch_Parameter_Data"() {
            "wd:Launch_Parameter_Reference"() {
                "wd:ID"("wd:type": LAUNCH_PARAMETER_NAME, "wd:parent_id": integrationSystemId, "wd:parent_type": INTEGRATION_SYSTEM_ID, paramName)
            }
            "wd:Launch_Parameter_Value_Data"() {
                if (paramValue == null || String.class.isAssignableFrom(paramValue.getClass())) {
                    "wd:Text"(paramValue)
                } else if (Boolean.class.isAssignableFrom(paramValue.getClass())) {
                    "wd:Boolean"(paramValue)
                }
            }
        }
    }
}
