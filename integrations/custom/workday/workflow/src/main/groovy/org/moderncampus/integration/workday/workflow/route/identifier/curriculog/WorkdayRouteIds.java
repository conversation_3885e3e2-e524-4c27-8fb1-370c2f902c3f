package org.moderncampus.integration.workday.workflow.route.identifier.curriculog;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;
import static org.moderncampus.integration.workday.workflow.route.identifier.Constants.CREATE_COURSE;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum WorkdayRouteIds implements IRouteId {

    V1_CURRICULOG_WORKDAY_CREATE_COURSE(CREATE_COURSE),
    V1_CURRICULOG_WORKDAY_UPDATE_COURSE(Constants.UPDATE);

    String contextPath;

    String id;

    WorkdayRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, CURRICULOG_SYSTEM_ID, "*", WORKDAY_SYSTEM_ID, contextPath});
    }
}