package org.moderncampus.integration.workday.workflow.transform.cewd.helper;

import org.moderncampus.integration.dto.cewd.CewdPhone;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;

public class WDToCewdPhoneTransformHelper {

    public static CewdPhone mapPhone(String type, String phoneNumber) {
        CewdPhone cewdPhone = new CewdPhone();
        cewdPhone.setType(type);
        try {
            Phonenumber.PhoneNumber numberMeta = getPhoneNumberParts(phoneNumber);
            boolean isUsOrCanada = numberMeta.getCountryCode() == 1;
            String countryCode = String.valueOf(numberMeta.getCountryCode());
            String nationalNumber = String.valueOf(numberMeta.getNationalNumber());
            String number = isUsOrCanada ? nationalNumber.substring(3) : nationalNumber;
            String areaCode = isUsOrCanada ? nationalNumber.substring(0, 3) : null;
            cewdPhone.setCountryCode(countryCode);
            cewdPhone.setNumber(number);
            cewdPhone.setAreaCode(areaCode);
            cewdPhone.setExtension(numberMeta.getExtension());
        } catch (NumberParseException ignored) {
            return null;
        }
        return cewdPhone;
    }

    private static Phonenumber.PhoneNumber getPhoneNumberParts(String phoneNumber) throws NumberParseException {
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
        return phoneUtil.parse(phoneNumber, null);
    }
}
