package org.moderncampus.integration.workday.workflow.transform.cewd.helper

import groovy.transform.CompileStatic
import groovy.xml.XmlParser

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNameSpaceFromNode
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.bodyNode
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.wdNode

@CompileStatic
class CewdWDRespTransformHelper {

    public static Node extractPersonContactInfoDataNodeFromGetChangeContactInfoResp(String body, boolean isHome) {
        String changeQualifier = isHome ? "Home" : "Work"
        def envelopeNode = new XmlParser(false, false).parseText(body)
        String bodyNameSpace = extractNameSpaceFromNode(envelopeNode)
        Node bodyNode = (envelopeNode[bodyNode(bodyNameSpace, "Body")] as NodeList)[0] as Node
        def changeContactInfoRespNode = (bodyNode.value() as NodeList)[0] as Node
        def changeContactInfoDataNode = changeContactInfoRespNode[wdNode("Response_Data")][wdNode("Change_$changeQualifier" +
                "_Contact_Information")][wdNode("Change_$changeQualifier" + "_Contact_Information_Data")] as NodeList
        if (changeContactInfoDataNode.isEmpty()) {
            throw new RuntimeException("No Change_$changeQualifier" + "_Contact_Information_Data Node found in response")
        }
        def personContactInfoDataNode = changeContactInfoDataNode[wdNode('Person_Contact_Information_Data')]
        return (personContactInfoDataNode.isEmpty() ? new Node(changeContactInfoDataNode[0] as Node, wdNode("Person_Contact_Information_Data")) :
                personContactInfoDataNode[0] as Node)
    }

    public static Node extractCustomIdNodeFromGetChangeOtherIdsResp(String body) {
        def envelopeNode = new XmlParser(false, false).parseText(body)
        String bodyNameSpace = extractNameSpaceFromNode(envelopeNode)
        Node bodyNode = (envelopeNode[bodyNode(bodyNameSpace, "Body")] as NodeList)[0] as Node
        def changeOtherIdsResponseNode = (bodyNode.value() as NodeList)[0] as Node
        def changeOtherIdsNode = changeOtherIdsResponseNode[wdNode("Response_Data")][wdNode("Change_Other_IDs")][wdNode(
                'Change_Other_IDs_Data')] as NodeList
        if (changeOtherIdsNode.isEmpty()) {
            throw new RuntimeException("No Change_Other_IDs_Data Node found in response")
        }
        def customIdNode = changeOtherIdsNode[wdNode('Custom_Identification_Data')]
        return (customIdNode.isEmpty() ? new Node(changeOtherIdsNode[0] as Node, wdNode("Custom_Identification_Data")) : customIdNode[0] as Node)
    }

}
