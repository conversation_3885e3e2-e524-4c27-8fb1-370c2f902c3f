package org.moderncampus.integration.workday.workflow.route.identifier.common;

import static org.moderncampus.integration.Constants.VERSION_1;
import static org.moderncampus.integration.Constants.WORKDAY_SYSTEM_ID;
import static org.moderncampus.integration.constants.Constants.HEALTH;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum WorkdayRouteIds implements IRouteId {

    V1_WORKDAY_HEALTH(HEALTH);

    String contextPath;

    String id;

    WorkdayRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, WORKDAY_SYSTEM_ID, contextPath});
    }

}
