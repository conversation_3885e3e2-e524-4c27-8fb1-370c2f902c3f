package org.moderncampus.integration.workday.workflow.transform.cewd

import groovy.transform.CompileStatic
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.commons.lang3.StringUtils
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntStagedResult
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWDNamespacePrefix

@CompileStatic
@Component
class DupCheckIntRespToDupCheckStagedRespTransform extends BaseTransformer<NodeChild, DuplicateCheckStudioIntStagedResult> {

    static final String EXTERNAL_JOB_ID = "External_Job_ID"

    static final String WID = "WID"

    @Override
    protected DuplicateCheckStudioIntStagedResult doTransform(TransformContext context, NodeChild responseNode) {
        String namespacePrefix = extractWDNamespacePrefix(responseNode) ?: "wd"
        def responseDataNode = (responseNode['Body']['Launch_Integration_Event_Response'] as NodeChildren)
        String eventId = extractNodeText((responseDataNode['Integration_Event']['Integration_Event_Reference']['*'] as
                NodeChildren)?.find {
            NodeChild integrationEventRefId ->
                integrationEventRefId["@" + namespacePrefix + ":type"] == WID
        })
        String externalJobId = extractNodeText((responseDataNode['Integration_Event']['Integration_Event_Data']['Integration_Runtime_Parameter_Data' +
                '']['*'] as
                NodeChildren)?.find {
            NodeChild integrationEventRefId ->
                integrationEventRefId["@" + namespacePrefix + ":Label"] == EXTERNAL_JOB_ID
        }?.parent()['Text'] as NodeChildren)
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(externalJobId)) {
            throw new RuntimeException("EventID or External Job ID is missing from the response")
        }
        DuplicateCheckStudioIntStagedResult searchMatchInitResponse = new DuplicateCheckStudioIntStagedResult()
        searchMatchInitResponse.correlationId = externalJobId
        searchMatchInitResponse.businessProcessId = eventId
        return searchMatchInitResponse
    }
}
