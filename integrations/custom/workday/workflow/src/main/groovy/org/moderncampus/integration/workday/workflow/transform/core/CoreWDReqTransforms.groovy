package org.moderncampus.integration.workday.workflow.transform.core


import groovy.transform.CompileStatic
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.dto.base.SearchEntityRequest
import org.moderncampus.integration.exception.ApplicationException
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.dto.WorkdayAPIPaginationConstruct
import org.moderncampus.integration.workday.dto.course.WorkdayCourse
import org.moderncampus.integration.workday.workflow.transform.helper.WDRequestParamHelper

import java.time.ZoneOffset

import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWDNamespacePrefix
import static org.moderncampus.integration.workday.workflow.transform.helper.WDStudentRecordsServiceReqTransformHelper.mapToGetStudentCrseRequest

@CompileStatic
public class CoreWDReqTransforms {

    public static ITransformer<SearchEntityRequest, String> getStudentCoursesRequestTransformer = (TransformContext ctx, SearchEntityRequest body)
            -> {
        WorkdayAPIPaginationConstruct construct = body.getPaginationConstruct() as WorkdayAPIPaginationConstruct
        WDRequestParamHelper.StudentCrseRequestParams paramsHelper = new WDRequestParamHelper.StudentCrseRequestParams()
        def records = mapToGetStudentCrseRequest(ctx, (paramsHelper.with(true, {
            studentCrseId = body.criteria?[PARAM_STUDENT_CRSE_ID]
            pageNumber = construct.pageNumber
            pageSize = construct.pageSize
            asOfEffectiveDate = construct.asOfEffectiveDate
            asOfEntryMoment = construct.asOfEntryMoment?.withOffsetSameInstant(ZoneOffset.UTC)
        })) as WDRequestParamHelper.StudentCrseRequestParams)
        return records
    }

    public static ITransformer<NodeChild, WorkdayCourse> submitStudentCrseResponseTransformer = (TransformContext ctx, NodeChild rootNode) -> {
        WorkdayCourse workdayCourse = new WorkdayCourse()
        String namespacePrefix = extractWDNamespacePrefix(rootNode) ?: "wd"
        def responseDataNode = (rootNode['Body']['Submit_Student_Course_Response']['Student_Course_Reference']['*'] as NodeChildren)
        workdayCourse.studentCourseRefWID = responseDataNode?.find { NodeChild studentCrseRef ->
            studentCrseRef["@" + namespacePrefix + ":type"] == 'WID'
        }?.text()
        workdayCourse.id = responseDataNode?.find { NodeChild studentCrseRef ->
            studentCrseRef["@" + namespacePrefix + ":type"] == 'Student_Course_ID'
        }?.text()
        if (workdayCourse == null && workdayCourse.studentCourseRefWID) {
            throw new ApplicationException("Invalid response (No ID's) returned from submit student course API")
        }
        return workdayCourse
    }
}
