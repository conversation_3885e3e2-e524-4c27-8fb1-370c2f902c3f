package org.moderncampus.integration.workday.workflow.transform.cewd

import groovy.transform.CompileStatic
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.collections4.MapUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.tuple.Pair
import org.moderncampus.integration.dto.cewd.CewdAddress
import org.moderncampus.integration.dto.cewd.CewdEmail
import org.moderncampus.integration.dto.cewd.CewdPerson
import org.moderncampus.integration.dto.cewd.CewdPhone
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.workday.workflow.transform.cewd.Constants.ADDRESS_MAPPING
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.wdNode

@Component
@CompileStatic
class CewdPersonToPersonContactInfoDataNodeTransform extends BaseTransformer<CewdPerson, Node> {

    static final String TENANTED_USAGE_BEHAVIOR_ID = "Communication_Usage_Behavior_Tenanted_ID"
    static final String USAGE_TYPE_ID = "Communication_Usage_Type_ID"
    static final String PHONE_DEVICE_TYPE_ID = "Phone_Device_Type_ID"
    static final String COUNTRY_PHONE_CODE_ID = "Country_Phone_Code_ID"
    static final String HOME = "HOME"
    static final String WORK = "WORK"
    static final String DEFAULT_PHONE_COUNTRY_CODE = "USA_1"
    static final String ADDRESS_LINE_1 = "ADDRESS_LINE_1"
    public static final String ISO_3166_1_ALPHA_2_CODE = "ISO_3166-1_Alpha-2_Code"
    public static final String ISO_3166_2_CODE = "ISO_3166-2_Code"

    @Override
    protected Node doTransform(TransformContext ctx, CewdPerson input) {
        Node dataType = ctx.getContextProp(org.moderncampus.integration.constants.Constants.LOADED_ENTITY, Node.class)
        boolean isWDHomeMapping = ctx.getContextProp("IS_WD_HOME_MAPPING", Boolean.class)
        if (dataType == null) {
            throw new RuntimeException("Mapping without a loaded entity is not implemented");
        }
        Map<String, String> customAddressTypeMapping = ctx.getContextProp(ADDRESS_MAPPING,
                Map.class) as Map<String, String>
        boolean isAddressMappingPresent = MapUtils.isNotEmpty(customAddressTypeMapping)
        if (CollectionUtils.isNotEmpty(input.getAddresses())) {
            List<Pair<Set<String>, Node>> tenantedUsageRefToAddressInfoNodeList = tenantedUsageRefToAddressInfoNodeList(
                    dataType[wdNode('Person_Address_Information_Data')] as NodeList)
            boolean isPrimaryAddressPresent = isPrimaryAddressPresent(dataType[wdNode('Person_Address_Information_Data')] as NodeList)
            input.addresses
                    .findAll(getCewdAddressFilterPredicate(customAddressTypeMapping, isAddressMappingPresent, isWDHomeMapping))
                    .forEach { address ->
                        {
                            String wdAddressType = isAddressMappingPresent ? customAddressTypeMapping.get(address.getType())
                                    : address.getType().split(":")[1]
                            List<Node> addressInfoDataNodes = tenantedUsageRefToAddressInfoNodeList.findAll {
                                entry -> entry.getKey().contains(wdAddressType)
                            }.collect {
                                entry -> entry.getValue()
                            }
                            if (addressInfoDataNodes.size() > 1) {
                                throw new RuntimeException(
                                        String.format(
                                                "Multiple mappings found for address usage type: %s on Workday. Unable to disambiguate address " +
                                                        "update",
                                                wdAddressType))
                            } else if (addressInfoDataNodes.size() == 1) {
                                boolean isDeleteAssoc = org.moderncampus.integration.constants.Constants.ASSOCIATION_DELETE_MODE.equalsIgnoreCase(
                                        String.valueOf(address.getDynamicProperties().get(org.moderncampus.integration.constants.Constants.
                                        ASSOCIATION_MODE)));
                                if (isDeleteAssoc) {
                                    addressInfoDataNodes[0].attributes()[wdNode('Delete')] = "true"
                                } else {
                                    //update address
                                    mapAddress(address, addressInfoDataNodes[0]);
                                }
                            } else {
                                Node personAddressInfoDataNode
                                NodeList personAddressInfoDataNodeList = dataType[wdNode('Person_Address_Information_Data')] as NodeList
                                if (!personAddressInfoDataNodeList.isEmpty()) {
                                    personAddressInfoDataNode = personAddressInfoDataNodeList[0] as Node
                                } else {
                                    personAddressInfoDataNode = new Node(dataType, wdNode('Person_Address_Information_Data'))
                                }
                                Node newAddressInfoDataNode = new Node(personAddressInfoDataNode, wdNode('Address_Information_Data'))
                                Node newAddressCoreDataNode = new Node(newAddressInfoDataNode, wdNode('Address_Data'))
                                mapAddress(address, newAddressInfoDataNode)
                                addUsageTypeToAddress(wdAddressType, newAddressInfoDataNode, isPrimaryAddressPresent, isWDHomeMapping)
                            }
                        }
                    }
        }
        Map<String, String> customPhoneTypeMapping = ctx.getContextProp(Constants.PHONE_MAPPING, Map.class) as Map<String, String>
        boolean isPhoneMappingPresent = MapUtils.isNotEmpty(customPhoneTypeMapping)

        if (CollectionUtils.isNotEmpty(input.getPhones())) {
            List<Pair<Set<String>, Node>> deviceTypeToPhoneInfoDataNodeList = deviceTypeToPhoneInfoDataNodeList(
                    dataType[wdNode("Person_Phone_Information_Data")] as NodeList)
            boolean isPrimaryPhonePresent = isPrimaryPhonePresent(dataType[wdNode("Person_Phone_Information_Data")] as NodeList)
            input.getPhones().findAll(getCewdPhonePredicate(customPhoneTypeMapping, isPhoneMappingPresent, isWDHomeMapping))
                    .forEach { phone ->
                        {
                            String wdPhoneType = isPhoneMappingPresent ? customPhoneTypeMapping.get(phone.getType())
                                    : phone.getType().split(":")[1]
                            List<Node> phoneInfoDataNodes = deviceTypeToPhoneInfoDataNodeList.findAll {
                                entry -> entry.getKey().contains(wdPhoneType)
                            }.collect {
                                entry -> entry.getValue()
                            }
                            if (phoneInfoDataNodes.size() > 1) {
                                throw new RuntimeException(
                                        String.format(
                                                "Multiple mappings found for phone device type: %s on Workday. Unable to disambiguate phone update",
                                                wdPhoneType))
                            } else if (phoneInfoDataNodes.size() == 1) {
                                boolean isDeleteAssoc = org.moderncampus.integration.constants.Constants.ASSOCIATION_DELETE_MODE.equalsIgnoreCase(
                                        String.valueOf(phone.getDynamicProperties().get(org.moderncampus.integration.constants.Constants.
                                        ASSOCIATION_MODE)));
                                if (isDeleteAssoc) {
                                    phoneInfoDataNodes[0].attributes()[wdNode('Delete')] = "true"
                                } else {
                                    //update phone
                                    mapPhone(phone, phoneInfoDataNodes[0], wdPhoneType);
                                }
                            } else {
                                Node personPhoneInfoDataNode
                                NodeList personPhoneInfoDataNodeList = dataType[wdNode('Person_Phone_Information_Data')] as NodeList
                                if (!personPhoneInfoDataNodeList.isEmpty()) {
                                    personPhoneInfoDataNode = personPhoneInfoDataNodeList[0] as Node
                                } else {
                                    personPhoneInfoDataNode = new Node(dataType, wdNode('Person_Phone_Information_Data'))
                                }
                                Node newPhoneInfoDataNode = new Node(personPhoneInfoDataNode, wdNode('Phone_Information_Data'))
                                Node newPhoneCoreDataNode = new Node(newPhoneInfoDataNode, wdNode('Phone_Data'))
                                mapPhone(phone, newPhoneInfoDataNode, wdPhoneType)
                                addUsageTypeToPhone(newPhoneInfoDataNode, isPrimaryPhonePresent, isWDHomeMapping)
                            }
                        }
                    }
        }

        if (CollectionUtils.isNotEmpty(input.getEmails())) {//email deletions not supported
            CewdEmail cewdEmail = extractEmail(input)
            if (cewdEmail != null) {
                String type = cewdEmail.getType()
                String emailAddress = cewdEmail.getEmailAddress()
                if (isWDHomeMapping && HOME.equalsIgnoreCase(type) && StringUtils.isNotBlank(emailAddress)) {
                    mapEmail(dataType, emailAddress, HOME, isWDHomeMapping)
                } else if (!isWDHomeMapping && WORK.equalsIgnoreCase(type) && StringUtils.isNotBlank(emailAddress)) {
                    mapEmail(dataType, emailAddress, WORK, isWDHomeMapping)
                }
            }
        }

        return dataType
    }

    void addUsageTypeToAddress(String addressType, Node newAddressType,
                               boolean isPrimaryAddressPresent, boolean isWDHomeMapping) {
        Node usageDataNode = new Node(newAddressType, wdNode('Usage_Data'))
        Node useForTenantedRefNode = new Node(usageDataNode, wdNode('Use_For_Tenanted_Reference'))
        Node useForTenantedRefIdNode = new Node(useForTenantedRefNode, wdNode('ID'),
                [(wdNode('type')): TENANTED_USAGE_BEHAVIOR_ID] as Map<String, String>, addressType)

        Node usageTypeDataNode = new Node(usageDataNode, wdNode('Type_Data'),
                [(wdNode('Primary')): !isPrimaryAddressPresent ? "1" : "0"])
        Node usageTypeRefNode = new Node(usageTypeDataNode, wdNode('Type_Reference'))
        Node usageTypeRefIdNode = new Node(usageTypeRefNode, wdNode('ID'), [(wdNode('type')): USAGE_TYPE_ID],
                isWDHomeMapping ? HOME : WORK)
    }

    void mapAddress(CewdAddress address, Node addressDataType) {
        NodeList addressDataList = addressDataType[wdNode('Address_Data')] as NodeList
        if (!addressDataList.isEmpty() && addressDataList.size() == 1) {
            Node addressLineInfoNode = addressDataList[wdNode('Address_Line_Data')].find {
                ADDRESS_LINE_1 == (it as Node).attribute(wdNode("Type"))
            } as Node
            if (addressLineInfoNode == null) {
                addressLineInfoNode = new Node(addressDataList[0] as Node, wdNode("Address_Line_Data"),
                        [(wdNode("Type")): ADDRESS_LINE_1] as Map, "")
            }
            if (StringUtils.isNotBlank(address.line1)) {
                addressLineInfoNode.value = address.line1
            }

            Node countryRefIdNode = addressDataList[wdNode('Country_Reference')]['*'].find {
                (it as Node).name() == wdNode('ID') && ISO_3166_1_ALPHA_2_CODE == (it as Node).attributes()[wdNode('type')]
            } as Node
            if (countryRefIdNode == null) {
                Node countryRefNode = new Node(addressDataList[0] as Node, wdNode('Country_Reference'))
                countryRefIdNode = new Node(countryRefNode, wdNode('ID'), [(wdNode("type")): ISO_3166_1_ALPHA_2_CODE])
            }
            if (StringUtils.isNotBlank(address.country)) {
                countryRefIdNode.value = address.country
            }

            Node countryRegionIdNode = addressDataList[wdNode('Country_Region_Reference')]['*'].find {
                (it as Node).name() == wdNode('ID') && ISO_3166_2_CODE == (it as Node).attributes()[wdNode('type')]
            } as Node
            if (countryRegionIdNode == null) {
                Node countryRegionRefNode = new Node(addressDataList[0] as Node, wdNode('Country_Region_Reference'))
                countryRegionIdNode = new Node(countryRegionRefNode, wdNode('ID'), [(wdNode("type")): ISO_3166_2_CODE])
            }
            if (StringUtils.isNotBlank(address.state)) {
                countryRegionIdNode.value = address.state
            }

            if (StringUtils.isNotBlank(address.city)) {
                NodeList municipalityNode = addressDataList[wdNode('Municipality')]
                if (municipalityNode.isEmpty()) {
                    new Node(addressDataList[0] as Node, wdNode('Municipality'), address.city)
                } else {
                    (municipalityNode[0] as Node).value = address.city
                }
            }

            if (StringUtils.isNotBlank(address.postalCode)) {
                NodeList postalCodeNode = addressDataList[wdNode('Postal_Code')]
                if (postalCodeNode.isEmpty()) {
                    new Node(addressDataList[0] as Node, wdNode('Postal_Code'), address.postalCode)
                } else {
                    (postalCodeNode[0] as Node).value = address.postalCode
                }
            }
        }
    }

    def List<Pair<Set<String>, Node>> tenantedUsageRefToAddressInfoNodeList(NodeList personAddressInfoDataNode) {
        return (personAddressInfoDataNode[wdNode('Address_Information_Data')][wdNode('Usage_Data')] as List<Node>).findResults {
            Node usageDataNode = (it as Node)
            Set<String> tenantKeys = retrieveTenantedUsageBehaviorIDs(usageDataNode)
            if (tenantKeys != null && !tenantKeys.isEmpty()) {
                return Pair.of(tenantKeys, usageDataNode.parent())
            }
        } as List
    }

    def Set<String> retrieveTenantedUsageBehaviorIDs(Node usageData) {
        def list = (usageData[wdNode('Use_For_Tenanted_Reference')])
        List<String> usageBehaviorIdsList = list.collect {
            NodeList tenantedUsageBehaviorIDNodeValue = (((it as Node).children().find {
                (it as Node).name() == (wdNode("ID"))
                        && TENANTED_USAGE_BEHAVIOR_ID == (it as Node).attributes()[wdNode("type")]
            } as Node)?.value()) as NodeList
            return tenantedUsageBehaviorIDNodeValue != null && !tenantedUsageBehaviorIDNodeValue.isEmpty() ?
                    tenantedUsageBehaviorIDNodeValue[0] as String : null
        }
        usageBehaviorIdsList.removeAll { it == null }
        return usageBehaviorIdsList.toSet()
    }

    def boolean isPrimaryAddressPresent(NodeList personAddressInfoDataNode) {
        NodeList typeDataNodeList = personAddressInfoDataNode[wdNode('Address_Information_Data')][wdNode('Usage_Data')][wdNode(
                'Type_Data')]
        return typeDataNodeList.isEmpty() ? false : (typeDataNodeList as List).any {
            (it as Node).attributes()[wdNode("Primary")] == "1"
        }
    }

    Closure<Boolean> getCewdAddressFilterPredicate(Map<String, String> customAddressTypeMapping,
                                                   boolean isAddressMappingPresent, boolean isWDHomeMapping) {
        return (CewdAddress address) -> {
            if (!isAddressMappingPresent) {
                boolean isAddressTypeColonFormat = address != null && address.getType().contains(":")
                        && address.getType().split(":").length == 2;
                if (isAddressTypeColonFormat) {
                    String[] typeArray = address.getType().split(":");
                    String wdParentType = typeArray[0];
                    String wdChildType = typeArray[1];
                    return (isWDHomeMapping && HOME.equalsIgnoreCase(wdParentType)) || (!isWDHomeMapping
                            && WORK.equalsIgnoreCase(
                            wdParentType));
                }
            }
            return customAddressTypeMapping.containsKey(address.getType());
        }
    }

    List<Pair<Set<String>, Node>> deviceTypeToPhoneInfoDataNodeList(NodeList personPhoneInformationDataType) {
        return (personPhoneInformationDataType[wdNode('Phone_Information_Data')] as List<Node>).findResults {
            Node phoneInfoDataNode = (it as Node)
            Set<String> tenantKeys = retrievePhoneDeviceTypeIDs(phoneInfoDataNode)
            if (tenantKeys != null && !tenantKeys.isEmpty()) {
                return Pair.of(tenantKeys, phoneInfoDataNode)
            }
        } as List
    }

    def Set<String> retrievePhoneDeviceTypeIDs(Node phoneInfoDataNode) {
        def list = (phoneInfoDataNode[wdNode('Phone_Data')][wdNode('Device_Type_Reference')])
        List<String> deviceTypeIdsList = list.collect {
            NodeList phoneDeviceTypeIdNodeValue = (((it as Node).children().find {
                (it as Node).name() == (wdNode("ID"))
                        && (it as Node).attributes()[wdNode("type")] == PHONE_DEVICE_TYPE_ID
            } as Node)?.value()) as NodeList
            return phoneDeviceTypeIdNodeValue != null && !phoneDeviceTypeIdNodeValue.isEmpty() ?
                    phoneDeviceTypeIdNodeValue[0] as String : null
        }
        deviceTypeIdsList.removeAll { it == null }
        return deviceTypeIdsList.toSet()
    }

    boolean isPrimaryPhonePresent(NodeList personPhoneInformationDataType) {
        NodeList typeDataNodeList = personPhoneInformationDataType[wdNode('Phone_Information_Data')][wdNode('Usage_Data')][wdNode(
                'Type_Data')]
        return typeDataNodeList.isEmpty() ? false : (typeDataNodeList as List).any {
            (it as Node).attributes()[wdNode("Primary")] == "1"
        }
    }

    Closure<Boolean> getCewdPhonePredicate(Map<String, String> customPhoneTypeMapping,
                                           boolean isPhoneMappingPresent, boolean isWDHomeMapping) {
        (CewdPhone phone) -> {
            if (!isPhoneMappingPresent) {
                boolean isPhoneTypeColonFormat = phone.getType() != null && phone.getType().contains(":")
                        && phone.getType().split(":").length == 2;
                if (isPhoneTypeColonFormat) {
                    String[] typeArray = phone.getType().split(":");
                    String wdParentType = typeArray[0];
                    String wdChildType = typeArray[1];
                    return (isWDHomeMapping && HOME.equalsIgnoreCase(wdParentType)) || (!isWDHomeMapping
                            && WORK.equalsIgnoreCase(
                            wdParentType));
                }
            }
            return customPhoneTypeMapping.containsKey(phone.getType())
        }
    }

    void mapPhone(CewdPhone phone, Node personPhoneDataType, String wdPhoneType) {
        NodeList phoneDataList = personPhoneDataType[wdNode('Phone_Data')] as NodeList
        if (!phoneDataList.isEmpty() && phoneDataList.size() == 1) {
            Node deviceTypeRefIdNode = phoneDataList[wdNode('Device_Type_Reference')]['*'].find {
                (it as Node).name() == wdNode('ID') && PHONE_DEVICE_TYPE_ID == (it as Node).attributes()[wdNode('type')]
            } as Node
            if (deviceTypeRefIdNode == null) {
                Node deviceTypeRefNode = new Node(phoneDataList[0] as Node, wdNode('Device_Type_Reference'))
                deviceTypeRefIdNode = new Node(deviceTypeRefNode, wdNode('ID'), [(wdNode("type")): PHONE_DEVICE_TYPE_ID])
            }
            deviceTypeRefIdNode.value = wdPhoneType

            Node countryCodeIdNode = phoneDataList[wdNode('Country_Code_Reference')]['*'].find {
                (it as Node).name() == wdNode('ID') && "Country_Phone_Code_ID" == (it as Node).attributes()[wdNode('type')]
            } as Node
            if (countryCodeIdNode == null) {
                Node countryRegionRefNode = new Node(phoneDataList[0] as Node, wdNode('Country_Code_Reference'))
                countryCodeIdNode = new Node(countryRegionRefNode, wdNode('ID'), [(wdNode("type")): "Country_Phone_Code_ID"])
            }
            countryCodeIdNode.value = DEFAULT_PHONE_COUNTRY_CODE

            if (StringUtils.isNotBlank(phone.number)) {
                String phoneNumber = phone.getAreaCode() + phone.getNumber()
                NodeList completePhoneNumberNode = phoneDataList[wdNode('Complete_Phone_Number')]
                if (completePhoneNumberNode.isEmpty()) {
                    new Node(phoneDataList[0] as Node, wdNode('Complete_Phone_Number'), phoneNumber)
                } else {
                    (completePhoneNumberNode[0] as Node).value = phoneNumber
                }
            }
        }
    }

    void addUsageTypeToPhone(Node newPhoneType, boolean isPrimaryPhonePresent, boolean isWDHomeMapping) {
        Node usageDataNode = new Node(newPhoneType, wdNode('Usage_Data'))
        Node usageTypeDataNode = new Node(usageDataNode, wdNode('Type_Data'),
                [(wdNode('Primary')): !isPrimaryPhonePresent ? "1" : "0"])
        Node usageTypeRefNode = new Node(usageTypeDataNode, wdNode('Type_Reference'))
        Node usageTypeRefIdNode = new Node(usageTypeRefNode, wdNode('ID'), [(wdNode('type')): USAGE_TYPE_ID],
                isWDHomeMapping ? HOME : WORK)
    }

    CewdEmail extractEmail(CewdPerson input) {
        return input.getEmails().find {
            cewdEmail -> cewdEmail.getType() != null
        }
    }

    boolean isPrimaryEmailPresent(NodeList informationDataType) {
        NodeList typeDataNodeList = informationDataType[wdNode('Email_Information_Data')][wdNode('Usage_Data')]
        [wdNode('Type_Data')]
        return typeDataNodeList.isEmpty() ? false : (typeDataNodeList[0] as Node).attributes()[wdNode("Primary")] == "1"
    }

    void mapEmail(Node dataType, String emailAddress, String usageType, boolean isWDHomeMapping) {
        if (StringUtils.isNotBlank(emailAddress)) {
            boolean isPrimaryEmailPresent = isPrimaryEmailPresent(dataType[wdNode('Person_Email_Information_Data')] as NodeList)
            List<Pair<Set<String>, Node>> usageRefToEmailList = usageRefToEmailList(
                    dataType[wdNode('Person_Email_Information_Data')] as NodeList)
            Node emailAddressNode
            Node emailInfoDataNode = usageRefToEmailList
                    .find {
                        it -> it.key.contains(usageType)
                    }?.value
            if (emailInfoDataNode != null) {
                NodeList emailAddressNodeList = emailInfoDataNode[wdNode('Email_Data')][wdNode('Email_Address')] as NodeList
                if (!emailAddressNodeList.isEmpty()) {
                    emailAddressNode = emailAddressNodeList[0] as Node
                } else {
                    Node emailDataNode = new Node(emailInfoDataNode, wdNode('Email_Data'))
                    emailAddressNode = new Node(emailDataNode, wdNode('Email_Address'))
                }
            } else {
                Node personEmailInfoDataNode
                NodeList personEmailInfoDataNodeList = dataType[wdNode('Person_Email_Information_Data')] as NodeList
                if (!personEmailInfoDataNodeList.isEmpty()) {
                    personEmailInfoDataNode = personEmailInfoDataNodeList[0] as Node
                } else {
                    personEmailInfoDataNode = new Node(dataType, wdNode('Person_Email_Information_Data'))
                }
                Node newEmailInfoDataNode = new Node(personEmailInfoDataNode, wdNode('Email_Information_Data'))
                Node newEmailCoreDataNode = new Node(newEmailInfoDataNode, wdNode('Email_Data'))
                emailAddressNode = new Node(newEmailCoreDataNode, wdNode('Email_Address'))
                addUsageTypeToEmail(newEmailInfoDataNode, isPrimaryEmailPresent, isWDHomeMapping)
            }
            emailAddressNode.value = emailAddress
        }
    }

    List<Pair<Set<String>, Node>> usageRefToEmailList(NodeList personEmailInformationDataType) {
        return (personEmailInformationDataType[wdNode('Email_Information_Data')] as List<Node>).findResults {
            Node emailInfoDataNode = (it as Node)
            Set<String> tenantKeys = retrieveEmailTypeIDs(emailInfoDataNode)
            if (tenantKeys != null && !tenantKeys.isEmpty()) {
                return Pair.of(tenantKeys, emailInfoDataNode)
            }
        } as List
    }

    def Set<String> retrieveEmailTypeIDs(Node emailInfoDataNode) {
        def list = (emailInfoDataNode[wdNode('Usage_Data')][wdNode('Type_Data')][wdNode('Type_Reference')])
        List<String> emailTypeIdsList = list.collect {
            NodeList usageTypeRefIdNode = (((it as Node).children().find {
                (it as Node).name() == (wdNode("ID"))
                        && (it as Node).attributes()[wdNode("type")] == USAGE_TYPE_ID
            } as Node)?.value()) as NodeList
            return usageTypeRefIdNode != null && !usageTypeRefIdNode.isEmpty() ?
                    usageTypeRefIdNode[0] as String : null
        }
        emailTypeIdsList.removeAll { it == null }
        return emailTypeIdsList.toSet()
    }

    void addUsageTypeToEmail(Node newEmailDataType, boolean isPrimaryEmailPresent, boolean isWDHomeMapping) {
        Node usageDataNode = new Node(newEmailDataType, wdNode('Usage_Data'), [(wdNode("Public")): "1"])
        Node usageTypeDataNode = new Node(usageDataNode, wdNode('Type_Data'),
                [(wdNode('Primary')): !isPrimaryEmailPresent ? "1" : "0"])
        Node usageTypeRefNode = new Node(usageTypeDataNode, wdNode('Type_Reference'))
        Node usageTypeRefIdNode = new Node(usageTypeRefNode, wdNode('ID'), [(wdNode('type')): USAGE_TYPE_ID],
                isWDHomeMapping ? HOME : WORK)
    }
}
