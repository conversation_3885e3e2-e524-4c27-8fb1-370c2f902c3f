package org.moderncampus.integration.workday.workflow.transform.helper


import groovy.xml.StreamingMarkupBuilder
import org.moderncampus.integration.transform.TransformContext

class WDHRServiceReqTransformHelper {

    public static String mapToGetChangeOtherIdsRequest(TransformContext ctx, String uid) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Request_References"() {
                if (uid != null) {
                    "wd:Universal_ID_Reference"() {
                        "wd:ID"("wd:type": "Universal_Identifier_ID", uid)
                    }
                }
            }
        }
        return WDRequestTransformHelper.buildReadSOAPRequest(xml.toString(), version, "Get_Change_Other_IDs_Request")
    }

    public static String mapToPutChangeOtherIdsRequest(TransformContext ctx, boolean skipBusinessProcess, String changeOtherIdsDataNode) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Business_Process_Parameters"() {
                "wd:Auto_Complete"(skipBusinessProcess)
            }
            if (changeOtherIdsDataNode != null) {
                mkp.yieldUnescaped changeOtherIdsDataNode
            }
        }
        return WDRequestTransformHelper.buildWriteSOAPRequest(xml.toString(), version, "Change_Other_IDs_Request")
    }

    public static String mapToGetChangeHomeContactInfoRequest(TransformContext ctx, String uid) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Request_References"() {
                if (uid != null) {
                    "wd:Universal_ID_Reference"() {
                        "wd:ID"("wd:type": "Universal_Identifier_ID", uid)
                    }
                }
            }
        }
        return WDRequestTransformHelper.buildReadSOAPRequest(xml.toString(), version, "Get_Change_Home_Contact_Information_Request")
    }

    public static String mapToPutChangeHomeContactInfoRequest(TransformContext ctx, boolean skipBusinessProcess,
                                                              String changeHomeContactInfoDataNode) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Business_Process_Parameters"() {
                "wd:Auto_Complete"(skipBusinessProcess)
            }
            if (changeHomeContactInfoDataNode != null) {
                mkp.yieldUnescaped changeHomeContactInfoDataNode
            }
        }
        return WDRequestTransformHelper.buildWriteSOAPRequest(xml.toString(), version, "Change_Home_Contact_Information_Request")
    }

    public static String mapToGetChangeWorkContactInfoRequest(TransformContext ctx, String uid) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Request_References"() {
                if (uid != null) {
                    "wd:Universal_ID_Reference"() {
                        "wd:ID"("wd:type": "Universal_Identifier_ID", uid)
                    }
                }
            }
        }
        return WDRequestTransformHelper.buildReadSOAPRequest(xml.toString(), version, "Get_Change_Work_Contact_Information_Request")
    }

    public static String mapToPutChangeWorkContactInfoRequest(TransformContext ctx, boolean skipBusinessProcess,
                                                              String changeWorkContactInfoDataNode) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Business_Process_Parameters"() {
                "wd:Auto_Complete"(skipBusinessProcess)
            }
            if (changeWorkContactInfoDataNode != null) {
                mkp.yieldUnescaped changeWorkContactInfoDataNode
            }
        }
        return WDRequestTransformHelper.buildWriteSOAPRequest(xml.toString(), version, "Change_Work_Contact_Information_Request")
    }
}
