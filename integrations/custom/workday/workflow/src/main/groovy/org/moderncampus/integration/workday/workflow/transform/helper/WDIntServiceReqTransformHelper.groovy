package org.moderncampus.integration.workday.workflow.transform.helper


import groovy.xml.StreamingMarkupBuilder
import org.moderncampus.integration.transform.TransformContext

class WDIntServiceReqTransformHelper {

    public static String mapToLaunchIntegrationEventRequest(TransformContext ctx, String launchIntegrationEventRequestNodes) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            if (launchIntegrationEventRequestNodes != null) {
                mkp.yieldUnescaped launchIntegrationEventRequestNodes
            }
        }
        return WDRequestTransformHelper.buildWriteSOAPRequest(xml.toString(), version, "Launch_Integration_Event_Request")
    }
}
