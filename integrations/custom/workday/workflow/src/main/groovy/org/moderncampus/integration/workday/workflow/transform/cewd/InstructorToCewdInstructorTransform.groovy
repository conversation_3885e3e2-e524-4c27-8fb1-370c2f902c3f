package org.moderncampus.integration.workday.workflow.transform.cewd

import groovy.transform.CompileStatic
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.Attributes
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.dto.cewd.CewdAddress
import org.moderncampus.integration.dto.cewd.CewdEmail
import org.moderncampus.integration.dto.cewd.CewdInstructor
import org.moderncampus.integration.dto.cewd.CewdPhone
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.workflow.transform.cewd.helper.WDToCewdPhoneTransformHelper
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.helper.GroovyXMLSupport.nodeDefinedAndNotEmpty
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWDNamespacePrefix

@Component
@CompileStatic
class InstructorToCewdInstructorTransform extends BaseTransformer<String, CewdInstructor> {

    static final DateTimeFormatter wdDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-ddZZZZZ")
    static final String HOME = "Home"
    static final String WORK = "Work"
    static final String ISO_3166_1_ALPHA_2_CODE = "ISO_3166-1_Alpha-2_Code"
    static Set<String> MAPPED_ADDRESS_PROP_NAMES = Set.of("Address_Line_1", "Address_Line_2", "City", "State_ISO_Code",
            "Country", "Postal_Code", "Country_Region")

    @Override
    protected CewdInstructor doTransform(TransformContext context, String input) {
        def rootNode = new XmlSlurper().parseText(input) as NodeChild
        String namespacePrefix = extractWDNamespacePrefix(rootNode) ?: fieldNotDefinedException("namespace")
        CewdInstructor instructor = new CewdInstructor()
        if (rootNode.size() <= 0 || extractNodeText(rootNode['Universal_ID'] as NodeChildren) == null) {
            return null;
        }
        instructor.id = extractNodeText(rootNode['Universal_ID'] as NodeChildren)
        def dataGroup = rootNode.children().find { (it as NodeChild).name() == "Worker_group" } ? rootNode['Worker_group'] :
                rootNode['Academic_Affiliate_group']
        instructor.firstName = extractNodeText(dataGroup['First_Name'] as NodeChildren)
        instructor.middleName = extractNodeText(dataGroup['Middle_Name'] as NodeChildren)
        instructor.lastName = extractNodeText(dataGroup['Last_Name'] as NodeChildren)
        instructor.birthDate = extractInstructorBirthDate(extractNodeText(dataGroup['Date_of_Birth'] as NodeChildren))
        instructor.salutation = extractNodeText(dataGroup['Salutation'] as NodeChildren)
        instructor.genderCode = extractNodeText dataGroup['Gender']['*'].find {
            node ->
                node["@" + namespacePrefix + ":type"] == 'Gender_Code'
        } as NodeChild

        List<CewdPhone> cewdPhones = new ArrayList<>()
        String homePhone = extractNodeText dataGroup['Primary_Home_Phone']["@" + namespacePrefix + ":Descriptor"] as Attributes,
                { extractNodeText dataGroup['Primary_Home_Phone'] as NodeChildren }
        String workPhone = extractNodeText dataGroup['Primary_Work_Phone']["@" + namespacePrefix + ":Descriptor"] as Attributes,
                { extractNodeText dataGroup['Primary_Work_Phone'] as NodeChildren }
        mapPhone(HOME, homePhone, cewdPhones)
        mapPhone(WORK, workPhone, cewdPhones)
        instructor.phones = cewdPhones

        List<CewdEmail> cewdEmails = new ArrayList<>()
        String homeEmail = extractNodeText dataGroup['Primary_Home_Email'] as NodeChildren
        String workEmail = extractNodeText dataGroup['Primary_Work_Email'] as NodeChildren
        mapEmail(HOME, homeEmail, cewdEmails)
        mapEmail(WORK, workEmail, cewdEmails)
        instructor.emails = cewdEmails

        List<CewdAddress> addresses = new ArrayList<>()
        def workerPrimaryHomeAddressGroup = rootNode['Worker_Primary_Home_Address_group'] as NodeChildren
        def affiliatePrimaryHomeAddressGroup = rootNode['Academic_Affiliate_Primary_Home_Address_group'] as NodeChildren
        if (!workerPrimaryHomeAddressGroup.isEmpty() || !affiliatePrimaryHomeAddressGroup.isEmpty()) {
            CewdAddress address = mapAddress(namespacePrefix, workerPrimaryHomeAddressGroup, null,
                    affiliatePrimaryHomeAddressGroup, null)
            addresses << address
        }
        def workerPrimaryWorkAddressGroup = rootNode['Worker_Primary_Work_Address_group'] as NodeChildren
        def affiliatePrimaryWorkAddressGroup = rootNode['Academic_Affiliate_Primary_Work_Address_group'] as NodeChildren
        if (!affiliatePrimaryWorkAddressGroup.isEmpty()) {
            affiliatePrimaryWorkAddressGroup = affiliatePrimaryWorkAddressGroup.last() as NodeChild
        }
        if (!workerPrimaryWorkAddressGroup.isEmpty() || !affiliatePrimaryWorkAddressGroup.isEmpty()) {
            CewdAddress address = mapAddress(namespacePrefix, null, workerPrimaryWorkAddressGroup,
                    null, affiliatePrimaryWorkAddressGroup)
            addresses << address
        }
        instructor.addresses = addresses
        return instructor
    }

    def LocalDate extractInstructorBirthDate(String birthDate) {
        try {
            return LocalDate.parse(birthDate, wdDateTimeFormatter)
        } catch (Exception ignored) {
            return null
        }
    }

    def void mapEmail(String type, String emailAddress, List<CewdEmail> cewdEmails) {
        if (emailAddress != null) {
            CewdEmail email = new CewdEmail();
            email.setType(type);
            email.setEmailAddress(emailAddress);
            cewdEmails.add(email);
        }
    }

    def CewdAddress mapAddress(String namespacePrefix, GPathResult workerPrimaryHomeAddressGroup, GPathResult workerPrimaryWorkAddressGroup,
                               GPathResult affiliateHomeAddressGroup, GPathResult affiliateWorkAddressGroup) {
        if (!nodeDefinedAndNotEmpty(workerPrimaryHomeAddressGroup) && !nodeDefinedAndNotEmpty(workerPrimaryWorkAddressGroup) &&
                !nodeDefinedAndNotEmpty(affiliateHomeAddressGroup) && !nodeDefinedAndNotEmpty(affiliateWorkAddressGroup)) {
            return null
        }

        CewdAddress address = new CewdAddress()
        String type = nodeDefinedAndNotEmpty(workerPrimaryHomeAddressGroup) || nodeDefinedAndNotEmpty(affiliateHomeAddressGroup) ? HOME : WORK
        String line1 = HOME == type ? extractNodeText(workerPrimaryHomeAddressGroup['Address_Line_1'] as NodeChildren,
                {
                    extractNodeText(affiliateHomeAddressGroup['Address_Line_1'] as NodeChildren)
                }) : extractNodeText(workerPrimaryWorkAddressGroup['Address_Line_1'] as NodeChildren,
                { extractNodeText(affiliateWorkAddressGroup['Address_Line_1'] as NodeChildren) })
        String line2 = HOME == type ? extractNodeText(workerPrimaryHomeAddressGroup['Address_Line_2'] as NodeChildren,
                {
                    extractNodeText(affiliateHomeAddressGroup['Address_Line_2'] as NodeChildren)
                }) : extractNodeText(workerPrimaryWorkAddressGroup['Address_Line_2'] as NodeChildren,
                { extractNodeText(affiliateWorkAddressGroup['Address_Line_2'] as NodeChildren) })
        String city = HOME == type ? extractNodeText(workerPrimaryHomeAddressGroup['City'] as NodeChildren,
                {
                    extractNodeText(affiliateHomeAddressGroup['City'] as NodeChildren)
                }) : extractNodeText(workerPrimaryWorkAddressGroup['City'] as NodeChildren,
                { extractNodeText(affiliateWorkAddressGroup['City'] as NodeChildren) })
        String state = HOME == type ? extractNodeText(workerPrimaryHomeAddressGroup['State_ISO_Code'] as NodeChildren,
                {
                    extractNodeText(affiliateHomeAddressGroup['State_ISO_Code'] as NodeChildren)
                }) : extractNodeText(workerPrimaryWorkAddressGroup['State_ISO_Code'] as NodeChildren,
                { extractNodeText(affiliateWorkAddressGroup['State_ISO_Code'] as NodeChildren) })
        def countryNode = HOME == type ? ((workerPrimaryHomeAddressGroup['Country'] as NodeChildren).isEmpty() ?
                (affiliateHomeAddressGroup['Country'] as NodeChildren) : workerPrimaryHomeAddressGroup['Country'] as NodeChildren) : (
                (workerPrimaryWorkAddressGroup['Country'] as NodeChildren).isEmpty() ? (affiliateWorkAddressGroup['Country'] as NodeChildren) :
                        workerPrimaryWorkAddressGroup['Country'] as NodeChildren)
        String country = extractNodeText((countryNode['*'] as NodeChildren)?.find {
            NodeChild countryId ->
                countryId["@" + namespacePrefix + ":type"] == ISO_3166_1_ALPHA_2_CODE
        })
        String postalCode = HOME == type ? extractNodeText(workerPrimaryHomeAddressGroup['Postal_Code'] as NodeChildren,
                {
                    extractNodeText(affiliateHomeAddressGroup['Postal_Code'] as NodeChildren)
                }) : extractNodeText(workerPrimaryWorkAddressGroup['Postal_Code'] as NodeChildren,
                { extractNodeText(affiliateWorkAddressGroup['Postal_Code'] as NodeChildren) })
        address.type = type
        address.line1 = line1
        address.line2 = line2
        address.city = city
        address.state = state
        address.country = country
        address.postalCode = postalCode
        if (HOME == type) {
            if (!workerPrimaryHomeAddressGroup.isEmpty()) {
                setExtraAddressProps(address, workerPrimaryHomeAddressGroup)
            } else if (!affiliateHomeAddressGroup.isEmpty()) {
                setExtraAddressProps(address, affiliateHomeAddressGroup)
            }
        } else if (!workerPrimaryWorkAddressGroup.isEmpty()) {
            setExtraAddressProps(address, workerPrimaryWorkAddressGroup)
        } else if (!affiliateWorkAddressGroup.isEmpty()) {
            setExtraAddressProps(address, affiliateWorkAddressGroup)
        }
        return address
    }

    def void setExtraAddressProps(CewdAddress address, GPathResult addressGroup) {
        List<String> propertyNames = addressGroup['*'].collect {
            NodeChild it -> it.name()
        }
        for (String propertyName : propertyNames) {
            // Check if the property name matches the filter value
            if (!MAPPED_ADDRESS_PROP_NAMES.contains(propertyName)) {
                address.setDynamicProperty(convertPropertyName(propertyName), extractNodeText(addressGroup[propertyName] as NodeChildren));
            }
        }
    }

    def String convertPropertyName(String input) {
        def parts = input.split('_') as String[]
        return parts[0].toLowerCase() +
                parts[1..-1].collect { it.capitalize() }.join('')
    }

    def void mapPhone(String type, String phoneNumber, List<CewdPhone> cewdPhones) {
        if (phoneNumber != null) {
            CewdPhone phone = WDToCewdPhoneTransformHelper.mapPhone(type, phoneNumber)
            cewdPhones.add(phone);
        }
    }

    def void fieldNotDefinedException(String fieldName) throws RuntimeException {
        throw new RuntimeException(fieldName + " is required but not present in the response");
    }
}
