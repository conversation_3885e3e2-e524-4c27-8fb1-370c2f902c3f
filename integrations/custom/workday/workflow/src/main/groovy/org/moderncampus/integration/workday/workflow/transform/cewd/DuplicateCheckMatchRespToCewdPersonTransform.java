package org.moderncampus.integration.workday.workflow.transform.cewd;

import static org.moderncampus.integration.workday.workflow.transform.cewd.helper.WDToCewdPhoneTransformHelper.mapPhone;

import java.time.LocalDate;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.xml.datatype.XMLGregorianCalendar;

import org.moderncampus.integration.dto.cewd.CewdAddress;
import org.moderncampus.integration.dto.cewd.CewdEmail;
import org.moderncampus.integration.dto.cewd.CewdPerson;
import org.moderncampus.integration.dto.cewd.CewdPhone;
import org.moderncampus.integration.transform.BaseTransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntegrationResult;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class DuplicateCheckMatchRespToCewdPersonTransform extends
        BaseTransformer<DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo, CewdPerson> {

    static final String HOME = "Home";
    static final String WORK = "Work";
    static final String INSTITUTION = "Institution";

    @Override
    protected CewdPerson doTransform(TransformContext context,
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo input) {
        if (input == null) {
            throw new RuntimeException("Invalid match data specified for transform");
        }

        String uId = Optional.ofNullable(input.getReferenceIDs()).map(
                DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.ReferenceIDs::getUniversalID).orElse(null);

        if (uId == null) {
            log.info("Unable to transform duplicate studio integration match result. No UID found for match");
            return null;
        }

        CewdPerson person = new CewdPerson();
        person.setId(uId);
        if (input.getLegalName() != null) {
            person.setFirstName(input.getLegalName().getFirstName());
            person.setMiddleName(input.getLegalName().getMiddleName());
            person.setLastName(input.getLegalName().getLastName());
        }
        person.setBirthDate(Optional.ofNullable(input.getDateOfBirth()).map(this::convertToLocalDate).orElse(null));

        List<CewdAddress> addresses = Optional.ofNullable(input.getAllHomeAddresses())
                .map(allHomeAddresses -> allHomeAddresses.getData().stream().map(this::mapHomeAddress)
                        .collect(Collectors.toList())).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllWorkAddresses())
                        .map(allWorkAddresses -> allWorkAddresses.getData().stream().map(this::mapWorkAddress)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllInstitutionalAddresses())
                        .map(allInstitutionalAddresses -> allInstitutionalAddresses.getData().stream()
                                .map(this::mapInstitutionalAddress)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty()).orElse(null);
        person.setAddresses(addresses);

        List<CewdPhone> phones = Optional.ofNullable(input.getAllHomePhoneNumbers())
                .map(allHomePhones -> allHomePhones.getData().stream().map(this::mapHomePhone)
                        .collect(Collectors.toList())).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllWorkPhoneNumbers())
                        .map(allWorkPhones -> allWorkPhones.getData().stream()
                                .map(this::mapWorkPhone)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllInstitutionalPhoneNumbers())
                        .map(allInstitutionalPhones -> allInstitutionalPhones.getData().stream()
                                .map(this::mapInstitutionalPhone)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty()).orElse(null);
        person.setPhones(phones);

        List<CewdEmail> emails = Optional.ofNullable(input.getAllHomeEmailAddresses())
                .map(allHomeEmails -> allHomeEmails.getData().stream().map(this::mapHomeEmail)
                        .collect(Collectors.toList())).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllWorkEmailAddresses())
                        .map(allWorkEmails -> allWorkEmails.getData().stream()
                                .map(this::mapWorkEmail)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty())
                .or(() -> Optional.ofNullable(input.getAllInstitutionalEmailAddresses())
                        .map(allInstitutionalEmails -> allInstitutionalEmails.getData().stream()
                                .map(this::mapInstitutionalEmail)
                                .collect(Collectors.toList()))).filter(list -> !list.isEmpty()).orElse(null);
        person.setEmails(emails);
        return person;
    }

    private CewdAddress mapHomeAddress(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllHomeAddresses.Data address) {
        return mapAddress(HOME, address.getAddressLine1(), address.getAddressLine2(), address.getCity(),
                address.getRegionISO31662Code(), address.getPostalCode(), address.getCountryISOAlpha3Code());
    }

    private CewdAddress mapWorkAddress(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllWorkAddresses.Data address) {
        return mapAddress(WORK, address.getAddressLine1(), address.getAddressLine2(), address.getCity(),
                address.getRegionISO31662Code(), address.getPostalCode(), address.getCountryISOAlpha3Code());
    }

    private CewdAddress mapInstitutionalAddress(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllInstitutionalAddresses.Data address) {
        return mapAddress(INSTITUTION, address.getAddressLine1(), address.getAddressLine2(), address.getCity(),
                address.getRegionISO31662Code(), address.getPostalCode(), address.getCountryISOAlpha3Code());
    }

    private CewdAddress mapAddress(String type, String addressLine1, String addressLine2, String city,
            String regionISO31662Code, String postalCode, String countryISOAlpha3Code) {
        CewdAddress cewdAddress = new CewdAddress();
        cewdAddress.setType(type);
        cewdAddress.setLine1(addressLine1);
        cewdAddress.setLine2(addressLine2);
        cewdAddress.setCity(city);
        cewdAddress.setState(regionISO31662Code);
        cewdAddress.setPostalCode(postalCode);
        cewdAddress.setCountry(countryISOAlpha3Code);
        return cewdAddress;
    }

    private CewdPhone mapHomePhone(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllHomePhoneNumbers.Data homePhone) {
        return mapPhone(HOME, homePhone.getE164FormattedPhoneNumber());
    }

    private CewdPhone mapWorkPhone(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllWorkPhoneNumbers.Data workPhone) {
        return mapPhone(WORK, workPhone.getE164FormattedPhoneNumber());
    }

    private CewdPhone mapInstitutionalPhone(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllInstitutionalPhoneNumbers.Data institutionalPhone) {
        return mapPhone(INSTITUTION, institutionalPhone.getE164FormattedPhoneNumber());
    }

    private CewdEmail mapHomeEmail(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllHomeEmailAddresses.Data homeEmail) {
        return mapEmail(HOME, homeEmail.getEmailAddress());
    }

    private CewdEmail mapWorkEmail(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllWorkEmailAddresses.Data workEmail) {
        return mapEmail(WORK, workEmail.getEmailAddress());
    }

    private CewdEmail mapInstitutionalEmail(
            DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo.AllInstitutionalEmailAddresses.Data institutionalEmail) {
        return mapEmail(INSTITUTION, institutionalEmail.getEmailAddress());
    }

    private CewdEmail mapEmail(String type, String emailAddress) {
        CewdEmail email = new CewdEmail();
        email.setType(type);
        email.setEmailAddress(emailAddress);
        return email;
    }

    private LocalDate convertToLocalDate(XMLGregorianCalendar xmlGregorianCalendar) {
        GregorianCalendar gc = xmlGregorianCalendar.toGregorianCalendar();
        return gc.toZonedDateTime().toLocalDate();
    }
}
