package org.moderncampus.integration.workday.workflow.validation;

import org.moderncampus.integration.validation.IValidationGroups;

import jakarta.validation.GroupSequence;
import jakarta.validation.groups.Default;

public interface IWorkdayValidationUseCase {

    @GroupSequence({Default.class, IValidationGroups.IIntPersonUpdate.class})
    interface IWorkdayStudentUpdateUseCase {

    }

    @GroupSequence({Default.class, IValidationGroups.IWorkdayDuplicateCheckRequest.class})
    interface IWorkdayCreateDuplicateCheckUseCase {

    }
}
