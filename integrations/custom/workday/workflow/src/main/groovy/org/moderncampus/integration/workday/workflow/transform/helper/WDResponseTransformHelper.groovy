package org.moderncampus.integration.workday.workflow.transform.helper

import groovy.transform.CompileStatic
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.Node
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.exception.ApplicationException
import org.moderncampus.integration.exception.ValidationException
import org.moderncampus.integration.exception.ValidationExceptions
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext

import java.time.LocalDate
import java.time.format.DateTimeFormatter

@CompileStatic
class WDResponseTransformHelper {

    static DateTimeFormatter wdDateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    public static <T> ITransformer<InputStream, T> wdSOAPResponseMapper(ITransformer<NodeChild, T> respMapper) {
        return (TransformContext ctx, InputStream body) -> {
            def rootNode = new XmlSlurper().parse(body) as NodeChild
            mapSOAPErrors(rootNode)
            return respMapper.transform(ctx, rootNode)
        }
    }

    public static String extractWDNamespacePrefix(NodeChild rootNode) {
        def namespace = rootNode.getClass().getSuperclass().getDeclaredField("namespaceTagHints")
        namespace.setAccessible(true)
        return (namespace.get(rootNode) as Map<String, String>).find { key, value -> !key.contains("xml") && !key.contains("env") }?.key
    }

    public static void mapSOAPErrors(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        mapSOAPErrors(rootNode)
    }

    public static String bodyNode(String nameSpace, String nodeName) {
        return nameSpace + ":" + nodeName
    }

    public static String wdNode(String nodeName) {
        return "wd" + ":" + nodeName
    }


    public static void mapSOAPErrors(NodeChild rootNode) {
        def faultNode = (rootNode['Body']['Fault'] as NodeChildren)
        if (faultNode.size() > 0) {
            def faultString = (faultNode['faultstring'] as NodeChildren)
            if (faultString.size() > 0) {
                def defaultException = new ApplicationException(faultString.text(), 400)
                NodeChildren validationErrorNodes = (faultNode['detail']['Validation_Fault']['Validation_Error'] as NodeChildren)
                if (validationErrorNodes.size() > 0) {
                    def validationExceptionsList = validationErrorNodes.collect { child ->
                        {
                            NodeChild theChild = child as NodeChild
                            Iterator childNodes = theChild.childNodes()
                            String message = null, detailMessage = null, xPath = null
                            childNodes.each { childNode ->
                                if ((childNode as Node).name() == 'Message') {
                                    message = (childNode as Node).text()
                                } else if ((childNode as Node).name() == 'Detail_Message') {
                                    detailMessage = (childNode as Node).text()
                                } else if ((childNode as Node).name() == 'Xpath') {
                                    xPath = (childNode as Node).text()
                                }
                            }
                            ValidationException validationException = new ValidationException(message ?: detailMessage, xPath)
                            return validationException
                        }
                    }
                    throw new ValidationExceptions(validationExceptionsList)
                }
                NodeChildren processErrorNodes = (faultNode['detail']['Processing_Fault'] as NodeChildren)
                if (processErrorNodes.size() > 0) {
                    throw new ApplicationException((processErrorNodes['Detail_Message'] as NodeChildren).text(), 500)
                }
                throw defaultException
            }
        }
    }

    public static LocalDate extractWorkdayDate(String date) {
        if (date == null) return null
        try {
            return LocalDate.parse(date, wdDateFormatter)
        } catch (Exception ignored) {
            return null
        }
    }

}
