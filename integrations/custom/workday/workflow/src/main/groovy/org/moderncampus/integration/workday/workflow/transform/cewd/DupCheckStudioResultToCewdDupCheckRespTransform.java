package org.moderncampus.integration.workday.workflow.transform.cewd;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.moderncampus.integration.dto.cewd.CewdPerson;
import org.moderncampus.integration.transform.BaseTransformer;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntegrationResult;
import org.moderncampus.integration.workday.dto.dupsearch.cewd.response.WDStudentDuplicateCheckResponse;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DupCheckStudioResultToCewdDupCheckRespTransform extends
        BaseTransformer<DuplicateCheckStudioIntegrationResult, WDStudentDuplicateCheckResponse> {

    static final String NO_RULES_EVALUATED = "No rules evaluated";
    static final String EXACT_MATCH = "EXACT_MATCH";

    ITransformer<DuplicateCheckStudioIntegrationResult.Matches.Match.BioDemo, CewdPerson> duplicateCheckMatchToCewdPersonTransform;

    @Override
    protected WDStudentDuplicateCheckResponse doTransform(TransformContext context,
            DuplicateCheckStudioIntegrationResult result) throws Exception {
        WDStudentDuplicateCheckResponse response = new WDStudentDuplicateCheckResponse();
        response.setErrors(Optional.ofNullable(result.getErrors()).map(
                DuplicateCheckStudioIntegrationResult.Errors::getError).orElse(null));
        response.setWarnings(Optional.ofNullable(result.getWarnings()).map(
                DuplicateCheckStudioIntegrationResult.Warnings::getWarning).orElse(null));
        response.setCorrelationId(result.getEvent().getExternalJobID());
        response.setBusinessProcessId(result.getEvent().getIntegrationEventWID());
        if (result.getEvent().getRulesProcessed() == 0) {
            //send an error payload
            response.setIntegrationStatus(
                    WDStudentDuplicateCheckResponse.IntegrationStatus.FAILED);

            response.getErrors().add(NO_RULES_EVALUATED);

            response.setMatches(new ArrayList<>());
        } else {
            response.setIntegrationStatus(
                    WDStudentDuplicateCheckResponse.IntegrationStatus.SUCCESS);
            List<CewdPerson> list = new ArrayList<>();
            for (DuplicateCheckStudioIntegrationResult.Matches.Match match : result.getMatches().getMatch()) {
                boolean exactMatch = Optional.ofNullable(match.getMatchRules())
                        .map((rule -> rule.getData().stream()
                                .anyMatch(ruleData -> ruleData.getAutomaticMergeEnabled() == 1)))
                        .orElse(false);
                Optional<CewdPerson> person = Optional.ofNullable(duplicateCheckMatchToCewdPersonTransform.transform(
                        context,
                        match.getBioDemo()));
                person.ifPresent((p) -> {
                    p.setDynamicProperty(EXACT_MATCH, exactMatch);
                });
                person.ifPresent(list::add);
            }
            response.setMatches(list);
        }
        return response;
    }
}
