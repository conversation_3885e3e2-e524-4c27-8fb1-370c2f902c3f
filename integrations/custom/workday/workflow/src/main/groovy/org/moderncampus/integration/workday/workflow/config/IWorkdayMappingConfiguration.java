package org.moderncampus.integration.workday.workflow.config;

import java.util.Map;

public interface IWorkdayMappingConfiguration {

    Map<String, String> getHomeAddressMapping();

    Map<String, String> getWorkAddressMapping();

    Map<String, String> getHomePhoneMapping();

    Map<String, String> getWorkPhoneMapping();

    String getDuplicateCheckIntSystemId();

    String getStudentIdType();

    String getNationalIdType();

    boolean isSkipBusinessProcess();
}
