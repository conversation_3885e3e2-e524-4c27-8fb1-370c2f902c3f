package org.moderncampus.integration.workday.workflow.route.beans.cc;

import static org.moderncampus.integration.route.support.RouteSupport.*;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.CONNECTION_CONFIG_PARAM;
import static org.moderncampus.integration.workday.component.constants.Constants.WORKDAY_COMPONENT_SCHEME;
import static org.moderncampus.integration.workday.workflow.route.beans.CommonRouteBeanDefinitions.VERSION;
import static org.moderncampus.integration.workday.workflow.route.identifier.acalog.WorkdayRouteIds.V1_ACALOG_WORKDAY_GET_COURSES;
import static org.moderncampus.integration.workday.workflow.route.identifier.curriculog.WorkdayRouteIds.V1_CURRICULOG_WORKDAY_CREATE_COURSE;
import static org.moderncampus.integration.workday.workflow.route.identifier.curriculog.WorkdayRouteIds.V1_CURRICULOG_WORKDAY_UPDATE_COURSE;
import static org.moderncampus.integration.workday.workflow.transform.core.CoreWDReqTransforms.getStudentCoursesRequestTransformer;
import static org.moderncampus.integration.workday.workflow.transform.core.CoreWDReqTransforms.submitStudentCrseResponseTransformer;
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.wdSOAPResponseMapper;

import java.util.Map;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.route.builder.BaseCustomRouteBuilder;
import org.moderncampus.integration.transform.support.BaseTransformProcessor;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.WorkdayStudentRecordsRecordType;
import org.moderncampus.integration.workday.workflow.route.beans.CommonRouteBeanDefinitions;
import org.moderncampus.integration.workday.workflow.route.tasks.cc.GetAndPutCourseInformationTask;
import org.moderncampus.integration.workday.workflow.transform.core.CourseToSubmitStudentCourseDataTransform;
import org.moderncampus.integration.workday.workflow.transform.core.GetStudentCoursesResponseToCourseTransform;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Import(CommonRouteBeanDefinitions.class)
public class CCRouteBeanDefinitions {

    static final String BEAN_VALIDATOR_URI = "bean-validator://x";

    WorkdayConnectionConfiguration connectionConfiguration;

    ObjectMapper mapper;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1AcalogWorkdayGetCourses(GetStudentCoursesResponseToCourseTransform transform) {
        return new BaseCustomRouteBuilder(V1_ACALOG_WORKDAY_GET_COURSES.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.setProperty(VERSION, connectionConfiguration::getVersion);
                routeDefinition.process(
                        new BaseTransformProcessor(getStudentCoursesRequestTransformer, SearchEntityRequest.class));
                routeDefinition.toD(buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                        IWorkdayEndpointConfiguration.Entity.soapWS.name(), routeQueryParamStr(
                                Map.of(CONNECTION_CONFIG_PARAM,
                                        parameterBeanRef(WorkdayConnectionConfiguration.class),
                                        IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM,
                                        parameterValueRef(WorkdayStudentRecordsRecordType.class,
                                                WorkdayStudentRecordsRecordType.GET_STUDENT_COURSE)))));
                routeDefinition.process(
                        new BaseTransformProcessor(wdSOAPResponseMapper(transform)));
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1CurriculogWorkdayCreateCourse(CourseToSubmitStudentCourseDataTransform transform) {
        return new BaseCustomRouteBuilder(V1_CURRICULOG_WORKDAY_CREATE_COURSE.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.to(BEAN_VALIDATOR_URI);
                routeDefinition.setProperty(VERSION, connectionConfiguration::getVersion);
                routeDefinition.process(
                        new BaseTransformProcessor(transform));
                routeDefinition.toD(buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                        IWorkdayEndpointConfiguration.Entity.soapWS.name(), routeQueryParamStr(
                                Map.of(CONNECTION_CONFIG_PARAM,
                                        parameterBeanRef(WorkdayConnectionConfiguration.class),
                                        IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM,
                                        parameterValueRef(WorkdayStudentRecordsRecordType.class,
                                                WorkdayStudentRecordsRecordType.SUBMIT_STUDENT_COURSE)))));
                routeDefinition.process(
                        new BaseTransformProcessor(wdSOAPResponseMapper(submitStudentCrseResponseTransformer)));
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1CurriculogWorkdayUpdateCourse(GetAndPutCourseInformationTask courseInformationTask) {
        return new BaseCustomRouteBuilder(V1_CURRICULOG_WORKDAY_UPDATE_COURSE.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.to(BEAN_VALIDATOR_URI);
                routeDefinition.process(courseInformationTask);
                routeDefinition.process((exchange) -> {
                    exchange.getMessage().setBody(mapper.createObjectNode());
                });
            }
        };
    }
}
