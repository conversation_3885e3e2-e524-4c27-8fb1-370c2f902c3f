package org.moderncampus.integration.workday.workflow.route.beans.cewd;

import static org.moderncampus.integration.component.constants.Constants.MODERN_CAMPUS_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.Constants.DEFAULT_ROUTE_RESULT_MAPPER_BEAN_NAME;
import static org.moderncampus.integration.route.support.RouteSupport.*;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.CONNECTION_CONFIG_PARAM;
import static org.moderncampus.integration.workday.component.constants.Constants.WORKDAY_COMPONENT_SCHEME;
import static org.moderncampus.integration.workday.workflow.route.beans.CommonRouteBeanDefinitions.VERSION;
import static org.moderncampus.integration.workday.workflow.route.identifier.destiny.WorkdayRouteIds.*;
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.wdSOAPResponseMapper;

import java.util.Map;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.component.MCComponent;
import org.moderncampus.integration.component.endpoint.config.IMCDestinyConnectionConfiguration;
import org.moderncampus.integration.component.endpoint.config.MCDestinyAsyncEventEndpointConfig;
import org.moderncampus.integration.component.endpoint.config.MCDestinyConnectionConfiguration;
import org.moderncampus.integration.component.endpoint.config.MCDestinyEndpointConfiguration;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.helper.IntegrationResponseMapper;
import org.moderncampus.integration.route.builder.BaseCustomRouteBuilder;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.moderncampus.integration.system.IntegrationSystem;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.support.BaseTransformProcessor;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.WorkdayIntegrationRecordType;
import org.moderncampus.integration.workday.dto.WorkdayRAASRecordType;
import org.moderncampus.integration.workday.dto.dupsearch.DuplicateCheckStudioIntegrationResult;
import org.moderncampus.integration.workday.dto.dupsearch.cewd.response.WDStudentDuplicateCheckResponse;
import org.moderncampus.integration.workday.workflow.route.beans.CommonRouteBeanDefinitions;
import org.moderncampus.integration.workday.workflow.route.tasks.cewd.GetAndPutHomeContactInfoTask;
import org.moderncampus.integration.workday.workflow.route.tasks.cewd.GetAndPutOtherIdTask;
import org.moderncampus.integration.workday.workflow.route.tasks.cewd.GetAndPutWorkContactInfoTask;
import org.moderncampus.integration.workday.workflow.transform.cewd.CewdStudentDupCheckReqToLaunchIntegrationTransform;
import org.moderncampus.integration.workday.workflow.transform.cewd.DupCheckIntRespToDupCheckStagedRespTransform;
import org.moderncampus.integration.workday.workflow.transform.cewd.InstructorToCewdInstructorTransform;
import org.moderncampus.integration.workday.workflow.validation.IWorkdayValidationUseCase;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Import(CommonRouteBeanDefinitions.class)
public class CewdRouteBeanDefinitions {

    public static final String VALIDATE_STUDENT_UPDATE_URI = "bean-validator://x?group="
            + IWorkdayValidationUseCase.IWorkdayStudentUpdateUseCase.class.getName();
    public static final String VALIDATE_CREATE_DUP_CHECK_REQ_URI = "bean-validator://x?group="
            + IWorkdayValidationUseCase.IWorkdayCreateDuplicateCheckUseCase.class.getName();
    public static final String STUDENT = "student";
    public static final String DUPLICATE_SEARCH_ACTION = "duplicate-search";
    public static final String EVENT_ID = "eventId";
    public static final String EVENT_ID_REF = "${exchangeProperty.eventId}";

    WorkdayConnectionConfiguration connectionConfiguration;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1WorkdayGetInstructors(InstructorToCewdInstructorTransform transform,
            DefaultListAggregationStrategy aggregationStrategy) {
        return new BaseCustomRouteBuilder(V1_D1_WORKDAY_GET_INSTRUCTORS.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.toD(buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                        IWorkdayEndpointConfiguration.Entity.report.name(), routeQueryParamStr(
                                Map.of(CONNECTION_CONFIG_PARAM, parameterBeanRef(WorkdayConnectionConfiguration.class),
                                        IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM,
                                        parameterValueRef(WorkdayRAASRecordType.class,
                                                WorkdayRAASRecordType.MC_D1_GET_INSTRUCTORS)))));
                routeDefinition.split(body().tokenizeXML("Report_Entry", "Report_Data"), aggregationStrategy)
                        .stopOnException(true)
                        .process(new BaseTransformProcessor(transform));
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1WorkdayUpdateStudent(ObjectMapper mapper, GetAndPutOtherIdTask getAndPutOtherIdTask,
            GetAndPutHomeContactInfoTask getAndPutHomeContactInfoTask,
            GetAndPutWorkContactInfoTask getAndPutWorkContactInfoTask
    ) {
        return new BaseCustomRouteBuilder(V1_D1_WORKDAY_UPDATE_STUDENT.getId(), null) {

            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.to(VALIDATE_STUDENT_UPDATE_URI).multicast()
                        .stopOnException()
                        .process(getAndPutOtherIdTask)
                        .process(getAndPutHomeContactInfoTask)
                        .process(getAndPutWorkContactInfoTask)
                        .process((exchange) -> {
                            exchange.getMessage().setBody(mapper.createObjectNode());
                        });
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1WorkdaySendDuplicateCheckRequest(
            CewdStudentDupCheckReqToLaunchIntegrationTransform studentToLaunchIntegTransform,
            DupCheckIntRespToDupCheckStagedRespTransform launchIntegrationRespToDupCheckStagedRespTransform) {
        return new BaseCustomRouteBuilder(V1_D1_WORKDAY_CREATE_DUPLICATE_CHECK_REQ.getId(), null) {

            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.setProperty(VERSION, connectionConfiguration::getVersion);
                routeDefinition.to(VALIDATE_CREATE_DUP_CHECK_REQ_URI)
                        .process(new BaseTransformProcessor(studentToLaunchIntegTransform))
                        .toD(buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                                IWorkdayEndpointConfiguration.Entity.soapWS.name(), routeQueryParamStr(
                                        Map.of(CONNECTION_CONFIG_PARAM,
                                                parameterBeanRef(WorkdayConnectionConfiguration.class),
                                                IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM,
                                                parameterValueRef(WorkdayIntegrationRecordType.class,
                                                        WorkdayIntegrationRecordType.LAUNCH_INTEGRATION)))))
                        .process(new BaseTransformProcessor(
                                wdSOAPResponseMapper(launchIntegrationRespToDupCheckStagedRespTransform)));
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1WorkdayD1SendDuplicateCheckResponse(ObjectMapper objectMapper,
            IMCDestinyConnectionConfiguration d1EndpointConfig,
            ITransformer<DuplicateCheckStudioIntegrationResult, WDStudentDuplicateCheckResponse> dupCheckStudioResultMapper,
            IntegrationResponseMapper intResponseMapper) {
        return new BaseCustomRouteBuilder(V1_WORKDAY_D1_SEND_DUPLICATE_CHECK_RESP.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.process(new BaseTransformProcessor(dupCheckStudioResultMapper))
                        .bean(DEFAULT_ROUTE_RESULT_MAPPER_BEAN_NAME)
                        .bean(intResponseMapper, "mapRouteResult(${body}, null, true)")
                        .process((exchange) -> {
                            IntegrationResponse<WDStudentDuplicateCheckResponse> outboundResult =
                                    (IntegrationResponse<WDStudentDuplicateCheckResponse>) exchange.getMessage()
                                            .getBody();
                            exchange.setProperty(EVENT_ID,
                                    outboundResult.getData().getCorrelationId() + ":" + outboundResult.getData()
                                            .getBusinessProcessId());
                        }).toD(buildRouteURI(MODERN_CAMPUS_COMPONENT_SCHEME,
                                MCComponent.Product.destinyOne + ":" + MCDestinyEndpointConfiguration.Entity.asyncEventWS + ":"
                                        + "/", routeQueryParamStr(
                                        Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                                parameterBeanRef(MCDestinyConnectionConfiguration.class),
                                                MCDestinyAsyncEventEndpointConfig.SOURCE_SYSTEM_PARAM,
                                                parameterValueRef(IntegrationSystem.class, IntegrationSystem.WORKDAY),
                                                MCDestinyAsyncEventEndpointConfig.DESTINY_ENTITY_PARAM, STUDENT,
                                                MCDestinyAsyncEventEndpointConfig.ACTION_PARAM, DUPLICATE_SEARCH_ACTION,
                                                MCDestinyAsyncEventEndpointConfig.EVENT_ID_PARAM, EVENT_ID_REF))))
                        .process((exchange) -> {
                            exchange.getMessage().setBody(objectMapper.createObjectNode());
                        });
            }
        };
    }
}
