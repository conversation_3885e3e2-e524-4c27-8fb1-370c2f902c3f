package org.moderncampus.integration.workday.workflow.route.tasks.cc;

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY;
import static org.moderncampus.integration.workday.workflow.transform.core.CoreWDReqTransforms.getStudentCoursesRequestTransformer;
import static org.moderncampus.integration.workday.workflow.transform.core.helper.CoreWDRespTransformHelper.extractCourseDataNodeFromGetResp;
import static org.moderncampus.integration.workday.workflow.transform.core.helper.CoreWDRespTransformHelper.extractEffectiveSnapshotNodeFromCourseDataNode;
import static org.moderncampus.integration.workday.workflow.transform.helper.WDRequestParamHelper.StudentCrseRequestParams.PARAM_STUDENT_CRSE_ID;

import java.util.Map;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.moderncampus.integration.workday.dto.WorkdayAPIPaginationConstruct;
import org.moderncampus.integration.workday.dto.WorkdayStudentRecordsRecordType;
import org.moderncampus.integration.workday.dto.course.WorkdayCourse;
import org.moderncampus.integration.workday.workflow.route.tasks.GetAndPutWorkdayInformationTask;
import org.springframework.stereotype.Component;

import groovy.xml.slurpersupport.NodeChild;
import groovy.xml.slurpersupport.NodeChildren;

@Component
public class GetAndPutCourseInformationTask extends
        GetAndPutWorkdayInformationTask<String, String, String, String, WorkdayCourse> {

    ITransformer<WorkdayCourse, String> transformer;

    public GetAndPutCourseInformationTask(
            IWorkdayConnectionConfiguration connectionConfiguration,
            ITransformer<WorkdayCourse, String> transformer) {
        super(connectionConfiguration, String.class, String.class);
        this.transformer = transformer;
    }

    @Override
    protected IWorkdayRecordType getRequestRecordType() {
        return WorkdayStudentRecordsRecordType.GET_STUDENT_COURSE;
    }

    @Override
    protected IWorkdayRecordType putRequestRecordType() {
        return WorkdayStudentRecordsRecordType.SUBMIT_STUDENT_COURSE;
    }

    @Override
    protected String mapToGetRequest(WorkdayCourse inputObject) throws Exception {
        TransformContext context = getDefaultTransformContext();
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        WorkdayAPIPaginationConstruct paginationConstruct = new WorkdayAPIPaginationConstruct();
        paginationConstruct.setAsOfEffectiveDate(inputObject.getEffectiveDate());
        entityRequest.setPaginationConstruct(paginationConstruct);
        entityRequest.setCriteria(Map.of(PARAM_STUDENT_CRSE_ID, inputObject.getId()));
        return getStudentCoursesRequestTransformer.transform(context, entityRequest);
    }

    @Override
    protected String mapToPutRequest(TaskInput input, String getResponse) throws Exception {
        WorkdayCourse ccWorkdayCourse = (WorkdayCourse) input.getInputData();
        NodeChildren courseDataNode = extractCourseDataNodeFromGetResp(getResponse, ccWorkdayCourse);
        NodeChild effectiveSnapShotNode = extractEffectiveSnapshotNodeFromCourseDataNode(courseDataNode,
                ccWorkdayCourse.getEffectiveDate(), true);
        input.getContextProperties().put(LOADED_ENTITY, effectiveSnapShotNode);
        return transformer.transform(new TransformContext(input.getContextProperties()), ccWorkdayCourse);
    }
}
