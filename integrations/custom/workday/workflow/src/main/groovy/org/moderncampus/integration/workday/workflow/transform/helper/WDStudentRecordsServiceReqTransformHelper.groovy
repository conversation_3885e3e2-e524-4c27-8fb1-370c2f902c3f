package org.moderncampus.integration.workday.workflow.transform.helper


import groovy.xml.StreamingMarkupBuilder
import org.moderncampus.integration.transform.TransformContext

import java.time.format.DateTimeFormatter

class WDStudentRecordsServiceReqTransformHelper {

    public static String mapToGetStudentCrseRequest(TransformContext ctx, WDRequestParamHelper.StudentCrseRequestParams params) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            if (params.studentCrseId) {
                "wd:Request_References"() {
                    "wd:Student_Course_Reference"() {
                        "wd:ID"("wd:type": "Student_Course_ID", params.studentCrseId)
                    }
                }
            }
            "wd:Response_Filter"() {
                if (params.pageNumber != null) {
                    "wd:Page"(params.pageNumber)
                }
                if (params.pageSize != null) {
                    "wd:Count"(params.pageSize)
                }
                if (params.asOfEffectiveDate != null) {
                    "wd:As_Of_Effective_Date"(params.asOfEffectiveDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                }
                if (params.asOfEntryMoment != null) {
                    "wd:As_Of_Entry_DateTime"(params.asOfEntryMoment.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                }
            }
        }
        return WDRequestTransformHelper.buildReadSOAPRequest(xml.toString(), version, "Get_Student_Courses_Request")
    }

    public static String mapToSubmitStudentCrseRequest(TransformContext ctx, String studentCrseRefNode, String studentCrseDataNode, boolean isCreate,
                                                       boolean isAutoComplete = true) {
        String version = ctx.getContextProp('version', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "wd:Business_Process_Parameters"() {
                "wd:Auto_Complete"(isAutoComplete)
                "wd:Run_Now"(isAutoComplete)
            }
            if (studentCrseRefNode != null) {
                mkp.yieldUnescaped studentCrseRefNode
            }
            if (studentCrseDataNode != null) {
                mkp.yieldUnescaped studentCrseDataNode
            }
        }
        return WDRequestTransformHelper.buildWriteSOAPRequest(xml.toString(), version, "Submit_Student_Course_Request",
                ["wd:Add_Only": isCreate as String])
    }
}
