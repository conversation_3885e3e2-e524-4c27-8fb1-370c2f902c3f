package org.moderncampus.integration.workday.workflow.route.identifier.destiny;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;
import static org.moderncampus.integration.workday.workflow.route.identifier.Constants.*;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum WorkdayRouteIds implements IRouteId {

    V1_D1_WORKDAY_GET_INSTRUCTORS(GET_INSTRUCTORS),
    V1_D1_WORKDAY_UPDATE_STUDENT(UPDATE_STUDENT),
    V1_D1_WORKDAY_CREATE_DUPLICATE_CHECK_REQ(CREATE_DUPLICATE_CHECK_REQ),
    V1_WORKDAY_D1_SEND_DUPLICATE_CHECK_RESP(SEND_DUPLICATE_CHECK_RESP, true);

    String contextPath;

    String id;

    WorkdayRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, DESTINY_SYSTEM_ID, "*", WORKDAY_SYSTEM_ID, contextPath});
    }

    WorkdayRouteIds(String contextPath, boolean inBound) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, WORKDAY_SYSTEM_ID, "*", DESTINY_SYSTEM_ID, contextPath});
    }
}
