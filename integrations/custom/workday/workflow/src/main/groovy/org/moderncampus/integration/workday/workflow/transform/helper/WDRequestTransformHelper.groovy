package org.moderncampus.integration.workday.workflow.transform.helper


import groovy.xml.StreamingMarkupBuilder

class WDRequestTransformHelper {

    public static String buildSOAPRequest(String request, String version, String requestRoot, boolean isRead,
                                          Map<String, String> requestRootParams = null) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "soap:Envelope"("xmlns:soap": "http://schemas.xmlsoap.org/soap/envelope/") {
                if (isRead) {
                    "soap:Header"() {
                        "Workday_Common_Header"(xmlns: "urn:com.workday/bsvc") {
                            "Include_Reference_Descriptors_In_Response"(true)
                        }
                    }
                }
                "soap:Body"() {
                    if (requestRootParams) {
                        "wd:$requestRoot"("xmlns:wd": "urn:com.workday/bsvc", "wd:version": version, *: requestRootParams) {
                            mkp.yieldUnescaped request
                        }
                    } else {
                        "wd:$requestRoot"("xmlns:wd": "urn:com.workday/bsvc", "wd:version": version) {
                            mkp.yieldUnescaped request
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    public static String buildReadSOAPRequest(String request, String version, String requestRoot) {
        return buildSOAPRequest(request, version, requestRoot, true)
    }

    public static String buildWriteSOAPRequest(String request, String version, String requestRoot, Map<String, String> requestRootParams = null) {
        return buildSOAPRequest(request, version, requestRoot, false, requestRootParams)
    }


}
