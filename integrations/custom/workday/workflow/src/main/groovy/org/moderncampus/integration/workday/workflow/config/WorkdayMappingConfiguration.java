package org.moderncampus.integration.workday.workflow.config;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;

@Component
@Getter
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class WorkdayMappingConfiguration implements IWorkdayMappingConfiguration {

    @Value("#{${workday.integration.mapping.address.home:null} ?: null}")
    Map<String, String> homeAddressMapping;

    @Value("#{${workday.integration.mapping.address.work:null} ?: null}")
    Map<String, String> workAddressMapping;

    @Value("#{${workday.integration.mapping.phone.home:null} ?: null}")
    Map<String, String> homePhoneMapping;

    @Value("#{${workday.integration.mapping.phone.work:null} ?: null}")
    Map<String, String> workPhoneMapping;

    @Value("${workday.integration.mapping.duplicateCheckIntSystemId}")
    String duplicateCheckIntSystemId;

    @Value("${workday.integration.mapping.studentIdType}")
    String studentIdType;

    @Value("${workday.integration.mapping.nationalIdType}")
    String nationalIdType;

    @Value("${workday.integration.mapping.skipBusinessProcess}")
    boolean skipBusinessProcess;

}
