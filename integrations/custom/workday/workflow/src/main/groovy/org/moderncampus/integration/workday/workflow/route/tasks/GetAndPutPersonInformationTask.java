package org.moderncampus.integration.workday.workflow.route.tasks;

import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.workflow.config.IWorkdayMappingConfiguration;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class GetAndPutPersonInformationTask<T, U, X, Y, Z> extends
        GetAndPutWorkdayInformationTask<T, U, X, Y, Z> {

    public static final String UNIVERSAL_IDENTIFIER_ID = "Universal_Identifier_ID";

    @Getter(value = AccessLevel.PROTECTED)
    IWorkdayMappingConfiguration mappingConfiguration;

    public GetAndPutPersonInformationTask(IWorkdayConnectionConfiguration connectionConfiguration,
            IWorkdayMappingConfiguration mappingConfiguration,
            Class<U> getResponseClass, Class<Y> putResponseClass) {
        super(connectionConfiguration, getResponseClass, putResponseClass);
        this.mappingConfiguration = mappingConfiguration;
    }
}
