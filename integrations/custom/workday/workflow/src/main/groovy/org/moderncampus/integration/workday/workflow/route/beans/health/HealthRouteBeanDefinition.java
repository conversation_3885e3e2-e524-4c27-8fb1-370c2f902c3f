package org.moderncampus.integration.workday.workflow.route.beans.health;

import static org.moderncampus.integration.route.support.RouteSupport.*;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.CONNECTION_CONFIG_PARAM;
import static org.moderncampus.integration.workday.component.constants.Constants.WORKDAY_COMPONENT_SCHEME;
import static org.moderncampus.integration.workday.workflow.route.identifier.common.WorkdayRouteIds.V1_WORKDAY_HEALTH;

import java.util.Collection;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.health.HealthCheck;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.route.builder.BaseHealthCheckRouteBuilder;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.component.WorkdayEndpoint;
import org.moderncampus.integration.workday.workflow.route.beans.CommonRouteBeanDefinitions;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Import(CommonRouteBeanDefinitions.class)
public class HealthRouteBeanDefinition {

    CamelContext camelContext;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1WorkdayHealthRoute() {
        return new BaseHealthCheckRouteBuilder(V1_WORKDAY_HEALTH.getId()) {
            @Override
            protected void buildHealthRoute(RouteDefinition routeDefinition) {
                routeDefinition.process((exchange) -> {
                    String workdayUri = buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                            IWorkdayEndpointConfiguration.Entity.health.name(), routeQueryParamStr(
                                    Map.of(CONNECTION_CONFIG_PARAM,
                                            parameterBeanRef(WorkdayConnectionConfiguration.class))));
                    WorkdayEndpoint endpoint = camelContext.getEndpoint(workdayUri, WorkdayEndpoint.class);
                    Collection<HealthCheck.Result> healthResult = endpoint.healthCheck(exchange);
                    exchange.getMessage().setBody(healthResult);
                });
            }
        };
    }
}
