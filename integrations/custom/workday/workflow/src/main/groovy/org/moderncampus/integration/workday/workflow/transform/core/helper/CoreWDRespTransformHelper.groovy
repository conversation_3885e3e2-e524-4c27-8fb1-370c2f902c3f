package org.moderncampus.integration.workday.workflow.transform.core.helper

import groovy.transform.CompileStatic
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.workday.dto.course.WorkdayCourse

import java.time.LocalDate

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWDNamespacePrefix
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWorkdayDate

@CompileStatic
class CoreWDRespTransformHelper {

    public static NodeChild extractEffectiveSnapshotNodeFromCourseDataNode(NodeChildren responseDataNode, LocalDate effectiveDate,
                                                                           boolean throwExceptionOnNotFound = false) {
        def responseDataSnapshotDataNode = responseDataNode['Student_Course_Data_Snapshot_Data'] as NodeChildren
        if (responseDataSnapshotDataNode.size() > 0) {
            GPathResult snapShotNode = extractLatestEffectiveDatedSnapshotNode(effectiveDate, responseDataSnapshotDataNode)
            if (snapShotNode?.size() > 0) {
                return snapShotNode as NodeChild
            } else if (snapShotNode == null || snapShotNode.size() <= 0) {
                try {
                    //attempt to get the nearest snapshot as all snapshot effective dates are in the future. This is consistent with Workday UI
                    // behavior
                    return (responseDataSnapshotDataNode.first() as NodeChild)
                } catch (Exception ignored) {
                }
            }
        }
        if (throwExceptionOnNotFound) {
            throw new RuntimeException("Unable to continue update: No course snapshots exist on course")
        }

        return null
    }

    public static NodeChildren extractCourseDataNodeFromGetResp(String response, WorkdayCourse courseInput) {
        def rootNode = new XmlSlurper().parseText(response) as NodeChild
        String namespacePrefix = extractWDNamespacePrefix(rootNode) ?: "wd"
        def responseDataNode = (rootNode['Body']['Get_Student_Courses_Response']['Response_Data']['Student_Course']['Student_Course_Data'] as
                NodeChildren)
        if (responseDataNode.size() > 0) {
            return responseDataNode
        }
        throw new RuntimeException("Course does not exist for Id: " + courseInput.id)
    }

    private static NodeChild extractLatestEffectiveDatedSnapshotNode(LocalDate effectiveDate, NodeChildren snapshotNode) {
        LocalDate parsedEffectiveDate = effectiveDate
        if (parsedEffectiveDate == null) {
            parsedEffectiveDate = LocalDate.now()
        }
        NodeChild latestSnapshot = null
        Iterator reverseIterator = snapshotNode.iterator().reverse()
        while (reverseIterator.hasNext()) {
            NodeChild child = reverseIterator.next() as NodeChild
            LocalDate snapShotEffDate = extractWorkdayDate(extractNodeText(child['Effective_Date'] as NodeChildren))
            if (snapShotEffDate.isBefore(parsedEffectiveDate) || snapShotEffDate.isEqual(parsedEffectiveDate)) {
                latestSnapshot = child
                break
            }
        }
        return latestSnapshot
    }
}
