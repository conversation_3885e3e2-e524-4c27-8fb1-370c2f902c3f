package org.moderncampus.integration.workday.workflow.transform.cewd

import groovy.transform.CompileStatic
import org.apache.commons.lang3.tuple.Pair
import org.moderncampus.integration.dto.cewd.CewdPerson
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.workday.workflow.transform.cewd.Constants.STUDENT_ID_TYPE
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.wdNode

@Component
@CompileStatic
class CewdPersonToCustomIdNodeTransform extends BaseTransformer<CewdPerson, Node> {

    @Override
    protected Node doTransform(TransformContext ctx, CewdPerson person) {
        String personIdType = ctx.getContextProp(STUDENT_ID_TYPE, String.class)
        Node customIdNode = ctx.getContextProp(LOADED_ENTITY, Node.class)
        if (customIdNode == null) {
            throw new RuntimeException("Unable to continue mapping without custom ID node")
        }
        def customIdentificationDataType = customIdNode
        String personNumber = person.getNumber()
        if (personIdType == null) {
            boolean compositeType = person.getNumber() != null && person.getNumber().contains(":")
                    && person.getNumber().split(":").length == 2
            if (!compositeType) {
                throw new RuntimeException("Missing id type for mapping to the custom id call")
            }
            String[] numberComponents = person.getNumber().split(":")
            personIdType = numberComponents[0]
            personNumber = numberComponents[1]
        }
        Map<String, List<Node>> customIDTypeMap = customIdTypeToEntryMap(customIdentificationDataType)
        customIdentificationDataType.attributes()[wdNode('Replace_All')] = false
        if (customIDTypeMap.containsKey(personIdType)) {
            List<Node> idTypes = customIDTypeMap.get(personIdType)
            if (idTypes.size() > 1) {
                throw new RuntimeException("Multiple ID associations found when updating the ID attribute on Workday")
            }
            Node idType = idTypes.get(0);
            idType.attributes()[wdNode("Delete")] = false
            Node customIdRefNode = idType.children().find { child -> (child as Node).name() == (wdNode("Custom_ID_Reference")) } as Node
            idType.remove(customIdRefNode)
            ((idType[wdNode('Custom_ID_Data')][wdNode("ID")] as NodeList)[0] as Node).value = personNumber
        } else {
            Node newCustomIdType = new Node(customIdentificationDataType, wdNode("Custom_ID"))
            Node newCustomIdDataType = new Node(newCustomIdType, wdNode("Custom_ID_Data"))
            Node newCustomIdDataIdNode = new Node(newCustomIdDataType, wdNode("ID"), personNumber)
            Node newCustomIdDataIdTypeRefNode = new Node(newCustomIdDataType, wdNode("ID_Type_Reference"))
            Node newCustomIdDataIdTypeRefIdNode = new Node(newCustomIdDataIdTypeRefNode, wdNode("ID"), personIdType)
            newCustomIdDataIdTypeRefIdNode.attributes()[wdNode('type')] = 'Custom_ID_Type_ID'
        }

        List<Node> existingNodes = customIdentificationDataType.children().findResults {
            String key = retrieveCustomTypeID((it as Node)[wdNode('Custom_ID_Data')] as NodeList)
            personIdType != key ? it : null
        } as List<Node>

        existingNodes.each { customIdentificationDataType.remove(it) }

        return customIdentificationDataType
    }

    def Map<String, List<Node>> customIdTypeToEntryMap(Node customIdentificationDataType) {
        return customIdentificationDataType[wdNode('Custom_ID')].collect {
            String key = retrieveCustomTypeID((it as Node)[wdNode('Custom_ID_Data')] as NodeList)
            if (key != null) {
                return Pair.of(key, [it])
            }
        }.groupBy { it.key }
                .collectEntries { k, v ->
                    [k, v.collectMany { it.value }]
                }
    }

    def String retrieveCustomTypeID(NodeList customIdData) {
        NodeList list = (customIdData[wdNode('ID_Type_Reference')])
        def value = !list.isEmpty() ? ((list[0] as Node).children().find {
            (it as Node).name() == (wdNode("ID"))
                    && (it as Node).attributes()[wdNode("type")] == "Custom_ID_Type_ID"
        } as Node)?.value() : null
        if (value instanceof NodeList) {
            return (value as NodeList).isEmpty() ? null : value[0]
        }
        return null
    }
}
