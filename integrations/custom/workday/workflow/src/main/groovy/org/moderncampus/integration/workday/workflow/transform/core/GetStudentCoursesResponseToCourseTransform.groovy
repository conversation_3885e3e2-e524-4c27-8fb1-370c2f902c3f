package org.moderncampus.integration.workday.workflow.transform.core

import groovy.transform.CompileStatic
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.constants.Constants
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.workday.dto.course.WorkdayCourse
import org.springframework.stereotype.Component

import java.time.LocalDate

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.workday.workflow.transform.core.helper.CoreWDRespTransformHelper.extractEffectiveSnapshotNodeFromCourseDataNode
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWDNamespacePrefix
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.extractWorkdayDate

@CompileStatic
@Component
class GetStudentCoursesResponseToCourseTransform extends BaseTransformer<NodeChild, List<WorkdayCourse>> {

    @Override
    protected List<WorkdayCourse> doTransform(TransformContext ctx, NodeChild rootNode) {
        def returnList = [] as List<WorkdayCourse>
        String namespacePrefix = extractWDNamespacePrefix(rootNode) ?: "wd"
        def getStudentCrsesRespNode = rootNode['Body']['Get_Student_Courses_Response'] as NodeChildren
        def responseDataNode = (getStudentCrsesRespNode['Response_Data'] as NodeChildren)
        def effectiveDate = extractWorkdayDate(extractNodeText getStudentCrsesRespNode['Response_Filter']['As_Of_Effective_Date'] as GPathResult)
        (responseDataNode['Student_Course'] as NodeChildren).each { studentCrseNode ->
            {
                WorkdayCourse workdayCourse = new WorkdayCourse()
                mapStudentCrseRef(studentCrseNode as NodeChild, namespacePrefix, workdayCourse)
                mapStudentCrseData(studentCrseNode as NodeChild, namespacePrefix, workdayCourse, effectiveDate)
                returnList << workdayCourse
            }
        }
        mapPagination(ctx, (getStudentCrsesRespNode['Response_Results'] as NodeChildren))
        return returnList
    }

    private void mapStudentCrseData(NodeChild studentCrseNode, String namespacePrefix, WorkdayCourse workdayCourse, LocalDate effectiveDate) {
        def studentCrseDataNode = studentCrseNode['Student_Course_Data'] as NodeChildren
        if (studentCrseDataNode.size() > 0) {//if the node exists
// Get the most effective snapshot
            def studentCrseDataDataNode = studentCrseDataNode['Student_Course_Data_Data'] as NodeChildren
            workdayCourse.lastAvailable = extractWorkdayDate(extractNodeText(studentCrseDataDataNode['Last_Available'] as NodeChildren))
            def studentCrseSnapShotNode = extractEffectiveSnapshotNodeFromCourseDataNode(studentCrseDataNode, effectiveDate)
            if (studentCrseSnapShotNode) {
                workdayCourse.academicUnitData = [new WorkdayCourse.AcademicUnitData().with {
                    refId = extractNodeText((studentCrseSnapShotNode['Academic_Unit_Data']['Academic_Unit_Reference']['*'] as
                            NodeChildren)?.find {
                        NodeChild acadUnitRef ->
                            acadUnitRef["@" + namespacePrefix + ":type"] == 'Academic_Unit_ID'
                    })
                    it
                }] as List<WorkdayCourse.AcademicUnitData>
                workdayCourse.privateNotes = extractNodeText(studentCrseSnapShotNode['Private_Notes'] as NodeChildren)
                workdayCourse.publicNotes = extractNodeText(studentCrseSnapShotNode['Public_Notes'] as NodeChildren)
                workdayCourse.contactHours = (extractNodeText(studentCrseSnapShotNode['Contact_Hours'] as NodeChildren)) as BigDecimal
                workdayCourse.courseListingData = ((studentCrseSnapShotNode['Student_Course_Listing_Data'] as NodeChildren)?.findResults {
                    courseListingDataNode ->
                        new WorkdayCourse.CourseListingData().with {
                            refId = extractNodeText(
                                    (courseListingDataNode['Course_Subject_Reference']['*']
                                            as NodeChildren)
                                            ?.find { NodeChild studentCrseRef ->
                                                studentCrseRef["@" + namespacePrefix + ":type"] == 'Course_Subject_ID'
                                            })
                            courseNumber = extractNodeText((studentCrseSnapShotNode['Student_Course_Listing_Data']['Course_Number'] as
                                    NodeChildren))
                            it
                        }

                } as List<WorkdayCourse.CourseListingData>)?.findAll { it.refId != null && it.courseNumber != null }
                workdayCourse.courseTitle = extractNodeText((studentCrseSnapShotNode['Course_Title'] as NodeChildren))
                workdayCourse.maxUnits = extractNodeText(studentCrseSnapShotNode['Maximum_Units'] as NodeChildren) as BigDecimal
                workdayCourse.minUnits = extractNodeText(studentCrseSnapShotNode['Minimum_Units'] as NodeChildren) as BigDecimal
                workdayCourse.description = extractNodeText(studentCrseSnapShotNode['Description'] as NodeChildren)
                workdayCourse.educationalTaxCodes = (studentCrseSnapShotNode['Educational_Taxonomy_Codes_Reference']['*'] as NodeChildren).
                        findResults({ referenceNode ->
                            {
                                referenceNode["@" + namespacePrefix + ":type"] == 'Educational_Taxonomy_Code_ID' ? (referenceNode as NodeChild).text()
                                        : null
                            }
                        }) as List<String>
                workdayCourse.combinationInstructionalFormats = (
                        (studentCrseSnapShotNode['Course_Combination_Instructional_Format_Contact_Hours_Data']
                                as NodeChildren)?.findResults {
                            childHoursNode ->
                                new WorkdayCourse.CombinationInstructionalFormat().with {
                                    refId = extractNodeText(childHoursNode['Combination_Instructional_Format_Reference']['*'].find {
                                        referenceNode ->
                                            {
                                                referenceNode["@" + namespacePrefix + ":type"] == 'Instructional_Format_ID'
                                            }
                                    } as GPathResult)
                                    contactHours = extractNodeText(childHoursNode['Combination_Instructional_Format_Contact_Hours'] as
                                            NodeChildren) as
                                            BigDecimal
                                    it
                                }
                        } as List<WorkdayCourse.CombinationInstructionalFormat>)?.findAll {
                    it.refId != null && it.contactHours != null
                }
                workdayCourse.academicLevelRefId = extractNodeText((studentCrseSnapShotNode['Academic_Level_Reference']['*'] as NodeChildren)?.
                        find {
                            NodeChild acadLevelRef ->
                                acadLevelRef["@" + namespacePrefix + ":type"] == 'Academic_Level_ID'
                        })
                workdayCourse.effectiveDate = extractWorkdayDate(extractNodeText(studentCrseSnapShotNode['Effective_Date'] as NodeChildren))
                if (extractNodeText(studentCrseSnapShotNode['Special_Topics_Course'] as NodeChildren) == '1') {
                    workdayCourse.specialTopicTitles = (studentCrseSnapShotNode['Special_Topic_Data']['Special_Topic_Name'] as NodeChildren).
                            findResults({ referenceNode ->
                                {
                                    extractNodeText(referenceNode as NodeChild)
                                }
                            }) as List<String>
                }
            }
        }
    }

    private void mapStudentCrseRef(NodeChild studentCrseNode, String namespacePrefix, WorkdayCourse workdayCourse) {
        def studentCrseRefNode = studentCrseNode['Student_Course_Reference']['*'] as NodeChildren
        workdayCourse.studentCourseRefWID = studentCrseRefNode?.find { NodeChild studentCrseRef ->
            studentCrseRef["@" + namespacePrefix + ":type"] == 'WID'
        }?.text()
        workdayCourse.id = studentCrseRefNode?.find { NodeChild studentCrseRef ->
            studentCrseRef["@" + namespacePrefix + ":type"] == 'Student_Course_ID'
        }?.text()
    }

    private void mapPagination(TransformContext context, NodeChildren responseResultsNode) {
        context.setContextProp(Constants.PAGE_SIZE, extractNodeText(responseResultsNode['Page_Results'] as NodeChildren))
        context.setContextProp(Constants.PAGE_NUM, extractNodeText(responseResultsNode['Page'] as NodeChildren))
        context.setContextProp(Constants.TOTAL_PAGES, extractNodeText(responseResultsNode['Total_Pages'] as NodeChildren))
        context.setContextProp(Constants.TOTAL_SIZE, extractNodeText(responseResultsNode['Total_Results'] as NodeChildren))
    }
}
