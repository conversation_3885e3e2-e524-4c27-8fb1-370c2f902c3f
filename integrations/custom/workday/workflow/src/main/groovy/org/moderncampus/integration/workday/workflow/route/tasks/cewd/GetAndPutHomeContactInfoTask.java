package org.moderncampus.integration.workday.workflow.route.tasks.cewd;

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY;
import static org.moderncampus.integration.workday.dto.WorkdayHRRecordType.CHANGE_HOME_CONTACT_INFORMATION;
import static org.moderncampus.integration.workday.dto.WorkdayHRRecordType.GET_CHANGE_HOME_CONTACT_INFORMATION;
import static org.moderncampus.integration.workday.workflow.transform.cewd.Constants.ADDRESS_MAPPING;
import static org.moderncampus.integration.workday.workflow.transform.cewd.Constants.PHONE_MAPPING;
import static org.moderncampus.integration.workday.workflow.transform.cewd.helper.CewdWDRespTransformHelper.extractPersonContactInfoDataNodeFromGetChangeContactInfoResp;

import java.util.HashMap;
import java.util.Optional;

import org.moderncampus.integration.dto.cewd.CewdPerson;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.moderncampus.integration.workday.workflow.config.IWorkdayMappingConfiguration;
import org.moderncampus.integration.workday.workflow.route.tasks.GetAndPutPersonInformationTask;
import org.moderncampus.integration.workday.workflow.transform.helper.WDHRServiceReqTransformHelper;
import org.springframework.stereotype.Component;

import groovy.util.Node;

@Component
public class GetAndPutHomeContactInfoTask extends
        GetAndPutPersonInformationTask<String, String, String, String, CewdPerson> {

    ITransformer<CewdPerson, Node> transformer;

    public GetAndPutHomeContactInfoTask(
            IWorkdayConnectionConfiguration connectionConfiguration,
            ITransformer<CewdPerson, Node> cewdPersonToPersonContactInfoDataNodeTransform,
            IWorkdayMappingConfiguration workdayMappingConfiguration) {
        super(connectionConfiguration, workdayMappingConfiguration,
                String.class, String.class);
        this.transformer = cewdPersonToPersonContactInfoDataNodeTransform;
    }

    @Override
    protected IWorkdayRecordType getRequestRecordType() {
        return GET_CHANGE_HOME_CONTACT_INFORMATION;
    }

    @Override
    protected IWorkdayRecordType putRequestRecordType() {
        return CHANGE_HOME_CONTACT_INFORMATION;
    }

    @Override
    protected String mapToGetRequest(CewdPerson person) {
        TransformContext context = getDefaultTransformContext();
        return WDHRServiceReqTransformHelper.mapToGetChangeHomeContactInfoRequest(context, person.getId());
    }

    @Override
    protected String mapToPutRequest(TaskInput input, String getResponse) throws Exception {
        Node personContactInfoDataNode = extractPersonContactInfoDataNodeFromGetChangeContactInfoResp(getResponse,
                true);
        input.getContextProperties().put(LOADED_ENTITY, personContactInfoDataNode);
        input.getContextProperties().put(ADDRESS_MAPPING,
                Optional.ofNullable(getMappingConfiguration().getHomeAddressMapping()).orElse(new HashMap<>()));
        input.getContextProperties().put(PHONE_MAPPING,
                Optional.ofNullable(getMappingConfiguration().getHomePhoneMapping()).orElse(new HashMap<>()));
        input.getContextProperties().put("IS_WD_HOME_MAPPING", true);
        TransformContext context = new TransformContext(input.getContextProperties());
        Node mappedPersonContactInfoDataNode = transformer.transform(context, (CewdPerson) input.getInputData());
        String changeHomeContactInfoDataNodeStr = serializeXMLNode(mappedPersonContactInfoDataNode.parent());
        return WDHRServiceReqTransformHelper.mapToPutChangeHomeContactInfoRequest(getDefaultTransformContext(),
                getMappingConfiguration().isSkipBusinessProcess(), changeHomeContactInfoDataNodeStr);
    }
}
