package org.moderncampus.integration.workday.workflow.route.tasks.cewd;

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY;
import static org.moderncampus.integration.workday.workflow.transform.cewd.Constants.STUDENT_ID_TYPE;
import static org.moderncampus.integration.workday.workflow.transform.cewd.helper.CewdWDRespTransformHelper.extractCustomIdNodeFromGetChangeOtherIdsResp;

import org.moderncampus.integration.dto.cewd.CewdPerson;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.moderncampus.integration.workday.dto.WorkdayHRRecordType;
import org.moderncampus.integration.workday.workflow.config.IWorkdayMappingConfiguration;
import org.moderncampus.integration.workday.workflow.route.tasks.GetAndPutPersonInformationTask;
import org.moderncampus.integration.workday.workflow.transform.helper.WDHRServiceReqTransformHelper;
import org.springframework.stereotype.Component;

import groovy.util.Node;

@Component
public class GetAndPutOtherIdTask extends
        GetAndPutPersonInformationTask<String, String, String, String, CewdPerson> {

    IWorkdayMappingConfiguration mappingConfiguration;

    ITransformer<CewdPerson, Node> transformer;

    public GetAndPutOtherIdTask(
            IWorkdayConnectionConfiguration connectionConfiguration,
            IWorkdayMappingConfiguration mappingConfiguration,
            ITransformer<CewdPerson, Node> cewdPersonToCustomIdNodeTransform) {
        super(connectionConfiguration, mappingConfiguration, String.class,
                String.class);
        this.transformer = cewdPersonToCustomIdNodeTransform;
    }

    @Override
    protected IWorkdayRecordType getRequestRecordType() {
        return WorkdayHRRecordType.GET_CHANGE_OTHER_IDS;
    }

    @Override
    protected IWorkdayRecordType putRequestRecordType() {
        return WorkdayHRRecordType.CHANGE_OTHER_IDS;
    }

    @Override
    protected String mapToGetRequest(CewdPerson person) {
        TransformContext context = getDefaultTransformContext();
        return WDHRServiceReqTransformHelper.mapToGetChangeOtherIdsRequest(context, person.getId());
    }

    @Override
    protected String mapToPutRequest(TaskInput input, String getResponse)
            throws Exception {
        Node customIdNode = extractCustomIdNodeFromGetChangeOtherIdsResp(getResponse);
        input.getContextProperties().put(STUDENT_ID_TYPE, getMappingConfiguration().getStudentIdType());
        input.getContextProperties().put(LOADED_ENTITY, customIdNode);
        TransformContext context = new TransformContext(input.getContextProperties());
        Node mappedCustomIdNode = transformer.transform(context, (CewdPerson) input.getInputData());
        String changeOtherIdsDataNodeStr = serializeXMLNode(mappedCustomIdNode.parent());
        return WDHRServiceReqTransformHelper.mapToPutChangeOtherIdsRequest(
                getDefaultTransformContext(), getMappingConfiguration().isSkipBusinessProcess(),
                changeOtherIdsDataNodeStr);
    }
}
