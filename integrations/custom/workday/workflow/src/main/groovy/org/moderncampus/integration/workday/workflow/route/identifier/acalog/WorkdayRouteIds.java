package org.moderncampus.integration.workday.workflow.route.identifier.acalog;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;
import static org.moderncampus.integration.workday.workflow.route.identifier.Constants.GET_COURSES;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum WorkdayRouteIds implements IRouteId {

    V1_ACALOG_WORKDAY_GET_COURSES(GET_COURSES);

    String contextPath;

    String id;

    WorkdayRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, ACALOG_SYSTEM_ID, "*", WORKDAY_SYSTEM_ID, contextPath});
    }
}
