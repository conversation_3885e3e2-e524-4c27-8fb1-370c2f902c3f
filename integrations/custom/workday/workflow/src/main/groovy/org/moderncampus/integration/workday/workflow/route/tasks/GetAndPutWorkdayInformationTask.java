package org.moderncampus.integration.workday.workflow.route.tasks;

import static org.moderncampus.integration.route.support.RouteSupport.*;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.CONNECTION_CONFIG_PARAM;
import static org.moderncampus.integration.workday.component.constants.Constants.WORKDAY_COMPONENT_SCHEME;
import static org.moderncampus.integration.workday.workflow.transform.helper.WDResponseTransformHelper.mapSOAPErrors;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.route.tasks.BaseTask;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.route.tasks.result.TaskOutput;
import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.springframework.beans.factory.annotation.Autowired;

import groovy.util.IndentPrinter;
import groovy.util.Node;
import groovy.xml.XmlNodePrinter;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class GetAndPutWorkdayInformationTask<T, U, X, Y, Z> extends BaseTask {

    @Getter(value = AccessLevel.PROTECTED)
    IWorkdayConnectionConfiguration connectionConfiguration;
    Class<U> getResponseClass;
    Class<Y> putResponseClass;
    @Setter(onMethod_ = {@Autowired})
    @NonFinal
    ProducerTemplate producerTemplate;

    @Override
    public TaskOutput execute(TaskInput input) throws Exception {
        Z inputData = (Z) input.getInputData();
        T getRequest = prepareGetRequest(inputData);
        U getResponse = (U) executeSOAPRequest(input, getRequest, getRequestRecordType(), getResponseClass);
        if (shouldExecutePutRequest(getResponse)) {
            X changeRequest = mapToPutRequest(input, getResponse);
            Y changeResponse = (Y) executeSOAPRequest(input, changeRequest, putRequestRecordType(), putResponseClass);
            return taskOutput(changeResponse);
        }
        return taskOutput(getResponse);
    }

    protected boolean shouldExecutePutRequest(U getResponse) {
        return true;
    }

    private Object executeSOAPRequest(TaskInput input, Object requestType, IWorkdayRecordType recordType,
            Class<?> responseClass)
            throws Exception {
        String routeUri = buildRouteURI(WORKDAY_COMPONENT_SCHEME,
                IWorkdayEndpointConfiguration.Entity.soapWS.name(), routeQueryParamStr(
                        Map.of(CONNECTION_CONFIG_PARAM, parameterBeanRef(WorkdayConnectionConfiguration.class),
                                IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM,
                                parameterValueRef(recordType.getClass(), recordType))));
        Exchange exchange = getExchange(input);
        exchange.getMessage().setBody(requestType);
        exchange = producerTemplate.send(routeUri, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
        String returnObj = exchange.getMessage().getBody(String.class);
        exchange.getMessage().setBody(returnObj);
        mapSOAPErrors(returnObj);
        return returnObj;
    }

    protected abstract IWorkdayRecordType getRequestRecordType();

    protected abstract IWorkdayRecordType putRequestRecordType();

    protected T prepareGetRequest(Z inputObject) throws Exception {
        return mapToGetRequest(inputObject);
    }

    protected abstract T mapToGetRequest(Z inputObject) throws Exception;

    protected abstract X mapToPutRequest(TaskInput input, U getResponse) throws Exception;

    protected TransformContext getDefaultTransformContext() {
        return new TransformContext(Map.of("version", getConnectionConfiguration().getVersion()));
    }

    protected String serializeXMLNode(Node node) {
        StringWriter writer = new StringWriter();
        new XmlNodePrinter(new IndentPrinter(new PrintWriter(writer), "", false)).print(node);
        return writer.toString();
    }
}
