package org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint;

import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.Map;

import org.apache.camel.Consumer;
import org.apache.camel.Processor;
import org.apache.camel.Producer;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.support.DefaultEndpoint;
import org.apache.camel.util.ObjectHelper;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftBaseConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftComponent;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftEndpointConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.ID1TMUPeopleSoftPipelineConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.producer.D1TMUPeopleSoftProducer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.Getter;

@Getter
@Component
@Lazy(value = false)
public class D1TMUPeopleSoftEndpoint extends DefaultEndpoint implements ApplicationContextAware {

    private static ApplicationContext context;

    D1TMUPeopleSoftEndpointConfiguration endpointConfiguration = new D1TMUPeopleSoftEndpointConfiguration();

    ProducerTemplate producerTemplate;

    String[] activeAppProfiles;

    public D1TMUPeopleSoftEndpoint() {
    }

    public D1TMUPeopleSoftEndpoint(String endpointUri, D1TMUPeopleSoftComponent component) {
        super(endpointUri, component);
    }

    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String serviceName;
        try {
            URI u = new URI(remaining);
            serviceName = u.getPath();
        } catch (Exception e) {
            throw new MalformedURLException(
                    String.format("An invalid TMU PeopleSoft endpoint remaining uri: '%s' was provided. Error: '%s'",
                            remaining,
                            e.getMessage()));
        }
        ObjectHelper.notNull(serviceName, "Service Name");
        setServiceName(D1TMUPSServiceName.valueOf(serviceName));
        producerTemplate = context.getBean(beanRef(ProducerTemplate.class), ProducerTemplate.class);
        activeAppProfiles = context.getBean(beanRef(Environment.class), Environment.class).getActiveProfiles();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public void validateConfigurationParameters() {
        ObjectHelper.notNull(endpointConfiguration.getPipelineConfig(), "ServiceConfig");
        ObjectHelper.notNull(endpointConfiguration.getServiceName(), "ServiceName");
    }

    @Override
    public Producer createProducer() throws Exception {
        if (getEndpointConfiguration().getBaseConfig() == null
                || getEndpointConfiguration().getPipelineConfig() == null) {
            throw new ApplicationException("Missing configuration definition");
        }
        return new D1TMUPeopleSoftProducer(this);
    }

    @Override
    public Consumer createConsumer(Processor processor) throws Exception {
        throw new UnsupportedOperationException("Consumer is not implemented.");
    }

    public void setPipelineConfig(ID1TMUPeopleSoftPipelineConfiguration serviceConfiguration) {
        getEndpointConfiguration().setPipelineConfig(serviceConfiguration);
    }

    public void setBaseConfig(D1TMUPeopleSoftBaseConfiguration serviceConfiguration) {
        getEndpointConfiguration().setBaseConfig(serviceConfiguration);
    }

    public void setServiceName(D1TMUPSServiceName serviceName) {
        getEndpointConfiguration().setServiceName(serviceName);
    }

    public void setHttpMethod(String httpMethod) {
        getEndpointConfiguration().setHttpMethod(httpMethod);
    }
}
