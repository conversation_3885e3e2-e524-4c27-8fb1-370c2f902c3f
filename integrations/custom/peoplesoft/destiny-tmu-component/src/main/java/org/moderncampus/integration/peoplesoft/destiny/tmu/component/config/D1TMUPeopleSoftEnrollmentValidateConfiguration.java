package org.moderncampus.integration.peoplesoft.destiny.tmu.component.config;

import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUAuthMode;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftPipelineConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@ConfigurationProperties(prefix = "peoplesoft.integration.d1-tmu-enrollment-validate")
public class D1TMUPeopleSoftEnrollmentValidateConfiguration extends D1TMUPeopleSoftPipelineConfiguration {

    @Override
    public D1TMUAuthMode getAuthMode() {
        return D1TMUAuthMode.header;
    }
}
