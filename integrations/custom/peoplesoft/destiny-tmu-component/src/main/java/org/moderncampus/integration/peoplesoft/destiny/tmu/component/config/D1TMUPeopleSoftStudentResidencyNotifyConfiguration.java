package org.moderncampus.integration.peoplesoft.destiny.tmu.component.config;

import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftPipelineEncryptSupportConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@ConfigurationProperties(prefix = "peoplesoft.integration.d1-tmu-student-residency-notify")
public class D1TMUPeopleSoftStudentResidencyNotifyConfiguration extends
        D1TMUPeopleSoftPipelineEncryptSupportConfiguration {

}
