package org.moderncampus.integration.peoplesoft.destiny.tmu.component;

import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.constants.Constants.D1_PS_TOKEN_KEY;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.constants.Constants.PS_TOKEN_KEY;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName.*;

import java.util.HashMap;
import java.util.Map;

import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@ConfigurationProperties(prefix = "peoplesoft.integration.base")
public class D1TMUPeopleSoftBaseConfiguration {

    @Getter
    static final Map<D1TMUPSServiceName, String> uriMap = new HashMap<>() {{
        put(TMU_D1_STDNT_STATUS, "PSIGW/RESTListeningConnector/RU_D1_STDNT_STATUS_SO.v1/studentid");
        put(TMU_D1_REGANDTRAN, "PSIGW/RESTListeningConnector/RU_D1_REGANDTRAN_SO.v1/regandtrans");
        put(TMU_D1_CREATEWEBID, "PSIGW/RESTListeningConnector/RU_D1_CREATEWEBID_SO.v1/createid");
        put(TMU_D1_STDNT_PROFILE, "PSIGW/RESTListeningConnector/RU_D1_STDNT_PROFILE_SO.v1/acad_career/strm/studentid");
        put(TMU_D1_TOKEN_SO, "PSIGW/RESTListeningConnector/RU_D1_TOKEN_SO.v1/studentid");
        put(TMU_D1_CLASS_CAPADJUST,
                "PSIGW/RESTListeningConnector/RU_D1_CLASS_CAPADJUST_SO.v1/acad_career/strm/class_nbr/enrl_action");
        put(TMU_D1_SETTLEMENT, "PSIGW/RESTListeningConnector/RU_D1_SETTLEMENT_SO.v1/ru_reference_number");
        put(TMU_D1_CLASS_FEE, "PSIGW/RESTListeningConnector/RU_D1_CLASS_FEE_SO.v1/classfee");
        put(TMU_D1_STDNT_RESIDENCY, "PSIGW/RESTListeningConnector/RU_D1_STDNT_RESIDENCY_SO.v1/acad_career/studentid");
        put(TMU_D1_GET_OPTINOUT, "PSIGW/RESTListeningConnector/RU_D1_GET_OPTINOUT_SO.v1/studentid");
        put(TMU_D1_PUT_OPTINOUT, "PSIGW/RESTListeningConnector/RU_D1_PUT_OPTINOUT_SO.v1/studentid");
        put(SCC_SC_ADDITEM,
                "PSIGW/RESTListeningConnector/SCC_SC_ADDITEM_R.v1/shopping_cart/add");
        put(SCC_SC_REMOVEITEM,
                "PSIGW/RESTListeningConnector/SCC_SC_REMOVEITEM_R.v1/shopping_cart/removeItem");
        put(SSR_ADD_ENROLLMENT,
                "PSIGW/RESTListeningConnector/SSR_ADD_ENROLLMENT_R.v1/enroll/add");
        put(SSR_DROP_ENROLLMENT,
                "PSIGW/RESTListeningConnector/SSR_DROP_ENROLLMENT_R.v1/enroll/drop");
        put(SSR_GET_CLASS_SECTION,
                "PSIGW/RESTListeningConnector/SSR_GET_CLASS_SECTION_R.v1/get/classSection");
        put(SSR_ENR_VALIDATE,
                "PSIGW/RESTListeningConnector/SSR_ENR_VALIDATE_R.v1/enroll/validate");
    }};

    @Getter
    static final Map<D1TMUPSServiceName, String> queryParamMap = new HashMap<>() {{
        put(SCC_SC_ADDITEM, "type=test&languageCd=eng");
        put(SCC_SC_REMOVEITEM, "type=complete&languageCd=eng");
        put(SSR_ADD_ENROLLMENT, "type=complete&languageCd=eng");
        put(SSR_DROP_ENROLLMENT, "type=complete&languageCd=eng");
        put(SSR_GET_CLASS_SECTION, "type=test&languageCd=eng");
        put(SSR_ENR_VALIDATE, "type=test&languageCd=eng");
    }};

    String host;

    Auth auth;

    @Getter
    @Setter
    public static class Auth {

        String authString;

        String encryptFieldKey;

        String encryptFieldIV;

        String authHeaderName = PS_TOKEN_KEY;

        String fromAuthHeaderName = D1_PS_TOKEN_KEY;
    }

}
