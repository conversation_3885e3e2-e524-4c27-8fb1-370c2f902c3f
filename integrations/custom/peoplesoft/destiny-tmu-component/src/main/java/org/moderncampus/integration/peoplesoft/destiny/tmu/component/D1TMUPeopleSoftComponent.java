package org.moderncampus.integration.peoplesoft.destiny.tmu.component;

import java.util.Map;

import org.apache.camel.Endpoint;
import org.apache.camel.spi.annotations.Component;
import org.apache.camel.support.DefaultComponent;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.constants.Constants;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.D1TMUPeopleSoftEndpoint;

@Component(Constants.D1_TMU_PEOPLESOFT_COMPONENT_SCHEME)
public class D1TMUPeopleSoftComponent extends DefaultComponent {

    @Override
    protected Endpoint createEndpoint(String uri, String remaining, Map<String, Object> parameters) throws Exception {
        D1TMUPeopleSoftEndpoint endpoint = new D1TMUPeopleSoftEndpoint(uri, this);
        endpoint.parseURI(remaining, parameters);
        setProperties(endpoint, parameters);

        return endpoint;
    }

    @Override
    protected void afterConfiguration(String uri, String remaining, Endpoint endpoint, Map<String, Object> parameters)
            throws Exception {
        super.afterConfiguration(uri, remaining, endpoint, parameters);
        D1TMUPeopleSoftEndpoint d1TMUPeopleSoftEndpoint = (D1TMUPeopleSoftEndpoint) endpoint;
        ((D1TMUPeopleSoftEndpoint) endpoint).validateConfigurationParameters();
    }
}
