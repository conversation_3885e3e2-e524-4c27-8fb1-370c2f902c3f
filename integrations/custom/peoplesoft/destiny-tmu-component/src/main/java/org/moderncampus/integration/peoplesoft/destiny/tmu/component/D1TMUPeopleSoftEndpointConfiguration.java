package org.moderncampus.integration.peoplesoft.destiny.tmu.component;

import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class D1TMUPeopleSoftEndpointConfiguration {

    public static final String BASE_CONFIG_PARAM = "baseConfig";

    public static final String PIPELINE_CONFIG_PARAM = "pipelineConfig";

    D1TMUPeopleSoftBaseConfiguration baseConfig;

    ID1TMUPeopleSoftPipelineConfiguration pipelineConfig;

    D1TMUPSServiceName serviceName;

    String httpMethod;
}
