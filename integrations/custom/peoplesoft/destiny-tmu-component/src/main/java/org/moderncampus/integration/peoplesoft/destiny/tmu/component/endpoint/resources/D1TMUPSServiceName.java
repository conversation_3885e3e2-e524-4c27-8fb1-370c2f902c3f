package org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum D1TMUPSServiceName {

    TMU_D1_TOKEN_SO("MOCK_TMU_D1_TOKEN"),

    SCC_SC_ADDITEM("MOCK_SCC_SC_ADDITEM"),

    SSR_ENR_VALIDATE("MOCK_SSR_ENR_VALIDATE"),

    SCC_SC_REMOVEITEM("MOCK_SCC_REMOVEITEM"),

    SSR_ADD_ENROLLMENT("MOCK_SSR_ADD_ENROLLMENT"),

    SSR_DROP_ENROLLMENT("MOCK_SSR_DROP_ENROLLMENT"),

    SSR_GET_CLASS_SECTION("MOCK_SSR_GET_CLASS_SECTION"),

    TMU_D1_SETTLEMENT("MOCK_TMU_D1_SETTLEMENT"),

    TMU_D1_STDNT_PROFILE("MOCK_TMU_D1_STDNT_PROFILE"),

    TMU_D1_CLASS_CAPADJUST("MOCK_TMU_D1_CLASS_CAPADJUST"),

    TMU_D1_CLASS_FEE("MOCK_TMU_D1_CLASS_FEE"),

    TMU_D1_CREATEWEBID("MOCK_TMU_D1_CREATEWEBID"),

    TMU_D1_STDNT_RESIDENCY("MOCK_TMU_D1_STDNT_RESIDENCY"),

    TMU_D1_REGANDTRAN("MOCK_TMU_D1_REGANDTRAN"),

    TMU_D1_STDNT_STATUS("MOCK_TMU_D1_STDNT_STATUS"),

    TMU_D1_GET_OPTINOUT("MOCK_TMU_D1_GET_OPTINOUT"),

    TMU_D1_PUT_OPTINOUT("MOCK_TMU_D1_PUT_OPTINOUT"),

    TMU_D1_BLUEPAY_INFO("MOCK_TMU_D1_BLUEPAY_INFO");

    String mockEndpoint;

    D1TMUPSServiceName(String mockEndpoint) {
        this.mockEndpoint = mockEndpoint;
    }
}
