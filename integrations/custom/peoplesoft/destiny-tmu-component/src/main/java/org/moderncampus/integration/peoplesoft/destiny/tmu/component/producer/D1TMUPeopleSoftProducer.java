package org.moderncampus.integration.peoplesoft.destiny.tmu.component.producer;

import static org.apache.camel.component.http.HttpConstants.HTTP_METHOD;
import static org.apache.hc.core5.http.ContentType.TEXT_XML;
import static org.apache.hc.core5.http.HttpHeaders.*;
import static org.moderncampus.integration.route.Constants.ROUTE_HEADER_PREFIX;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.component.http.HttpEndpoint;
import org.apache.camel.component.http.HttpMethods;
import org.apache.camel.support.DefaultEndpoint;
import org.apache.camel.support.DefaultProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.core5.util.TimeValue;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.helper.DefaultEndpointHeaderFilterStrategy;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUAuthMode;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftBaseConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftEndpointConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.ID1TMUPeopleSoftPipelineConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.D1TMUPeopleSoftEndpoint;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.internal.D1TMUServiceMode;

public class D1TMUPeopleSoftProducer extends DefaultProducer {

    public static final String RESPONSE_TIMEOUT = "responseTimeout";
    public static final String CONNECT_TIMEOUT = "connectTimeout";
    public static final String RESPONSE_TIMEOUT_MS = "900000";
    public static final String CONNECT_TIMEOUT_MS = "30000";
    static final String PROFILE_PROD = "prod";
    static final String PROFILE_TEST = "test";
    static final DefaultHttpRequestRetryStrategy retryStrategy = new DefaultHttpRequestRetryStrategy(5,
            TimeValue.ofSeconds(3L));
    static final String CONTENT_TYPE_TEXT_XML = TEXT_XML.toString();

    public D1TMUPeopleSoftProducer(D1TMUPeopleSoftEndpoint endpoint) {
        super(endpoint);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        D1TMUPeopleSoftEndpoint serviceEndpoint = (D1TMUPeopleSoftEndpoint) getEndpoint();
        D1TMUPeopleSoftEndpointConfiguration endpointConfiguration = serviceEndpoint.getEndpointConfiguration();
        D1TMUServiceMode serviceMode = endpointConfiguration.getPipelineConfig().getServiceMode();
        D1TMUPSServiceName serviceName = endpointConfiguration.getServiceName();
        ProducerTemplate producerTemplate = serviceEndpoint.getProducerTemplate();
        if (serviceMode == null) {
            serviceMode = D1TMUServiceMode.DEFAULT;
        }
        boolean isMockServiceApplicable = isMockServiceApplicable(serviceEndpoint.getActiveAppProfiles());
        if (Objects.requireNonNull(serviceMode) == D1TMUServiceMode.MOCK && isMockServiceApplicable) {
            sendToMockEndpoint(exchange, serviceName, producerTemplate);
        } else if ((Objects.requireNonNull(serviceMode) == D1TMUServiceMode.DEFAULT) || !isMockServiceApplicable) {
            sendToDefaultEndpoint(exchange, endpointConfiguration, serviceName, producerTemplate);
        } else {
            throw new ApplicationException("Invalid service mode specified");
        }
    }

    public void sendToDefaultEndpoint(Exchange exchange, D1TMUPeopleSoftEndpointConfiguration endpointConfiguration,
            D1TMUPSServiceName serviceName, ProducerTemplate template) throws Exception {
        D1TMUPeopleSoftBaseConfiguration baseConfiguration = endpointConfiguration.getBaseConfig();
        ID1TMUPeopleSoftPipelineConfiguration pipelineConfiguration = endpointConfiguration.getPipelineConfig();
        Message message = exchange.getMessage();
        Optional<String> httpMethod = Optional.ofNullable(endpointConfiguration.getHttpMethod());
        httpMethod.ifPresent(s -> message.setHeader(HTTP_METHOD, HttpMethods.valueOf(s)));
        message.setHeader(ACCEPT, TEXT_XML.getMimeType());
        if (exchange.getMessage().getBody() != null) {
            message.setHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_XML);
        }
        message.setHeader(HTTP_METHOD, HttpMethods.POST);
        getHeaderMap(message, baseConfiguration, pipelineConfiguration).forEach(message::setHeader);
        String basePath = baseConfiguration.getHost();
        String resourceURI = getResourceURI(serviceName, D1TMUPeopleSoftBaseConfiguration.getUriMap());
        if (resourceURI == null) {
            throw new ApplicationException("Resource URI is missing and not configured");
        }
        String queryParams = getQueryParams(serviceName, D1TMUPeopleSoftBaseConfiguration.getQueryParamMap());
        if (queryParams != null) {
            message.setHeader(Exchange.HTTP_QUERY, queryParams);
        }
        String endpointURI = buildRouteURI(Constants.HTTPS,
                basePath + "/" + resourceURI,
                routeQueryParamStr(Map.of(RESPONSE_TIMEOUT, RESPONSE_TIMEOUT_MS, CONNECT_TIMEOUT, CONNECT_TIMEOUT_MS,
                        Constants.HEADER_FILTER_STRATEGY, beanRef(
                                DefaultEndpointHeaderFilterStrategy.class))));
        HttpEndpoint httpEndpoint = (HttpEndpoint) template.getCamelContext().getEndpoint(endpointURI);
        httpEndpoint.getClientBuilder()
                .setRetryStrategy(retryStrategy);
        exchange = template.send(httpEndpoint, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
    }

    public void sendToMockEndpoint(Exchange exchange, D1TMUPSServiceName serviceName, ProducerTemplate template)
            throws Exception {
        String mockServiceName = serviceName.getMockEndpoint();
        if (mockServiceName == null) {
            throw new RuntimeException("Mock Service for Service: " + serviceName + " is not implemented");
        }
        Message message = exchange.getMessage();
        DefaultEndpoint endpoint = (DefaultEndpoint) template.getCamelContext()
                .getEndpoint(buildDirectRouteURI(serviceName.getMockEndpoint(), "synchronous=true"));
        //Create new mock exchange/unit of work to capture originals. "InOnly" MEP ensures the Input is directly modified, simpler than creating an out message
        Exchange mockEndpointExchange = endpoint.createExchange(ExchangePattern.InOnly);
        mockEndpointExchange.setMessage(message);
        exchange = template.send(buildDirectRouteURI(serviceName.getMockEndpoint(), "synchronous=true"),
                mockEndpointExchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
    }

    private Map<String, String> getHeaderMap(Message message,
            D1TMUPeopleSoftBaseConfiguration baseConfiguration,
            ID1TMUPeopleSoftPipelineConfiguration pipelineConfiguration)
            throws ApplicationException {
        D1TMUAuthMode authMode = pipelineConfiguration.getAuthMode();
        D1TMUPeopleSoftBaseConfiguration.Auth authConfig = baseConfiguration.getAuth();
        switch (authMode) {
            case basic -> {
                return Map.of(AUTHORIZATION, authConfig.getAuthString());
            }
            case header -> {
                String headerValue = null;
                if (StringUtils.isNotBlank(authConfig.getFromAuthHeaderName())) {
                    headerValue = message.getHeader(ROUTE_HEADER_PREFIX + authConfig.getFromAuthHeaderName(), null,
                            String.class);
                }
                if (headerValue == null || StringUtils.isBlank(authConfig.getAuthHeaderName())) {
                    return Map.of();
                }
                return Map.of(authConfig.getAuthHeaderName(), headerValue);
            }
            default -> throw new ApplicationException("Unrecognizable auth mode configured: " + authMode);
        }
    }

    private String getResourceURI(D1TMUPSServiceName serviceName, Map<D1TMUPSServiceName, String> resourceURIMap) {
        return Optional.ofNullable(resourceURIMap).map(map -> map.getOrDefault(serviceName, null)).orElse(null);
    }

    private String getQueryParams(D1TMUPSServiceName serviceName, Map<D1TMUPSServiceName, String> queryParamMap) {
        return Optional.ofNullable(queryParamMap).map(map -> map.getOrDefault(serviceName, null)).orElse(null);
    }

    private boolean isMockServiceApplicable(String[] appProfiles) {
        if (appProfiles == null || appProfiles.length == 0) {
            return true;
        }
        return Arrays.stream(appProfiles)
                .noneMatch(element -> element.equalsIgnoreCase(PROFILE_PROD) || element.equalsIgnoreCase(PROFILE_TEST));
    }
}
