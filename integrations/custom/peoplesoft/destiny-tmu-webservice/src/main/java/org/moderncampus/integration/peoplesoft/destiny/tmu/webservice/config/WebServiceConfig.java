package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration
public class WebServiceConfig {

    @Bean
    @Lazy(value = false)
    public GroupedOpenApi destinyTMUPeopleSoftOpenApi() {
        String paths[] = {"/**/destiny/tmu/**"};
        return GroupedOpenApi.builder().group("Destiny TMU PeopleSoft Integration").pathsToMatch(paths).build();
    }
}
