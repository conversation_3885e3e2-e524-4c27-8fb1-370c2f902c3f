package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.controller;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.constants.Constants.D1_PS_TOKEN_KEY;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier.DestinyTMUPSCommonRouteIds.V1_D1_TMU_PEOPLESOFT_HEALTH;

import java.util.HashMap;
import java.util.Map;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.service.DestinyTMUPeopleSoftIntegrationService;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.moderncampus.integration.webservice.controller.IHealthEndpointController;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + DESTINY_SYSTEM_ID + "/" + Constants.SCHOOL_ID_TMU + "/"
        + PEOPLESOFT_SYSTEM_ID)
@Tag(name = "Destiny TMU PeopleSoft Integration API", description = "Manages integration requests from Destiny to PeopleSoft for the TMU school.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class DestinyTMUPeopleSoftIntegrationController implements IHealthEndpointController {

    static final String STUDENT_TOKEN_RETRIEVE = "studentTokenRetrieve";
    static final String STUDENT_ENROLLMENT_VALIDATE = "studentEnrollmentValidate";
    static final String STUDENT_ENROLLMENT_CREATE = "studentEnrollmentCreate";
    static final String STUDENT_ENROLLMENT_DROP = "studentEnrollmentDrop";
    static final String SECTION_AVAILABILITY_RETRIEVE = "sectionAvailabilityRetrieve";
    static final String SECTION_AVAILABILITY_VALIDATE = "sectionAvailabilityValidate";
    static final String STUDENT_ACCOUNT_SETTLEMENT = "studentAccountSettlement";
    static final String STUDENT_PROFILE_RETRIEVE = "studentProfileRetrieve";
    static final String SECTION_CAPACITY_RESERVE = "sectionCapacityReserve";
    static final String SECTION_FEE_RETRIEVE = "sectionFeeRetrieve";
    static final String STUDENT_LOOKUP = "studentLookup";
    static final String STUDENT_RESIDENCY_NOTIFY = "studentResidencyNotify";
    static final String STUDENT_CERTIFICATE_ENROLL = "studentCertificateEnroll";
    static final String STUDENT_STATUS_VALIDATE = "studentStatusValidate";
    static final String STUDENT_CHOICE_RETRIEVE = "studentChoiceRetrieve";
    static final String STUDENT_CHOICE_UPDATE = "studentChoiceUpdate";

    DestinyTMUPeopleSoftIntegrationService service;

    @Operation(summary = "TMU PeopleSoft Token Retrieve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_TOKEN_RETRIEVE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> retrieveStudentToken(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML).body(service.retrieveStudentToken(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Enrollment Validate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_ENROLLMENT_VALIDATE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentEnrollmentValidate(@RequestBody String request,
            @RequestHeader(name = D1_PS_TOKEN_KEY, required = false) String psToken)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentEnrollmentValidate(request, new HashMap<>() {{
                    put(D1_PS_TOKEN_KEY, psToken);
                }}));
    }

    @Operation(summary = "TMU PeopleSoft Student Enrollment Create")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_ENROLLMENT_CREATE, produces = {MediaType.ALL_VALUE})
    @Parameter(name = "OVRD_ENRL_ACTN_DT", schema = @Schema(type = "string"), required = false)
    @Parameter(name = "OVRD_CLASS_LIMIT", schema = @Schema(type = "string"), required = false)
    @Parameter(name = "OVRD_CLASS_PRMSN", schema = @Schema(type = "string"), required = false)
    public ResponseEntity<String> studentEnrollmentCreate(@RequestBody String request,
            @RequestHeader(name = D1_PS_TOKEN_KEY, required = false) String psToken,
            @RequestParam(required = false) Map<String, String> requestParams)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentEnrollmentCreate(request, new HashMap<>() {{
                    put(D1_PS_TOKEN_KEY, psToken);
                }}, requestParams));
    }

    @Operation(summary = "TMU PeopleSoft Student Enrollment Drop")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_ENROLLMENT_DROP, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentEnrollmentDrop(@RequestBody String request,
            @RequestHeader(name = D1_PS_TOKEN_KEY, required = false) String psToken)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentEnrollmentDrop(request, new HashMap<>() {{
                    put(D1_PS_TOKEN_KEY, psToken);
                }}));
    }

    @Operation(summary = "TMU PeopleSoft Section Availability Retrieve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = SECTION_AVAILABILITY_RETRIEVE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> sectionAvailabilityRetrieve(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.sectionAvailabilityRetrieve(request));
    }

    @Operation(summary = "TMU PeopleSoft Section Availability Validate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = SECTION_AVAILABILITY_VALIDATE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> sectionAvailabilityValidate(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.sectionAvailabilityValidate(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Account Settlement")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_ACCOUNT_SETTLEMENT, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentAccountSettlement(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentAccountSettlement(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Profile Retrieve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_PROFILE_RETRIEVE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentProfileRetrieve(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentProfileRetrieve(request));
    }

    @Operation(summary = "TMU PeopleSoft Section Capacity Reserve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = SECTION_CAPACITY_RESERVE, produces = {MediaType.ALL_VALUE})
    @Parameter(name = "ENRL_ACTION", schema = @Schema(type = "string"), required = false)
    public ResponseEntity<String> sectionCapacityReserve(@RequestBody String request,
            @RequestParam(required = false) Map<String, String> requestParams)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.sectionCapacityReserve(request, requestParams));
    }

    @Operation(summary = "TMU PeopleSoft Section Fee Retrieve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = SECTION_FEE_RETRIEVE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> sectionFeeRetrieve(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.sectionFeeRetrieve(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Lookup")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_LOOKUP, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentLookup(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentLookup(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Residency Notify")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_RESIDENCY_NOTIFY, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentResidencyNotify(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentResidencyNotify(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Certificate Enroll")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_CERTIFICATE_ENROLL, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentCertificateEnroll(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentCertificateEnroll(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Status Validate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_STATUS_VALIDATE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentStatusValidate(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentStatusValidate(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Choice Retrieve")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_CHOICE_RETRIEVE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentChoiceRetrieve(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentChoiceRetrieve(request));
    }

    @Operation(summary = "TMU PeopleSoft Student Choice Update")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = STUDENT_CHOICE_UPDATE, produces = {MediaType.ALL_VALUE})
    public ResponseEntity<String> studentChoiceUpdate(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.studentChoiceUpdate(request));
    }

    @Override
    public BaseIntegrationService getIntegrationService() {
        return service;
    }

    @Override
    public IRouteId getHeathRouteId() {
        return V1_D1_TMU_PEOPLESOFT_HEALTH;
    }
}
