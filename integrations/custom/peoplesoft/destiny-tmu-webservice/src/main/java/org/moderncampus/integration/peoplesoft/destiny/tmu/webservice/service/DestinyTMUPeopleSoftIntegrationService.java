package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.service;

import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier.DestinyTMUPSRouteIds.*;

import java.util.Map;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.stereotype.Service;

@Service
public class DestinyTMUPeopleSoftIntegrationService extends BaseIntegrationService {

    private Map<String, String> mergeMaps(Map<String, String> map1, Map<String, String> map2) {
        if (map1 != null && map2 != null) {
            map1.putAll(map2);
        }
        return map1;
    }

    public String retrieveStudentToken(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_TOKEN_RETRIEVE,
                request, null);
        return result.getData();
    }

    public String studentEnrollmentValidate(String request, Map<String, String> headers) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_VALIDATE,
                request, headers, null);
        return result.getData();
    }

    public String studentEnrollmentCreate(String request, Map<String, String> headers, Map<String, String> queryParams)
            throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_CREATE,
                request, mergeMaps(headers, queryParams), null);
        return result.getData();
    }

    public String studentEnrollmentDrop(String request, Map<String, String> headers)
            throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_DROP,
                request, headers, null);
        return result.getData();
    }

    public String sectionAvailabilityRetrieve(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_RETRIEVE,
                request, null);
        return result.getData();
    }

    public String sectionAvailabilityValidate(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_VALIDATE,
                request, null);
        return result.getData();
    }

    public String studentAccountSettlement(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_ACCOUNT_SETTLEMENT,
                request, null);
        return result.getData();
    }

    public String studentProfileRetrieve(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_PROFILE_RETRIEVE,
                request, null);
        return result.getData();
    }

    public String sectionCapacityReserve(String request, Map<String, String> queryParams) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_SECTION_CAPACITY_RESERVE,
                request, queryParams, null);
        return result.getData();
    }

    public String sectionFeeRetrieve(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_SECTION_FEE_RETRIEVE,
                request, null);
        return result.getData();
    }

    public String studentLookup(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_LOOKUP,
                request, null);
        return result.getData();
    }

    public String studentResidencyNotify(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_RESIDENCY_NOTIFY,
                request, null);
        return result.getData();
    }

    public String studentCertificateEnroll(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_CERTIFICATE_ENROLL,
                request, null);
        return result.getData();
    }

    public String studentStatusValidate(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_STATUS_VALIDATE,
                request, null);
        return result.getData();
    }

    public String studentChoiceRetrieve(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_RETRIEVE,
                request, null);
        return result.getData();
    }

    public String studentChoiceUpdate(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_UPDATE,
                request, null);
        return result.getData();
    }

    public String mockBluePayInfo(String request) throws Exception {
        IntegrationResponse<String> result = executeRoute(V1_D1_TMU_PEOPLESOFT_MOCK_BLUEPAY_INFO,
                request, null);
        return result.getData();
    }
}
