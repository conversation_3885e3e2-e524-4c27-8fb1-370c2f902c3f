package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.controller;

import static org.moderncampus.integration.Constants.*;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.service.DestinyTMUPeopleSoftIntegrationService;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + DESTINY_SYSTEM_ID + "/" + Constants.SCHOOL_ID_TMU + "/"
        + PEOPLESOFT_SYSTEM_ID + "/" + "mock")
@Tag(name = "Mock Destiny TMU PeopleSoft Integration API", description = "Manages mock integration requests from Destiny to PeopleSoft for the TMU school.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "basicAuth")
@Profile("!test & !prod")
public class MockTMUPeopleSoftIntegrationController {

    static final String MOCK_BLUEPAY_INFO = "mockBluePayInfo";

    DestinyTMUPeopleSoftIntegrationService service;

    @Operation(summary = "TMU PeopleSoft Mock BluePay Info")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/xml")}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/xml")})})
    @PostMapping(path = MOCK_BLUEPAY_INFO, produces = {MediaType.ALL_VALUE})
    @Profile("!test & !prod")
    public ResponseEntity<String> mockBluePayInfo(@RequestBody String request)
            throws Exception {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML)
                .body(service.mockBluePayInfo(request));
    }
}
