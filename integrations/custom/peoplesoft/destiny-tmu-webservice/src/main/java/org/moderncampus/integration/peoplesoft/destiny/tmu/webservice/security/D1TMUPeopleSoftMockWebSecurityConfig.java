package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.security;

import static org.moderncampus.integration.Constants.DESTINY_SYSTEM_ID;
import static org.moderncampus.integration.Constants.PEOPLESOFT_SYSTEM_ID;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.tuple.Pair;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.tenants.service.TenantApiTokenService;
import org.moderncampus.integration.webservice.security.BasicAuthNoPreemptiveResponseEntryPoint;
import org.moderncampus.integration.webservice.security.WebSecurityConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;

@Configuration
@Import(WebSecurityConfig.class)
public class D1TMUPeopleSoftMockWebSecurityConfig {

    @Value("${spring.profiles.active:}")
    private String activeProfiles;

    static final String MOCK_PATH_PATTERN =
            "/integration/**/" + DESTINY_SYSTEM_ID + "/" + Constants.SCHOOL_ID_TMU + "/" + PEOPLESOFT_SYSTEM_ID + "/"
                    + "mock"
                    + "/**";

    @Bean
    @Order(1)
    public SecurityFilterChain d1TMUPeopleSoftMockSecurityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable);

        http.sessionManagement(configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        http
                .securityMatcher(new OrRequestMatcher(List.of(AntPathRequestMatcher.antMatcher(MOCK_PATH_PATTERN))))
                .authorizeHttpRequests(
                        auth -> {
                            if (activeProfiles.contains("dev")) {
                                auth.anyRequest().permitAll();
                            } else {
                                auth.anyRequest().authenticated();
                            }
                        })
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .httpBasic(configurer -> configurer.authenticationEntryPoint(
                        new BasicAuthNoPreemptiveResponseEntryPoint()));
        return http.build();
    }

    @Bean
    public UserDetailsService d1TMUPeopleSoftMockTenantAuthDetailsService(TenantApiTokenService apiTokenService) {
        return username ->
                Optional.ofNullable(apiTokenService.retrieveApiTokenForTenant(username))
                        .map((token) -> new D1TMUPeopleSoftMockPrincipal(Pair.of(username, token)))
                        .orElseThrow(
                                () -> new UsernameNotFoundException(
                                        "Username does not match an existing tenant name."));
    }

    @Bean
    public PasswordEncoder encoder() {
        return NoOpPasswordEncoder.getInstance();
    }
}
