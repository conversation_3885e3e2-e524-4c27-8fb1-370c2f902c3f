package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.exception;

import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.helper.MethodStubs.mapException;

import java.util.List;
import java.util.stream.Collectors;

import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.exception.ValidationException;
import org.moderncampus.integration.exception.ValidationExceptions;
import org.moderncampus.integration.peoplesoft.destiny.tmu.webservice.controller.DestinyTMUPeopleSoftIntegrationController;
import org.moderncampus.integration.webservice.dto.APIErrorResponse;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.exception.IWebserviceExceptionHandler;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.ErrorResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.validation.ConstraintViolationException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestControllerAdvice(assignableTypes = DestinyTMUPeopleSoftIntegrationController.class)
@Order(Ordered.HIGHEST_PRECEDENCE)
@RequestMapping(produces = "application/xml")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class D1TMUPeopleSoftExceptionHandler implements IWebserviceExceptionHandler {

    ObjectMapper mapper;

    @ExceptionHandler(value = {ConstraintViolationException.class})
    public ResponseEntity<String> handleConstraintViolationException(
            ConstraintViolationException ex) throws JsonProcessingException {
        APIErrorResponse errorResponse = new APIErrorResponse(ex.getMessage(),
                HttpStatus.BAD_REQUEST.value());
        APIErrorResponseWrapper errorResponseWrapper = new APIErrorResponseWrapper(
                List.of(errorResponse));
        String error = mapper.writeValueAsString(errorResponseWrapper);
        String errorResp = mapException(error, ex);
        return ResponseEntity.badRequest().contentType(MediaType.APPLICATION_XML).body(errorResp);
    }

    @ExceptionHandler(value = {ValidationExceptions.class})
    public ResponseEntity<String> handleBeanValidationExceptions(ValidationExceptions ex)
            throws JsonProcessingException {
        APIErrorResponseWrapper errorResponseWrapper = new APIErrorResponseWrapper(ex.getExceptions().stream()
                .map((APIErrorResponse::new)).collect(
                        Collectors.toList()));
        String error = mapper.writeValueAsString(errorResponseWrapper);
        String errorResp = mapException(error, ex);
        return ResponseEntity.badRequest().contentType(MediaType.APPLICATION_XML).body(errorResp);
    }

    @ExceptionHandler(value = {ValidationException.class})
    public ResponseEntity<String> handleBeanValidationException(ValidationException ex) throws JsonProcessingException {
        APIErrorResponseWrapper errorResponseWrapper = new APIErrorResponseWrapper(
                List.of(new APIErrorResponse(ex)));
        String error = mapper.writeValueAsString(errorResponseWrapper);
        String errorResp = mapException(error, ex);
        return ResponseEntity.status(HttpStatusCode.valueOf(ex.getStatusCode())).contentType(MediaType.APPLICATION_XML)
                .body(errorResp);
    }

    @ExceptionHandler(value = {ApplicationException.class})
    public ResponseEntity<String> handleApplicationException(ApplicationException ex) throws JsonProcessingException {
        APIErrorResponseWrapper errorResponseWrapper = new APIErrorResponseWrapper(
                List.of(new APIErrorResponse(ex)));
        String error = mapper.writeValueAsString(errorResponseWrapper);
        String errorResp = mapException(error, ex);
        return ResponseEntity.status(HttpStatusCode.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()))
                .contentType(MediaType.APPLICATION_XML)
                .body(errorResp);
    }

    @ExceptionHandler(value = {Exception.class})
    public ResponseEntity<String> handleException(Exception e) throws JsonProcessingException {
        int statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value();
        if (e instanceof ErrorResponse bindingException) {
            statusCode = bindingException.getStatusCode().value();
        }
        APIErrorResponse errorResponse = new APIErrorResponse(e.getMessage(), statusCode);
        APIErrorResponseWrapper errorResponseWrapper = new APIErrorResponseWrapper(
                List.of(errorResponse));
        String error = mapper.writeValueAsString(errorResponseWrapper);
        String errorResp = mapException(error, e);
        return ResponseEntity.status(HttpStatusCode.valueOf(statusCode)).contentType(MediaType.APPLICATION_XML)
                .body(errorResp);
    }
}
