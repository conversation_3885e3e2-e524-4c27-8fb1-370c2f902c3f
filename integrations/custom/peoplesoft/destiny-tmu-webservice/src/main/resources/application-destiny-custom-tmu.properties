peoplesoft.integration.base.host=
peoplesoft.integration.base.auth.auth-string=
peoplesoft.integration.base.auth.encrypt-field-i-v=
peoplesoft.integration.base.auth.encrypt-field-key=
peoplesoft.integration.d1-tmu-token-retrieve.by-pass-encryption=
peoplesoft.integration.d1-tmu-token-retrieve.service-mode=
peoplesoft.integration.d1-tmu-enrollment-validate.service-mode=
peoplesoft.integration.d1-tmu-enrollment-create.service-mode=
peoplesoft.integration.d1-tmu-enrollment-drop.service-mode=
peoplesoft.integration.d1-tmu-section-availability-retrieve.service-mode=
peoplesoft.integration.d1-tmu-account-settlement.by-pass-encryption=
peoplesoft.integration.d1-tmu-account-settlement.service-mode=
peoplesoft.integration.d1-tmu-student-profile-retrieve.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-profile-retrieve.service-mode=
peoplesoft.integration.d1-tmu-section-capacity-reserve.by-pass-encryption=
peoplesoft.integration.d1-tmu-section-capacity-reserve.service-mode=
peoplesoft.integration.d1-tmu-section-fee-retrieve.by-pass-encryption=
peoplesoft.integration.d1-tmu-section-fee-retrieve.service-mode=
peoplesoft.integration.d1-tmu-student-lookup.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-lookup.service-mode=
peoplesoft.integration.d1-tmu-student-residency-notify.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-residency-notify.service-mode=
peoplesoft.integration.d1-tmu-student-certificate-enroll.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-certificate-enroll.service-mode=
peoplesoft.integration.d1-tmu-student-status-validate.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-status-validate.service-mode=
peoplesoft.integration.d1-tmu-student-choice-retrieve.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-choice-retrieve.service-mode=
peoplesoft.integration.d1-tmu-student-choice-update.by-pass-encryption=
peoplesoft.integration.d1-tmu-student-choice-update.service-mode=