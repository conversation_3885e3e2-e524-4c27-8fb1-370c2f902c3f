package org.moderncampus.integration.peoplesoft.destiny.tmu.webservice;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.moderncampus.integration.webservice.config.SharedAppConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.xmlunit.builder.DiffBuilder;
import org.xmlunit.diff.Diff;

@SpringBootTest(classes = SharedAppConfiguration.class)
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class DestinyTMUPeopleSoftIntegrationControllerTests {

    @Autowired
    MockMvc mockMvc;

    private void compareXML(String actualXML, String expectedXML, Set<String> ignoreFieldNames) throws Exception {
        // Use XMLUnit to compare the actual response with the expected response
        Diff diff;
        DiffBuilder diffBuilder = DiffBuilder.compare(expectedXML)
                .withTest(actualXML)
                .checkForSimilar()
                .normalizeWhitespace()  // Checks if XML is identical after ignoring whitespace and newlines
                .ignoreWhitespace()
                .ignoreComments();   // Ignores whitespace differences (spaces, newlines)
        if (ignoreFieldNames != null) {
            diff = diffBuilder.withNodeFilter(node ->
                            !ignoreFieldNames.contains(node.getNodeName()))
                    .build();
        } else {
            diff = diffBuilder.build();
        }

        // Assert that the diff has no differences (i.e., the XMLs are identical)
        if (diff.hasDifferences()) {
            // Log the differences if any
            diff.getDifferences().forEach(System.out::println);
            // Fail the test if there are differences
            assert false : "XMLs are different";
        }
    }

    private void executeTest(String resource, String requestFileName, String responseFileName,
            Set<String> ignoreFieldNames) throws Exception {
        List<File> reqRespFiles = Stream.of(requestFileName, responseFileName).map((fileName -> {
            return new File(
                    Objects.requireNonNull(
                                    getClass().getClassLoader().getResource("success-cases" + "/" + fileName + ".xml"))
                            .getPath());
        })).toList();
        List<String> reqRespStr = reqRespFiles.stream().map((file -> {
            try {
                return Files.readString(file.toPath(),
                        StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        })).toList();
        String respXML = this.mockMvc.perform(
                        post("/integration/v1/destiny/tmu/peoplesoft/" + resource).content(reqRespStr.getFirst()))
                .andDo(print())
                .andReturn().getResponse().getContentAsString();
        compareXML(respXML, reqRespStr.getLast(), ignoreFieldNames);
    }

    private void executeTest(String resource, String requestFileName, String responseFileName) throws Exception {
        executeTest(resource, requestFileName, responseFileName, null);
    }

    @Test
    void studentTokenRetrieveSuccess() throws Exception {
        executeTest("studentTokenRetrieve", "StudentTokenRetrieveSuccessReq", "StudentTokenRetrieveSuccessResp");
    }

    @Test
    void studentEnrollmentValidateSuccess() throws Exception {
        executeTest("studentEnrollmentValidate", "StudentEnrollmentValidateSuccessReq",
                "StudentEnrollmentValidateSuccessResp");
    }

    @Test
    void studentEnrollmentCreateSuccess() throws Exception {
        executeTest("studentEnrollmentCreate", "StudentEnrollmentCreateSuccessReq",
                "StudentEnrollmentCreateSuccessResp");
    }

    @Test
    void studentEnrollmentDropSuccess() throws Exception {
        executeTest("studentEnrollmentDrop", "StudentEnrollmentDropSuccessReq", "StudentEnrollmentDropSuccessResp");
    }

    @Test
    void sectionAvailabilityRetrieveSuccess() throws Exception {
        executeTest("sectionAvailabilityRetrieve", "SectionAvailabilityRetrieveSuccessReq",
                "SectionAvailabilityRetrieveSuccessResp");
    }

    @Test
    void sectionAvailabilityValidateSuccess() throws Exception {
        executeTest("sectionAvailabilityValidate", "SectionAvailabilityValidateSuccessReq",
                "SectionAvailabilityValidateSuccessResp");
    }

    @Test
    void studentAccountSettlementSuccess() throws Exception {
        executeTest("studentAccountSettlement", "StudentAccountSettlementSuccessReq",
                "StudentAccountSettlementSuccessResp");
    }

    @Test
    void studentProfileRetrieveSuccess() throws Exception {
        executeTest("studentProfileRetrieve", "StudentProfileRetrieveSuccessReq", "StudentProfileRetrieveSuccessResp",
                Set.of("birthDate", "BIRTHDATE"));
    }

    @Test
    void sectionCapacityReserveSuccess() throws Exception {
        executeTest("sectionCapacityReserve", "SectionCapacityReserveSuccessReq", "SectionCapacityReserveSuccessResp");
    }

    @Test
    void sectionFeeRetrieveSuccess() throws Exception {
        executeTest("sectionFeeRetrieve", "SectionFeeRetrieveSuccessReq", "SectionFeeRetrieveSuccessResp");
    }

    @Test
    void studentLookupSuccess() throws Exception {
        executeTest("studentLookup", "StudentLookupSuccessReq", "StudentLookupSuccessResp");
    }

    @Test
    void studentResidencyNotifySuccess() throws Exception {
        executeTest("studentResidencyNotify", "StudentResidencyNotifySuccessReq", "StudentResidencyNotifySuccessResp");
    }

    @Test
    void studentCertificateEnrollSuccess() throws Exception {
        executeTest("studentCertificateEnroll", "StudentCertificateEnrollSuccessReq",
                "StudentCertificateEnrollSuccessResp");
    }

    @Test
    void studentStatusValidateSuccess() throws Exception {
        executeTest("studentStatusValidate", "StudentStatusValidateSuccessReq", "StudentStatusValidateSuccessResp");
    }

    @Test
    void studentChoiceRetrieveSuccess() throws Exception {
        executeTest("studentChoiceRetrieve", "StudentChoiceRetrieveSuccessReq", "StudentChoiceRetrieveSuccessResp");
    }

    @Test
    void studentChoiceUpdateSuccess() throws Exception {
        executeTest("studentChoiceUpdate", "StudentChoiceUpdateSuccessReq", "StudentChoiceUpdateSuccessResp");
    }

}
