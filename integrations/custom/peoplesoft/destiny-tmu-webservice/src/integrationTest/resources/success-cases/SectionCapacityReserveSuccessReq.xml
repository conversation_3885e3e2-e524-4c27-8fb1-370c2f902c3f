<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <transactionBasket>
      <code>Processed</code>
      <createTime>01 Oct 2018 03:16:11 PM</createTime>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description>Processed</description>
      <name>Processed</name>
      <objectId>93624</objectId>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>6</version>
      <affiliatedGroup>
        <objectId>0</objectId>
        <version>0</version>
      </affiliatedGroup>
      <applyGstToPst>false</applyGstToPst>
      <basketCreationTime>01 Oct 2018 03:16:11 PM</basketCreationTime>
      <cslwAdjusted>false</cslwAdjusted>
      <client>
        <objectId>63500</objectId>
        <version>95</version>
        <number>X000029</number>
        <personType>Student</personType>
      </client>
      <enrollmentEvents>
        <enrollmentEvent>
          <objectId>93629</objectId>
          <version>3</version>
          <activityCode>Exchange</activityCode>
          <courseSectionId>48472</courseSectionId>
          <creationTime>01 Oct 2018 03:16:11 PM</creationTime>
          <enrollCourseSection>
            <code>001</code>
            <createTime>16 Mar 2018 06:51:09 PM</createTime>
            <creator>
              <objectId>1</objectId>
              <version>14</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </creator>
            <description>001</description>
            <name>001</name>
            <objectId>48472</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>1</objectId>
              <version>14</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </updater>
            <version>12</version>
            <maximumAcademicUnit>1.0</maximumAcademicUnit>
            <activeWaitListSize>14</activeWaitListSize>
            <courseSectionAssociations/>
            <associatedCourse>
              <objectId>48468</objectId>
              <version>3</version>
              <code>ERR.2003</code>
              <courseVersionNumber>0</courseVersionNumber>
              <costingUnitCode>CU0001</costingUnitCode>
              <costingUnitName>Creative Writing</costingUnitName>
              <name>Short Story: Class Available</name>
              <programOfficeCode>PO0001</programOfficeCode>
              <programOfficeName>Continuing Education</programOfficeName>
            </associatedCourse>
            <sectionSchedules>
              <sectionSchedule>
                <code>48484</code>
                <createTime>16 Mar 2018 06:51:48 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>14</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>48484</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>14</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>0</version>
                <assignedInstructors/>
                <breakHours>0.0</breakHours>
                <broadcastChannelType>Live</broadcastChannelType>
                <daysOfWeek>
                  <dayOfWeek>1</dayOfWeek>
                </daysOfWeek>
                <daysOfWeekString>1</daysOfWeekString>
                <endDate>01 Sep 2017 12:00:00 AM</endDate>
                <endTimeString></endTimeString>
                <endType>date</endType>
                <externalResourceManagement>false</externalResourceManagement>
                <frequency>1</frequency>
                <hasBroadcastChannel>false</hasBroadcastChannel>
                <instructorIds/>
                <core>false</core>
                <distanceLearning>true</distanceLearning>
                <included>true</included>
                <numberOfSessions>1</numberOfSessions>
                <scheduleId>48484</scheduleId>
                <scheduleType>Discussion</scheduleType>
                <courseSectionProfile>
                  <objectId>48472</objectId>
                  <version>12</version>
                  <campusFisId>1010</campusFisId>
                  <associatedSemester>
                    <objectId>18768</objectId>
                    <version>2</version>
                    <academicYear>
                      <objectId>18765</objectId>
                      <version>0</version>
                      <name>2016-2017</name>
                    </academicYear>
                    <beginDate>01 May 2017 12:00:00 AM</beginDate>
                    <campusId>1189</campusId>
                    <code>Summer 2017</code>
                    <endDate>01 Sep 2017 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 May 2017 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>01 Sep 2017 12:00:00 AM</lmsExportEndDate>
                    <name>Summer 2017</name>
                  </associatedSemester>
                </courseSectionProfile>
                <sessionDates/>
                <startDate>01 May 2017 12:00:00 AM</startDate>
                <startTimeString></startTimeString>
              </sectionSchedule>
            </sectionSchedules>
            <courseSectionFees>
              <courseSectionFee>
                <objectId>48491</objectId>
                <version>0</version>
                <federalTaxApplicable>false</federalTaxApplicable>
                <flatFeeEnabled>true</flatFeeEnabled>
                <hstApplicable>false</hstApplicable>
                <nonBroadcastSection>false</nonBroadcastSection>
                <stateTaxApplicable>false</stateTaxApplicable>
                <taxRefundEligible>true</taxRefundEligible>
                <tuitionProfileEnabled>false</tuitionProfileEnabled>
                <associatedSection>
                  <objectId>48472</objectId>
                  <version>12</version>
                  <campusFisId>1010</campusFisId>
                  <associatedSemester>
                    <objectId>18768</objectId>
                    <version>2</version>
                    <academicYear>
                      <objectId>18765</objectId>
                      <version>0</version>
                      <name>2016-2017</name>
                    </academicYear>
                    <beginDate>01 May 2017 12:00:00 AM</beginDate>
                    <campusId>1189</campusId>
                    <code>Summer 2017</code>
                    <endDate>01 Sep 2017 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 May 2017 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>01 Sep 2017 12:00:00 AM</lmsExportEndDate>
                    <name>Summer 2017</name>
                  </associatedSemester>
                </associatedSection>
                <associatedSectionFeeTuitionProfiles>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>48489</code>
                      <createTime>16 Mar 2018 06:52:14 PM</createTime>
                      <creator>
                        <objectId>0</objectId>
                        <version>0</version>
                      </creator>
                      <description></description>
                      <name></name>
                      <objectId>48489</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>0</objectId>
                        <version>0</version>
                      </updater>
                      <version>0</version>
                      <feeCategory></feeCategory>
                      <printCode>Flat Fee</printCode>
                      <udfValues/>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>true</groupPayAllowed>
                      <paymentMethods/>
                      <publicDescription></publicDescription>
                      <publishCode>Flat Fee</publishCode>
                      <reasonsForNotAccepting/>
                      <specialRequests/>
                      <studentCategories/>
                      <studentPayAllowed>false</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>-1</code>
                          <createTime>16 Mar 2018 06:51:52 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>-1</name>
                          <objectId>48490</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>0</version>
                          <printCode>Flat Fee</printCode>
                          <udfValues/>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>100.00</amount>
                              <discountable>true</discountable>
                              <name>Flat Fee</name>
                              <revenueGLAccount>
                                <objectId>18769</objectId>
                                <version>3</version>
                                <accountName>Default Revenue</accountName>
                                <accountNumber>123456</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator>&lt;=</operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>creditInterval</creditType>
                    <markedAsFlatFee>true</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                </associatedSectionFeeTuitionProfiles>
                <creator>
                  <objectId>0</objectId>
                  <version>0</version>
                </creator>
                <udfValues/>
                <updater>
                  <objectId>0</objectId>
                  <version>0</version>
                </updater>
              </courseSectionFee>
            </courseSectionFees>
            <associatedSemester>
              <objectId>18768</objectId>
              <version>2</version>
              <academicYear>
                <objectId>18765</objectId>
                <version>0</version>
                <name>2016-2017</name>
              </academicYear>
              <beginDate>01 May 2017 12:00:00 AM</beginDate>
              <campusId>1189</campusId>
              <code>Summer 2017</code>
              <endDate>01 Sep 2017 12:00:00 AM</endDate>
              <lmsExportBeginDate>01 May 2017 12:00:00 AM</lmsExportBeginDate>
              <lmsExportEndDate>01 Sep 2017 12:00:00 AM</lmsExportEndDate>
              <name>Summer 2017</name>
            </associatedSemester>
            <associatedSpecialRequests/>
            <autoFilledInCompletionDate>false</autoFilledInCompletionDate>
            <campusFisId>10100</campusFisId>
            <courseInstructionMethod>Classroom</courseInstructionMethod>
            <sectionStatusCode>final_approval</sectionStatusCode>
            <discounts/>
            <distanceLearning>0</distanceLearning>
            <dueDateRule>
              <objectId>48473</objectId>
              <version>5</version>
            </dueDateRule>
            <eCollege>false</eCollege>
            <sectionEndDate>01 Sep 2017 12:00:00 AM</sectionEndDate>
            <enrolListSize>0</enrolListSize>
            <enrollmentByUpload>false</enrollmentByUpload>
            <enrollmentRestrictions/>
            <evalutionCompleted>false</evalutionCompleted>
            <evaluationNotes></evaluationNotes>
            <familyCheckoutEligible>false</familyCheckoutEligible>
            <flagSection>0</flagSection>
            <formerScsNumber></formerScsNumber>
            <gradeLevels/>
            <sectionHistoryIndicatorCode>newproposal</sectionHistoryIndicatorCode>
            <ipIssuesResovled>0</ipIssuesResovled>
            <immediateApprovalRequired>0</immediateApprovalRequired>
            <InstructionMethods/>
            <instructorContracts/>
            <invokedCorpContractPricing>true</invokedCorpContractPricing>
            <lectureSessionTopics></lectureSessionTopics>
            <maximumEnrollmentSize>100</maximumEnrollmentSize>
            <maximumWaitListSize>0</maximumWaitListSize>
            <maxCEUnit>1.0</maxCEUnit>
            <minimumEnrollmentSize>1</minimumEnrollmentSize>
            <minimumAcademicUnit>1.0</minimumAcademicUnit>
            <minCEUnit>1.0</minCEUnit>
            <multipleProCeditAllowed>false</multipleProCeditAllowed>
            <numberOfAssignments>0</numberOfAssignments>
            <onlineResources/>
            <onlineTransferRequestDays>7</onlineTransferRequestDays>
            <onlineWithdrawalRequestDays>7</onlineWithdrawalRequestDays>
            <optInProCredit>false</optInProCredit>
            <optionalReading></optionalReading>
            <otherLocation></otherLocation>
            <overrideMaximumCEUnit>1.0</overrideMaximumCEUnit>
            <potentialIPIssues></potentialIPIssues>
            <preventOnlineEnrollment>false</preventOnlineEnrollment>
            <proctoredExams/>
            <publicizeSection>1</publicizeSection>
            <pvAvailabilityBeginDate>01 Mar 2018 12:00:00 AM</pvAvailabilityBeginDate>
            <pvAvailabilityEndDate>16 Mar 2019 11:59:00 PM</pvAvailabilityEndDate>
            <pvEnrollmentBeginDate>01 Mar 2018 12:00:00 AM</pvEnrollmentBeginDate>
            <pvEnrollmentEndDate>16 Mar 2019 11:59:00 PM</pvEnrollmentEndDate>
            <receiptNotes></receiptNotes>
            <rquiredAVList/>
            <requiredReading></requiredReading>
            <requiredSoftwareTechnology></requiredSoftwareTechnology>
            <reservedListSize>0</reservedListSize>
            <room></room>
            <scheduleTypeCode>Undefined</scheduleTypeCode>
            <sectionContacts/>
            <sectionLMSInfo>
              <objectId>48475</objectId>
              <version>5</version>
              <lmsAccessPeriod>7</lmsAccessPeriod>
              <lmsSectionId></lmsSectionId>
              <lmsType></lmsType>
            </sectionLMSInfo>
            <sectionMaterials/>
            <sectionNotes></sectionNotes>
            <sectionNotesInternal></sectionNotesInternal>
            <sectionTitle>Short Story</sectionTitle>
            <serviceCharges/>
            <sectionStartDate>01 May 2017 12:00:00 AM</sectionStartDate>
            <completionRuleTypeCode>CompleteOnSchEndDate</completionRuleTypeCode>
            <svEnrollmentBeginDate>01 Mar 2018 12:00:00 AM</svEnrollmentBeginDate>
            <svEnrollmentDate>16 Mar 2019 11:59:00 PM</svEnrollmentDate>
            <timeCategory>regular</timeCategory>
            <totalSessionHours>0.0</totalSessionHours>
            <transcriptTitle></transcriptTitle>
            <udfValues>
              <udfValue>
                <objectId>90933</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1000</objectId>
                  <version>1</version>
                  <udfFieldName>sasCapacity</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>90934</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>2000</objectId>
                  <version>1</version>
                  <udfFieldName>SessionCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
            </udfValues>
            <waitListSize>0</waitListSize>
          </enrollCourseSection>
          <feeLWs>
            <feeLW>
              <objectId>93630</objectId>
              <version>2</version>
              <enrolment>
                <objectId>93629</objectId>
                <version>3</version>
              </enrolment>
              <creationTime>01 Oct 2018 03:16:11 PM</creationTime>
              <dispersements/>
              <fee xsi:type="tuitionProfile" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <code>48489</code>
                <createTime>16 Mar 2018 06:52:14 PM</createTime>
                <creator>
                  <objectId>0</objectId>
                  <version>0</version>
                </creator>
                <description></description>
                <name></name>
                <objectId>48489</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>0</objectId>
                  <version>0</version>
                </updater>
                <version>0</version>
                <feeCategory></feeCategory>
                <printCode>Flat Fee</printCode>
                <udfValues/>
                <anyInstructionMethod>true</anyInstructionMethod>
                <anyPaymentMethod>true</anyPaymentMethod>
                <anyStudentCategory>true</anyStudentCategory>
                <applicability>public</applicability>
                <basis>FlatFee</basis>
                <effectiveDateOption>byDate</effectiveDateOption>
                <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                <expiryDateOption>neverExpire</expiryDateOption>
                <groupPayAllowed>true</groupPayAllowed>
                <paymentMethods/>
                <publicDescription></publicDescription>
                <publishCode>Flat Fee</publishCode>
                <reasonsForNotAccepting/>
                <specialRequests/>
                <studentCategories/>
                <studentPayAllowed>false</studentPayAllowed>
                <tuitionFees>
                  <tuitionFee>
                    <code>-1</code>
                    <createTime>16 Mar 2018 06:51:52 PM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>-1</name>
                    <objectId>48490</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>0</objectId>
                      <version>0</version>
                    </updater>
                    <version>0</version>
                    <printCode>Flat Fee</printCode>
                    <udfValues/>
                    <tuitionFeeItems>
                      <tuitionFeeItem>
                        <amount>100.00</amount>
                        <discountable>true</discountable>
                        <name>Flat Fee</name>
                        <revenueGLAccount>
                          <objectId>18769</objectId>
                          <version>3</version>
                          <accountName>Default Revenue</accountName>
                          <accountNumber>123456</accountNumber>
                          <accountTypeCode>revenue</accountTypeCode>
                        </revenueGLAccount>
                        <surchargeable>true</surchargeable>
                      </tuitionFeeItem>
                    </tuitionFeeItems>
                    <operator>&lt;=</operator>
                    <useStepFunction>false</useStepFunction>
                  </tuitionFee>
                </tuitionFees>
              </fee>
              <feeGroup>TuitionProfile</feeGroup>
              <quantity>1</quantity>
              <totalAmount>100.00</totalAmount>
            </feeLW>
          </feeLWs>
          <numOfUnits>1.0</numOfUnits>
          <originalTransactionBasketId>-1</originalTransactionBasketId>
          <student>
            <code>code63500</code>
            <createTime>15 Jun 2018 01:43:34 PM</createTime>
            <creator>
              <objectId>2</objectId>
              <version>9</version>
              <loginId>webreg</loginId>
              <personType>Staff</personType>
              <printName>Web Registrar</printName>
            </creator>
            <description></description>
            <name>Anita Huang</name>
            <objectId>63500</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>1</objectId>
              <version>14</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </updater>
            <version>95</version>
            <addresses>
              <address>
                <typeCode>Home</typeCode>
                <city>Toronto</city>
                <country>USA</country>
                <effectiveDate>15 Jun 2018 01:42:18 PM</effectiveDate>
                <foreign>false</foreign>
                <foreignState></foreignState>
                <modifiedDate>15 Jun 2018 01:42:18 PM</modifiedDate>
                <preferred>true</preferred>
                <provinceState>GA</provinceState>
                <street0></street0>
                <street1>111 Main Street</street1>
                <street2>1</street2>
                <terminate>false</terminate>
                <postalZip>91210</postalZip>
              </address>
            </addresses>
            <alternateName/>
            <birthDate>04 Jun 2018 12:00:00 AM</birthDate>
            <cautionaryRequired>false</cautionaryRequired>
            <comments/>
            <communicationMethod>Email</communicationMethod>
            <contactMethods>
              <contactMethod>Any</contactMethod>
            </contactMethods>
            <emails>
              <email>
                <emailAddress><EMAIL></emailAddress>
                <typeCode>Standard</typeCode>
                <preferred>true</preferred>
                <release>false</release>
                <return>false</return>
              </email>
            </emails>
            <socialSecurityNum></socialSecurityNum>
            <firstName1>Anita</firstName1>
            <firstName2></firstName2>
            <interestAreaAssociations/>
            <lastName>Huang</lastName>
            <lmsPersonId>X000029</lmsPersonId>
            <loginId>RU_X000029</loginId>
            <netId></netId>
            <personNumber>X000029</personNumber>
            <preferredContactTime></preferredContactTime>
            <preferredEmail>
              <emailAddress><EMAIL></emailAddress>
              <typeCode>Standard</typeCode>
              <preferred>true</preferred>
              <release>false</release>
              <return>false</return>
            </preferredEmail>
            <preferredLocaleString>en_US</preferredLocaleString>
            <privacyQuestions>
              <privacyQuestion>
                <objectId>86999</objectId>
                <version>0</version>
                <answer>aa</answer>
                <question>0</question>
              </privacyQuestion>
              <privacyQuestion>
                <objectId>87050</objectId>
                <version>0</version>
                <answer>aa</answer>
                <question>1</question>
              </privacyQuestion>
            </privacyQuestions>
            <profileHolds/>
            <salutationCode></salutationCode>
            <schoolPersonnelNumber></schoolPersonnelNumber>
            <telephones>
              <telephone>
                <areaCode>416</areaCode>
                <preferred>true</preferred>
                <release>false</release>
                <tcpaConsented>false</tcpaConsented>
                <telephoneExt></telephoneExt>
                <telephoneNumber>5555555</telephoneNumber>
                <typeCode>Home</typeCode>
              </telephone>
            </telephones>
            <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
            <additionalAssociation></additionalAssociation>
            <birthCity></birthCity>
            <birthState></birthState>
            <copyOfVisa>false</copyOfVisa>
            <countryOfCitizenship></countryOfCitizenship>
            <countryOfOrigin></countryOfOrigin>
            <department></department>
            <directBillingAccount></directBillingAccount>
            <documentEmailed>true</documentEmailed>
            <documentFaxed>false</documentFaxed>
            <educationHistory/>
            <emergencyContactEmail></emergencyContactEmail>
            <emergencyContactName></emergencyContactName>
            <employerEmail></employerEmail>
            <employerFirstName></employerFirstName>
            <employerJobTitle></employerJobTitle>
            <employerName></employerName>
            <employerSalutation></employerSalutation>
            <employerSurname></employerSurname>
            <enrolledPreviously>false</enrolledPreviously>
            <enrollmentRaces/>
            <enrollmentTimeframes/>
            <enrolmentGroups/>
            <internationalStudent>false</internationalStudent>
            <jobTitle></jobTitle>
            <laterLifeLearner>false</laterLifeLearner>
            <learningGoals/>
            <major></major>
            <nameTagName></nameTagName>
            <otherEnrollmentTimeframe></otherEnrollmentTimeframe>
            <otherInterests></otherInterests>
            <otherLearningGoals></otherLearningGoals>
            <otherNames></otherNames>
            <preferredName>Anne</preferredName>
            <proficiencyExamScores/>
            <profileStatus>Active</profileStatus>
            <schoolStudentNumber></schoolStudentNumber>
            <secondCountryOfCitizenship></secondCountryOfCitizenship>
            <studentAssociationDetails/>
            <studentCategories/>
            <studentCredentials/>
            <studentNumber>X000029</studentNumber>
            <udfValues>
              <udfValue>
                <objectId>63502</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>1002</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceQuestion</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>Yes</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>63503</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>1005</objectId>
                  <version>1</version>
                  <udfFieldName>citizenshipStatus</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64466</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1001</objectId>
                  <version>1</version>
                  <udfFieldName>oenNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64467</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1003</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceYear</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64468</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1004</objectId>
                  <version>1</version>
                  <udfFieldName>existingStudentNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64469</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1006</objectId>
                  <version>2</version>
                  <udfFieldName>dateOfEntry</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>12 Jun 2018 12:00:00 AM</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64470</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1007</objectId>
                  <version>1</version>
                  <udfFieldName>countryOfCitizenship</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64471</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1008</objectId>
                  <version>1</version>
                  <udfFieldName>certificate</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>64472</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1009</objectId>
                  <version>1</version>
                  <udfFieldName>certificateApprovalCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
            </udfValues>
            <willStudyInCountry>false</willStudyInCountry>
            <willTravelToCountry>false</willTravelToCountry>
            <willWorkInCountry>false</willWorkInCountry>
            <youthParticipant>false</youthParticipant>
          </student>
          <amount>100.00</amount>
          <transactionBasketId>93624</transactionBasketId>
          <voided>false</voided>
          <withdrawn>false</withdrawn>
        </enrollmentEvent>
      </enrollmentEvents>
      <feeLWs/>
      <migrated>false</migrated>
      <newSessionTransactions/>
      <basketNumber>167</basketNumber>
      <saleEvents/>
      <sessionAdjustedTransactions/>
      <settlementClient>
        <objectId>63500</objectId>
        <version>95</version>
        <loginId>RU_X000029</loginId>
        <personNumber>X000029</personNumber>
        <personType>Student</personType>
        <printName>Anita Huang</printName>
      </settlementClient>
      <status>Processed</status>
      <transactionAmount>200.00</transactionAmount>
      <transactionBasketType>Single</transactionBasketType>
      <transactions>
        <transaction>
          <objectId>93701</objectId>
          <version>0</version>
          <adjusted>false</adjusted>
          <adjustedAmount>0.00</adjustedAmount>
          <adjustment>false</adjustment>
          <allocateToAllocations/>
          <amount>200.00</amount>
          <cancelled>false</cancelled>
          <originalTransactionId>93701</originalTransactionId>
          <paymentAllocations>
            <paymentAllocation>
              <objectId>93702</objectId>
              <version>0</version>
              <amount>100.00</amount>
              <dispersement>
                <objectId>93628</objectId>
                <version>3</version>
                <amount>100.0000</amount>
                <federalTaxRate>0.0000</federalTaxRate>
                <fee>
                  <objectId>93627</objectId>
                  <version>3</version>
                  <enrollment>
                    <objectId>93625</objectId>
                    <version>4</version>
                    <enrollCourseSection>
                      <objectId>18813</objectId>
                      <version>28</version>
                      <campusFisId>1014</campusFisId>
                      <associatedSemester>
                        <objectId>18768</objectId>
                        <version>2</version>
                        <academicYear>
                          <objectId>18765</objectId>
                          <version>0</version>
                          <name>2016-2017</name>
                        </academicYear>
                        <beginDate>01 May 2017 12:00:00 AM</beginDate>
                        <campusId>1189</campusId>
                        <code>Summer 2017</code>
                        <endDate>01 Sep 2017 12:00:00 AM</endDate>
                        <lmsExportBeginDate>01 May 2017 12:00:00 AM</lmsExportBeginDate>
                        <lmsExportEndDate>01 Sep 2017 12:00:00 AM</lmsExportEndDate>
                        <name>Summer 2017</name>
                      </associatedSemester>
                    </enrollCourseSection>
                    <transactionBasketId>93624</transactionBasketId>
                  </enrollment>
                </fee>
                <GSTAmount>0.0000</GSTAmount>
                <glAccount>
                  <objectId>18769</objectId>
                  <version>3</version>
                  <accountName>Default Revenue</accountName>
                  <accountNumber>123456</accountNumber>
                  <accountTypeCode>revenue</accountTypeCode>
                </glAccount>
                <HSTAmount>0.0000</HSTAmount>
                <harmonizedTaxRate>0.0000</harmonizedTaxRate>
                <PSTAmount>0.0000</PSTAmount>
                <stateTaxRate>0.0000</stateTaxRate>
                <totalAmount>100.0000</totalAmount>
              </dispersement>
            </paymentAllocation>
            <paymentAllocation>
              <objectId>93703</objectId>
              <version>0</version>
              <amount>100.00</amount>
              <dispersement>
                <objectId>93632</objectId>
                <version>2</version>
                <amount>100.0000</amount>
                <federalTaxRate>0.0000</federalTaxRate>
                <fee>
                  <objectId>93631</objectId>
                  <version>2</version>
                  <enrollment>
                    <objectId>93629</objectId>
                    <version>3</version>
                    <enrollCourseSection>
                      <objectId>48472</objectId>
                      <version>12</version>
                      <campusFisId>1010</campusFisId>
                      <associatedSemester>
                        <objectId>18768</objectId>
                        <version>2</version>
                        <academicYear>
                          <objectId>18765</objectId>
                          <version>0</version>
                          <name>2016-2017</name>
                        </academicYear>
                        <beginDate>01 May 2017 12:00:00 AM</beginDate>
                        <campusId>1189</campusId>
                        <code>Summer 2017</code>
                        <endDate>01 Sep 2017 12:00:00 AM</endDate>
                        <lmsExportBeginDate>01 May 2017 12:00:00 AM</lmsExportBeginDate>
                        <lmsExportEndDate>01 Sep 2017 12:00:00 AM</lmsExportEndDate>
                        <name>Summer 2017</name>
                      </associatedSemester>
                    </enrollCourseSection>
                    <transactionBasketId>93624</transactionBasketId>
                  </enrollment>
                </fee>
                <GSTAmount>0.0000</GSTAmount>
                <glAccount>
                  <objectId>18769</objectId>
                  <version>3</version>
                  <accountName>Default Revenue</accountName>
                  <accountNumber>123456</accountNumber>
                  <accountTypeCode>revenue</accountTypeCode>
                </glAccount>
                <HSTAmount>0.0000</HSTAmount>
                <harmonizedTaxRate>0.0000</harmonizedTaxRate>
                <PSTAmount>0.0000</PSTAmount>
                <stateTaxRate>0.0000</stateTaxRate>
                <totalAmount>100.0000</totalAmount>
              </dispersement>
            </paymentAllocation>
          </paymentAllocations>
          <reallocatedPaymentAllocations/>
          <settlementFlowType>Recv</settlementFlowType>
          <settlementSubtype>Visa</settlementSubtype>
          <settlementType>CreditCard</settlementType>
          <transactionBasketId>93624</transactionBasketId>
          <transactionStatus>Approved</transactionStatus>
          <transactionTime>01 Oct 2018 03:16:11 PM</transactionTime>
        </transaction>
      </transactions>
    </transactionBasket>
  </body>
</integrationMessage>