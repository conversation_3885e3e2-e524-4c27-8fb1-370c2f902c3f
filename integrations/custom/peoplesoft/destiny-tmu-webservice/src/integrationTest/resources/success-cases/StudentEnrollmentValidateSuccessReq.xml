<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <transactionBasket>
      <code>1147802</code>
      <createTime>24 Oct 2019 02:13:54 PM</createTime>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description></description>
      <name></name>
      <objectId>1147803</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>5</version>
      <affiliatedGroup>
        <objectId>0</objectId>
        <version>0</version>
      </affiliatedGroup>
      <applyGstToPst>false</applyGstToPst>
      <basketCreationTime>24 Oct 2019 02:13:54 PM</basketCreationTime>
      <cslwAdjusted>false</cslwAdjusted>
      <client>
        <objectId>1147889</objectId>
        <version>6</version>
        <number>X001000</number>
        <personType>Student</personType>
      </client>
      <enrollmentEvents>
        <enrollmentEvent>
          <objectId>1147908</objectId>
          <version>4</version>
          <activityCode>Sale</activityCode>
          <courseSectionId>92678</courseSectionId>
          <creationTime>24 Oct 2019 02:13:54 PM</creationTime>
          <enrollCourseSection>
            <code>007</code>
            <createTime>16 Jul 2018 12:49:25 PM</createTime>
            <creator>
              <objectId>1</objectId>
              <version>211</version>
              <loginId>sanagal7</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </creator>
            <description>007</description>
            <name>007</name>
            <objectId>92678</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>1</objectId>
              <version>211</version>
              <loginId>sanagal7</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </updater>
            <version>19</version>
            <maximumAcademicUnit>4.0</maximumAcademicUnit>
            <activeWaitListSize>2</activeWaitListSize>
            <allowStreamlinedTransfer>false</allowStreamlinedTransfer>
            <courseSectionAssociations/>
            <associatedCourse>
              <objectId>18784</objectId>
              <version>10</version>
              <code>0001</code>
              <courseVersionNumber>0</courseVersionNumber>
              <costingUnitCode>CU0001</costingUnitCode>
              <costingUnitName>Creative Writing</costingUnitName>
              <name>The Novel</name>
              <programOfficeCode>PO0001</programOfficeCode>
              <programOfficeName>Continuing Education</programOfficeName>
            </associatedCourse>
            <sectionSchedules>
              <sectionSchedule>
                <code>92687</code>
                <createTime>16 Jul 2018 12:49:25 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>92687</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>0</version>
                <assignedInstructors/>
                <breakHours>0.5</breakHours>
                <broadcastChannelType>Live</broadcastChannelType>
                <building>
                  <code>EE1</code>
                  <createTime>21 Jun 2018 11:57:01 AM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>EEE1</name>
                  <objectId>78000</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>0</version>
                </building>
                <campus>
                  <code>CA0001</code>
                  <createTime>21 Jun 2018 11:56:41 AM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>EEE</name>
                  <objectId>77899</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>0</version>
                </campus>
                <daysOfWeek>
                  <dayOfWeek>2</dayOfWeek>
                </daysOfWeek>
                <daysOfWeekString>2</daysOfWeekString>
                <endDate>15 Jul 2019 12:00:00 AM</endDate>
                <endTimeString>PM200</endTimeString>
                <endType>date</endType>
                <externalResourceManagement>false</externalResourceManagement>
                <frequency>1</frequency>
                <hasBroadcastChannel>false</hasBroadcastChannel>
                <instructorIds/>
                <core>false</core>
                <distanceLearning>false</distanceLearning>
                <included>true</included>
                <numberOfSessions>53</numberOfSessions>
                <room>
                  <code>RM0001</code>
                  <createTime>21 Jun 2018 11:57:24 AM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>R1</name>
                  <objectId>78001</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>2</version>
                  <accesibleEntry>false</accesibleEntry>
                  <building>
                    <code>EE1</code>
                    <createTime>21 Jun 2018 11:57:01 AM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>EEE1</name>
                    <objectId>78000</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>0</objectId>
                      <version>0</version>
                    </updater>
                    <version>0</version>
                  </building>
                  <codeArea></codeArea>
                  <comments></comments>
                  <emailAddress></emailAddress>
                  <firstName1></firstName1>
                  <maximumNumberOfStudents>1000</maximumNumberOfStudents>
                  <requiredAVs/>
                  <roomConfiguations/>
                  <surName></surName>
                  <telephoneExt></telephoneExt>
                  <telephoneNumber></telephoneNumber>
                </room>
                <scheduleId>92687</scheduleId>
                <scheduleType>Lab</scheduleType>
                <courseSectionProfile>
                  <objectId>92678</objectId>
                  <version>19</version>
                  <campusFisId>1334455</campusFisId>
                  <associatedSemester>
                    <objectId>75515</objectId>
                    <version>42</version>
                    <academicYear>
                      <objectId>18764</objectId>
                      <version>0</version>
                      <name>2017-2018</name>
                    </academicYear>
                    <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
                    <campusId>11892</campusId>
                    <code>11892</code>
                    <endDate>01 Aug 2018 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
                    <name>11892</name>
                  </associatedSemester>
                </courseSectionProfile>
                <sessionDates>
                  <sessionDate>16 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>23 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>30 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>06 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>13 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>20 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>27 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>03 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>10 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>17 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>24 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>01 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>08 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>15 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>22 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>29 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>05 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>12 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>19 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>26 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>03 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>10 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>17 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>24 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>31 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>07 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>14 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>21 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>28 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>04 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>11 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>18 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>25 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>04 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>11 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>18 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>25 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>01 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>08 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>15 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>22 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>29 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>06 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>13 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>20 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>27 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>03 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>10 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>17 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>24 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>01 Jul 2019 12:00:00 AM</sessionDate>
                  <sessionDate>08 Jul 2019 12:00:00 AM</sessionDate>
                  <sessionDate>15 Jul 2019 12:00:00 AM</sessionDate>
                </sessionDates>
                <startDate>16 Jul 2018 12:00:00 AM</startDate>
                <startTimeString>PM100</startTimeString>
              </sectionSchedule>
            </sectionSchedules>
            <courseSectionFees>
              <courseSectionFee>
                <objectId>92684</objectId>
                <version>11</version>
                <federalTaxApplicable>false</federalTaxApplicable>
                <flatFeeEnabled>true</flatFeeEnabled>
                <hstApplicable>false</hstApplicable>
                <nonBroadcastSection>false</nonBroadcastSection>
                <stateTaxApplicable>false</stateTaxApplicable>
                <taxRefundEligible>true</taxRefundEligible>
                <tuitionProfileEnabled>true</tuitionProfileEnabled>
                <associatedSection>
                  <objectId>92678</objectId>
                  <version>19</version>
                  <campusFisId>1334455</campusFisId>
                  <associatedSemester>
                    <objectId>75515</objectId>
                    <version>42</version>
                    <academicYear>
                      <objectId>18764</objectId>
                      <version>0</version>
                      <name>2017-2018</name>
                    </academicYear>
                    <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
                    <campusId>11892</campusId>
                    <code>11892</code>
                    <endDate>01 Aug 2018 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
                    <name>11892</name>
                  </associatedSemester>
                </associatedSection>
                <associatedSectionFeeTuitionProfiles>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>TPINTL</code>
                      <createTime>31 Dec 2018 10:51:54 PM</createTime>
                      <creator>
                        <objectId>1</objectId>
                        <version>211</version>
                        <loginId>sanagal7</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </creator>
                      <description>External Fee: International</description>
                      <name>External Fee: International</name>
                      <objectId>101</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>1</objectId>
                        <version>211</version>
                        <loginId>sanagal7</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </updater>
                      <version>13</version>
                      <feeCategory>ExternalFee</feeCategory>
                      <printCode>International Fee TP</printCode>
                      <udfValues/>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDate>02/01/2019 12:00 AM</effectiveDate>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <effectiveDatsBeforeSectionStart>0</effectiveDatsBeforeSectionStart>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateBeforeStart>0</expiryDateBeforeStart>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>false</groupPayAllowed>
                      <internalDescription>Placeholder for international fee retrieved on checkout
                        from an external source. The fee component amount will be overridden with
                        the externally retrieved amount.
                      </internalDescription>
                      <paymentMethods/>
                      <publicDescription></publicDescription>
                      <publishCode>International Fee TP</publishCode>
                      <reasonsForNotAccepting>
                        <reasonForNotAccepting>Staff override</reasonForNotAccepting>
                        <reasonForNotAccepting>Status Update</reasonForNotAccepting>
                        <reasonForNotAccepting>externalFeeOverride</reasonForNotAccepting>
                      </reasonsForNotAccepting>
                      <specialRequests>
                        <specialRequest>
                          <specialRequest>
                            <code>SR0013</code>
                            <createTime>10 Jul 2019 05:03:13 PM</createTime>
                            <creator>
                              <objectId>1</objectId>
                              <version>211</version>
                              <loginId>sanagal7</loginId>
                              <personType>Staff</personType>
                              <printName>Super User</printName>
                            </creator>
                            <description>CESAR (2019-2020)=1</description>
                            <name>SR0013</name>
                            <objectId>1104197</objectId>
                            <objectStatusCode>active</objectStatusCode>
                            <updater>
                              <objectId>1</objectId>
                              <version>211</version>
                              <loginId>sanagal7</loginId>
                              <personType>Staff</personType>
                              <printName>Super User</printName>
                            </updater>
                            <version>0</version>
                            <amount>7.65</amount>
                            <feeCategory></feeCategory>
                            <printCode>CESAR (2019-2020)</printCode>
                            <udfValues/>
                            <amountDetails>
                              <amountDetail>
                                <accountMappingId>18775</accountMappingId>
                                <amount>7.65</amount>
                                <changeReason></changeReason>
                                <deptIncetiveCalc>false</deptIncetiveCalc>
                                <externallyPayable>false</externallyPayable>
                                <glAccountId>18769</glAccountId>
                                <name>CESAR (2019-2020)=1</name>
                                <taxCreditEligible>false</taxCreditEligible>
                              </amountDetail>
                            </amountDetails>
                            <feeType>SpecialRequest</feeType>
                            <gstApplicable>false</gstApplicable>
                            <pstApplicable>false</pstApplicable>
                            <specialRequestType>************</specialRequestType>
                          </specialRequest>
                          <studentType/>
                        </specialRequest>
                      </specialRequests>
                      <studentCategories/>
                      <studentPayAllowed>true</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>373440</code>
                          <createTime>03 Jan 2019 12:49:11 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>373440</name>
                          <objectId>373440</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>11</version>
                          <printCode>International Fee TP</printCode>
                          <udfValues/>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>0.00</amount>
                              <discountable>true</discountable>
                              <name>IntFee</name>
                              <revenueGLAccount>
                                <objectId>18769</objectId>
                                <version>3</version>
                                <accountName>Default Revenue</accountName>
                                <accountNumber>123456</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator></operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>creditInterval</creditType>
                    <markedAsFlatFee>false</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>92685</code>
                      <createTime>16 Jul 2018 12:49:24 PM</createTime>
                      <creator>
                        <objectId>1</objectId>
                        <version>211</version>
                        <loginId>sanagal7</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </creator>
                      <description></description>
                      <name></name>
                      <objectId>92685</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>1</objectId>
                        <version>211</version>
                        <loginId>sanagal7</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </updater>
                      <version>3</version>
                      <feeCategory></feeCategory>
                      <printCode>F11</printCode>
                      <udfValues/>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>true</groupPayAllowed>
                      <paymentMethods/>
                      <publicDescription>FeeSection</publicDescription>
                      <publishCode>F11</publishCode>
                      <reasonsForNotAccepting/>
                      <specialRequests/>
                      <studentCategories/>
                      <studentPayAllowed>false</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>92686</code>
                          <createTime>16 Jul 2018 12:49:24 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>92686</name>
                          <objectId>92686</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>3</version>
                          <printCode>F11</printCode>
                          <udfValues/>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>10.00</amount>
                              <discountable>true</discountable>
                              <name>Ffee</name>
                              <revenueGLAccount>
                                <objectId>18769</objectId>
                                <version>3</version>
                                <accountName>Default Revenue</accountName>
                                <accountNumber>123456</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator></operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>nonCredit</creditType>
                    <markedAsFlatFee>true</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                </associatedSectionFeeTuitionProfiles>
                <creator>
                  <objectId>0</objectId>
                  <version>0</version>
                </creator>
                <udfValues/>
                <updater>
                  <objectId>0</objectId>
                  <version>0</version>
                </updater>
              </courseSectionFee>
            </courseSectionFees>
            <associatedSemester>
              <objectId>75515</objectId>
              <version>42</version>
              <academicYear>
                <objectId>18764</objectId>
                <version>0</version>
                <name>2017-2018</name>
              </academicYear>
              <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
              <campusId>11892</campusId>
              <code>11892</code>
              <endDate>01 Aug 2018 12:00:00 AM</endDate>
              <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
              <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
              <name>11892</name>
            </associatedSemester>
            <associatedSpecialRequests/>
            <autoFilledInCompletionDate>false</autoFilledInCompletionDate>
            <campusFisId>1334455</campusFisId>
            <courseInstructionMethod>All</courseInstructionMethod>
            <sectionStatusCode>final_approval</sectionStatusCode>
            <discounts/>
            <distanceLearning>0</distanceLearning>
            <dueDateRule>
              <objectId>92750</objectId>
              <version>7</version>
              <daysAfterEnroll>10</daysAfterEnroll>
              <daysPerConcurrentEnroll>0</daysPerConcurrentEnroll>
              <dueDateRuleTypeCode/>
              <gracePeriodDays>0</gracePeriodDays>
            </dueDateRule>
            <eCollege>false</eCollege>
            <sectionEndDate>15 Jul 2019 12:00:00 AM</sectionEndDate>
            <enrolListSize>0</enrolListSize>
            <enrollmentByUpload>true</enrollmentByUpload>
            <enrollmentRestrictions/>
            <evalutionCompleted>false</evalutionCompleted>
            <evaluationNotes></evaluationNotes>
            <familyCheckoutEligible>true</familyCheckoutEligible>
            <flagSection>0</flagSection>
            <formerScsNumber>12121</formerScsNumber>
            <gradeLevels/>
            <sectionHistoryIndicatorCode>newproposal</sectionHistoryIndicatorCode>
            <ipIssuesResovled>1</ipIssuesResovled>
            <immediateApprovalRequired>0</immediateApprovalRequired>
            <InstructionMethods/>
            <instructorContracts>
              <instructorContract>
                <code>92689</code>
                <createTime>16 Jul 2018 12:49:24 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>92689</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>8</version>
                <contractStatus>Proposed</contractStatus>
                <courseSectionProfile>
                  <objectId>92678</objectId>
                  <version>19</version>
                  <campusFisId>1334455</campusFisId>
                  <associatedSemester>
                    <objectId>75515</objectId>
                    <version>42</version>
                    <academicYear>
                      <objectId>18764</objectId>
                      <version>0</version>
                      <name>2017-2018</name>
                    </academicYear>
                    <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
                    <campusId>11892</campusId>
                    <code>11892</code>
                    <endDate>01 Aug 2018 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
                    <name>11892</name>
                  </associatedSemester>
                  <course>
                    <objectId>18784</objectId>
                    <version>10</version>
                    <code>0001</code>
                    <courseVersionNumber>0</courseVersionNumber>
                    <costingUnitCode>CU0001</costingUnitCode>
                    <costingUnitName>Creative Writing</costingUnitName>
                    <name>The Novel</name>
                    <programOfficeCode>PO0001</programOfficeCode>
                    <programOfficeName>Continuing Education</programOfficeName>
                  </course>
                  <sectionCode>007</sectionCode>
                  <sectionTitle>The Novel Section</sectionTitle>
                  <udfValues>
                    <udfValue>
                      <objectId>92698</objectId>
                      <version>4</version>
                      <udfFieldSpec>
                        <objectId>1000</objectId>
                        <version>1</version>
                        <udfFieldName>sasCapacity</udfFieldName>
                      </udfFieldSpec>
                      <udfFieldValue></udfFieldValue>
                    </udfValue>
                    <udfValue>
                      <objectId>92699</objectId>
                      <version>4</version>
                      <udfFieldSpec>
                        <objectId>2000</objectId>
                        <version>1</version>
                        <udfFieldName>SessionCode</udfFieldName>
                      </udfFieldSpec>
                      <udfFieldValue></udfFieldValue>
                    </udfValue>
                  </udfValues>
                </courseSectionProfile>
                <instructor>
                  <code>code72838</code>
                  <createTime>08 Jun 2018 01:50:02 PM</createTime>
                  <creator>
                    <objectId>1</objectId>
                    <version>211</version>
                    <loginId>sanagal7</loginId>
                    <personType>Staff</personType>
                    <printName>Super User</printName>
                  </creator>
                  <description></description>
                  <name>Syed Abbas</name>
                  <objectId>72838</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>1</objectId>
                    <version>211</version>
                    <loginId>sanagal7</loginId>
                    <personType>Staff</personType>
                    <printName>Super User</printName>
                  </updater>
                  <version>17</version>
                  <addresses>
                    <address>
                      <typeCode>Home</typeCode>
                      <city>asdfasdf</city>
                      <country>USA</country>
                      <effectiveDate>08 Jun 2018 01:49:38 PM</effectiveDate>
                      <foreign>false</foreign>
                      <foreignState></foreignState>
                      <modifiedDate>08 Jun 2018 01:49:38 PM</modifiedDate>
                      <preferred>true</preferred>
                      <provinceState>AE</provinceState>
                      <returnMail>false</returnMail>
                      <street0></street0>
                      <street1>asdf</street1>
                      <street2></street2>
                      <terminate>false</terminate>
                      <postalZip>23511</postalZip>
                    </address>
                  </addresses>
                  <alternateName/>
                  <birthDate>03 Jun 2018 12:00:00 AM</birthDate>
                  <cautionaryRequired>false</cautionaryRequired>
                  <comments/>
                  <communicationMethod>Email</communicationMethod>
                  <contactMethods/>
                  <emails>
                    <email>
                      <emailAddress><EMAIL></emailAddress>
                      <typeCode>Standard</typeCode>
                      <preferred>true</preferred>
                      <release>false</release>
                      <return>false</return>
                    </email>
                  </emails>
                  <socialSecurityNum></socialSecurityNum>
                  <firstName1>Syed</firstName1>
                  <firstName2></firstName2>
                  <genderCode>M</genderCode>
                  <interestAreaAssociations/>
                  <lastName>Abbas</lastName>
                  <lmsPersonId>T000001</lmsPersonId>
                  <loginId>T000001</loginId>
                  <netId></netId>
                  <personNumber>T000001</personNumber>
                  <preferredContactTime></preferredContactTime>
                  <preferredEmail>
                    <emailAddress><EMAIL></emailAddress>
                    <typeCode>Standard</typeCode>
                    <preferred>true</preferred>
                    <release>false</release>
                    <return>false</return>
                  </preferredEmail>
                  <preferredLocaleString>en_US</preferredLocaleString>
                  <privacyQuestions/>
                  <profileHolds/>
                  <salutationCode></salutationCode>
                  <schoolPersonnelNumber>55X000779XO1</schoolPersonnelNumber>
                  <telephones>
                    <telephone>
                      <areaCode>234</areaCode>
                      <preferred>true</preferred>
                      <release>false</release>
                      <tcpaConsented>false</tcpaConsented>
                      <telephoneExt></telephoneExt>
                      <telephoneNumber>32453245</telephoneNumber>
                      <typeCode>Home</typeCode>
                    </telephone>
                  </telephones>
                </instructor>
                <role></role>
              </instructorContract>
            </instructorContracts>
            <invokedCorpContractPricing>true</invokedCorpContractPricing>
            <lectureSessionTopics></lectureSessionTopics>
            <maximumEnrollmentSize>111</maximumEnrollmentSize>
            <maximumWaitListSize>5</maximumWaitListSize>
            <maxCEUnit>2.65</maxCEUnit>
            <minimumEnrollmentSize>1</minimumEnrollmentSize>
            <minimumAcademicUnit>1.0</minimumAcademicUnit>
            <minCEUnit>0.0</minCEUnit>
            <multipleProCeditAllowed>false</multipleProCeditAllowed>
            <numberOfAssignments>0</numberOfAssignments>
            <onlineResources/>
            <onlineTransferRequestDays>7</onlineTransferRequestDays>
            <onlineWithdrawalRequestDays>7</onlineWithdrawalRequestDays>
            <optInProCredit>false</optInProCredit>
            <optionalReading></optionalReading>
            <otherLocation></otherLocation>
            <potentialIPIssues></potentialIPIssues>
            <preventOnlineEnrollment>false</preventOnlineEnrollment>
            <proctoredExams/>
            <publicizeSection>1</publicizeSection>
            <pvAvailabilityBeginDate>02 Jan 2018 01:00:00 PM</pvAvailabilityBeginDate>
            <pvAvailabilityEndDate>02 Jan 2020 01:00:00 PM</pvAvailabilityEndDate>
            <pvEnrollmentBeginDate>02 Jan 2018 01:00:00 PM</pvEnrollmentBeginDate>
            <pvEnrollmentEndDate>02 Jan 2020 01:00:00 PM</pvEnrollmentEndDate>
            <receiptNotes></receiptNotes>
            <rquiredAVList/>
            <requiredReading>Section Text reuqired</requiredReading>
            <requiredSoftwareTechnology></requiredSoftwareTechnology>
            <reservedListSize>0</reservedListSize>
            <room></room>
            <scheduleTypeCode>Undefined</scheduleTypeCode>
            <sectionContacts/>
            <sectionLMSInfo>
              <objectId>92679</objectId>
              <version>8</version>
              <lmsAccessPeriod>7</lmsAccessPeriod>
              <lmsSectionId></lmsSectionId>
              <lmsType></lmsType>
            </sectionLMSInfo>
            <sectionMaterials/>
            <sectionNotes></sectionNotes>
            <sectionNotesInternal></sectionNotesInternal>
            <sectionTitle>The Novel Section</sectionTitle>
            <serviceCharges/>
            <sectionStartDate>16 Jul 2018 12:00:00 AM</sectionStartDate>
            <completionRuleTypeCode>CompleteOnSchEndDate</completionRuleTypeCode>
            <svEnrollmentBeginDate>02 Jan 2018 01:00:00 PM</svEnrollmentBeginDate>
            <svEnrollmentDate>02 Jan 2020 01:00:00 PM</svEnrollmentDate>
            <timeCategory>regular</timeCategory>
            <totalSessionHours>26.5</totalSessionHours>
            <transcriptTitle></transcriptTitle>
            <udfValues>
              <udfValue>
                <objectId>92698</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>1000</objectId>
                  <version>1</version>
                  <udfFieldName>sasCapacity</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>92699</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>2000</objectId>
                  <version>1</version>
                  <udfFieldName>SessionCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
            </udfValues>
            <waitListSize>0</waitListSize>
          </enrollCourseSection>
          <feeLWs>
            <feeLW>
              <objectId>1147919</objectId>
              <version>2</version>
              <enrolment>
                <objectId>1147908</objectId>
                <version>4</version>
              </enrolment>
              <creationTime>24 Oct 2019 02:13:54 PM</creationTime>
              <dispersements/>
              <fee xsi:type="tuitionProfile" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <code>92685</code>
                <createTime>16 Jul 2018 12:49:24 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>92685</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>211</version>
                  <loginId>sanagal7</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>3</version>
                <feeCategory></feeCategory>
                <printCode>F11</printCode>
                <udfValues/>
                <anyInstructionMethod>true</anyInstructionMethod>
                <anyPaymentMethod>true</anyPaymentMethod>
                <anyStudentCategory>true</anyStudentCategory>
                <applicability>public</applicability>
                <basis>FlatFee</basis>
                <effectiveDateOption>byDate</effectiveDateOption>
                <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                <expiryDateOption>neverExpire</expiryDateOption>
                <groupPayAllowed>true</groupPayAllowed>
                <paymentMethods/>
                <publicDescription>FeeSection</publicDescription>
                <publishCode>F11</publishCode>
                <reasonsForNotAccepting/>
                <specialRequests/>
                <studentCategories/>
                <studentPayAllowed>false</studentPayAllowed>
                <tuitionFees>
                  <tuitionFee>
                    <code>92686</code>
                    <createTime>16 Jul 2018 12:49:24 PM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>92686</name>
                    <objectId>92686</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>0</objectId>
                      <version>0</version>
                    </updater>
                    <version>3</version>
                    <printCode>F11</printCode>
                    <udfValues/>
                    <tuitionFeeItems>
                      <tuitionFeeItem>
                        <amount>10.00</amount>
                        <discountable>true</discountable>
                        <name>Ffee</name>
                        <revenueGLAccount>
                          <objectId>18769</objectId>
                          <version>3</version>
                          <accountName>Default Revenue</accountName>
                          <accountNumber>123456</accountNumber>
                          <accountTypeCode>revenue</accountTypeCode>
                        </revenueGLAccount>
                        <surchargeable>true</surchargeable>
                      </tuitionFeeItem>
                    </tuitionFeeItems>
                    <operator></operator>
                    <useStepFunction>false</useStepFunction>
                  </tuitionFee>
                </tuitionFees>
              </fee>
              <feeGroup>TuitionProfile</feeGroup>
              <quantity>1</quantity>
              <totalAmount>916.91</totalAmount>
            </feeLW>
          </feeLWs>
          <numOfUnits>0.0</numOfUnits>
          <originalTransactionBasketId>-1</originalTransactionBasketId>
          <student>
            <code>code1147889</code>
            <createTime>24 Oct 2019 02:43:36 PM</createTime>
            <creator>
              <objectId>2</objectId>
              <version>19</version>
              <loginId>webreg</loginId>
              <personType>Staff</personType>
              <printName>Web Registrar</printName>
            </creator>
            <description></description>
            <name>Loris Nina</name>
            <objectId>1147889</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>2</objectId>
              <version>19</version>
              <loginId>webreg</loginId>
              <personType>Staff</personType>
              <printName>Web Registrar</printName>
            </updater>
            <version>6</version>
            <addresses>
              <address>
                <typeCode>Home</typeCode>
                <city>Tehran</city>
                <country>IRN</country>
                <effectiveDate>24 Oct 2019 03:01:39 PM</effectiveDate>
                <foreign>false</foreign>
                <foreignState></foreignState>
                <modifiedDate>24 Oct 2019 03:01:39 PM</modifiedDate>
                <preferred>true</preferred>
                <provinceState></provinceState>
                <returnMail>false</returnMail>
                <street0></street0>
                <street1>848 Lana Road</street1>
                <street2>Zaferanieh St.,</street2>
                <terminate>false</terminate>
                <postalZip></postalZip>
              </address>
            </addresses>
            <alternateName/>
            <birthDate>08 Jan 1993 12:00:00 AM</birthDate>
            <cautionaryRequired>false</cautionaryRequired>
            <comments/>
            <communicationMethod>Email</communicationMethod>
            <contactMethods>
              <contactMethod>Any</contactMethod>
            </contactMethods>
            <emails>
              <email>
                <emailAddress><EMAIL></emailAddress>
                <typeCode>Standard</typeCode>
                <preferred>true</preferred>
                <release>false</release>
                <return>false</return>
              </email>
            </emails>
            <socialSecurityNum></socialSecurityNum>
            <firstName1>Loris</firstName1>
            <firstName2></firstName2>
            <interestAreaAssociations/>
            <lastName>Nina</lastName>
            <loginId>RUCE_X001000</loginId>
            <netId>12345</netId>
            <personNumber>X001000</personNumber>
            <preferredEmail>
              <emailAddress><EMAIL></emailAddress>
              <typeCode>Standard</typeCode>
              <preferred>true</preferred>
              <release>false</release>
              <return>false</return>
            </preferredEmail>
            <preferredLocaleString>en_US</preferredLocaleString>
            <privacyQuestions>
              <privacyQuestion>
                <objectId>1147935</objectId>
                <version>0</version>
                <answer>aa</answer>
                <question>0</question>
              </privacyQuestion>
              <privacyQuestion>
                <objectId>1147936</objectId>
                <version>0</version>
                <answer>aa</answer>
                <question>1</question>
              </privacyQuestion>
            </privacyQuestions>
            <profileHolds/>
            <salutationCode>Ms</salutationCode>
            <schoolPersonnelNumber>50502345</schoolPersonnelNumber>
            <telephones>
              <telephone>
                <areaCode>416</areaCode>
                <preferred>true</preferred>
                <release>false</release>
                <tcpaConsented>false</tcpaConsented>
                <telephoneExt></telephoneExt>
                <telephoneNumber>9795000</telephoneNumber>
                <typeCode>Home</typeCode>
              </telephone>
            </telephones>
            <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
            <additionalAssociation></additionalAssociation>
            <copyOfVisa>false</copyOfVisa>
            <department></department>
            <directBillingAccount></directBillingAccount>
            <documentEmailed>false</documentEmailed>
            <documentFaxed>false</documentFaxed>
            <educationHistory/>
            <emergencyContactEmail></emergencyContactEmail>
            <emergencyContactName></emergencyContactName>
            <emergencyContactRelationship></emergencyContactRelationship>
            <employerEmail></employerEmail>
            <employerName></employerName>
            <employerSalutation></employerSalutation>
            <enrollmentRaces/>
            <enrollmentTimeframes/>
            <enrolmentGroups/>
            <internationalStudent>false</internationalStudent>
            <jobTitle></jobTitle>
            <laterLifeLearner>false</laterLifeLearner>
            <learningGoals/>
            <otherNames></otherNames>
            <proficiencyExamScores/>
            <profileStatus>Active</profileStatus>
            <studentAssociationDetails/>
            <studentCategories>
              <studentCategory>
                <category>Domestic</category>
              </studentCategory>
            </studentCategories>
            <studentCredentials/>
            <studentNumber>X001000</studentNumber>
            <udfValues>
              <udfValue>
                <objectId>1147891</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1005</objectId>
                  <version>1</version>
                  <udfFieldName>citizenshipStatus</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147892</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1010</objectId>
                  <version>1</version>
                  <udfFieldName>studentGender</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147893</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1002</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceQuestion</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147894</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1001</objectId>
                  <version>1</version>
                  <udfFieldName>oenNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147895</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1003</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceYear</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147896</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1004</objectId>
                  <version>1</version>
                  <udfFieldName>existingStudentNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147897</objectId>
                <version>2</version>
                <udfFieldSpec>
                  <objectId>1006</objectId>
                  <version>1</version>
                  <udfFieldName>dateOfEntry</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147898</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1007</objectId>
                  <version>1</version>
                  <udfFieldName>countryOfCitizenship</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147899</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1008</objectId>
                  <version>1</version>
                  <udfFieldName>certificate</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>1147900</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1009</objectId>
                  <version>1</version>
                  <udfFieldName>certificateApprovalCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
            </udfValues>
            <willStudyInCountry>false</willStudyInCountry>
            <willTravelToCountry>false</willTravelToCountry>
            <willWorkInCountry>false</willWorkInCountry>
            <youthParticipant>false</youthParticipant>
          </student>
          <amount>916.91</amount>
          <transactionBasketId>1147803</transactionBasketId>
          <voided>false</voided>
          <withdrawn>false</withdrawn>
        </enrollmentEvent>
      </enrollmentEvents>
      <migrated>false</migrated>
      <newSessionTransactions/>
      <basketNumber>Temp1147803</basketNumber>
      <saleEvents/>
      <sessionAdjustedTransactions/>
      <settlementClient>
        <objectId>1147889</objectId>
        <version>6</version>
        <loginId>RUCE_X001000</loginId>
        <personNumber>X001000</personNumber>
        <personType>Student</personType>
        <printName>Sara test</printName>
      </settlementClient>
      <status>Unprocessed</status>
      <transactionAmount>0</transactionAmount>
      <transactionBasketType>Single</transactionBasketType>
      <transactions/>
    </transactionBasket>
  </body>
</integrationMessage>