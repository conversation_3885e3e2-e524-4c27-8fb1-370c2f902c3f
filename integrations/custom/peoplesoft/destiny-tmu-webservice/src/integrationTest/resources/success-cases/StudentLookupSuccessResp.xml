<?xml version='1.0' encoding='UTF-8'?>
<integrationMessage>
  <body>
    <integrationActionResult>
      <status>SC0000</status>
      <results>
        <result>
          <status>SC0000</status>
          <entity>
            <student>
              <schoolPersonnelNumber>1</schoolPersonnelNumber>
            </student>
          </entity>
          <entityType>student</entityType>
          <entityInXMLFormat>true</entityInXMLFormat>
          <errorMsg>
          </errorMsg>
        </result>
      </results>
      <response>
        <RU_D1_CREATEWEBID_RESPONSE>
          <STUDENTID>1</STUDENTID>
          <SEARCH_ORDER_NUMBER>0</SEARCH_ORDER_NUMBER>
          <MESSAGE>Student Created</MESSAGE>
          <TRANSACTION_NO>MOCK1234567891011121314151617181920</TRANSACTION_NO>
          <original>&lt;?xml version='1.0' encoding='UTF-8'?&gt;
            &lt;RU_D1_CREATEWEBID_REQUEST&gt;&lt;RU_PREV_ATT&gt;Y&lt;/RU_PREV_ATT&gt;&lt;RU_PREV_YR/&gt;&lt;TITLE&gt;Fr&lt;/TITLE&gt;&lt;FIRST_NAME&gt;Jimmy&lt;/FIRST_NAME&gt;&lt;MIDDLE_NAME&gt;&lt;/MIDDLE_NAME&gt;&lt;LAST_NAME&gt;Beam&lt;/LAST_NAME&gt;&lt;FIRST_NAME_CD/&gt;&lt;LAST_NAME_CD&gt;&lt;/LAST_NAME_CD&gt;&lt;ADDRESS1&gt;111
            Main Street&lt;/ADDRESS1&gt;&lt;ADDRESS2&gt;1&lt;/ADDRESS2&gt;&lt;CITY&gt;Toronto&lt;/CITY&gt;&lt;PROVINCE&gt;&lt;/PROVINCE&gt;&lt;HOMECOUNTRY/&gt;&lt;POSTAL&gt;91210&lt;/POSTAL&gt;&lt;HOME_PHONE&gt;(416)5555555&lt;/HOME_PHONE&gt;&lt;BUS_PHONE&gt;&lt;/BUS_PHONE&gt;&lt;EMAIL_ADDR&gt;<EMAIL>&lt;/EMAIL_ADDR&gt;&lt;ACCEPT_PB&gt;Y&lt;/ACCEPT_PB&gt;&lt;MAR_STATUS/&gt;&lt;LANG_CD/&gt;&lt;SEX&gt;M&lt;/SEX&gt;&lt;BIRTHDATE&gt;2000-10-28&lt;/BIRTHDATE&gt;&lt;RESIDENCY&gt;0&lt;/RESIDENCY&gt;&lt;ENTRY_DATE&gt;&lt;/ENTRY_DATE&gt;&lt;COUNTRY&gt;XYZ&lt;/COUNTRY&gt;&lt;IP&gt;NOIP&lt;/IP&gt;&lt;EXTENSION&gt;&lt;/EXTENSION&gt;&lt;RU_OEN_NBR/&gt;&lt;OPTION&gt;REG&lt;/OPTION&gt;&lt;ACAD_PROG&gt;NOCER&lt;/ACAD_PROG&gt;&lt;RU_CERT_APPR_CD&gt;0&lt;/RU_CERT_APPR_CD&gt;&lt;STUDENTID_VERIFY/&gt;&lt;RU_DESTINY_ID&gt;X000072&lt;/RU_DESTINY_ID&gt;&lt;/RU_D1_CREATEWEBID_REQUEST&gt;
          </original>
          <origin>MOCK_RU_D1_CREATEWEBID</origin>
        </RU_D1_CREATEWEBID_RESPONSE>
      </response>
    </integrationActionResult>
  </body>
</integrationMessage>