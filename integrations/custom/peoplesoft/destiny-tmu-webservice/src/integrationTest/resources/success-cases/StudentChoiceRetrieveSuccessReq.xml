<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <transactionBasket>
      <code></code>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description></description>
      <name></name>
      <objectId>-1</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>0</version>
      <affiliatedGroup>
        <objectId>0</objectId>
        <version>0</version>
      </affiliatedGroup>
      <applyGstToPst>false</applyGstToPst>
      <basketCreationTime>17 May 2019 12:41:38 PM</basketCreationTime>
      <cslwAdjusted>false</cslwAdjusted>
      <enrollmentEvents>
        <enrollmentEvent>
          <objectId>1025001</objectId>
          <version>4</version>
          <activityCode>Sale</activityCode>
          <courseSectionId>142794</courseSectionId>
          <creationTime>12 Nov 2019 03:00:28 PM</creationTime>
          <enrollCourseSection>
            <code>012</code>
            <createTime>05 Mar 2019 01:59:59 PM</createTime>
            <creator>
              <objectId>22912</objectId>
              <version>1</version>
              <loginId>parul.chaturvedi</loginId>
              <personNumber></personNumber>
              <personType>Staff</personType>
              <printName>Parul</printName>
            </creator>
            <description>012</description>
            <name>012</name>
            <objectId>142794</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>6</objectId>
              <version>3</version>
              <loginId>websvc</loginId>
              <personType>Staff</personType>
              <printName>Web Service User</printName>
            </updater>
            <version>16</version>
            <maximumAcademicUnit>0.0</maximumAcademicUnit>
            <activeWaitListSize>0</activeWaitListSize>
            <courseSectionAssociations/>
            <associatedCourse>
              <objectId>24019</objectId>
              <version>4</version>
              <code>COHS 718</code>
              <courseVersionNumber>0</courseVersionNumber>
              <costingUnitCode>CU0004</costingUnitCode>
              <costingUnitName>20014 Com Serv Prog Office</costingUnitName>
              <name>Systems Management I</name>
              <programOfficeCode>PO0004</programOfficeCode>
              <programOfficeName>Community Services</programOfficeName>
            </associatedCourse>
            <sectionSchedules>
              <sectionSchedule>
                <code>142800</code>
                <createTime>05 Mar 2019 01:59:59 PM</createTime>
                <creator>
                  <objectId>22912</objectId>
                  <version>1</version>
                  <loginId>parul.chaturvedi</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Parul</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>142800</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>170849</objectId>
                  <version>1</version>
                  <loginId>rosereid</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Rose Reid</printName>
                </updater>
                <version>4</version>
                <assignedInstructors/>
                <breakHours>0.0</breakHours>
                <broadcastChannelType>Live</broadcastChannelType>
                <daysOfWeek/>
                <daysOfWeekString></daysOfWeekString>
                <endDate>18 Apr 2020 12:00:00 AM</endDate>
                <endTimeString></endTimeString>
                <endType>date</endType>
                <externalResourceManagement>false</externalResourceManagement>
                <frequency>1</frequency>
                <hasBroadcastChannel>false</hasBroadcastChannel>
                <instructorIds/>
                <core>false</core>
                <distanceLearning>true</distanceLearning>
                <included>true</included>
                <numberOfSessions>1</numberOfSessions>
                <scheduleId>142800</scheduleId>
                <scheduleType>LEC</scheduleType>
                <courseSectionProfile>
                  <objectId>142794</objectId>
                  <version>16</version>
                  <campusFisId>5662</campusFisId>
                  <associatedSemester>
                    <objectId>18410</objectId>
                    <version>0</version>
                    <academicYear>
                      <objectId>18404</objectId>
                      <version>0</version>
                      <name>2019-2020</name>
                    </academicYear>
                    <beginDate>02 Nov 2019 12:00:00 AM</beginDate>
                    <campusId>1201</campusId>
                    <code>Winter 2020</code>
                    <endDate>30 Apr 2020 12:00:00 AM</endDate>
                    <lmsExportBeginDate>02 Nov 2019 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Apr 2020 12:00:00 AM</lmsExportEndDate>
                    <name>Winter 2020</name>
                  </associatedSemester>
                </courseSectionProfile>
                <sessionDates>
                  <sessionDate>14 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>26 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>02 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>09 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>16 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>23 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>02 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>09 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>16 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>23 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>30 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>06 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>13 Apr 2019 12:00:00 AM</sessionDate>
                </sessionDates>
                <startDate>13 Jan 2020 12:00:00 AM</startDate>
                <startTimeString></startTimeString>
              </sectionSchedule>
            </sectionSchedules>
            <courseSectionFees>
              <courseSectionFee>
                <objectId>142803</objectId>
                <version>0</version>
                <federalTaxApplicable>false</federalTaxApplicable>
                <flatFeeEnabled>true</flatFeeEnabled>
                <hstApplicable>false</hstApplicable>
                <nonBroadcastSection>false</nonBroadcastSection>
                <stateTaxApplicable>false</stateTaxApplicable>
                <taxRefundEligible>true</taxRefundEligible>
                <tuitionProfileEnabled>true</tuitionProfileEnabled>
                <associatedSection>
                  <objectId>142794</objectId>
                  <version>16</version>
                  <campusFisId>5662</campusFisId>
                  <associatedSemester>
                    <objectId>18410</objectId>
                    <version>0</version>
                    <academicYear>
                      <objectId>18404</objectId>
                      <version>0</version>
                      <name>2019-2020</name>
                    </academicYear>
                    <beginDate>02 Nov 2019 12:00:00 AM</beginDate>
                    <campusId>1201</campusId>
                    <code>Winter 2020</code>
                    <endDate>30 Apr 2020 12:00:00 AM</endDate>
                    <lmsExportBeginDate>02 Nov 2019 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Apr 2020 12:00:00 AM</lmsExportEndDate>
                    <name>Winter 2020</name>
                  </associatedSemester>
                </associatedSection>
                <associatedSectionFeeTuitionProfiles>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>TPINTL</code>
                      <createTime>17 Jan 2019 08:17:56 AM</createTime>
                      <creator>
                        <objectId>1</objectId>
                        <version>2</version>
                        <loginId>super</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </creator>
                      <description>External Fee: International</description>
                      <name>External Fee: International</name>
                      <objectId>101</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>22912</objectId>
                        <version>1</version>
                        <loginId>parul.chaturvedi</loginId>
                        <personNumber></personNumber>
                        <personType>Staff</personType>
                        <printName>Parul</printName>
                      </updater>
                      <version>2</version>
                      <feeCategory>International Catalog Fee</feeCategory>
                      <printCode>International Fee</printCode>
                      <udfValues/>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDate>01/01/2019 12:00 AM</effectiveDate>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <effectiveDatsBeforeSectionStart>0</effectiveDatsBeforeSectionStart>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateBeforeStart>0</expiryDateBeforeStart>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>false</groupPayAllowed>
                      <internalDescription>Placeholder for international fee retrieved on checkout
                        from an external source. The fee component amount will be overridden with
                        the externally retrieved amount.
                      </internalDescription>
                      <paymentMethods/>
                      <publicDescription></publicDescription>
                      <publishCode>International Fee</publishCode>
                      <reasonsForNotAccepting>
                        <reasonForNotAccepting>Special permission</reasonForNotAccepting>
                        <reasonForNotAccepting>externalFeeOverride</reasonForNotAccepting>
                      </reasonsForNotAccepting>
                      <specialRequests/>
                      <studentCategories/>
                      <studentPayAllowed>true</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>88358</code>
                          <createTime>04 Mar 2019 06:00:06 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>88358</name>
                          <objectId>88358</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>3</version>
                          <printCode>International Fee</printCode>
                          <udfValues/>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>0.00</amount>
                              <discountable>true</discountable>
                              <name>International Fee</name>
                              <revenueGLAccount>
                                <objectId>22816</objectId>
                                <version>0</version>
                                <accountName>Revenue (International)</accountName>
                                <accountNumber>1012</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator>&lt;=</operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>nonCredit</creditType>
                    <markedAsFlatFee>false</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>142801</code>
                      <createTime>05 Mar 2019 01:59:59 PM</createTime>
                      <creator>
                        <objectId>22912</objectId>
                        <version>1</version>
                        <loginId>parul.chaturvedi</loginId>
                        <personNumber></personNumber>
                        <personType>Staff</personType>
                        <printName>Parul</printName>
                      </creator>
                      <description></description>
                      <name></name>
                      <objectId>142801</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>6</objectId>
                        <version>3</version>
                        <loginId>websvc</loginId>
                        <personType>Staff</personType>
                        <printName>Web Service User</printName>
                      </updater>
                      <version>1</version>
                      <printCode>Domestic Fee</printCode>
                      <udfValues/>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>true</groupPayAllowed>
                      <paymentMethods/>
                      <publicDescription>Domestic Catalog Fee</publicDescription>
                      <publishCode>Domestic Fee</publishCode>
                      <reasonsForNotAccepting>
                        <reasonForNotAccepting>staff</reasonForNotAccepting>
                      </reasonsForNotAccepting>
                      <specialRequests/>
                      <studentCategories/>
                      <studentPayAllowed>false</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>56614</code>
                          <createTime>04 Mar 2019 04:43:43 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>56614</name>
                          <objectId>142802</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>6</objectId>
                            <version>3</version>
                            <loginId>websvc</loginId>
                            <personType>Staff</personType>
                            <printName>Web Service User</printName>
                          </updater>
                          <version>1</version>
                          <printCode>Domestic Fee</printCode>
                          <udfValues/>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>575.79</amount>
                              <discountable>true</discountable>
                              <name>Domestic Fee</name>
                              <revenueGLAccount>
                                <objectId>18501</objectId>
                                <version>1</version>
                                <accountName>Revenue (Domestic)</accountName>
                                <accountNumber>1011</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator>&lt;=</operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>nonCredit</creditType>
                    <markedAsFlatFee>true</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                </associatedSectionFeeTuitionProfiles>
                <creator>
                  <objectId>0</objectId>
                  <version>0</version>
                </creator>
                <udfValues/>
                <updater>
                  <objectId>0</objectId>
                  <version>0</version>
                </updater>
              </courseSectionFee>
            </courseSectionFees>
            <associatedSemester>
              <objectId>18410</objectId>
              <version>0</version>
              <academicYear>
                <objectId>18404</objectId>
                <version>0</version>
                <name>2019-2020</name>
              </academicYear>
              <beginDate>02 Nov 2019 12:00:00 AM</beginDate>
              <campusId>1201</campusId>
              <code>Winter 2020</code>
              <endDate>30 Apr 2020 12:00:00 AM</endDate>
              <lmsExportBeginDate>02 Nov 2019 12:00:00 AM</lmsExportBeginDate>
              <lmsExportEndDate>30 Apr 2020 12:00:00 AM</lmsExportEndDate>
              <name>Winter 2020</name>
            </associatedSemester>
            <associatedSpecialRequests>
              <associatedSpecialRequest>
                <associatedSpecialRequest>
                  <code>SR0004</code>
                  <createTime>28 Jun 2019 02:34:22 PM</createTime>
                  <creator>
                    <objectId>22914</objectId>
                    <version>1</version>
                    <loginId>serena.wong</loginId>
                    <personNumber></personNumber>
                    <personType>Staff</personType>
                    <printName>Serena</printName>
                  </creator>
                  <description>CESAR (2019-2020)=1</description>
                  <name>SR0004</name>
                  <objectId>286582</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>22912</objectId>
                    <version>1</version>
                    <loginId>parul.chaturvedi</loginId>
                    <personNumber></personNumber>
                    <personType>Staff</personType>
                    <printName>Parul</printName>
                  </updater>
                  <version>2</version>
                  <amount>7.65</amount>
                  <feeCategory>Optional Fee</feeCategory>
                  <printCode>CESAR (AY2019-2020)</printCode>
                  <udfValues/>
                  <amountDetails>
                    <amountDetail>
                      <accountMappingId>18503</accountMappingId>
                      <amount>7.65</amount>
                      <changeReason></changeReason>
                      <deptIncetiveCalc>false</deptIncetiveCalc>
                      <externallyPayable>false</externallyPayable>
                      <glAccountId>282117</glAccountId>
                      <name>CESAR (2019-2020)=1</name>
                      <taxCreditEligible>false</taxCreditEligible>
                    </amountDetail>
                  </amountDetails>
                  <feeType>SpecialRequest</feeType>
                  <gstApplicable>false</gstApplicable>
                  <pstApplicable>false</pstApplicable>
                  <specialRequestType>************</specialRequestType>
                </associatedSpecialRequest>
                <conditionalSelectionMode/>
                <defaultSelectedWhenOptional>true</defaultSelectedWhenOptional>
                <publicViewSelectionMode>Conditional</publicViewSelectionMode>
              </associatedSpecialRequest>
              <associatedSpecialRequest>
                <associatedSpecialRequest>
                  <code>SR0012</code>
                  <createTime>28 Jun 2019 02:42:38 PM</createTime>
                  <creator>
                    <objectId>22914</objectId>
                    <version>1</version>
                    <loginId>serena.wong</loginId>
                    <personNumber></personNumber>
                    <personType>Staff</personType>
                    <printName>Serena</printName>
                  </creator>
                  <description>CFS (2019-2020)=1</description>
                  <name>SR0012</name>
                  <objectId>286592</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>22914</objectId>
                    <version>1</version>
                    <loginId>serena.wong</loginId>
                    <personNumber></personNumber>
                    <personType>Staff</personType>
                    <printName>Serena</printName>
                  </updater>
                  <version>2</version>
                  <amount>2.66</amount>
                  <feeCategory>Optional Fee</feeCategory>
                  <printCode>Canadian Federation of Students (CE) (AY2019-2020)</printCode>
                  <udfValues/>
                  <amountDetails>
                    <amountDetail>
                      <accountMappingId>18503</accountMappingId>
                      <amount>2.66</amount>
                      <changeReason></changeReason>
                      <deptIncetiveCalc>false</deptIncetiveCalc>
                      <externallyPayable>false</externallyPayable>
                      <glAccountId>282117</glAccountId>
                      <name>CFS (2019-2020)=1</name>
                      <taxCreditEligible>false</taxCreditEligible>
                    </amountDetail>
                  </amountDetails>
                  <feeType>SpecialRequest</feeType>
                  <gstApplicable>false</gstApplicable>
                  <pstApplicable>false</pstApplicable>
                  <specialRequestType>************</specialRequestType>
                </associatedSpecialRequest>
                <conditionalSelectionMode/>
                <defaultSelectedWhenOptional>true</defaultSelectedWhenOptional>
                <publicViewSelectionMode>Conditional</publicViewSelectionMode>
              </associatedSpecialRequest>
            </associatedSpecialRequests>
            <autoFilledInCompletionDate>false</autoFilledInCompletionDate>
            <campusFisId>5662</campusFisId>
            <courseInstructionMethod>All</courseInstructionMethod>
            <sectionStatusCode>final_approval</sectionStatusCode>
            <customSectionNumber>DJ0</customSectionNumber>
            <discounts/>
            <distanceLearning>1</distanceLearning>
            <dueDateRule>
              <objectId>142806</objectId>
              <version>4</version>
            </dueDateRule>
            <eCollege>false</eCollege>
            <sectionEndDate>18 Apr 2020 12:00:00 AM</sectionEndDate>
            <enrolListSize>0</enrolListSize>
            <enrollmentByUpload>true</enrollmentByUpload>
            <enrollmentRestrictions/>
            <evalutionCompleted>false</evalutionCompleted>
            <evaluationNotes></evaluationNotes>
            <familyCheckoutEligible>false</familyCheckoutEligible>
            <flagSection>0</flagSection>
            <formerScsNumber></formerScsNumber>
            <gradeLevels/>
            <sectionHistoryIndicatorCode>newproposal</sectionHistoryIndicatorCode>
            <ipIssuesResovled>1</ipIssuesResovled>
            <immediateApprovalRequired>0</immediateApprovalRequired>
            <InstructionMethods>
              <instructionMethod>
                <code>IN</code>
                <createTime>17 Dec 2018 06:54:56 PM</createTime>
                <creator>
                  <objectId>17421</objectId>
                  <version>3</version>
                  <loginId>parul1</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Parul Chaturvedi</printName>
                </creator>
                <description>Online</description>
                <name>IN</name>
                <objectId>18749</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>17421</objectId>
                  <version>3</version>
                  <loginId>parul1</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Parul Chaturvedi</printName>
                </updater>
                <version>2</version>
                <udfValues/>
              </instructionMethod>
            </InstructionMethods>
            <instructorContracts/>
            <invokedCorpContractPricing>true</invokedCorpContractPricing>
            <lectureSessionTopics></lectureSessionTopics>
            <maximumEnrollmentSize>9999</maximumEnrollmentSize>
            <maximumWaitListSize>0</maximumWaitListSize>
            <maxCEUnit>0.0</maxCEUnit>
            <minimumEnrollmentSize>1</minimumEnrollmentSize>
            <minimumAcademicUnit>0.0</minimumAcademicUnit>
            <minCEUnit>0.0</minCEUnit>
            <multipleProCeditAllowed>false</multipleProCeditAllowed>
            <numberOfAssignments>0</numberOfAssignments>
            <onlineResources/>
            <onlineTransferRequestDays>7</onlineTransferRequestDays>
            <onlineWithdrawalRequestDays>7</onlineWithdrawalRequestDays>
            <optInProCredit>false</optInProCredit>
            <optionalReading></optionalReading>
            <otherLocation></otherLocation>
            <overrideMaximumCEUnit>0.0</overrideMaximumCEUnit>
            <potentialIPIssues></potentialIPIssues>
            <preventOnlineEnrollment>false</preventOnlineEnrollment>
            <proctoredExams/>
            <publicizeSection>1</publicizeSection>
            <pvAvailabilityBeginDate>02 Jun 2019 12:00:00 AM</pvAvailabilityBeginDate>
            <pvAvailabilityEndDate>02 Jun 2020 11:59:00 PM</pvAvailabilityEndDate>
            <pvEnrollmentBeginDate>26 Aug 2019 12:00:00 AM</pvEnrollmentBeginDate>
            <pvEnrollmentDeadlineDate>23 Jan 2020 11:59:59 PM</pvEnrollmentDeadlineDate>
            <pvEnrollmentEndDate>23 Jan 2020 11:59:59 PM</pvEnrollmentEndDate>
            <receiptNotes></receiptNotes>
            <rquiredAVList/>
            <requiredReading></requiredReading>
            <requiredSoftwareTechnology></requiredSoftwareTechnology>
            <reservedListSize>0</reservedListSize>
            <room></room>
            <scheduleTypeCode>Undefined</scheduleTypeCode>
            <sectionContacts/>
            <sectionLMSInfo>
              <objectId>142795</objectId>
              <version>4</version>
              <lmsAccessPeriod>7</lmsAccessPeriod>
              <lmsSectionId></lmsSectionId>
              <lmsType></lmsType>
            </sectionLMSInfo>
            <sectionMaterials/>
            <sectionNotes></sectionNotes>
            <sectionNotesInternal></sectionNotesInternal>
            <sectionTitle>Systems Management I</sectionTitle>
            <serviceCharges/>
            <sectionStartDate>13 Jan 2020 12:00:00 AM</sectionStartDate>
            <completionRuleTypeCode>CompleteOnSchEndDate</completionRuleTypeCode>
            <svEnrollmentBeginDate>26 Aug 2019 12:00:00 AM</svEnrollmentBeginDate>
            <svEnrollmentDate>23 Jan 2020 11:59:59 PM</svEnrollmentDate>
            <timeCategory>regular</timeCategory>
            <totalSessionHours>0.0</totalSessionHours>
            <transcriptTitle>Systems Management I</transcriptTitle>
            <udfValues>
              <udfValue>
                <objectId>142804</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>1000</objectId>
                  <version>1</version>
                  <udfFieldName>sasCapacity</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>55</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>142805</objectId>
                <version>4</version>
                <udfFieldSpec>
                  <objectId>2000</objectId>
                  <version>1</version>
                  <udfFieldName>SessionCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>1RS</udfFieldValue>
              </udfValue>
            </udfValues>
            <waitListSize>0</waitListSize>
          </enrollCourseSection>
          <feeLWs>
            <feeLW>
              <objectId>1025002</objectId>
              <version>2</version>
              <enrolment>
                <objectId>1025001</objectId>
                <version>4</version>
              </enrolment>
              <creationTime>12 Nov 2019 03:00:28 PM</creationTime>
              <dispersements/>
              <fee>
                <code>142801</code>
                <createTime>05 Mar 2019 01:59:59 PM</createTime>
                <creator>
                  <objectId>22912</objectId>
                  <version>1</version>
                  <loginId>parul.chaturvedi</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Parul</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>142801</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>6</objectId>
                  <version>3</version>
                  <loginId>websvc</loginId>
                  <personType>Staff</personType>
                  <printName>Web Service User</printName>
                </updater>
                <version>1</version>
                <printCode>Domestic Fee</printCode>
                <udfValues/>
                <anyInstructionMethod>true</anyInstructionMethod>
                <anyPaymentMethod>true</anyPaymentMethod>
                <anyStudentCategory>true</anyStudentCategory>
                <applicability>public</applicability>
                <basis>FlatFee</basis>
                <effectiveDateOption>byDate</effectiveDateOption>
                <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                <expiryDateOption>neverExpire</expiryDateOption>
                <groupPayAllowed>true</groupPayAllowed>
                <paymentMethods/>
                <publicDescription>Domestic Catalog Fee</publicDescription>
                <publishCode>Domestic Fee</publishCode>
                <reasonsForNotAccepting>
                  <reasonForNotAccepting>staff</reasonForNotAccepting>
                </reasonsForNotAccepting>
                <specialRequests/>
                <studentCategories/>
                <studentPayAllowed>false</studentPayAllowed>
                <tuitionFees>
                  <tuitionFee>
                    <code>56614</code>
                    <createTime>04 Mar 2019 04:43:43 PM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>56614</name>
                    <objectId>142802</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>6</objectId>
                      <version>3</version>
                      <loginId>websvc</loginId>
                      <personType>Staff</personType>
                      <printName>Web Service User</printName>
                    </updater>
                    <version>1</version>
                    <printCode>Domestic Fee</printCode>
                    <udfValues/>
                    <tuitionFeeItems>
                      <tuitionFeeItem>
                        <amount>575.79</amount>
                        <discountable>true</discountable>
                        <name>Domestic Fee</name>
                        <revenueGLAccount>
                          <objectId>18501</objectId>
                          <version>1</version>
                          <accountName>Revenue (Domestic)</accountName>
                          <accountNumber>1011</accountNumber>
                          <accountTypeCode>revenue</accountTypeCode>
                        </revenueGLAccount>
                        <surchargeable>true</surchargeable>
                      </tuitionFeeItem>
                    </tuitionFeeItems>
                    <operator>&lt;=</operator>
                    <useStepFunction>false</useStepFunction>
                  </tuitionFee>
                </tuitionFees>
              </fee>
              <feeGroup>TuitionProfile</feeGroup>
              <quantity>1</quantity>
              <totalAmount>575.79</totalAmount>
            </feeLW>
            <feeLW>
              <objectId>1025006</objectId>
              <version>2</version>
              <enrolment>
                <objectId>1025001</objectId>
                <version>4</version>
              </enrolment>
              <creationTime>12 Nov 2019 03:00:28 PM</creationTime>
              <dispersements>
                <dispersement>
                  <objectId>1025007</objectId>
                  <version>2</version>
                  <amount>7.65</amount>
                  <federalTaxRate>0</federalTaxRate>
                  <fee>
                    <objectId>1025006</objectId>
                    <version>2</version>
                    <enrollment>
                      <objectId>1025001</objectId>
                      <version>4</version>
                      <enrollCourseSection>
                        <objectId>142794</objectId>
                        <version>16</version>
                        <campusFisId>5662</campusFisId>
                        <associatedSemester>
                          <objectId>18410</objectId>
                          <version>0</version>
                          <academicYear>
                            <objectId>18404</objectId>
                            <version>0</version>
                            <name>2019-2020</name>
                          </academicYear>
                          <beginDate>02 Nov 2019 12:00:00 AM</beginDate>
                          <campusId>1201</campusId>
                          <code>Winter 2020</code>
                          <endDate>30 Apr 2020 12:00:00 AM</endDate>
                          <lmsExportBeginDate>02 Nov 2019 12:00:00 AM</lmsExportBeginDate>
                          <lmsExportEndDate>30 Apr 2020 12:00:00 AM</lmsExportEndDate>
                          <name>Winter 2020</name>
                        </associatedSemester>
                      </enrollCourseSection>
                      <transactionBasketId>1024981</transactionBasketId>
                    </enrollment>
                    <feeId>286582</feeId>
                  </fee>
                  <GSTAmount>0.00</GSTAmount>
                  <glAccount>
                    <objectId>0</objectId>
                    <version>0</version>
                  </glAccount>
                  <HSTAmount>0.00</HSTAmount>
                  <harmonizedTaxRate>0</harmonizedTaxRate>
                  <PSTAmount>0.00</PSTAmount>
                  <stateTaxRate>0</stateTaxRate>
                  <totalAmount>7.65</totalAmount>
                </dispersement>
              </dispersements>
              <fee>
                <code>SR0004</code>
                <createTime>28 Jun 2019 02:34:22 PM</createTime>
                <creator>
                  <objectId>22914</objectId>
                  <version>1</version>
                  <loginId>serena.wong</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Serena</printName>
                </creator>
                <description>CESAR (2019-2020)=1</description>
                <name>SR0004</name>
                <objectId>286582</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>22912</objectId>
                  <version>1</version>
                  <loginId>parul.chaturvedi</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Parul</printName>
                </updater>
                <version>2</version>
                <amount>7.65</amount>
                <feeCategory>Optional Fee</feeCategory>
                <printCode>CESAR (AY2019-2020)</printCode>
                <udfValues/>
                <amountDetails>
                  <amountDetail>
                    <accountMappingId>18503</accountMappingId>
                    <amount>7.65</amount>
                    <changeReason></changeReason>
                    <deptIncetiveCalc>false</deptIncetiveCalc>
                    <externallyPayable>false</externallyPayable>
                    <glAccountId>282117</glAccountId>
                    <name>CESAR (2019-2020)=1</name>
                    <taxCreditEligible>false</taxCreditEligible>
                  </amountDetail>
                </amountDetails>
                <feeType>SpecialRequest</feeType>
                <gstApplicable>false</gstApplicable>
                <pstApplicable>false</pstApplicable>
                <specialRequestType>************</specialRequestType>
              </fee>
              <feeGroup>SectionFee</feeGroup>
              <quantity>1</quantity>
              <totalAmount>7.65</totalAmount>
            </feeLW>
            <feeLW>
              <objectId>1025008</objectId>
              <version>2</version>
              <enrolment>
                <objectId>1025001</objectId>
                <version>4</version>
              </enrolment>
              <creationTime>12 Nov 2019 03:00:28 PM</creationTime>
              <dispersements>
                <dispersement>
                  <objectId>1025009</objectId>
                  <version>2</version>
                  <amount>2.66</amount>
                  <federalTaxRate>0</federalTaxRate>
                  <fee>
                    <objectId>1025008</objectId>
                    <version>2</version>
                    <enrollment>
                      <objectId>1025001</objectId>
                      <version>4</version>
                      <enrollCourseSection>
                        <objectId>142794</objectId>
                        <version>16</version>
                        <campusFisId>5662</campusFisId>
                        <associatedSemester>
                          <objectId>18410</objectId>
                          <version>0</version>
                          <academicYear>
                            <objectId>18404</objectId>
                            <version>0</version>
                            <name>2019-2020</name>
                          </academicYear>
                          <beginDate>02 Nov 2019 12:00:00 AM</beginDate>
                          <campusId>1201</campusId>
                          <code>Winter 2020</code>
                          <endDate>30 Apr 2020 12:00:00 AM</endDate>
                          <lmsExportBeginDate>02 Nov 2019 12:00:00 AM</lmsExportBeginDate>
                          <lmsExportEndDate>30 Apr 2020 12:00:00 AM</lmsExportEndDate>
                          <name>Winter 2020</name>
                        </associatedSemester>
                      </enrollCourseSection>
                      <transactionBasketId>1024981</transactionBasketId>
                    </enrollment>
                    <feeId>286592</feeId>
                  </fee>
                  <GSTAmount>0.00</GSTAmount>
                  <glAccount>
                    <objectId>0</objectId>
                    <version>0</version>
                  </glAccount>
                  <HSTAmount>0.00</HSTAmount>
                  <harmonizedTaxRate>0</harmonizedTaxRate>
                  <PSTAmount>0.00</PSTAmount>
                  <stateTaxRate>0</stateTaxRate>
                  <totalAmount>2.66</totalAmount>
                </dispersement>
              </dispersements>
              <fee>
                <code>SR0012</code>
                <createTime>28 Jun 2019 02:42:38 PM</createTime>
                <creator>
                  <objectId>22914</objectId>
                  <version>1</version>
                  <loginId>serena.wong</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Serena</printName>
                </creator>
                <description>CFS (2019-2020)=1</description>
                <name>SR0012</name>
                <objectId>286592</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>22914</objectId>
                  <version>1</version>
                  <loginId>serena.wong</loginId>
                  <personNumber></personNumber>
                  <personType>Staff</personType>
                  <printName>Serena</printName>
                </updater>
                <version>2</version>
                <amount>2.66</amount>
                <feeCategory>Optional Fee</feeCategory>
                <printCode>Canadian Federation of Students (CE) (AY2019-2020)</printCode>
                <udfValues/>
                <amountDetails>
                  <amountDetail>
                    <accountMappingId>18503</accountMappingId>
                    <amount>2.66</amount>
                    <changeReason></changeReason>
                    <deptIncetiveCalc>false</deptIncetiveCalc>
                    <externallyPayable>false</externallyPayable>
                    <glAccountId>282117</glAccountId>
                    <name>CFS (2019-2020)=1</name>
                    <taxCreditEligible>false</taxCreditEligible>
                  </amountDetail>
                </amountDetails>
                <feeType>SpecialRequest</feeType>
                <gstApplicable>false</gstApplicable>
                <pstApplicable>false</pstApplicable>
                <specialRequestType>************</specialRequestType>
              </fee>
              <feeGroup>SectionFee</feeGroup>
              <quantity>1</quantity>
              <totalAmount>2.66</totalAmount>
            </feeLW>
          </feeLWs>
          <numOfUnits>0.0</numOfUnits>
          <originalTransactionBasketId>-1</originalTransactionBasketId>
          <student>
            <code>StudentX</code>
            <createTime>04 Dec 2018 04:07:28 PM</createTime>
            <creator>
              <objectId>1</objectId>
              <version>2</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </creator>
            <description>StudentX</description>
            <name>FirstX LastX</name>
            <objectId>68</objectId>
            <objectStatusCode>inactive</objectStatusCode>
            <updater>
              <objectId>1</objectId>
              <version>2</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </updater>
            <version>2</version>
            <addresses/>
            <alternateName/>
            <birthDate>04 Dec 2018 04:07:28 PM</birthDate>
            <cautionaryRequired>true</cautionaryRequired>
            <comments/>
            <communicationMethod>Text Email</communicationMethod>
            <contactMethods/>
            <emails/>
            <socialSecurityNum>123456789</socialSecurityNum>
            <firstName1>FirstX</firstName1>
            <firstName2></firstName2>
            <genderCode>M</genderCode>
            <interestAreaAssociations/>
            <lastName>LastX</lastName>
            <loginId>studentX</loginId>
            <personNumber>X000000</personNumber>
            <preferredLocaleString>en_US</preferredLocaleString>
            <privacyQuestions/>
            <profileHolds/>
            <salutationCode>Mr</salutationCode>
            <schoolPersonnelNumber>123456</schoolPersonnelNumber>
            <telephones/>
            <alumni>false</alumni>
            <copyOfVisa>false</copyOfVisa>
            <countryOfOrigin></countryOfOrigin>
            <documentEmailed>false</documentEmailed>
            <documentFaxed>false</documentFaxed>
            <educationHistory/>
            <enrollmentRaces/>
            <enrollmentTimeframes/>
            <enrolmentGroups/>
            <internationalStudent>false</internationalStudent>
            <laterLifeLearner>false</laterLifeLearner>
            <learningGoals/>
            <nativeLanguage></nativeLanguage>
            <proficiencyExamScores/>
            <profileStatus>Active</profileStatus>
            <schoolStudentNumber></schoolStudentNumber>
            <studentAssociationDetails/>
            <studentCategories/>
            <studentCredentials/>
            <studentNumber>X000000</studentNumber>
            <udfValues/>
            <willStudyInCountry>false</willStudyInCountry>
            <willTravelToCountry>false</willTravelToCountry>
            <willWorkInCountry>false</willWorkInCountry>
            <youthParticipant>false</youthParticipant>
          </student>
          <amount>586.10</amount>
          <transactionBasketId>1024981</transactionBasketId>
          <voided>false</voided>
          <withdrawn>false</withdrawn>
        </enrollmentEvent>
      </enrollmentEvents>
      <migrated>false</migrated>
      <newSessionTransactions/>
      <basketNumber>Temp1024981</basketNumber>
      <saleEvents/>
      <sessionAdjustedTransactions/>
      <settlementClient>
        <objectId>68</objectId>
        <version>0</version>
        <loginId>X68</loginId>
        <personType>Student</personType>
        <printName></printName>
      </settlementClient>
      <status>Unprocessed</status>
      <transactionAmount>0</transactionAmount>
      <transactionBasketType>Single</transactionBasketType>
      <transactions/>
    </transactionBasket>
  </body>
</integrationMessage>