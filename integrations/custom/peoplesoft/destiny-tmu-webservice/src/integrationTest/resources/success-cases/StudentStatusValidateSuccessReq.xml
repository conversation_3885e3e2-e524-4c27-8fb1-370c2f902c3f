<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <transactionBasket>
      <code>403734</code>
      <createTime>18 Jan 2019 04:33:25 PM</createTime>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description></description>
      <name></name>
      <objectId>403735</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>4</version>
      <affiliatedGroup>
        <objectId>0</objectId>
        <version>0</version>
      </affiliatedGroup>
      <applyGstToPst>false</applyGstToPst>
      <basketCreationTime>18 Jan 2019 04:33:25 PM</basketCreationTime>
      <cslwAdjusted>false</cslwAdjusted>
      <client>
        <objectId>404182</objectId>
        <version>2</version>
        <number>X000570</number>
        <personType>Student</personType>
      </client>
      <enrollmentEvents>
        <enrollmentEvent>
          <objectId>403736</objectId>
          <version>5</version>
          <activityCode>Sale</activityCode>
          <courseSectionId>378804</courseSectionId>
          <creationTime>18 Jan 2019 04:33:25 PM</creationTime>
          <enrollCourseSection>
            <code>047</code>
            <createTime>08 Jan 2019 01:43:46 PM</createTime>
            <creator>
              <objectId>1</objectId>
              <version>158</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </creator>
            <description>047</description>
            <name>047</name>
            <objectId>378804</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>1</objectId>
              <version>158</version>
              <loginId>super</loginId>
              <personType>Staff</personType>
              <printName>Super User</printName>
            </updater>
            <version>6</version>
            <activeWaitListSize>14</activeWaitListSize>
            <courseSectionAssociations></courseSectionAssociations>
            <associatedCourse>
              <objectId>18784</objectId>
              <version>5</version>
              <code>0001</code>
              <courseVersionNumber>0</courseVersionNumber>
              <costingUnitCode>CU0001</costingUnitCode>
              <costingUnitName>Creative Writing</costingUnitName>
              <name>The Novel</name>
              <programOfficeCode>PO0001</programOfficeCode>
              <programOfficeName>Continuing Education</programOfficeName>
            </associatedCourse>
            <sectionSchedules>
              <sectionSchedule>
                <code>378810</code>
                <createTime>08 Jan 2019 01:43:46 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>378810</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>0</version>
                <assignedInstructors></assignedInstructors>
                <breakHours>0.5</breakHours>
                <broadcastChannelType>Live</broadcastChannelType>
                <building>
                  <code>EE1</code>
                  <createTime>21 Jun 2018 11:57:01 AM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>EEE1</name>
                  <objectId>78000</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>0</version>
                </building>
                <campus>
                  <code>CA0001</code>
                  <createTime>21 Jun 2018 11:56:41 AM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>EEE</name>
                  <objectId>77899</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>0</version>
                </campus>
                <daysOfWeek>
                  <dayOfWeek>2</dayOfWeek>
                </daysOfWeek>
                <daysOfWeekString>2</daysOfWeekString>
                <endDate>15 Jul 2019 12:00:00 AM</endDate>
                <endTimeString>PM200</endTimeString>
                <endType>date</endType>
                <externalResourceManagement>false</externalResourceManagement>
                <frequency>1</frequency>
                <hasBroadcastChannel>false</hasBroadcastChannel>
                <instructorIds></instructorIds>
                <core>false</core>
                <distanceLearning>false</distanceLearning>
                <included>true</included>
                <numberOfSessions>53</numberOfSessions>
                <room>
                  <code>RM0002</code>
                  <createTime>22 Jun 2018 04:33:45 PM</createTime>
                  <creator>
                    <objectId>0</objectId>
                    <version>0</version>
                  </creator>
                  <description></description>
                  <name>R0</name>
                  <objectId>79337</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>0</objectId>
                    <version>0</version>
                  </updater>
                  <version>2</version>
                  <accesibleEntry>false</accesibleEntry>
                  <building>
                    <code>EE1</code>
                    <createTime>21 Jun 2018 11:57:01 AM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>EEE1</name>
                    <objectId>78000</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>0</objectId>
                      <version>0</version>
                    </updater>
                    <version>0</version>
                  </building>
                  <codeArea></codeArea>
                  <comments></comments>
                  <emailAddress></emailAddress>
                  <firstName1></firstName1>
                  <maximumNumberOfStudents>6</maximumNumberOfStudents>
                  <requiredAVs></requiredAVs>
                  <roomConfiguations>
                    <roomConfiguration xsi:type="roomConfigurations"
                      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                      <code>79338</code>
                      <creator>
                        <objectId>0</objectId>
                        <version>0</version>
                      </creator>
                      <description></description>
                      <name>1</name>
                      <objectId>79338</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>0</objectId>
                        <version>0</version>
                      </updater>
                      <version>1</version>
                      <maximumNumberOfStudents>1</maximumNumberOfStudents>
                    </roomConfiguration>
                  </roomConfiguations>
                  <surName></surName>
                  <telephoneExt></telephoneExt>
                  <telephoneNumber></telephoneNumber>
                </room>
                <scheduleId>378810</scheduleId>
                <scheduleType>Lab</scheduleType>
                <courseSectionProfile>
                  <objectId>378804</objectId>
                  <version>6</version>
                  <campusFisId>123456</campusFisId>
                  <associatedSemester>
                    <objectId>75515</objectId>
                    <version>12</version>
                    <academicYear>
                      <objectId>18764</objectId>
                      <version>0</version>
                      <name>2017-2018</name>
                    </academicYear>
                    <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
                    <campusId>1189</campusId>
                    <code>Term1</code>
                    <endDate>01 Aug 2018 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
                    <name>Term1</name>
                  </associatedSemester>
                </courseSectionProfile>
                <sessionDates>
                  <sessionDate>16 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>23 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>30 Jul 2018 12:00:00 AM</sessionDate>
                  <sessionDate>06 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>13 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>20 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>27 Aug 2018 12:00:00 AM</sessionDate>
                  <sessionDate>03 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>10 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>17 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>24 Sep 2018 12:00:00 AM</sessionDate>
                  <sessionDate>01 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>08 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>15 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>22 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>29 Oct 2018 12:00:00 AM</sessionDate>
                  <sessionDate>05 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>12 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>19 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>26 Nov 2018 12:00:00 AM</sessionDate>
                  <sessionDate>03 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>10 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>17 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>24 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>31 Dec 2018 12:00:00 AM</sessionDate>
                  <sessionDate>07 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>14 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>21 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>28 Jan 2019 12:00:00 AM</sessionDate>
                  <sessionDate>04 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>11 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>18 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>25 Feb 2019 12:00:00 AM</sessionDate>
                  <sessionDate>04 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>11 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>18 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>25 Mar 2019 12:00:00 AM</sessionDate>
                  <sessionDate>01 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>08 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>15 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>22 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>29 Apr 2019 12:00:00 AM</sessionDate>
                  <sessionDate>06 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>13 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>20 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>27 May 2019 12:00:00 AM</sessionDate>
                  <sessionDate>03 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>10 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>17 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>24 Jun 2019 12:00:00 AM</sessionDate>
                  <sessionDate>01 Jul 2019 12:00:00 AM</sessionDate>
                  <sessionDate>08 Jul 2019 12:00:00 AM</sessionDate>
                  <sessionDate>15 Jul 2019 12:00:00 AM</sessionDate>
                </sessionDates>
                <startDate>16 Jul 2018 12:00:00 AM</startDate>
                <startTimeString>PM100</startTimeString>
              </sectionSchedule>
            </sectionSchedules>
            <courseSectionFees>
              <courseSectionFee>
                <objectId>378813</objectId>
                <version>1</version>
                <federalTaxApplicable>false</federalTaxApplicable>
                <flatFeeEnabled>true</flatFeeEnabled>
                <hstApplicable>false</hstApplicable>
                <nonBroadcastSection>false</nonBroadcastSection>
                <stateTaxApplicable>false</stateTaxApplicable>
                <taxRefundEligible>true</taxRefundEligible>
                <tuitionProfileEnabled>true</tuitionProfileEnabled>
                <associatedSection>
                  <objectId>378804</objectId>
                  <version>6</version>
                  <campusFisId>123456</campusFisId>
                  <associatedSemester>
                    <objectId>75515</objectId>
                    <version>12</version>
                    <academicYear>
                      <objectId>18764</objectId>
                      <version>0</version>
                      <name>2017-2018</name>
                    </academicYear>
                    <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
                    <campusId>1189</campusId>
                    <code>Term1</code>
                    <endDate>01 Aug 2018 12:00:00 AM</endDate>
                    <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
                    <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
                    <name>Term1</name>
                  </associatedSemester>
                </associatedSection>
                <associatedSectionFeeTuitionProfiles>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>TPINTL</code>
                      <createTime>31 Dec 2018 10:51:54 PM</createTime>
                      <creator>
                        <objectId>1</objectId>
                        <version>158</version>
                        <loginId>super</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </creator>
                      <description>External Fee: International</description>
                      <name>External Fee: International</name>
                      <objectId>101</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>1</objectId>
                        <version>158</version>
                        <loginId>super</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </updater>
                      <version>5</version>
                      <feeCategory>ExternalFee</feeCategory>
                      <printCode>International Fee</printCode>
                      <udfValues></udfValues>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDate>02/01/2019 12:00 AM</effectiveDate>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <effectiveDatsBeforeSectionStart>0</effectiveDatsBeforeSectionStart>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateBeforeStart>0</expiryDateBeforeStart>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>false</groupPayAllowed>
                      <internalDescription>Placeholder for international fee retrieved on checkout
                        from an external source. The fee component amount will be overridden with
                        the externally retrieved amount.
                      </internalDescription>
                      <paymentMethods></paymentMethods>
                      <publicDescription></publicDescription>
                      <publishCode>International Fee</publishCode>
                      <reasonsForNotAccepting>
                        <reasonForNotAccepting>Staff override</reasonForNotAccepting>
                        <reasonForNotAccepting>Status Update</reasonForNotAccepting>
                        <reasonForNotAccepting>externalFeeOverride</reasonForNotAccepting>
                      </reasonsForNotAccepting>
                      <specialRequests></specialRequests>
                      <studentCategories></studentCategories>
                      <studentPayAllowed>true</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>373440</code>
                          <createTime>03 Jan 2019 12:49:11 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>373440</name>
                          <objectId>373440</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>3</version>
                          <printCode>International Fee</printCode>
                          <udfValues></udfValues>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>0.00</amount>
                              <discountable>true</discountable>
                              <name>IntFee</name>
                              <revenueGLAccount>
                                <objectId>18769</objectId>
                                <version>3</version>
                                <accountName>Default Revenue</accountName>
                                <accountNumber>123456</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator></operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>creditInterval</creditType>
                    <markedAsFlatFee>false</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                  <associatedSectionFeeTuitionProfile>
                    <associatedTuitionProfile>
                      <code>378811</code>
                      <createTime>08 Jan 2019 01:43:46 PM</createTime>
                      <creator>
                        <objectId>1</objectId>
                        <version>158</version>
                        <loginId>super</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </creator>
                      <description></description>
                      <name></name>
                      <objectId>378811</objectId>
                      <objectStatusCode>active</objectStatusCode>
                      <updater>
                        <objectId>1</objectId>
                        <version>158</version>
                        <loginId>super</loginId>
                        <personType>Staff</personType>
                        <printName>Super User</printName>
                      </updater>
                      <version>1</version>
                      <feeCategory></feeCategory>
                      <printCode>F11</printCode>
                      <udfValues></udfValues>
                      <anyInstructionMethod>true</anyInstructionMethod>
                      <anyPaymentMethod>true</anyPaymentMethod>
                      <anyStudentCategory>true</anyStudentCategory>
                      <applicability>public</applicability>
                      <basis>FlatFee</basis>
                      <effectiveDateOption>byDate</effectiveDateOption>
                      <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                      <expiryDateOption>neverExpire</expiryDateOption>
                      <groupPayAllowed>true</groupPayAllowed>
                      <paymentMethods></paymentMethods>
                      <publicDescription>FeeSection</publicDescription>
                      <publishCode>F11</publishCode>
                      <reasonsForNotAccepting></reasonsForNotAccepting>
                      <specialRequests></specialRequests>
                      <studentCategories></studentCategories>
                      <studentPayAllowed>false</studentPayAllowed>
                      <tuitionFees>
                        <tuitionFee>
                          <code>130804</code>
                          <createTime>01 Aug 2018 02:54:52 PM</createTime>
                          <creator>
                            <objectId>0</objectId>
                            <version>0</version>
                          </creator>
                          <description></description>
                          <name>130804</name>
                          <objectId>378812</objectId>
                          <objectStatusCode>active</objectStatusCode>
                          <updater>
                            <objectId>0</objectId>
                            <version>0</version>
                          </updater>
                          <version>3</version>
                          <printCode>F11</printCode>
                          <udfValues></udfValues>
                          <tuitionFeeItems>
                            <tuitionFeeItem>
                              <amount>114.00</amount>
                              <discountable>true</discountable>
                              <name>Ffee</name>
                              <revenueGLAccount>
                                <objectId>18769</objectId>
                                <version>3</version>
                                <accountName>Default Revenue</accountName>
                                <accountNumber>123456</accountNumber>
                                <accountTypeCode>revenue</accountTypeCode>
                              </revenueGLAccount>
                              <surchargeable>true</surchargeable>
                            </tuitionFeeItem>
                          </tuitionFeeItems>
                          <operator></operator>
                          <useStepFunction>false</useStepFunction>
                        </tuitionFee>
                      </tuitionFees>
                    </associatedTuitionProfile>
                    <creditInternal>1.0</creditInternal>
                    <creditType>nonCredit</creditType>
                    <markedAsFlatFee>true</markedAsFlatFee>
                  </associatedSectionFeeTuitionProfile>
                </associatedSectionFeeTuitionProfiles>
                <creator>
                  <objectId>0</objectId>
                  <version>0</version>
                </creator>
                <udfValues></udfValues>
                <updater>
                  <objectId>0</objectId>
                  <version>0</version>
                </updater>
              </courseSectionFee>
            </courseSectionFees>
            <associatedSemester>
              <objectId>75515</objectId>
              <version>12</version>
              <academicYear>
                <objectId>18764</objectId>
                <version>0</version>
                <name>2017-2018</name>
              </academicYear>
              <beginDate>01 Jun 2018 12:00:00 AM</beginDate>
              <campusId>1189</campusId>
              <code>Term1</code>
              <endDate>01 Aug 2018 12:00:00 AM</endDate>
              <lmsExportBeginDate>01 Jun 2018 12:00:00 AM</lmsExportBeginDate>
              <lmsExportEndDate>30 Aug 2018 12:00:00 AM</lmsExportEndDate>
              <name>Term1</name>
            </associatedSemester>
            <associatedSpecialRequests>
              <associatedSpecialRequest>
                <associatedSpecialRequest>
                  <code>SR0001</code>
                  <createTime>06 Jun 2017 04:54:41 PM</createTime>
                  <creator>
                    <objectId>1</objectId>
                    <version>158</version>
                    <loginId>super</loginId>
                    <personType>Staff</personType>
                    <printName>Super User</printName>
                  </creator>
                  <description>Parking Pass</description>
                  <name>SR0001</name>
                  <objectId>18776</objectId>
                  <objectStatusCode>active</objectStatusCode>
                  <updater>
                    <objectId>1</objectId>
                    <version>158</version>
                    <loginId>super</loginId>
                    <personType>Staff</personType>
                    <printName>Super User</printName>
                  </updater>
                  <version>1</version>
                  <amount>15.00</amount>
                  <feeCategory></feeCategory>
                  <printCode>PARKING</printCode>
                  <udfValues></udfValues>
                  <amountDetails>
                    <amountDetail>
                      <accountMappingId>18775</accountMappingId>
                      <amount>15.00</amount>
                      <changeReason></changeReason>
                      <deptIncetiveCalc>false</deptIncetiveCalc>
                      <externallyPayable>false</externallyPayable>
                      <glAccountId>18769</glAccountId>
                      <name>Parking</name>
                      <taxCreditEligible>false</taxCreditEligible>
                    </amountDetail>
                  </amountDetails>
                  <feeType>SpecialRequest</feeType>
                  <gstApplicable>false</gstApplicable>
                  <pstApplicable>false</pstApplicable>
                </associatedSpecialRequest>
                <defaultSelectedWhenOptional>false</defaultSelectedWhenOptional>
                <publicViewSelectionMode></publicViewSelectionMode>
              </associatedSpecialRequest>
            </associatedSpecialRequests>
            <autoFilledInCompletionDate>false</autoFilledInCompletionDate>
            <campusFisId>123456</campusFisId>
            <courseInstructionMethod>Classroom</courseInstructionMethod>
            <sectionStatusCode>final_approval</sectionStatusCode>
            <discounts>
              <discount>
                <code>DC0002</code>
                <createTime>10 Jan 2019 03:52:42 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description>10% disc</description>
                <name>DC0002</name>
                <objectId>382761</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>0</version>
                <printCode>10% disc</printCode>
                <udfValues></udfValues>
                <accountAppTypeCode>glaccount</accountAppTypeCode>
                <allowTypeAcross>false</allowTypeAcross>
                <details>10% disc</details>
                <discountTypeCode>Standard</discountTypeCode>
                <effectiveDate>01 Jan 2019 12:00:00 AM</effectiveDate>
                <effectiveDateCode>byDate</effectiveDateCode>
                <effectiveDaysBeforeStartDate>0</effectiveDaysBeforeStartDate>
                <expiryDateCode>neverExpire</expiryDateCode>
                <expiryDaysBeforeStartDate>0</expiryDaysBeforeStartDate>
                <feeApplicableScopeCode>public</feeApplicableScopeCode>
                <feeRepCode>percentage</feeRepCode>
                <glAccountId>18770</glAccountId>
                <groupEnrollmentsNumber>0</groupEnrollmentsNumber>
                <multipleSectionsEndLimit>0</multipleSectionsEndLimit>
                <multipleSectionsNumber>0</multipleSectionsNumber>
                <neverExpire>true</neverExpire>
                <optimizedEnabled>false</optimizedEnabled>
                <rate>10.0</rate>
                <roundDiscount>false</roundDiscount>
              </discount>
            </discounts>
            <distanceLearning>0</distanceLearning>
            <dueDateRule>
              <objectId>378816</objectId>
              <version>17</version>
            </dueDateRule>
            <eCollege>false</eCollege>
            <sectionEndDate>15 Jul 2019 12:00:00 AM</sectionEndDate>
            <enrolListSize>0</enrolListSize>
            <enrollmentByUpload>false</enrollmentByUpload>
            <enrollmentRestrictions></enrollmentRestrictions>
            <evalutionCompleted>false</evalutionCompleted>
            <evaluationNotes></evaluationNotes>
            <familyCheckoutEligible>false</familyCheckoutEligible>
            <flagSection>0</flagSection>
            <formerScsNumber></formerScsNumber>
            <gradeLevels></gradeLevels>
            <sectionHistoryIndicatorCode>newproposal</sectionHistoryIndicatorCode>
            <ipIssuesResovled>0</ipIssuesResovled>
            <immediateApprovalRequired>0</immediateApprovalRequired>
            <InstructionMethods></InstructionMethods>
            <instructorContracts></instructorContracts>
            <invokedCorpContractPricing>true</invokedCorpContractPricing>
            <lectureSessionTopics></lectureSessionTopics>
            <maximumEnrollmentSize>333</maximumEnrollmentSize>
            <maximumWaitListSize>1</maximumWaitListSize>
            <maxCEUnit>2.65</maxCEUnit>
            <minimumEnrollmentSize>1</minimumEnrollmentSize>
            <minCEUnit>2.65</minCEUnit>
            <multipleProCeditAllowed>false</multipleProCeditAllowed>
            <numberOfAssignments>0</numberOfAssignments>
            <onlineResources></onlineResources>
            <onlineTransferRequestDays>7</onlineTransferRequestDays>
            <onlineWithdrawalRequestDays>7</onlineWithdrawalRequestDays>
            <optInProCredit>false</optInProCredit>
            <optionalReading></optionalReading>
            <otherLocation></otherLocation>
            <potentialIPIssues></potentialIPIssues>
            <preventOnlineEnrollment>false</preventOnlineEnrollment>
            <proctoredExams></proctoredExams>
            <publicizeSection>1</publicizeSection>
            <pvAvailabilityBeginDate>18 Oct 2017 11:58:00 AM</pvAvailabilityBeginDate>
            <pvAvailabilityEndDate>16 Mar 2029 11:59:00 PM</pvAvailabilityEndDate>
            <pvEnrollmentBeginDate>18 Oct 2017 11:58:00 AM</pvEnrollmentBeginDate>
            <pvEnrollmentEndDate>16 Mar 2029 11:59:00 PM</pvEnrollmentEndDate>
            <receiptNotes></receiptNotes>
            <rquiredAVList></rquiredAVList>
            <requiredReading></requiredReading>
            <requiredSoftwareTechnology></requiredSoftwareTechnology>
            <reservedListSize>0</reservedListSize>
            <room></room>
            <scheduleTypeCode>Undefined</scheduleTypeCode>
            <sectionContacts></sectionContacts>
            <sectionLMSInfo>
              <objectId>378805</objectId>
              <version>5</version>
              <lmsAccessPeriod>7</lmsAccessPeriod>
              <lmsSectionId></lmsSectionId>
              <lmsType></lmsType>
            </sectionLMSInfo>
            <sectionMaterials></sectionMaterials>
            <sectionNotes></sectionNotes>
            <sectionNotesInternal></sectionNotesInternal>
            <sectionTitle>Kamal's 047 Section</sectionTitle>
            <serviceCharges></serviceCharges>
            <sectionStartDate>16 Jul 2018 12:00:00 AM</sectionStartDate>
            <completionRuleTypeCode>CompleteOnSchEndDate</completionRuleTypeCode>
            <svEnrollmentBeginDate>18 Oct 2017 11:58:00 AM</svEnrollmentBeginDate>
            <svEnrollmentDate>16 Mar 2029 11:59:00 PM</svEnrollmentDate>
            <timeCategory>regular</timeCategory>
            <totalSessionHours>26.5</totalSessionHours>
            <transcriptTitle>The Novel</transcriptTitle>
            <udfValues>
              <udfValue>
                <objectId>378814</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>1000</objectId>
                  <version>1</version>
                  <udfFieldName>sasCapacity</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>4444</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>378815</objectId>
                <version>3</version>
                <udfFieldSpec>
                  <objectId>2000</objectId>
                  <version>1</version>
                  <udfFieldName>SessionCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>11</udfFieldValue>
              </udfValue>
            </udfValues>
            <waitListSize>0</waitListSize>
          </enrollCourseSection>
          <feeLWs>
            <feeLW>
              <objectId>403749</objectId>
              <version>3</version>
              <enrolment>
                <objectId>403736</objectId>
                <version>5</version>
              </enrolment>
              <creationTime>18 Jan 2019 04:33:25 PM</creationTime>
              <dispersements></dispersements>
              <fee xsi:type="tuitionProfile" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <code>378811</code>
                <createTime>08 Jan 2019 01:43:46 PM</createTime>
                <creator>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </creator>
                <description></description>
                <name></name>
                <objectId>378811</objectId>
                <objectStatusCode>active</objectStatusCode>
                <updater>
                  <objectId>1</objectId>
                  <version>158</version>
                  <loginId>super</loginId>
                  <personType>Staff</personType>
                  <printName>Super User</printName>
                </updater>
                <version>1</version>
                <feeCategory></feeCategory>
                <printCode>F11</printCode>
                <udfValues></udfValues>
                <anyInstructionMethod>true</anyInstructionMethod>
                <anyPaymentMethod>true</anyPaymentMethod>
                <anyStudentCategory>true</anyStudentCategory>
                <applicability>public</applicability>
                <basis>FlatFee</basis>
                <effectiveDateOption>byDate</effectiveDateOption>
                <enforcedOnPublicOnly>false</enforcedOnPublicOnly>
                <expiryDateOption>neverExpire</expiryDateOption>
                <groupPayAllowed>true</groupPayAllowed>
                <paymentMethods></paymentMethods>
                <publicDescription>FeeSection</publicDescription>
                <publishCode>F11</publishCode>
                <reasonsForNotAccepting></reasonsForNotAccepting>
                <specialRequests></specialRequests>
                <studentCategories></studentCategories>
                <studentPayAllowed>false</studentPayAllowed>
                <tuitionFees>
                  <tuitionFee>
                    <code>130804</code>
                    <createTime>01 Aug 2018 02:54:52 PM</createTime>
                    <creator>
                      <objectId>0</objectId>
                      <version>0</version>
                    </creator>
                    <description></description>
                    <name>130804</name>
                    <objectId>378812</objectId>
                    <objectStatusCode>active</objectStatusCode>
                    <updater>
                      <objectId>0</objectId>
                      <version>0</version>
                    </updater>
                    <version>3</version>
                    <printCode>F11</printCode>
                    <udfValues></udfValues>
                    <tuitionFeeItems>
                      <tuitionFeeItem>
                        <amount>114.00</amount>
                        <discountable>true</discountable>
                        <name>Ffee</name>
                        <revenueGLAccount>
                          <objectId>18769</objectId>
                          <version>3</version>
                          <accountName>Default Revenue</accountName>
                          <accountNumber>123456</accountNumber>
                          <accountTypeCode>revenue</accountTypeCode>
                        </revenueGLAccount>
                        <surchargeable>true</surchargeable>
                      </tuitionFeeItem>
                    </tuitionFeeItems>
                    <operator></operator>
                    <useStepFunction>false</useStepFunction>
                  </tuitionFee>
                </tuitionFees>
              </fee>
              <feeGroup>TuitionProfile</feeGroup>
              <quantity>1</quantity>
              <totalAmount>825.22</totalAmount>
            </feeLW>
          </feeLWs>
          <numOfUnits>0.0</numOfUnits>
          <originalTransactionBasketId>-1</originalTransactionBasketId>
          <student>
            <code>code404182</code>
            <createTime>18 Jan 2019 04:35:11 PM</createTime>
            <creator>
              <objectId>2</objectId>
              <version>9</version>
              <loginId>webreg</loginId>
              <personType>Staff</personType>
              <printName>Web Registrar</printName>
            </creator>
            <description></description>
            <name>Loris Nina</name>
            <objectId>404182</objectId>
            <objectStatusCode>active</objectStatusCode>
            <updater>
              <objectId>2</objectId>
              <version>9</version>
              <loginId>webreg</loginId>
              <personType>Staff</personType>
              <printName>Web Registrar</printName>
            </updater>
            <version>2</version>
            <addresses>
              <address>
                <typeCode>Home</typeCode>
                <city>Tehran</city>
                <country>IRN</country>
                <effectiveDate>18 Jan 2019 04:35:17 PM</effectiveDate>
                <foreign>false</foreign>
                <foreignState></foreignState>
                <modifiedDate>18 Jan 2019 04:35:17 PM</modifiedDate>
                <preferred>true</preferred>
                <provinceState></provinceState>
                <street0></street0>
                <street1>848 Lana Road</street1>
                <street2>Zaferanieh St.,</street2>
                <terminate>false</terminate>
                <postalZip></postalZip>
              </address>
            </addresses>
            <alternateName></alternateName>
            <birthDate>08 Jan 1993 12:00:00 AM</birthDate>
            <cautionaryRequired>false</cautionaryRequired>
            <comments></comments>
            <communicationMethod>Email</communicationMethod>
            <contactMethods>
              <contactMethod>Any</contactMethod>
            </contactMethods>
            <emails>
              <email>
                <emailAddress><EMAIL></emailAddress>
                <typeCode>Standard</typeCode>
                <preferred>true</preferred>
                <release>false</release>
                <return>false</return>
              </email>
            </emails>
            <socialSecurityNum></socialSecurityNum>
            <firstName1>Loris</firstName1>
            <firstName2></firstName2>
            <interestAreaAssociations></interestAreaAssociations>
            <lastName>Nina</lastName>
            <lmsPersonId>X000570</lmsPersonId>
            <loginId>RUCE_calli</loginId>
            <netId>12345</netId>
            <personNumber>X000570</personNumber>
            <preferredEmail>
              <emailAddress><EMAIL></emailAddress>
              <typeCode>Standard</typeCode>
              <preferred>true</preferred>
              <release>false</release>
              <return>false</return>
            </preferredEmail>
            <preferredLocaleString>en_US</preferredLocaleString>
            <privacyQuestions>
              <privacyQuestion>
                <objectId>404184</objectId>
                <version>0</version>
                <answer>jhg</answer>
                <question>0</question>
              </privacyQuestion>
              <privacyQuestion>
                <objectId>404185</objectId>
                <version>0</version>
                <answer>jhg</answer>
                <question>1</question>
              </privacyQuestion>
            </privacyQuestions>
            <profileHolds></profileHolds>
            <salutationCode></salutationCode>
            <schoolPersonnelNumber>302055X0005701</schoolPersonnelNumber>
            <telephones>
              <telephone>
                <areaCode>416</areaCode>
                <preferred>true</preferred>
                <release>false</release>
                <tcpaConsented>false</tcpaConsented>
                <telephoneExt></telephoneExt>
                <telephoneNumber>9795000</telephoneNumber>
                <typeCode>Home</typeCode>
              </telephone>
            </telephones>
            <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
            <additionalAssociation></additionalAssociation>
            <birthCity></birthCity>
            <birthState></birthState>
            <copyOfVisa>false</copyOfVisa>
            <countryOfCitizenship></countryOfCitizenship>
            <countryOfOrigin></countryOfOrigin>
            <department></department>
            <directBillingAccount></directBillingAccount>
            <documentEmailed>true</documentEmailed>
            <documentFaxed>false</documentFaxed>
            <educationHistory></educationHistory>
            <emergencyContactEmail></emergencyContactEmail>
            <emergencyContactName></emergencyContactName>
            <emergencyContactRelationship></emergencyContactRelationship>
            <employerEmail></employerEmail>
            <employerFirstName></employerFirstName>
            <employerJobTitle></employerJobTitle>
            <employerName></employerName>
            <employerSalutation></employerSalutation>
            <employerSurname></employerSurname>
            <enrollmentRaces></enrollmentRaces>
            <enrollmentTimeframes></enrollmentTimeframes>
            <enrolmentGroups></enrolmentGroups>
            <internationalStudent>false</internationalStudent>
            <jobTitle></jobTitle>
            <laterLifeLearner>false</laterLifeLearner>
            <learningGoals></learningGoals>
            <major></major>
            <nameTagName></nameTagName>
            <otherInterests></otherInterests>
            <otherLearningGoals></otherLearningGoals>
            <otherNames></otherNames>
            <preferredName></preferredName>
            <proficiencyExamScores></proficiencyExamScores>
            <profileStatus>Active</profileStatus>
            <schoolStudentNumber></schoolStudentNumber>
            <secondCountryOfCitizenship></secondCountryOfCitizenship>
            <studentAssociationDetails></studentAssociationDetails>
            <studentCategories>
              <studentCategory>
                <category>Domestic</category>
              </studentCategory>
              <studentCategory>
                <category>International</category>
              </studentCategory>
            </studentCategories>
            <studentCredentials></studentCredentials>
            <studentNumber>X000570</studentNumber>
            <udfValues>
              <udfValue>
                <objectId>404186</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1001</objectId>
                  <version>1</version>
                  <udfFieldName>oenNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404187</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1002</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceQuestion</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404188</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1003</objectId>
                  <version>1</version>
                  <udfFieldName>attendanceYear</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404189</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1004</objectId>
                  <version>1</version>
                  <udfFieldName>existingStudentNumber</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404190</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1005</objectId>
                  <version>1</version>
                  <udfFieldName>citizenshipStatus</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404191</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1006</objectId>
                  <version>1</version>
                  <udfFieldName>dateOfEntry</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404192</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1007</objectId>
                  <version>1</version>
                  <udfFieldName>countryOfCitizenship</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404193</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1008</objectId>
                  <version>1</version>
                  <udfFieldName>certificate</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404194</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1009</objectId>
                  <version>1</version>
                  <udfFieldName>certificateApprovalCode</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue></udfFieldValue>
              </udfValue>
              <udfValue>
                <objectId>404195</objectId>
                <version>0</version>
                <udfFieldSpec>
                  <objectId>1010</objectId>
                  <version>1</version>
                  <udfFieldName>studentGender</udfFieldName>
                </udfFieldSpec>
                <udfFieldValue>*****</udfFieldValue>
              </udfValue>
            </udfValues>
            <willStudyInCountry>false</willStudyInCountry>
            <willTravelToCountry>false</willTravelToCountry>
            <willWorkInCountry>false</willWorkInCountry>
            <youthParticipant>false</youthParticipant>
          </student>
          <amount>825.22</amount>
          <transactionBasketId>403735</transactionBasketId>
          <voided>false</voided>
          <withdrawn>false</withdrawn>
        </enrollmentEvent>
      </enrollmentEvents>
      <migrated>false</migrated>
      <newSessionTransactions></newSessionTransactions>
      <basketNumber>Temp403735</basketNumber>
      <saleEvents></saleEvents>
      <sessionAdjustedTransactions></sessionAdjustedTransactions>
      <settlementClient>
        <objectId>404182</objectId>
        <version>2</version>
        <loginId>RUCE_calli</loginId>
        <personNumber>X000570</personNumber>
        <personType>Student</personType>
        <printName>Loris Nina</printName>
      </settlementClient>
      <status>Unprocessed</status>
      <transactionAmount>0</transactionAmount>
      <transactionBasketType>Single</transactionBasketType>
      <transactions></transactions>
    </transactionBasket>
  </body>
</integrationMessage>