<?xml version='1.0' encoding='UTF-8'?>
<integrationMessage>
  <body>
    <integrationActionResult>
      <status>SC0000</status>
      <results>
        <result>
          <status>SC0000</status>
          <entity>
            <courseSectionFee>
              <associatedSectionFeeTuitionProfiles>
                <associatedSectionFeeTuitionProfile>
                  <associatedTuitionProfile>
                    <tuitionFees>
                      <tuitionFee>
                        <printCode>Domestic Fee</printCode>
                        <tuitionFeeItems>
                          <tuitionFeeItem>
                            <name>Tuition</name>
                            <amount>916.91</amount>
                          </tuitionFeeItem>
                        </tuitionFeeItems>
                      </tuitionFee>
                    </tuitionFees>
                  </associatedTuitionProfile>
                  <markedAsFlatFee>true</markedAsFlatFee>
                </associatedSectionFeeTuitionProfile>
                <associatedSectionFeeTuitionProfile>
                  <associatedTuitionProfile>
                    <code>TPINTL</code>
                    <tuitionFees>
                      <tuitionFee>
                        <printCode>International Fee</printCode>
                        <tuitionFeeItems>
                          <tuitionFeeItem>
                            <name/>
                            <amount>4584.37</amount>
                          </tuitionFeeItem>
                        </tuitionFeeItems>
                      </tuitionFee>
                    </tuitionFees>
                  </associatedTuitionProfile>
                </associatedSectionFeeTuitionProfile>
                <associatedSectionFeeTuitionProfile>
                  <associatedTuitionProfile>
                    <code>TPDOOP</code>
                    <tuitionFees>
                      <tuitionFee>
                        <printCode>Domestic Out of Province Fee</printCode>
                        <tuitionFeeItems>
                          <tuitionFeeItem>
                            <name/>
                            <amount>1527.77</amount>
                          </tuitionFeeItem>
                        </tuitionFeeItems>
                      </tuitionFee>
                    </tuitionFees>
                  </associatedTuitionProfile>
                </associatedSectionFeeTuitionProfile>
              </associatedSectionFeeTuitionProfiles>
            </courseSectionFee>
          </entity>
          <entityType>CourseSectionFee</entityType>
          <entityInXMLFormat>true</entityInXMLFormat>
          <errorMsg>
          </errorMsg>
        </result>
      </results>
      <response>
        <RU_D1_CLASS_FEE_RESPONSE>
          <RESULT>True</RESULT>
          <CLASS_NBR INTERNATIONAL_FEE='4321.78' DOMESTIC_FEE='654.32' HST='262.59'
            OUT_OF_PROVINCE_FEE='1265.18'>434234
          </CLASS_NBR>
          <origin>MOCK_RU_D1_CLASS_FEE</origin>
        </RU_D1_CLASS_FEE_RESPONSE>
      </response>
    </integrationActionResult>
  </body>
</integrationMessage>