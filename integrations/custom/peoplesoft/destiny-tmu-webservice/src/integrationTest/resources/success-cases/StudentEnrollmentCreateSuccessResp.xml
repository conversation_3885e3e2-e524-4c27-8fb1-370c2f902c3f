<?xml version='1.0' encoding='UTF-8'?>
<integrationMessage>
  <body>
    <integrationActionResult>
      <status>SC0000</status>
      <results>
        <result>
          <status>SC0000</status>
          <entity>1147908</entity>
          <entityType>courseSectionLW</entityType>
          <entityInXMLFormat>false</entityInXMLFormat>
          <errorMsg>
          </errorMsg>
        </result>
      </results>
      <response>
        <SSR_ADD_ENROLLMENT_RESP>
          <detail>
            <MSGS>
              <MSG>
                <ID>1334455-50502345</ID>
                <DESCR>This class has been added to your schedule</DESCR>
                <MESSAGE_SEVERITY>M</MESSAGE_SEVERITY>
                <PROPS>
                  <PROP>
                    <SCC_ENTITY_INST_ID>1147908</SCC_ENTITY_INST_ID>
                    <PROPNAME>
                    </PROPNAME>
                  </PROP>
                </PROPS>
              </MSG>
            </MSGS>
          </detail>
          <ORIGINAL_MOCK>&lt;?xml version='1.0' encoding='UTF-8'?&gt;
            &lt;SSR_ADD_ENROLLMENT_REQ&gt;&lt;ENROLL_REQ_HEADER&gt;&lt;ENRL_REQUEST_ID/&gt;&lt;OPRID/&gt;&lt;ENRL_REQ_PROC_ST/&gt;&lt;ENRL_REQ_SOURCE/&gt;&lt;PROCESS_INSTANCE&gt;0&lt;/PROCESS_INSTANCE&gt;&lt;DTTM_STAMP_SEC/&gt;&lt;SSR_IS_ADMIN/&gt;&lt;ENROLL_REQUEST_DETAILS&gt;&lt;ENROLL_REQUEST_DETAIL&gt;&lt;ENRL_REQUEST_ID/&gt;&lt;ENRL_REQ_DETL_SEQ/&gt;&lt;EMPLID&gt;50502345&lt;/EMPLID&gt;&lt;ACAD_CAREER&gt;CNED&lt;/ACAD_CAREER&gt;&lt;INSTITUTION&gt;RYERU&lt;/INSTITUTION&gt;&lt;STRM&gt;11892&lt;/STRM&gt;&lt;CLASS_NBR&gt;1334455&lt;/CLASS_NBR&gt;&lt;ENRL_REQ_ACTION&gt;E&lt;/ENRL_REQ_ACTION&gt;&lt;ENRL_ACTION_REASON&gt;D1&lt;/ENRL_ACTION_REASON&gt;&lt;ENRL_ACTION_DT&gt;&lt;/ENRL_ACTION_DT&gt;&lt;UNT_TAKEN/&gt;&lt;UNT_EARNED/&gt;&lt;CRSE_COUNT/&gt;&lt;REPEAT_CODE/&gt;&lt;CRSE_GRADE_INPUT/&gt;&lt;GRADING_BASIS_ENRL/&gt;&lt;CLASS_PRMSN_NBR/&gt;&lt;CLASS_NBR_CHG_TO/&gt;&lt;DROP_CLASS_IF_ENRL/&gt;&lt;CHG_TO_WL_NUM/&gt;&lt;RELATE_CLASS_NBR_1/&gt;&lt;RELATE_CLASS_NBR_2/&gt;&lt;OVRD_CLASS_LIMIT&gt;&lt;/OVRD_CLASS_LIMIT&gt;&lt;OVRD_GRADING_BASIS/&gt;&lt;OVRD_CLASS_UNITS/&gt;&lt;OVRD_UNIT_LOAD/&gt;&lt;OVRD_CLASS_LINKS/&gt;&lt;OVRD_CLASS_PRMSN&gt;&lt;/OVRD_CLASS_PRMSN&gt;&lt;OVRD_REQUISITES/&gt;&lt;OVRD_TIME_CNFLCT/&gt;&lt;OVRD_CAREER/&gt;&lt;WAIT_LIST_OKAY&gt;N&lt;/WAIT_LIST_OKAY&gt;&lt;OVRD_ENRL_ACTN_DT&gt;&lt;/OVRD_ENRL_ACTN_DT&gt;&lt;OVRD_RQMNT_DESIG/&gt;&lt;OVRD_SRVC_INDIC/&gt;&lt;OVRD_APPT/&gt;&lt;INSTRUCTOR_ID/&gt;&lt;ENRL_REQ_DETL_STAT/&gt;&lt;RQMNT_DESIGNTN/&gt;&lt;RQMNT_DESIGNTN_OPT/&gt;&lt;RQMNT_DESIGNTN_GRD/&gt;&lt;TSCRPT_NOTE_ID/&gt;&lt;TSCRPT_NOTE_EXISTS/&gt;&lt;OPRID/&gt;&lt;DTTM_STAMP_SEC/&gt;&lt;START_DT/&gt;&lt;ACAD_PROG/&gt;&lt;SCC_ENTITY_INST_ID&gt;1147908&lt;/SCC_ENTITY_INST_ID&gt;&lt;/ENROLL_REQUEST_DETAIL&gt;&lt;/ENROLL_REQUEST_DETAILS&gt;&lt;/ENROLL_REQ_HEADER&gt;&lt;/SSR_ADD_ENROLLMENT_REQ&gt;
          </ORIGINAL_MOCK>
        </SSR_ADD_ENROLLMENT_RESP>
      </response>
    </integrationActionResult>
  </body>
</integrationMessage>