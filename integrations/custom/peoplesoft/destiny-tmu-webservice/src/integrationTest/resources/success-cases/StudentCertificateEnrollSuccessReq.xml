<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <student>
      <code></code>
      <createTime>20 Nov 2018 11:17:38 AM</createTime>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description></description>
      <name><PERSON></name>
      <objectId>-1</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>0</version>
      <addresses>
        <address>
          <typeCode>Home</typeCode>
          <city>Toronto</city>
          <country>USA</country>
          <effectiveDate>20 Nov 2018 11:12:59 AM</effectiveDate>
          <foreign>false</foreign>
          <foreignState></foreignState>
          <modifiedDate>20 Nov 2018 11:12:59 AM</modifiedDate>
          <preferred>true</preferred>
          <provinceState>GA</provinceState>
          <street0></street0>
          <street1>111 Main Street</street1>
          <street2>1</street2>
          <terminate>false</terminate>
          <postalZip>91210</postalZip>
        </address>
      </addresses>
      <birthDate>28 Oct 2000 12:00:00 AM</birthDate>
      <cautionaryRequired>false</cautionaryRequired>
      <communicationMethod>Email</communicationMethod>
      <contactMethods>
        <contactMethod>Any</contactMethod>
      </contactMethods>
      <emails>
        <email>
          <emailAddress><EMAIL></emailAddress>
          <typeCode>Standard</typeCode>
          <preferred>true</preferred>
          <release>false</release>
          <return>false</return>
        </email>
      </emails>
      <socialSecurityNum></socialSecurityNum>
      <firstName1>Jimmy</firstName1>
      <firstName2></firstName2>
      <interestAreaAssociations/>
      <lastName>Beam</lastName>
      <lmsPersonId>X000072</lmsPersonId>
      <loginId>RUCE_X000072</loginId>
      <personNumber>X000072</personNumber>
      <preferredEmail>
        <emailAddress><EMAIL></emailAddress>
        <typeCode>Standard</typeCode>
        <preferred>true</preferred>
        <release>false</release>
        <return>false</return>
      </preferredEmail>
      <preferredLocaleString>en_US</preferredLocaleString>
      <privacyQuestions>
        <privacyQuestion>
          <objectId>0</objectId>
          <version>0</version>
          <answer>aa</answer>
          <question>1</question>
        </privacyQuestion>
        <privacyQuestion>
          <objectId>0</objectId>
          <version>0</version>
          <answer>aa</answer>
          <question>1</question>
        </privacyQuestion>
      </privacyQuestions>
      <salutationCode>Fr</salutationCode>
      <schoolPersonnelNumber>213213</schoolPersonnelNumber>
      <telephones>
        <telephone>
          <areaCode>416</areaCode>
          <preferred>true</preferred>
          <release>false</release>
          <tcpaConsented>false</tcpaConsented>
          <telephoneExt></telephoneExt>
          <telephoneNumber>5555555</telephoneNumber>
          <typeCode>Home</typeCode>
        </telephone>
      </telephones>
      <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
      <additionalAssociation></additionalAssociation>
      <birthCity></birthCity>
      <birthState></birthState>
      <copyOfVisa>false</copyOfVisa>
      <countryOfCitizenship></countryOfCitizenship>
      <countryOfOrigin></countryOfOrigin>
      <department></department>
      <directBillingAccount></directBillingAccount>
      <documentEmailed>true</documentEmailed>
      <documentFaxed>false</documentFaxed>
      <educationHistory/>
      <emergencyContactEmail></emergencyContactEmail>
      <emergencyContactName></emergencyContactName>
      <employerEmail></employerEmail>
      <employerFirstName></employerFirstName>
      <employerJobTitle></employerJobTitle>
      <employerName></employerName>
      <employerSalutation></employerSalutation>
      <employerSurname></employerSurname>
      <enrollmentTimeframes/>
      <internationalStudent>false</internationalStudent>
      <jobTitle></jobTitle>
      <laterLifeLearner>false</laterLifeLearner>
      <learningGoals/>
      <major></major>
      <nameTagName></nameTagName>
      <otherInterests></otherInterests>
      <otherLearningGoals></otherLearningGoals>
      <otherNames></otherNames>
      <preferredName>George</preferredName>
      <proficiencyExamScores/>
      <profileStatus>Active</profileStatus>
      <schoolStudentNumber></schoolStudentNumber>
      <secondCountryOfCitizenship></secondCountryOfCitizenship>
      <studentCategories/>
      <studentCredentials/>
      <studentNumber>X000072</studentNumber>
      <udfValues>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1001</objectId>
            <version>1</version>
            <udfFieldName>oenNumber</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1002</objectId>
            <version>1</version>
            <udfFieldName>attendanceQuestion</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>Yes</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1003</objectId>
            <version>1</version>
            <udfFieldName>attendanceYear</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1004</objectId>
            <version>1</version>
            <udfFieldName>existingStudentNumber</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1005</objectId>
            <version>1</version>
            <udfFieldName>citizenshipStatus</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>0</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1006</objectId>
            <version>2</version>
            <udfFieldName>dateOfEntry</udfFieldName>
          </udfFieldSpec>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1007</objectId>
            <version>1</version>
            <udfFieldName>countryOfCitizenship</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1008</objectId>
            <version>1</version>
            <udfFieldName>certificate</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1009</objectId>
            <version>1</version>
            <udfFieldName>certificateApprovalCode</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1010</objectId>
            <version>1</version>
            <udfFieldName>studentGender</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>M</udfFieldValue>
        </udfValue>
      </udfValues>
      <willStudyInCountry>false</willStudyInCountry>
      <willTravelToCountry>false</willTravelToCountry>
      <willWorkInCountry>false</willWorkInCountry>
      <youthParticipant>false</youthParticipant>
    </student>
  </body>
</integrationMessage>