<?xml version="1.0" encoding="UTF-8"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <student>
      <code/>
      <createTime>29 Mar 2019 10:03:27 AM</createTime>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description/>
      <name>TestCAPClientA TestCAPClientB</name>
      <objectId>-1</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>0</version>
      <addresses>
        <address>
          <typeCode>Home</typeCode>
          <city>Toronto</city>
          <country>Canada</country>
          <effectiveDate>29 Mar 2019 09:59:34 AM</effectiveDate>
          <foreign>false</foreign>
          <foreignState/>
          <modifiedDate>29 Mar 2019 09:59:34 AM</modifiedDate>
          <preferred>true</preferred>
          <provinceState>ON</provinceState>
          <street0/>
          <street1>123 Work St</street1>
          <street2/>
          <terminate>false</terminate>
          <postalZip>M4S 3C3</postalZip>
        </address>
      </addresses>
      <birthDate>26 Mar 1991 12:00:00 AM</birthDate>
      <cautionaryRequired>false</cautionaryRequired>
      <communicationMethod>Email</communicationMethod>
      <contactMethods>
        <contactMethod>Any</contactMethod>
      </contactMethods>
      <emails>
        <email>
          <emailAddress><EMAIL></emailAddress>
          <typeCode>Standard</typeCode>
          <preferred>true</preferred>
          <release>false</release>
          <return>false</return>
        </email>
      </emails>
      <socialSecurityNum/>
      <firstName1>TestCAPClientA</firstName1>
      <firstName2/>
      <interestAreaAssociations/>
      <lastName>TestCAPClientB</lastName>
      <lmsPersonId>X000655</lmsPersonId>
      <loginId>RUCE_X000655</loginId>
      <netId>66133</netId>
      <personNumber>X000655</personNumber>
      <preferredEmail>
        <emailAddress><EMAIL></emailAddress>
        <typeCode>Standard</typeCode>
        <preferred>true</preferred>
        <release>false</release>
        <return>false</return>
      </preferredEmail>
      <preferredLocaleString>en_US</preferredLocaleString>
      <privacyQuestions/>
      <salutationCode/>
      <schoolPersonnelNumber>5423123</schoolPersonnelNumber>
      <telephones>
        <telephone>
          <areaCode>231</areaCode>
          <preferred>true</preferred>
          <release>false</release>
          <tcpaConsented>false</tcpaConsented>
          <telephoneExt/>
          <telephoneNumber>1232323</telephoneNumber>
          <typeCode>Home</typeCode>
        </telephone>
      </telephones>
      <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
      <copyOfVisa>false</copyOfVisa>
      <directBillingAccount/>
      <documentEmailed>false</documentEmailed>
      <documentFaxed>false</documentFaxed>
      <educationHistory/>
      <employerName/>
      <employerSalutation/>
      <internationalStudent>false</internationalStudent>
      <jobTitle/>
      <laterLifeLearner>false</laterLifeLearner>
      <learningGoals/>
      <otherNames/>
      <proficiencyExamScores/>
      <profileStatus>Active</profileStatus>
      <studentCategories>
        <studentCategory>
          <category>Domestic</category>
        </studentCategory>
        <studentCategory>
          <category>International</category>
        </studentCategory>
      </studentCategories>
      <studentCredentials/>
      <studentNumber>X000655</studentNumber>
      <udfValues>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1006</objectId>
            <version>1</version>
            <udfFieldName>dateOfEntry</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>01 Jan 2001 12:00:00 AM</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1005</objectId>
            <version>1</version>
            <udfFieldName>citizenshipStatus</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>1</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1007</objectId>
            <version>1</version>
            <udfFieldName>countryOfCitizenship</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>UZB</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1003</objectId>
            <version>1</version>
            <udfFieldName>attendanceYear</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>1900</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1010</objectId>
            <version>1</version>
            <udfFieldName>studentGender</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>F</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1002</objectId>
            <version>1</version>
            <udfFieldName>attendanceQuestion</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>No</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>0</objectId>
          <version>0</version>
          <udfFieldSpec>
            <objectId>1008</objectId>
            <version>1</version>
            <udfFieldName>certificate</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>PUBNG</udfFieldValue>
        </udfValue>
      </udfValues>
      <willStudyInCountry>false</willStudyInCountry>
      <willTravelToCountry>false</willTravelToCountry>
      <willWorkInCountry>false</willWorkInCountry>
      <youthParticipant>false</youthParticipant>
    </student>
  </body>
</integrationMessage>