<?xml version='1.0' encoding='UTF-8'?>
<integrationMessage>
  <body>
    <integrationActionResult>
      <status>SC0000</status>
      <results>
        <result>
          <status>SC0000</status>
          <entity>
            <map>
              <entries>
                <entry>
                  <key>1201:142794:000000002710</key>
                  <value>OPTED_IN</value>
                </entry>
                <entry>
                  <key>1201:142794:000000002712</key>
                  <value>OPTED_IN</value>
                </entry>
              </entries>
            </map>
          </entity>
          <entityType>valueMap</entityType>
          <entityInXMLFormat>true</entityInXMLFormat>
          <errorMsg>
          </errorMsg>
        </result>
      </results>
      <response>
        <RU_D1_GET_OPTINOUT_RESPONSE>
          <STUDENTID>123456</STUDENTID>
          <D1_PASS_THROUGH>{"1201:142794":["000000002710","000000002712"]}</D1_PASS_THROUGH>
          <STRMS>
            <STRM>1201</STRM>
            <ISEDITABLE>N</ISEDITABLE>
            <ITEM_TYPE OPTOUT='N' DESCR='CESAR CE'>000000002710</ITEM_TYPE>
            <ITEM_TYPE OPTOUT='N' DESCR='CESAR CE'>000000002712</ITEM_TYPE>
          </STRMS>
          <original>
            <RU_D1_GET_OPTINOUT_REQUEST>
              <STUDENTID>123456</STUDENTID>
              <ACAD_CAREER>CNED</ACAD_CAREER>
              <STRM>1201</STRM>
              <D1_PASS_THROUGH>{"1201:142794":["000000002710","000000002712"]}</D1_PASS_THROUGH>
            </RU_D1_GET_OPTINOUT_REQUEST>
          </original>
        </RU_D1_GET_OPTINOUT_RESPONSE>
      </response>
    </integrationActionResult>
  </body>
</integrationMessage>