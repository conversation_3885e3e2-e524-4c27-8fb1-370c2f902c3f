<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <student>
      <code>code18847</code>
      <createTime>06 Jun 2017 05:55:52 PM</createTime>
      <creator>
        <objectId>1</objectId>
        <version>9</version>
        <loginId>super</loginId>
        <personType>Staff</personType>
        <printName>Super User</printName>
      </creator>
      <description></description>
      <name><PERSON></name>
      <objectId>18847</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>1</objectId>
        <version>9</version>
        <loginId>super</loginId>
        <personType>Staff</personType>
        <printName>Super User</printName>
      </updater>
      <version>148</version>
      <addresses>
        <address>
          <typeCode>Home</typeCode>
          <city>Toronto</city>
          <country>USA</country>
          <effectiveDate>06 Jun 2017 05:54:55 PM</effectiveDate>
          <foreign>false</foreign>
          <foreignState></foreignState>
          <modifiedDate>06 Jun 2017 05:55:32 PM</modifiedDate>
          <preferred>true</preferred>
          <provinceState>OH</provinceState>
          <street0></street0>
          <street1>1111 Main Street</street1>
          <street2></street2>
          <terminate>false</terminate>
          <postalZip>91210</postalZip>
        </address>
      </addresses>
      <alternateName/>
      <birthDate>01 May 2018 12:00:00 AM</birthDate>
      <cautionaryRequired>false</cautionaryRequired>
      <comments/>
      <communicationMethod>Email</communicationMethod>
      <contactMethods>
        <contactMethod>Any</contactMethod>
      </contactMethods>
      <emails>
        <email>
          <emailAddress><EMAIL></emailAddress>
          <typeCode>Standard</typeCode>
          <preferred>true</preferred>
          <release>false</release>
          <return>false</return>
        </email>
      </emails>
      <socialSecurityNum></socialSecurityNum>
      <firstName1>Anita</firstName1>
      <firstName2></firstName2>
      <genderCode>F</genderCode>
      <interestAreaAssociations/>
      <lastName>Huang</lastName>
      <lmsPersonId>X000001</lmsPersonId>
      <loginId>X000001</loginId>
      <netId></netId>
      <personNumber>X000001</personNumber>
      <preferredContactTime></preferredContactTime>
      <preferredEmail>
        <emailAddress><EMAIL></emailAddress>
        <typeCode>Standard</typeCode>
        <preferred>true</preferred>
        <release>false</release>
        <return>false</return>
      </preferredEmail>
      <preferredLocaleString>en_US</preferredLocaleString>
      <privacyQuestions>
        <privacyQuestion>
          <objectId>48071</objectId>
          <version>52</version>
          <answer>aa</answer>
          <question>0</question>
        </privacyQuestion>
        <privacyQuestion>
          <objectId>48072</objectId>
          <version>52</version>
          <answer>aa</answer>
          <question>1</question>
        </privacyQuestion>
      </privacyQuestions>
      <profileHolds/>
      <salutationCode></salutationCode>
      <schoolPersonnelNumber>500202438</schoolPersonnelNumber>
      <telephones>
        <telephone>
          <areaCode>416</areaCode>
          <preferred>true</preferred>
          <release>false</release>
          <tcpaConsented>false</tcpaConsented>
          <telephoneExt></telephoneExt>
          <telephoneNumber>5555555</telephoneNumber>
          <typeCode>Home</typeCode>
        </telephone>
      </telephones>
      <acceptanceDocumentMailAddressType>Home</acceptanceDocumentMailAddressType>
      <additionalAssociation></additionalAssociation>
      <birthCity></birthCity>
      <birthState></birthState>
      <copyOfVisa>false</copyOfVisa>
      <countryOfCitizenship></countryOfCitizenship>
      <countryOfOrigin></countryOfOrigin>
      <department></department>
      <directBillingAccount></directBillingAccount>
      <documentEmailed>false</documentEmailed>
      <documentFaxed>false</documentFaxed>
      <educationHistory/>
      <emergencyContactEmail></emergencyContactEmail>
      <emergencyContactName></emergencyContactName>
      <emergencyMedicalInformation></emergencyMedicalInformation>
      <employerEmail></employerEmail>
      <employerFirstName></employerFirstName>
      <employerName></employerName>
      <employerSurname></employerSurname>
      <englishAbility></englishAbility>
      <enrolledPreviously>false</enrolledPreviously>
      <enrollmentRaces/>
      <enrollmentTimeframes/>
      <enrolmentGroups/>
      <housingPreference></housingPreference>
      <immigrationStatus></immigrationStatus>
      <internationalStudent>false</internationalStudent>
      <jobTitle></jobTitle>
      <laterLifeLearner>false</laterLifeLearner>
      <learningGoals/>
      <nameTagName>Anita Huang</nameTagName>
      <nativeLanguage></nativeLanguage>
      <occupation></occupation>
      <otherEnrollmentTimeframe></otherEnrollmentTimeframe>
      <otherLearningGoals></otherLearningGoals>
      <otherNames></otherNames>
      <parentGuardianGivenName></parentGuardianGivenName>
      <parentGuardianSurname></parentGuardianSurname>
      <passportIssuingCountry></passportIssuingCountry>
      <passportNumber></passportNumber>
      <preferredName></preferredName>
      <proficiencyExamScores/>
      <profileStatus>Active</profileStatus>
      <SEVISid></SEVISid>
      <secondCountryOfCitizenship></secondCountryOfCitizenship>
      <studentAssociationDetails/>
      <studentCategories/>
      <studentCredentials/>
      <studentNumber>X000001</studentNumber>
      <udfValues>
        <udfValue>
          <objectId>63310</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1001</objectId>
            <version>1</version>
            <udfFieldName>oenNumber</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63311</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1002</objectId>
            <version>1</version>
            <udfFieldName>attendanceQuestion</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>No</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63312</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1003</objectId>
            <version>1</version>
            <udfFieldName>attendanceYear</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63313</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1004</objectId>
            <version>1</version>
            <udfFieldName>existingStudentNumber</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63314</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1005</objectId>
            <version>1</version>
            <udfFieldName>citizenshipStatus</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>Landed Immigrant</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63315</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1006</objectId>
            <version>2</version>
            <udfFieldName>dateOfEntry</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue>01 Jun 2018 12:00:00 AM</udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63760</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1007</objectId>
            <version>1</version>
            <udfFieldName>countryOfCitizenship</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63761</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1008</objectId>
            <version>1</version>
            <udfFieldName>certificate</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
        <udfValue>
          <objectId>63762</objectId>
          <version>50</version>
          <udfFieldSpec>
            <objectId>1009</objectId>
            <version>1</version>
            <udfFieldName>certificateApprovalCode</udfFieldName>
          </udfFieldSpec>
          <udfFieldValue></udfFieldValue>
        </udfValue>
      </udfValues>
      <visaNumber></visaNumber>
      <visaType></visaType>
      <willStudyInCountry>false</willStudyInCountry>
      <willTravelToCountry>false</willTravelToCountry>
      <willWorkInCountry>false</willWorkInCountry>
      <youthParticipant>true</youthParticipant>
    </student>
  </body>
</integrationMessage>