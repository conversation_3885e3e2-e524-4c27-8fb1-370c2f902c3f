<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<integrationMessage>
  <action>NEW</action>
  <body>
    <courseSectionProfile>
      <code>002</code>
      <creator>
        <objectId>0</objectId>
        <version>0</version>
      </creator>
      <description></description>
      <name></name>
      <objectId>18979</objectId>
      <objectStatusCode>active</objectStatusCode>
      <updater>
        <objectId>0</objectId>
        <version>0</version>
      </updater>
      <version>0</version>
      <activeWaitListSize>0</activeWaitListSize>
      <courseSectionAssociations/>
      <associatedCourse>
        <objectId>18784</objectId>
        <version>0</version>
        <code>18784</code>
        <courseVersionNumber>0</courseVersionNumber>
        <name></name>
      </associatedCourse>
      <associatedSemester>
        <objectId>18768</objectId>
        <version>0</version>
        <campusId>1189</campusId>
      </associatedSemester>
      <autoFilledInCompletionDate>false</autoFilledInCompletionDate>
      <campusFisId>1010</campusFisId>
      <eCollege>false</eCollege>
      <enrolListSize>0</enrolListSize>
      <enrollmentByUpload>false</enrollmentByUpload>
      <evalutionCompleted>false</evalutionCompleted>
      <familyCheckoutEligible>false</familyCheckoutEligible>
      <invokedCorpContractPricing>false</invokedCorpContractPricing>
      <maxCEUnit>0.0</maxCEUnit>
      <minCEUnit>0.0</minCEUnit>
      <multipleProCeditAllowed>false</multipleProCeditAllowed>
      <onlineResources/>
      <onlineTransferRequestDays>7</onlineTransferRequestDays>
      <onlineWithdrawalRequestDays>7</onlineWithdrawalRequestDays>
      <optInProCredit>false</optInProCredit>
      <preventOnlineEnrollment>false</preventOnlineEnrollment>
      <reservedListSize>0</reservedListSize>
      <scheduleTypeCode>Undefined</scheduleTypeCode>
      <sectionContacts/>
      <sectionMaterials/>
      <completionRuleTypeCode>CompleteOnSchEndDate</completionRuleTypeCode>
      <totalSessionHours>0.0</totalSessionHours>
      <waitListSize>0</waitListSize>
    </courseSectionProfile>
  </body>
</integrationMessage>