spring.profiles.active=dev
peoplesoft.integration.d1-tmu-token-retrieve.by-pass-encryption=true
peoplesoft.integration.d1-tmu-token-retrieve.service-mode=mock
peoplesoft.integration.d1-tmu-enrollment-validate.service-mode=mock
peoplesoft.integration.d1-tmu-enrollment-create.service-mode=mock
peoplesoft.integration.d1-tmu-enrollment-drop.service-mode=mock
peoplesoft.integration.d1-tmu-section-availability-retrieve.service-mode=mock
peoplesoft.integration.d1-tmu-account-settlement.by-pass-encryption=true
peoplesoft.integration.d1-tmu-account-settlement.service-mode=mock
peoplesoft.integration.d1-tmu-student-profile-retrieve.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-profile-retrieve.service-mode=mock
peoplesoft.integration.d1-tmu-section-capacity-reserve.by-pass-encryption=true
peoplesoft.integration.d1-tmu-section-capacity-reserve.service-mode=mock
peoplesoft.integration.d1-tmu-section-fee-retrieve.by-pass-encryption=true
peoplesoft.integration.d1-tmu-section-fee-retrieve.service-mode=mock
peoplesoft.integration.d1-tmu-student-lookup.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-lookup.service-mode=mock
peoplesoft.integration.d1-tmu-student-residency-notify.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-residency-notify.service-mode=mock
peoplesoft.integration.d1-tmu-student-certificate-enroll.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-certificate-enroll.service-mode=mock
peoplesoft.integration.d1-tmu-student-status-validate.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-status-validate.service-mode=mock
peoplesoft.integration.d1-tmu-student-choice-retrieve.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-choice-retrieve.service-mode=mock
peoplesoft.integration.d1-tmu-student-choice-update.by-pass-encryption=true
peoplesoft.integration.d1-tmu-student-choice-update.service-mode=mock