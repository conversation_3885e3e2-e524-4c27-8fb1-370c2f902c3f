package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder;

import static org.apache.camel.builder.Builder.method;
import static org.apache.camel.converter.crypto.CryptoDataFormat.KEY;
import static org.apache.camel.support.builder.PredicateBuilder.not;
import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import org.apache.camel.builder.DataFormatBuilderFactory;
import org.apache.camel.model.ChoiceDefinition;
import org.apache.camel.model.ProcessorDefinition;
import org.apache.camel.model.dataformat.Base64DataFormat;
import org.apache.camel.model.dataformat.CryptoDataFormat;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomEncryptionRouteBuilder {

    static final String METHOD_GET_PADDED_VALUE = "getPaddedValue";
    static final String METHOD_GET_SECRET_KEY_INST = "getSecretKeyInst";
    static final String METHOD_GET_ENCRYPT_FIELD_KEY = "getAuth().getEncryptFieldKey()";
    static final String METHOD_GET_IV_IN_BYTES = "getIVInBytes";
    static final String METHOD_GET_ENCRYPT_FIELD_IV = "getAuth().getEncryptFieldIV()";
    static final String CAMEL_CRYPTO_INIT_VECTOR = "CamelCryptoInitVector";
    static final String METHOD_BYPASS_ENCRYPTION = "byPassEncryption";

    CryptoDataFormat d1TMUPeopleSoftTokenEncryptFormat;

    DataFormatBuilderFactory factory;

    Base64DataFormat base64DataFormat;

    public ProcessorDefinition<?> buildRoute(Object helper, Class<?> baseConfigClass, Class<?> pipelineConfigClass,
            ProcessorDefinition<?> processorDefinition, boolean skipPaddedValue) {
        ChoiceDefinition byPassEncryptionChoice = processorDefinition
                .choice().when(not(method(pipelineConfigClass,
                        METHOD_BYPASS_ENCRYPTION)));
        if (!skipPaddedValue) {
            byPassEncryptionChoice.bean(helper, METHOD_GET_PADDED_VALUE);
        }
        return byPassEncryptionChoice.setHeader(KEY, method(helper,
                        METHOD_GET_SECRET_KEY_INST + "(${bean:" + beanRef(
                                baseConfigClass)
                                + "?method=" + METHOD_GET_ENCRYPT_FIELD_KEY + "})"))
                .setHeader(CAMEL_CRYPTO_INIT_VECTOR, method(helper,
                        METHOD_GET_IV_IN_BYTES + "(${bean:" + beanRef(
                                baseConfigClass)
                                + "?method=" + METHOD_GET_ENCRYPT_FIELD_IV + "})"))
                .marshal(d1TMUPeopleSoftTokenEncryptFormat)
                .marshal(base64DataFormat);
    }

    public ProcessorDefinition<?> buildRoute(Object helper, Class<?> baseConfigClass, Class<?> pipelineConfigClass,
            ProcessorDefinition<?> processorDefinition) {
        return buildRoute(helper, baseConfigClass, pipelineConfigClass, processorDefinition, false);
    }
}
