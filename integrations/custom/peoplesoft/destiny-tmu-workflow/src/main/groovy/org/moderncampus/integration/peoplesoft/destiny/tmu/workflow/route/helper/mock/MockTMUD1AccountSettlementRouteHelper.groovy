package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock


import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1AccountSettlementRouteHelper {

    def String mapRequest(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        return extractNodeText(rootNode['RU_TERMINAL_ID'] as NodeChildren)
    }

    def boolean isFault(String terminalId) {
        return terminalId == null || (terminalId != null && (terminalId.endsWith("XX1") || terminalId.endsWith("XX2")))
    }

    def boolean isServerError(String terminalId) {
        return terminalId != null && terminalId.endsWith("401")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException("", 401)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(String terminalId) {
        boolean case1 = terminalId == null || terminalId.endsWith('XX1')
        boolean case2 = terminalId != null && terminalId.endsWith('XX2')
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_SETTLEMENT_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"(case2 ? "1003" : "1006")
                            "DESCR"(case2 ? "No corrresponding STUDENT ID exists for DESTINY ID" : "No corrresponding TERMINAL ID and REF NUM exists")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def ruRefNum = extractNodeText(new XmlSlurper().parseText(originalReq)['RU_REF_NUM'] as GPathResult)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_SETTLEMENT_RESPONSE"("xmlns:RU": "http://xmlns.ryerson.ca/ps/sas/services") {
                "RU_REF_NUM"(ruRefNum)
                "SETTLED"("Y")
                "original"(originalReq)
            }
        }
        return xml.toString()
    }
}
