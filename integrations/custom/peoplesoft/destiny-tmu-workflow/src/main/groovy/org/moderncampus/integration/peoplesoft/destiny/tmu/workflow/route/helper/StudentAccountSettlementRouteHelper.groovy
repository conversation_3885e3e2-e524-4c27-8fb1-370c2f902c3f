package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.moderncampus.integration.route.Constants
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentAccountSettlementRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftRouteHelper {

    public String mapInboundRequest(Exchange request) {
        def rootNode = parseXMLBody(request.message.body as String)
        NodeChildren requestEntries = rootNode['body']['valueMap']['entries']['entry'] as NodeChildren
        GPathResult terminalIdEntry = getTerminalIdEntry(requestEntries)
        GPathResult refNumEntry = getRefNumEntry(requestEntries)
        if (isRequestInvalid(requestEntries, terminalIdEntry, refNumEntry)) {
            throw new ApplicationException("Request does not contain the RU_REF_NUM or the RU_TERMINAL_ID")
        }
        request.message.setHeader(Constants.ROUTE_HEADER_PREFIX + 'RU_REF_NUM', extractNodeText(refNumEntry['value'] as GPathResult))
        return extractNodeText(terminalIdEntry['value'] as GPathResult)
    }

    boolean isRequestInvalid(NodeChildren requestEntries, GPathResult terminalIdEntry, GPathResult refNumEntry) {
        return requestEntries.isEmpty() || terminalIdEntry.isEmpty() || refNumEntry.isEmpty()
    }

    GPathResult getTerminalIdEntry(NodeChildren requestEntry) {
        return requestEntry.find { node ->
            extractNodeText(node['key'] as GPathResult) == 'RU_TERMINAL_ID'
        } as GPathResult
    }

    GPathResult getRefNumEntry(NodeChildren requestEntry) {
        return requestEntry.find { node ->
            extractNodeText(node['key'] as GPathResult) == 'RU_REF_NUM'
        } as GPathResult
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMUSettlementRequest(Exchange exchange) {
        String terminalId = exchange.message.getBody(String.class)
        String refNum = exchange.message.getHeader(Constants.ROUTE_HEADER_PREFIX + 'RU_REF_NUM', String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_SETTLEMENT_REQUEST"() {
                "RU_TERMINAL_ID"(terminalId)
                "RU_REF_NUM"(refNum)
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        boolean isFault = isFaultResponse(response, true)
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault ? '' : mkp.yieldUnescaped(getEntityContent(response))
            }
            "entityType"(VALUE_MAP)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(NodeChild respRoot) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "map"() {
                "entries"() {
                    "entry"() {
                        "key"('RU_REF_NUM')
                        "value"(extractNodeText respRoot['RU_REF_NUM'])
                    }
                    "entry"() {
                        "key"("SETTLED")
                        "value"(extractNodeText respRoot['SETTLED'])
                    }
                }
            }
        }
        return xml.toString()
    }
}
