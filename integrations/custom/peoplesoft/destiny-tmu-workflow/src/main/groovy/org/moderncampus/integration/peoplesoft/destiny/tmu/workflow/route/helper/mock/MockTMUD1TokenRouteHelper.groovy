package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1TokenRouteHelper {

    def String mapRequest(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        return extractNodeText(rootNode['STUDENTID'] as NodeChildren)
    }

    def boolean isFault(String studentId) {
        return studentId != null && studentId.endsWith("XXX")
    }

    def boolean isClientError(String studentId) {
        return studentId != null && studentId.endsWith("400")
    }

    def boolean isServerError(String studentId) {
        return studentId != null && studentId.endsWith("500")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException("Mock server error: " + studentId, 500)
    }

    def void getClientErrorContent() {
        throw new ApplicationException(JsonOutput.toJson([message: 'MOCK_RU_D1_TOKEN', data: "[&lt;?xml version=&quot;1.0&quot;?&gt;&lt;" +
                "RU_D1_TOKEN_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;&lt;FAULT&gt;&lt;" +
                "MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;MESSAGE_SEVERITY&gt;E&lt;" +
                "/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_TOKEN_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent() {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_TOKEN_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1015[mockid]")
                            "DESCR"("Something went wrong.")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def studentId = exchange.message.getBody(String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_TOKEN_RESPONSE"() {
                "STUDENTID"(studentId)
                "PS_TOKEN"(org.moderncampus.integration.peoplesoft.destiny.tmu.component.constants.Constants.PS_TOKEN_KEY + "_" + studentId)
                "original"(originalReq)
            }
        }
        return xml.toString()
    }
}
