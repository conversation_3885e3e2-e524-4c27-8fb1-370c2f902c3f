package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper


import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class SectionFeeRetrieveRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper, D1TMUPeopleSoftSectionRouteHelper,
        D1TMUPeopleSoftRouteHelper {

    static Logger LOGGER = LoggerFactory.getLogger(SectionFeeRetrieveRouteHelper.class)

    protected static final String PARSED_SECTION_PROFILE = "parsedSectionProfile"

    def String mapInboundRequest(Exchange exchange) {
        String body = exchange.message.getBody(String.class)
        NodeChild parsedReq = parseXMLBody(body)
        def sectionProfile = extractCourseSectionProfileFromRequest(parsedReq)
        validateRequest(body, sectionProfile)
        exchange.setProperty(PARSED_SECTION_PROFILE, sectionProfile)
        return extractStrm(sectionProfile)
    }

    private void validateRequest(String request, NodeChildren sectionProfile) {
        if (sectionProfile.isEmpty() || sectionProfile.size() > 1) {
            def emptyExceptionDetail = [error     : "Invalid Request",
                                        reason    : "Request must contain exactly one course section profile",
                                        original  : request,
                                        resolution: "Review and correct payload"]
            throw new ApplicationException(JsonOutput.toJson(emptyExceptionDetail), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMUClassFeeRequest(Exchange exchange) {
        String strm = exchange.message.getBody(String.class)
        def sectionProfile = exchange.getProperty(PARSED_SECTION_PROFILE, NodeChildren.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_CLASS_FEE_REQUEST"() {
                "ACAD_CAREER"('CNED')
                "STRM"(strm)
                "CLASS_NBR"(extractClassNbr(sectionProfile))
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response, true)
        NodeChildren classNbrNode = response['CLASS_NBR'] as NodeChildren
        BigDecimal sectionFee = getSectionFeeItemAmount(response)
        BigDecimal tuitionFee = getTuitionFeeItemAmount(response)
        BigDecimal outOfProvinceFee = getOutOfProvinceFeeItemAmount(response)
        BigDecimal hst = getHst(response)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_UNKNOWN_ERROR : (!isResultFound(response) ? STATUS_CODE_DATA_VALIDATION_ERROR : STATUS_CODE_SUCCESS))
            if (!isFault) {
                "entity"() {
                    mkp.yieldUnescaped(getEntityContent(classNbrNode, sectionFee, tuitionFee,
                            outOfProvinceFee, hst))
                }
            } else {
                mkp.yield response.'*'
            }
            "entityType"(ENTITY_COURSE_SECTION_FEE)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultDescriptions(response)) : ''
            }
        }
        return xml.toString()
    }

    private boolean isResultFound(NodeChild response) {
        return getSectionFeeItemAmount(response) > 0 || getTuitionFeeItemAmount(response) > 0
    }

    private boolean isResultTrue(NodeChild response) {
        return !(response['RESULT'] as NodeChildren).isEmpty() && Boolean.valueOf(extractNodeText(response['RESULT'] as NodeChildren))
    }

    private List<String> getFaultIds(NodeChild rootNode) {
        return (rootNode['FAULT']['MSGS']['MSG'] as NodeChildren).collect { it ->
            {
                NodeChild msgNode = it as NodeChild
                extractNodeText(msgNode['ID'] as NodeChildren)
            }
        } as List<String>
    }

    private String getFaultDescriptions(NodeChild rootNode) {
        return (rootNode['FAULT']['MSGS']['MSG'] as NodeChildren).collect { it ->
            {
                NodeChild msgNode = it as NodeChild
                extractNodeText(msgNode['DESCR'] as NodeChildren)
            }
        }.join(",")
    }

    private BigDecimal getSectionFeeItemAmount(NodeChild resp) {
        String domesticFee = getDomesticFee(resp)
        return domesticFee ? BigDecimal.valueOf(domesticFee as double).abs() : 0
    }

    private BigDecimal getTuitionFeeItemAmount(NodeChild resp) {
        String intlFee = getIntlFee(resp)
        return intlFee ? BigDecimal.valueOf(intlFee as double).abs() : 0
    }

    private BigDecimal getOutOfProvinceFeeItemAmount(NodeChild resp) {
        String outOfProvinceFee = getOutOfProvinceFee(resp)
        return outOfProvinceFee ? BigDecimal.valueOf(outOfProvinceFee as double).abs() : 0
    }

    private BigDecimal getHst(NodeChild resp) {
        String hst = extractNodeText(resp['CLASS_NBR']["@HST"] as NodeChildren)
        return hst ? BigDecimal.valueOf(hst as double).abs() : 0
    }

    private String getDomesticFee(NodeChild resp) {
        return extractNodeText(resp['CLASS_NBR']["@DOMESTIC_FEE"] as NodeChildren)
    }

    private String getIntlFee(NodeChild resp) {
        return extractNodeText(resp['CLASS_NBR']["@INTERNATIONAL_FEE"] as NodeChildren)
    }

    private String getOutOfProvinceFee(NodeChild resp) {
        return extractNodeText(resp['CLASS_NBR']["@OUT_OF_PROVINCE_FEE"] as NodeChildren)
    }

    private BigDecimal getTotalFeeItemAmount(NodeChildren classNbrNode, BigDecimal feeAmount) {
        try {
            BigDecimal amount = (classNbrNode[0] as NodeChild).attributes().findAll { attrName, attrValue ->
                return (attrName as String).startsWith("ITEM_TYPE_FEE")
            }.collect { it ->
                BigDecimal.valueOf(it.value as double)
            }.inject(feeAmount) { BigDecimal accum, BigDecimal currentValue ->
                accum.subtract(currentValue)
            }
            return amount
        } catch (Exception e) {
            LOGGER.info("Exception caught during mapping", e)
            throw e
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(NodeChildren classNbrNode, BigDecimal sectionFeeAmt, BigDecimal tuitionFeeAmt, BigDecimal outOfProvinceFeeAmt,
                                BigDecimal hstAmt) {
        boolean sectionFeeZero = sectionFeeAmt?.compareTo(BigDecimal.ZERO) == 0
        boolean tuitionFeeZero = tuitionFeeAmt?.compareTo(BigDecimal.ZERO) == 0
        boolean outOfProvinceFeeZero = outOfProvinceFeeAmt?.compareTo(BigDecimal.ZERO) == 0
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "courseSectionFee"() {
                "associatedSectionFeeTuitionProfiles"() {
                    if (!sectionFeeZero) {
                        "associatedSectionFeeTuitionProfile"() {
                            "associatedTuitionProfile"() {
                                "tuitionFees"() {
                                    "tuitionFee"() {
                                        "printCode"('Domestic Fee')
                                        "tuitionFeeItems"() {
                                            "tuitionFeeItem"() {
                                                "name"('Tuition')
                                                "amount"(getTotalFeeItemAmount(classNbrNode, sectionFeeAmt).add(hstAmt))
                                            }
                                        }
                                    }
                                }
                            }
                            "markedAsFlatFee"(true)
                        }
                    }
                    if (!tuitionFeeZero) {
                        "associatedSectionFeeTuitionProfile"() {
                            "associatedTuitionProfile"() {
                                "code"('TPINTL')
                                "tuitionFees"() {
                                    "tuitionFee"() {
                                        "printCode"('International Fee')
                                        "tuitionFeeItems"() {
                                            "tuitionFeeItem"() {
                                                "name"()
                                                "amount"(getTotalFeeItemAmount(classNbrNode, tuitionFeeAmt).add(hstAmt))
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!outOfProvinceFeeZero) {
                        "associatedSectionFeeTuitionProfile"() {
                            "associatedTuitionProfile"() {
                                "code"('TPDOOP')
                                "tuitionFees"() {
                                    "tuitionFee"() {
                                        "printCode"('Domestic Out of Province Fee')
                                        "tuitionFeeItems"() {
                                            "tuitionFeeItem"() {
                                                "name"()
                                                "amount"(getTotalFeeItemAmount(classNbrNode, outOfProvinceFeeAmt).add(hstAmt))
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }
}
