package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1CreateWebIdRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error: "
    static final String STUDENT_ID_PREFIX = "55555"

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return getLastName(respRoot).endsWith('ServerError')
    }

    def boolean isClientError(NodeChild respRoot) {
        return getLastName(respRoot).endsWith("ClientError")
    }

    def boolean isFault(NodeChild respRoot) {
        return getLastName(respRoot).endsWith('Fault')
    }

    private boolean isNone(NodeChild respRoot) {
        return getLastName(respRoot).endsWith('None')
    }

    private boolean isReturning(NodeChild respRoot) {
        return getLastName(respRoot).endsWith('Returning')
    }

    private String getLastName(NodeChild child) {
        return extractNodeText(child['LAST_NAME'] as GPathResult, () -> '')
    }

    private String getDestinyId(NodeChild child) {
        return extractNodeText(child['RU_DESTINY_ID'] as GPathResult)
    }

    def void getServerErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + getDestinyId(respRoot) + " " + this.getLastName(respRoot))
    }

    def void getClientErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(JsonOutput.toJson([message: 'MOCK_RU_D1_CREATEWEB_ID', data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "&lt;RU_D1_CREATEWEBID_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;&lt;" +
                "FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_CREATEWEBID_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(NodeChild request) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_CREATEWEBID_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1015[mockid]")
                            "DESCR"("Something went wrong.")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        NodeChild req = exchange.message.getBody(NodeChild.class)
        def originalReqStr = exchange.unitOfWork.originalInMessage.getBody(String.class)
        boolean isNone = isNone(req)
        boolean isReturning = isReturning(req)
        def address2 = extractNodeText(req['ADDRESS2'], () -> '')
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_CREATEWEBID_RESPONSE"() {
                "STUDENTID"(isNone ? "" : (address2 ?: STUDENT_ID_PREFIX + getDestinyId(req)))
                "SEARCH_ORDER_NUMBER"(isReturning ? 45 : 0)
                "MESSAGE"(isNone ? "Mutliple ID's exists" : (isReturning ? "Returning Student" : "Student Created"))
                "TRANSACTION_NO"("MOCK1234567891011121314151617181920")
                "original"(originalReqStr)
                "origin"("MOCK_RU_D1_CREATEWEBID")
            }
        }
        return xml.toString()
    }
}
