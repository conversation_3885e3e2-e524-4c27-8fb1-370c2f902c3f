package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren

abstract class MockPeopleSoftCartServiceRouteHelper {

    protected boolean hasErrorCode(NodeChild respRoot, List<String> errPrefixes) {
        NodeChildren msgNodes = respRoot['detail']['MSGS']['MSG'] as NodeChildren
        return msgNodes.collect {
            it -> getErrorCode(it as NodeChild)
        }.findAll {
            it -> it && errPrefixes.findIndexOf { prefix -> (it as String).startsWith(prefix) } >= 0
        }.size() > 0
    }

    protected String getErrorCode(NodeChild msgNode) {
        String errorField = getErrorField(msgNode)
        return errorField ? makeErrorCode(errorField) : null
    }

    protected abstract String getErrorField(NodeChild msgNode)

    protected abstract String makeErrorCode(String errField)
}
