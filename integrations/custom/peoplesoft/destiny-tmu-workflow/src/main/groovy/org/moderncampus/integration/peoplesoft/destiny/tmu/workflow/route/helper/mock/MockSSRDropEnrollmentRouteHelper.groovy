package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockSSRDropEnrollmentRouteHelper extends MockPeopleSoftCartServiceRouteHelper {

    static final String msgDescSuccess = "This class has been removed from your schedule."

    static final Map<String, String> msgDescErrorMap = [
            "ERR1.5001": "Internal Server Error (Mock)",
            "ERR1.4001": "Bad Request (Mock)",
            "ERR1.1001": "Student must be term activated and eligible to enroll",
            "ERR1.1002": "Student is withdrawn or cancelled",
            "ERR1.1003": "Student must have an active program for the term",
            "ERR1.1004": "Invalid EMPLID",
            "ERR1.1005": "Student must be in permitted academic career",
            "ERR1.2001": "Last date to enroll has passed",
            "ERR1.2002": "Enrollment into class requires an enrollment appointment",
            "ERR1.2003": "Class Full",
            "ERR1.3001": "Student is already enrolled",
            "ERR1.XXXX": "Unable to add this class - unknown error",
            "ER.5001"  : "Internal Server Error (Mock)",
            "ER.4001"  : "Bad Request (Mock)",
            "ER.2001"  : "None of your classes can be dropped at this time.",
            "ER.XXXX"  : "Unable to delete this class - unknown error"
    ]

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "SSR_DROP_ENROLLMENT_RESP"() {
                "detail"() {
                    "MSGS"() {
                        GPathResult items = extractMessageFromRequest(request)
                        if (items.isEmpty()) {
                            "MSG"()
                        }
                        items.each { item ->
                            "MSG"() {
                                mkp.yield item.'*'
                            }
                        }
                    }
                }
            }
        }
        return new XmlSlurper().parseText(xml.toString()) as NodeChild
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def respRoot = exchange.message.getBody(NodeChild.class)
        respRoot.detail.MSGS.MSG.replaceNode { node ->
            String emplId = (extractNodeText node.EMPLID as GPathResult) ?: null
            String classNbr = (extractNodeText node.CLASS_NBR as GPathResult) ?: null
            String errorCode = getErrorCode(node)
            String errorField = getErrorField(node)
            String messageSeverity = errorCode ? 'E' : 'I'
            "MSG" {
                "ID"(classNbr)
                "DESCR"(errorCode ? msgDescErrorMap[errorCode] + " [" + errorCode + "]" : msgDescSuccess)
                "MESSAGE_SEVERITY"(errorCode ? 'E' : 'I')
                "PROPS"() {
                    "PROP"() {
                        "SCC_ENTITY_INST_ID"() {
                            mkp.yieldUnescaped node.SCC_ENTITY_INST_ID
                        }
                        "PROPNAME"("")
                    }
                }
            }
        }
        respRoot << {
            "ORIGINAL_MOCK"(originalReq)
        }
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return applyMessageFilter(new XmlSlurper().parseText(xml.toString()) as NodeChild)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String applyMessageFilter(NodeChild nodeChild) {
        def isSeverityNodePresent = nodeChild['detail']['MSGS']['MSG'].collect {
            it -> extractNodeText(it['MESSAGE_SEVERITY'] as GPathResult)
        }.findAll { severity -> 'E' == severity }.size() > 0

        if (isSeverityNodePresent) {
            def msgNodes = nodeChild['detail']['MSGS']['MSG']
            def msgNodeWithSeverity = nodeChild['detail']['MSGS']['MSG'].find { node ->
                'E' == extractNodeText(node['MESSAGE_SEVERITY'] as GPathResult)
            }
            nodeChild['detail']['MSGS'].replaceNode {
                "MSGS"() {
                    "MSG"() {
                        "ID"('14690-439')
                        "DESCR"("None of your classes can be dropped at this time.")
                        "MESSAGE_SEVERITY"('E')
                        mkp.yield msgNodeWithSeverity.PROPS
                    }
                    mkp.yield msgNodeWithSeverity
                }
            }
        }

        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.yield nodeChild
        }

        return xml.toString()
    }

    def boolean isServerError(NodeChild respRoot) {
        return hasErrorCode(respRoot, ["ER.5"])
    }

    def boolean isClientError(NodeChild respRoot) {
        NodeChildren msgNodes = respRoot['detail']['MSGS']['MSG']['*'] as NodeChildren
        return msgNodes.size() <= 0 || hasErrorCode(respRoot, ['ER.4'])
    }

    def void getServerErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR.5001"], 500)
    }

    def void getClientErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR.4001"], 400)
    }

    @Override
    protected String getErrorField(NodeChild msgNode) {
        String classNbr = extractNodeText(msgNode['CLASS_NBR'] as GPathResult, () -> '')
        String emplId = extractNodeText(msgNode['EMPLID'] as GPathResult, () -> '')
        if (classNbr.startsWith('ER.4') || classNbr.startsWith('ER.5')) {
            return classNbr
        } else if (classNbr.startsWith('ER.1')) {
            return classNbr
        } else if (classNbr.startsWith("ER.")) {
            return classNbr
        } else if (classNbr.startsWith('ERR1.4') || classNbr.startsWith('ERR1.5')) {
            return classNbr
        } else if (emplId.startsWith('ERR1.1')) {
            return emplId
        } else if (classNbr.startsWith('ERR1.3') && classNbr == emplId) {
            return classNbr
        } else if (classNbr.startsWith('ERR1.')) {
            return classNbr
        } else {
            return null
        }
    }

    @Override
    protected String makeErrorCode(String errField) {
        if (errField.startsWith('ERR1.')) {
            return errField.startsWith("ERR1.") && errField.length() > 8 && errField.substring(0, 9) in msgDescErrorMap.keySet() ?
                    errField.substring(0, 9) : "ERR1.XXXX"
        }
        return errField.startsWith("ER.") && errField.length() > 7 && errField.substring(0, 8) in msgDescErrorMap.keySet() ?
                errField.substring(0, 8) : "ER.XXXX"
    }

    private GPathResult extractMessageFromRequest(String originalReq) {
        def rootNode = new XmlSlurper().parseText(originalReq) as NodeChild
        return rootNode['ENROLL_REQ_HEADER']['ENROLL_REQUEST_DETAILS']['ENROLL_REQUEST_DETAIL'] as GPathResult
    }
}
