package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

trait D1TMUPeopleSoftBasketRouteHelper extends D1TMUPeopleSoftRouteHelper {

    GPathResult extractEnrollmentEventsFromRequest(NodeChild enrollment, boolean applyActivityCodeFilter = true, boolean applyEmplIdFilter = false,
                                                   Closure additionalEventFilter = null) {
        NodeChildren enrollmentEvents = enrollment['body']['transactionBasket']['enrollmentEvents']['enrollmentEvent'] as NodeChildren
        if (applyActivityCodeFilter) {
            GPathResult filteredEvents = enrollmentEvents.findAll { node ->
                boolean ret = 'Sale' == extractNodeText(node['activityCode'] as NodeChildren)
                if (applyEmplIdFilter) {
                    String emplId = extractEmplId(node as NodeChild)
                    return ret && emplId && (additionalEventFilter ? additionalEventFilter(node) : true)
                }
                return ret
            }
            return filteredEvents
        }
        return enrollmentEvents
    }

    def List<String> extractUniqueStudentIdsFromEvent(GPathResult enrollmentEvents) {
        if (enrollmentEvents.isEmpty()) {
            return null
        }
        return enrollmentEvents.collect { it ->
            extractEmplId(it as NodeChild)
        }.unique()
    }

    String extractEmplId(NodeChild node) {
        return extractNodeText(node['student']['schoolPersonnelNumber'] as GPathResult)
    }

    String extractClassNbr(NodeChild node) {
        return extractNodeText(node['enrollCourseSection']['campusFisId'] as GPathResult)
    }

    String extractSectionId(NodeChild node) {
        return extractNodeText(node['enrollCourseSection']['objectId'] as GPathResult)
    }

    String extractStrm(NodeChild node) {
        return extractNodeText(node['enrollCourseSection']['associatedSemester']['campusId'] as GPathResult)
    }
}
