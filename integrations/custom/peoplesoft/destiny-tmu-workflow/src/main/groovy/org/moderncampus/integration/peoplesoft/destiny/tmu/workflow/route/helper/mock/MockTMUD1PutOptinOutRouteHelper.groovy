package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1PutOptinOutRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error. The server encountered an unexpected condition which prevented it from fulfilling " +
            "the request: "

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request) as NodeChild
    }

    private String extractTerm(NodeChild resp) {
        return extractNodeText(resp['STRM'] as GPathResult, () -> '')
    }

    private String extractStudentId(NodeChild resp) {
        return extractNodeText(resp['STUDENTID'] as GPathResult)
    }

    def boolean isServerError(NodeChild resp) {
        return extractTerm(resp).contains("ERR1x5")
    }

    def boolean isClientError(NodeChild resp) {
        return extractTerm(resp).contains("ERR1x4")
    }

    def boolean isFault(NodeChild resp) {
        return extractTerm(resp).contains("ERR1x")
    }

    def void getServerErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + extractStudentId(respRoot))
    }

    def void getClientErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(JsonOutput.toJson([data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "    &lt;RU_D1_PUT_OPTINOUT_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;" +
                "&lt;FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_PUT_OPTINOUT_FAULT&gt;"]), 400)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(NodeChild request) throws Exception {
        def term = extractTerm(request)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_PUT_OPTINOUT_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            if (term.contains("ERR1x12")) {
                                "ID"("")
                                "DESCR"("Item Type's are ignored : 000000002550")
                                "MESSAGE_SEVERITY"("I")
                            } else if (term.contains("ERR1x13")) {
                                "ID"("1008")
                                "DESCR"("Student does not exist")
                                "MESSAGE_SEVERITY"("E")
                            } else {
                                "ID"("")
                                "DESCR"("Student has already made consent for given term")
                                "MESSAGE_SEVERITY"("I")
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReqStr = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def originalReq = exchange.message.getBody(NodeChild.class)
        def d1PassThrough = extractNodeText(originalReq['D1_PASS_THROUGH'])
        def studentId = extractStudentId(originalReq)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_PUT_OPTINOUT_RESPONSE"() {
                "STUDENTID"(studentId)
                "D1_PASS_THROUGH"(d1PassThrough)
                "original"() {
                    mkp.yield(originalReqStr)
                }
            }
        }
        return xml.toString()
    }
}
