package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1StdntStatusRouteHelper {

    def String mapRequest(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        String studentId = extractNodeText(rootNode['STUDENTID'] as NodeChildren)
        return studentId && studentId.length() > 14 ? studentId.substring(14) : studentId
    }

    def boolean isClientError(String studentId) {
        return studentId?.endsWith("ERR404")
    }

    def boolean isServerError(String studentId) {
        return studentId?.endsWith("ERR500")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException("Mock server error: " + studentId, 500)
    }

    def void getClientErrorContent() {
        throw new ApplicationException(JsonOutput.toJson([data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "&lt;RU_D1_STDNT_STATUS_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;&lt;" +
                "FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_STDNT_STATUS_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(String studentId) {
        def careerArr = studentId?.endsWith("M") || studentId?.endsWith("MX") ? ["1X", studentId, "2X", "3X"] : [studentId]
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_STDNT_STATUS_RESPONSE"() {
                "STUDENTID"(studentId)
                careerArr.each { id ->
                    "CAREER_INFO"() {
                        "ACAD_CAREER"(getAcadCareer(id))
                        "PROG_STATUS"(getProgStatus(id))
                    }
                }
                "origin"("MOCK_RU_D1_STDNT_STATUS_RESPONSE")
            }
        }
        return xml.toString()
    }

    private String getAcadCareer(String studentId) {
        return studentId?.startsWith("1") ? "UGRD" : (studentId?.startsWith("2") ? "GRAD" : "CNED")
    }

    private String getProgStatus(String studentId) {
        return !studentId?.endsWith("X") ? "AC" : "INACTIVE"
    }
}
