package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder;

import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.route.builder.BaseCustomRouteBuilder;

public abstract class D1TMUPeopleSoftRouteBuilder extends BaseCustomRouteBuilder {

    static final String METHOD_MAP_PRE_INBOUND_REQUEST = "mapPreInboundRequest";
    static final String METHOD_MAP_INBOUND_REQUEST = "mapInboundRequest";
    static final String METHOD_MAP_PRE_OUTBOUND_RESPONSE = "mapPreOutboundResponse";
    static final String METHOD_MAP_OUTBOUND_RESPONSE = "mapOutboundResponse";

    Object helper;

    public D1TMUPeopleSoftRouteBuilder(String id, Object helper) {
        super(id, null);
        this.helper = helper;
    }

    protected abstract void buildCustomRoute(RouteDefinition definition);

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.bean(helper, METHOD_MAP_PRE_INBOUND_REQUEST);
        routeDefinition.bean(helper, METHOD_MAP_INBOUND_REQUEST);
        buildCustomRoute(routeDefinition);
        routeDefinition.bean(helper, METHOD_MAP_PRE_OUTBOUND_RESPONSE);
        routeDefinition.bean(helper, METHOD_MAP_OUTBOUND_RESPONSE);
    }
}
