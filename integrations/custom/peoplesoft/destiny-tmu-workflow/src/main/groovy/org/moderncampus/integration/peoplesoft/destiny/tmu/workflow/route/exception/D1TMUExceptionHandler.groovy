package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.exception


import groovy.xml.StreamingMarkupBuilder
import org.apache.commons.lang3.exception.ExceptionUtils
import org.moderncampus.integration.exception.ApplicationException

class D1TMUExceptionHandler {

    public static String mapException(String serializedError, Exception rawException) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(rawException instanceof ApplicationException ? (rawException as ApplicationException).statusCode : 500)
                        "results"() {
                            "result"() {
                                "status"("FC9996")
                                "action"("ERROR")
                                "entity"(serializedError)
                                "entityType"("")
                                "entityInXMLFormat"(false)
                                "errorMsg"(rawException.message)
                            }
                        }
                        "response"(ExceptionUtils.getStackTrace(rawException))
                    }
                }
            }
        }
        return xml.toString()
    }
}
