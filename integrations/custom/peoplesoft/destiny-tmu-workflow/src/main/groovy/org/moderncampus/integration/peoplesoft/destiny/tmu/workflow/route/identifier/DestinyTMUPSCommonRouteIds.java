package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.constants.Constants.HEALTH;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum DestinyTMUPSCommonRouteIds implements IRouteId {

    V1_D1_TMU_PEOPLESOFT_HEALTH(HEALTH);

    String contextPath;

    String id;

    DestinyTMUPSCommonRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(
                new String[]{VERSION_1, DESTINY_SYSTEM_ID, Constants.SCHOOL_ID_TMU, PEOPLESOFT_SYSTEM_ID, contextPath});
    }

}
