package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonOutput
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

trait D1TMUPeopleSoftStudentChoiceRouteHelper extends D1TMUPeopleSoftBasketRouteHelper {

    static final String PARSED_ORIGINAL_REQ = "parsedOriginal"
    static final String FILTERED_ENROLLMENT_EVENTS = "filteredEnrollmentEvents"

    def String mapInboundRequest(Exchange exchange) {
        def body = exchange.message.getBody(String.class)
        NodeChild inboundReq = parseXMLBody(body)
        exchange.setProperty(PARSED_ORIGINAL_REQ, inboundReq)
        GPathResult filteredEnrollmentEvents = extractEnrollmentEventsFromRequest(inboundReq, true, true,
                (node) -> {
                    GPathResult selectionMode = node['enrollCourseSection']['associatedSpecialRequests']['associatedSpecialRequest']['publicView' +
                            'SelectionMode'] as NodeChildren
                    GPathResult conditionalMode = selectionMode.find { it ->
                        extractNodeText(it as NodeChild) == "Conditional"
                    }
                    return !conditionalMode.isEmpty()
                })
        if (filteredEnrollmentEvents.isEmpty()) {
            throw new ApplicationException("Atleast one enrollment event must be present with an associated conditional SR of type: 'Conditional'",
                    400)
        }
        exchange.setProperty(FILTERED_ENROLLMENT_EVENTS, filteredEnrollmentEvents)
        List<String> uniqueStudentIdList = extractUniqueStudentIdsFromEvent(filteredEnrollmentEvents)
        validateRequest(body, uniqueStudentIdList)
        return uniqueStudentIdList[0]
    }

    void validateRequest(String originalReq, List<String> uniqueStudentIdList) {
        if (uniqueStudentIdList && uniqueStudentIdList.size() != 1) {
            def invalidRequestException = [error     : "Invalid Request",
                                           reason    : "Request must contain exactly 1 student across all enrollments.",
                                           original  : originalReq,
                                           resolution: "Modify request to contain exactly 1 student across all enrollments."]
            throw new ApplicationException(JsonOutput.toJson(invalidRequestException), 400)
        }
    }

    NodeChildren extractConditionalSpecialRequestsFromEvent(GPathResult event) {
        return (event['enrollCourseSection']['associatedSpecialRequests']['associatedSpecialRequest'] as NodeChildren).findAll { it ->
            extractNodeText(it['publicViewSelectionMode'] as GPathResult) == 'Conditional'
        } as NodeChildren
    }

}
