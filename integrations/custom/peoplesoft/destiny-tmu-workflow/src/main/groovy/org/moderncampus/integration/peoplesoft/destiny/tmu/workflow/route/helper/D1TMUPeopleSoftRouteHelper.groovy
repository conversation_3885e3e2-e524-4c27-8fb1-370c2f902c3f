package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.route.Constants
import org.moderncampus.integration.route.support.RouteSupport

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

trait D1TMUPeopleSoftRouteHelper {

    static final String STATUS_CODE_SUCCESS = "SC0000"
    static final String STATUS_CODE_UNKNOWN_ERROR = "FC9999"
    static final String STATUS_CODE_ACTION_CANNOT_PROCEED = "FC5000"
    static final String STATUS_CODE_NO_RESULT_FOUND = "FC9997"
    static final String STATUS_CODE_OPERATION_NO_LONGER_VALID = "FC2002"
    static final String STATUS_CODE_EXT_SYSTEM_ERROR = "FC9996"
    static final String STATUS_CODE_DATA_VALIDATION_ERROR = "FC1001"
    static final String ENTITY_COURSE_SECTION_LW = "courseSectionLW"
    static final String ENTITY_COURSE_SECTION = "courseSection"
    static final String ENTITY_COURSE_SECTION_FEE = "CourseSectionFee"
    static final String ENTITY_STUDENT = "student"
    static final String VALUE_MAP = "valueMap"
    static final String ASYNC_INVOCATION = "asyncInvocation"
    static final String ORIGINAL_BODY = "originalBody"

    static DateTimeFormatter d1DateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy hh:mm:ss a")

    void mapPreInboundRequest(Exchange exchange) {
        if (RouteSupport.isAsyncRoute(exchange)) {
            exchange.setProperty(ASYNC_INVOCATION, true)
            RouteSupport.setupPreAsyncRoute(exchange)
        }
    }

    void mapPreOutboundResponse(Exchange exchange) {
        if (exchange.getProperty(ASYNC_INVOCATION, false, Boolean.class)) {
            RouteSupport.setupPostAsyncRoute(exchange)
        }
    }

    NodeChild parseXMLBody(String response, Exchange exchange = null) {
        if (exchange != null) {
            String body = exchange.getMessage().getBody(String.class)
            exchange.setProperty(ORIGINAL_BODY, body)
        }
        return new XmlSlurper().parseText(response) as NodeChild
    }

    boolean isFaultResponse(NodeChild responseNode, boolean checkValue = false, boolean checkSeverity = false) {
        NodeChildren faultNode = responseNode['IS_FAULT'] as NodeChildren
        return !(faultNode.isEmpty()) && (checkValue ? 'Y' == extractNodeText(faultNode) : true) && (checkSeverity ?
                areAllFaultSevere(responseNode) : true)
    }

    boolean areAllFaultSevere(NodeChild responseNode) {
        GPathResult messageSeverityNodes = (responseNode['FAULT']['MSGS']['MSG'] as NodeChildren).find { it ->
            NodeChild msgNode = it as NodeChild
            "I" == extractNodeText(msgNode['MESSAGE_SEVERITY'] as NodeChildren)
        } as GPathResult
        return messageSeverityNodes.isEmpty()
    }

    String getFaultMsgs(NodeChild rootNode, NodeChildren faultMsgNode = null) {
        return (!faultMsgNode ? rootNode['FAULT']['MSGS']['MSG'] as NodeChildren : faultMsgNode).collect { it ->
            {
                NodeChild msgNode = it as NodeChild
                extractNodeText(msgNode['ID'] as NodeChildren) + ":" + extractNodeText(msgNode['DESCR'] as NodeChildren)
            }
        }.join(",")
    }

    LocalDate extractD1Date(String date) {
        if (date == null) return null
        try {
            return LocalDate.parse(date, d1DateFormatter)
        } catch (Exception ignored) {
            return null
        }
    }

    String serializeD1Date(String date) {
        if (date == null) return null
        try {
            return d1DateFormatter.format(LocalDate.parse(date).atStartOfDay())
        } catch (Exception ignored) {
            return null
        }
    }

    String getHeaderFromRequest(Exchange exchange, String propName, String defaultVal = "") {
        return exchange.message.getHeader(Constants.ROUTE_HEADER_PREFIX + propName, defaultVal, String.class)
    }

    String generateOriginalResp(String resp) {
        return resp.replaceFirst(/<\?xml.*\?>/, '')
    }
}
