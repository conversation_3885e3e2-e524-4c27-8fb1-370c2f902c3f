package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper


import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentResidencyNotifyRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftStudentRouteHelper, D1TMUPeopleSoftRouteHelper {

    static final String PARSED_ORIGINAL_REQ = "parsedOriginal"
    static final DateTimeFormatter TMU_D1_FORMAT_DATE_OF_ENTRY = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    String mapInboundRequest(Exchange exchange) {
        NodeChild request = parseXMLBody(exchange.message.getBody(String.class))
        exchange.setProperty(PARSED_ORIGINAL_REQ, request)
        return extractSchoolPersonnelNumber(request)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMUStudentResidencyRequest(Exchange exchange) {
        NodeChild original = exchange.getProperty(PARSED_ORIGINAL_REQ)
        NodeChildren studentReq = extractStudent(original)
        String studentId = exchange.message.getBody(String.class)
        NodeChildren udfValueNodes = studentReq['udfValues']['udfValue']
        String residency = getUdfFieldValue(udfValueNodes, "citizenshipStatus")
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_STDNT_RESIDENCY_REQUEST"() {
                "RESIDENCY"(residency)
                "ENTRY_DATE"((CANADIAN_CITIZEN == residency) ? '' : extractDateOfEntry(udfValueNodes, TMU_D1_FORMAT_DATE_OF_ENTRY))
                "COUNTRY"((CANADIAN_CITIZEN == residency) ? '' : getUdfFieldValue(udfValueNodes, "countryOfCitizenship"))
                "ACAD_CAREER"('CNED')
                "STUDENTID"(studentId)
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response, true, true)
        String studentId = extractNodeText(response['STUDENTID'])
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault ? '' : mkp.yieldUnescaped(getEntityContent(studentId))
            }
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(String studentId) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "student"() {
                "schoolPersonnelNumber"(studentId)
            }
        }
        return xml.toString()
    }

}
