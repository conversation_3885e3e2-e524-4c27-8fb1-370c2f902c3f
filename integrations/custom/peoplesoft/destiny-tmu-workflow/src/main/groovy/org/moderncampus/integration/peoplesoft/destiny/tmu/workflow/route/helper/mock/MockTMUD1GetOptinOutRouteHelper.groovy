package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1GetOptinOutRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error. The server encountered an unexpected condition which prevented it from fulfilling " +
            "the request: "
    static final String STUDENT_ID_PREFIX = "55555"

    @CompileStatic(TypeCheckingMode.SKIP)
    def String mapRequest(String request) {
        def rootNode = new XmlSlurper().parseText(request) as NodeChild
        return extractNodeText(rootNode['STUDENTID'] as NodeChildren)
    }

    def boolean isFault(String studentId) {
        return (studentId?.endsWith("XO1") || studentId?.endsWith('XO2') || studentId?.endsWith('XO3'))
    }

    def boolean isServerError(String studentId) {
        return studentId != null && studentId.endsWith("500")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + studentId, 500)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(String studentId) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_GET_OPTINOUT_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            if (studentId.endsWith('XO2')) {
                                "ID"("1002")
                                "DESCR"("ACAD_CAREER tag is missing in the request message.")
                                "MESSAGE_SEVERITY"("E")
                            } else if (studentId.endsWith('XO3')) {
                                "ID"("1007")
                                "DESCR"("Student does not exists")
                                "MESSAGE_SEVERITY"("E")
                            } else {
                                "ID"("1006")
                                "DESCR"("Invalid STRM.")
                                "MESSAGE_SEVERITY"("E")
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        NodeChild originalReq = new XmlSlurper().parseText exchange.unitOfWork.originalInMessage.getBody(String.class)
        def strms = (originalReq['STRM'] as NodeChildren).collect { it ->
            extractNodeText(it as GPathResult)
        }.unique()
        def d1PassThrough = extractNodeText(originalReq['D1_PASS_THROUGH'])
        def studentId = exchange.message.getBody(String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_GET_OPTINOUT_RESPONSE"() {
                "STUDENTID"(studentId)
                "D1_PASS_THROUGH"(d1PassThrough)
                strms.each { String strm ->
                    "STRMS"() {
                        "STRM"(strm)
                        "ISEDITABLE"(strm.endsWith("2") ? "Y" : "N")
                        getUniqueItemTypesForTerm(d1PassThrough, strm).getValue().each { item ->
                            "ITEM_TYPE"("DESCR": "CESAR CE", "OPTOUT": strm.startsWith('OPT') ? "Y" : "N", item)
                        }
                    }
                }
                "original"() {
                    mkp.yield(originalReq)
                }
            }
        }
        return xml.toString()
    }

    def Map.Entry<String, List<String>> getUniqueItemTypesForTerm(String passThroughJSON, String term) {
        def passThroughMap = new JsonSlurper().parseText(passThroughJSON) as Map<String, List<String>>
        passThroughMap.findAll { (String key, List<String> value) -> key.split(":")[0] == term }.entrySet()[0]
    }
}
