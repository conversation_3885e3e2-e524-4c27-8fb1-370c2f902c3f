package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1ClassCapAdjustRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error: "

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('0550')
    }

    def boolean isClientError(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('0440')
    }

    def boolean isFault(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('XYZ') && ("E" == getEnrlAction(respRoot) || "D" == getEnrlAction(respRoot))
    }

    def void getServerErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + getClassNbr(respRoot))
    }

    def void getClientErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(JsonOutput.toJson([message: 'MOCK_RU_D1_CLASS_CAPADJUST', data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "&lt;RU_D1_CLASS_CAPADJUST_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;" +
                "&lt;FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_CLASS_CAPADJUST_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(NodeChild request) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_CLASS_CAPADJUST_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1009-mock")
                            "DESCR"("E" == getEnrlAction(request) ? "Class is closed" : "D" == getEnrlAction(request) ? "Something went wrong." : "")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    private String getClassNbr(NodeChild child) {
        return extractNodeText(child['CLASS_NBR'] as GPathResult, () -> '')
    }

    private String getEnrlAction(NodeChild child) {
        return extractNodeText(child['ENRL_ACTION'] as GPathResult, () -> '')
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        NodeChild parsedReq = exchange.message.body as NodeChild
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_CLASS_CAPADJUST_RESPONSE"() {
                "RESULT"("True")
                "CLASS_NBR"(extractNodeText(parsedReq['CLASS_NBR']))
                "D1_PASS_THROUGH"(extractNodeText(parsedReq['D1_PASS_THROUGH']))
                "original"(originalReq)
                "origin"("MOCK_RU_D1_CLASS_CAPADJUST")
            }
        }
        return xml.toString()
    }
}
