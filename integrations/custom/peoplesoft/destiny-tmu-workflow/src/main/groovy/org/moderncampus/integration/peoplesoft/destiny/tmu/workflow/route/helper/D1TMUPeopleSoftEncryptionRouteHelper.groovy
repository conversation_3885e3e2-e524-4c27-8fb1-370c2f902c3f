package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

trait D1TMUPeopleSoftEncryptionRouteHelper {

    String getPaddedValue(String value) {
        return getDateTimeStr(ZonedDateTime.now().withZoneSameInstant(ZoneId.of("America/New_York")).toLocalDateTime()) + value
    }

    SecretKey getSecretKeyInst(String rawKeyValue) {
        return new SecretKeySpec(rawKeyValue.getBytes(), "AES")
    }

    byte[] getIVInBytes(String iv) {
        return iv.getBytes(StandardCharsets.UTF_8)
    }

    String getDateTimeStr(LocalDateTime localDateTime) {
        return pad2(localDateTime.getMonthValue()) + pad2(localDateTime.getDayOfMonth()) + localDateTime.getYear() +
                pad2(localDateTime.getHour()) + pad2(localDateTime.getMinute()) + pad2(localDateTime.getSecond())
    }

    String pad2(Integer value) {
        return (value + "").length() == 1 ? "0" + value : value
    }

}