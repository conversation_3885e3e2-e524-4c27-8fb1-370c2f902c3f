package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.springframework.stereotype.Component

@Component
@CompileStatic
class ShoppingCartRemoveRouteHelper extends StudentEnrollmentValidateRouteHelper {

    public void mapInboundRequest(Exchange body) {}

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateSCCSCRemoveItemRequest(Exchange exchange) {
        def originalReq = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        NodeChild validateItemResponse = exchange.message.getBody(NodeChild.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "SCC_SC_REMOVEITEM_REQ"() {
                "SHOPPING_CART"() {
                    "COURSE_SHOP_CART"() {
                        "SCC_SHOP_CART_TYPE"(COURSE_SHOP_CART_TYPE)
                        buildRemoveItemsRequest(builder, originalReq)
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private void buildRemoveItemsRequest(def builder, NodeChild originalReq) {
        builder."ITEMS"() {
            def filteredEvents = extractEnrollmentEventsFromRequest(originalReq)
            if (filteredEvents.size() == 0) {
                "ITEM"()
            } else {
                filteredEvents.each { node ->
                    "ITEM"() {
                        "SCC_ENTITY_INST_ID"(extractClassNbr(node) + ":" + extractEmplId(node))
                        "EMPLID"(extractEmplId(node))
                        "INSTITUTION"("RYERU")
                        "ACAD_CAREER"("CNED")
                        "STRM"(extractStrm(node))
                        "CLASS_NBR"(extractClassNbr(node))
                        "WAIT_LIST_OKAY"("N")
                        "CLASS_PRMSN_NBR"("0")
                        "GRADING_BASIS_ENRL"()
                        "UNT_TAKEN"("0")
                        "INSTRUCTOR_ID"()
                        "RQMNT_DESIGNTN_OPT"()
                        "START_DT"()
                        "CRSE_COUNT"("0")
                        "ACAD_PROG"()
                        "RELATE_CLASS_NBR_1"("0")
                        "RELATE_CLASS_NBR_2"("0")
                        "SSR_VALIDATIN_MODE"("N")
                    }
                }
            }
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {}
}
