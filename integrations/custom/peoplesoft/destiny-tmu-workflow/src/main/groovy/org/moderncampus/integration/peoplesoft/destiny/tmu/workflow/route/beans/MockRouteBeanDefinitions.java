package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.beans;

import static org.apache.camel.support.builder.PredicateBuilder.and;
import static org.apache.camel.support.builder.PredicateBuilder.not;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName.*;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.ChoiceDefinition;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder.D1TMUPeopleSoftMockRouteBuilder;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSCCSCAddItemRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSCCSCRemoveItemRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSSRAddEnrollmentRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSSRDropEnrollmentRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSSREnrValidateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockSSRGetClassSectionRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1AccountSettlementRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1BluePayInfoRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1ClassCapAdjustRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1ClassFeeRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1CreateWebIdRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1GetOptinOutRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1PutOptinOutRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1RegAndTranRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1StdntProfileRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1StdntResidencyRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1StdntStatusRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock.MockTMUD1TokenRouteHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;

@Configuration
public class MockRouteBeanDefinitions {

    static final String METHOD_IS_FAULT = "isFault";
    static final String METHOD_GET_SUCCESSFUL_CONTENT = "getSuccessfulContent";
    static final String METHOD_IS_SERVER_ERROR = "isServerError";
    static final String METHOD_GET_SERVER_ERROR_CONTENT = "getServerErrorContent";
    static final String METHOD_IS_CLIENT_ERROR = "isClientError";
    static final String METHOD_GET_CLIENT_ERROR_CONTENT = "getClientErrorContent";
    static final String METHOD_GET_FAULT_CONTENT = "getFaultContent";
    static final String METHOD_IS_VALIDATION_ERROR = "isValidationError";
    static final String METHOD_GET_VALIDATION_ERROR_CONTENT = "getValidationErrorContent";
    static final String METHOD_IS_ACTION_FAILURE = "isActionFailure";
    static final String METHOD_GET_ACTION_FAILURE_CONTENT = "getActionFailureContent";
    static final String METHOD_IS_EMPTY_RESPONSE = "isEmptyResponse";
    static final String METHOD_GET_EMPTY_RESPONSE_CONTENT = "getEmptyResponseContent";
    static final String METHOD_IS_FAULT1 = "isFault1";
    static final String METHOD_IS_FAULT2 = "isFault2";
    static final String METHOD_GET_FAULT1_CONTENT = "getFault1Content";
    static final String METHOD_GET_FAULT2_CONTENT = "getFault2Content";


    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1Token() {
        Class<MockTMUD1TokenRouteHelper> helperClass = MockTMUD1TokenRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_TOKEN_SO.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSCCSCAddItem() {
        Class<MockSCCSCAddItemRouteHelper> helperClass = MockSCCSCAddItemRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SCC_SC_ADDITEM.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR)),
                                not(method(helperClass, METHOD_IS_VALIDATION_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_VALIDATION_ERROR))
                        .bean(helperClass, METHOD_GET_VALIDATION_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSSREnrValidate() {
        Class<MockSSREnrValidateRouteHelper> helperClass = MockSSREnrValidateRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SSR_ENR_VALIDATE.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSSCSCRemoveItem() {
        Class<MockSCCSCRemoveItemRouteHelper> helperClass = MockSCCSCRemoveItemRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SCC_SC_REMOVEITEM.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR)),
                                not(method(helperClass, METHOD_IS_ACTION_FAILURE))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_ACTION_FAILURE))
                        .bean(helperClass, METHOD_GET_ACTION_FAILURE_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSSRAddEnrollment() {
        Class<MockSSRAddEnrollmentRouteHelper> helperClass = MockSSRAddEnrollmentRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SSR_ADD_ENROLLMENT.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                Class<MockSSRAddEnrollmentRouteHelper> helperClass = MockSSRAddEnrollmentRouteHelper.class;
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSSRDropEnrollment() {
        Class<MockSSRDropEnrollmentRouteHelper> helperClass = MockSSRDropEnrollmentRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SSR_DROP_ENROLLMENT.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockSSRGetClassSection() {
        Class<MockSSRGetClassSectionRouteHelper> helperClass = MockSSRGetClassSectionRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(SSR_GET_CLASS_SECTION.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_EMPTY_RESPONSE)),
                                not(method(helperClass, METHOD_IS_FAULT))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_EMPTY_RESPONSE))
                        .bean(helperClass, METHOD_GET_EMPTY_RESPONSE_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1AccountSettlement() {
        Class<MockTMUD1AccountSettlementRouteHelper> helperClass = MockTMUD1AccountSettlementRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_SETTLEMENT.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1StdntProfile() {
        Class<MockTMUD1StdntProfileRouteHelper> helperClass = MockTMUD1StdntProfileRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_STDNT_PROFILE.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_EMPTY_RESPONSE)),
                                not(method(helperClass, METHOD_IS_FAULT))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_EMPTY_RESPONSE))
                        .bean(helperClass, METHOD_GET_EMPTY_RESPONSE_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT1))
                        .bean(helperClass, METHOD_GET_FAULT1_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT2))
                        .bean(helperClass, METHOD_GET_FAULT2_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1ClassCapAdjust() {
        Class<MockTMUD1ClassCapAdjustRouteHelper> helperClass = MockTMUD1ClassCapAdjustRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_CLASS_CAPADJUST.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1ClassFee() {
        Class<MockTMUD1ClassFeeRouteHelper> helperClass = MockTMUD1ClassFeeRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_CLASS_FEE.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1CreateWebId() {
        Class<MockTMUD1CreateWebIdRouteHelper> helperClass = MockTMUD1CreateWebIdRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_CREATEWEBID.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1StdntResidency() {
        Class<MockTMUD1StdntResidencyRouteHelper> helperClass = MockTMUD1StdntResidencyRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_STDNT_RESIDENCY.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1StdntRegAndTran() {
        Class<MockTMUD1RegAndTranRouteHelper> helperClass = MockTMUD1RegAndTranRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_REGANDTRAN.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1StdntStatus() {
        Class<MockTMUD1StdntStatusRouteHelper> helperClass = MockTMUD1StdntStatusRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_STDNT_STATUS.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1GetOptinOut() {
        Class<MockTMUD1GetOptinOutRouteHelper> helperClass = MockTMUD1GetOptinOutRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_GET_OPTINOUT.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_FAULT))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1PutOptinOut() {
        Class<MockTMUD1PutOptinOutRouteHelper> helperClass = MockTMUD1PutOptinOutRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_PUT_OPTINOUT.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR)),
                                not(method(helperClass, METHOD_IS_CLIENT_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_CLIENT_ERROR))
                        .bean(helperClass, METHOD_GET_CLIENT_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder mockTMUD1BluePayInfo() {
        Class<MockTMUD1BluePayInfoRouteHelper> helperClass = MockTMUD1BluePayInfoRouteHelper.class;
        return new D1TMUPeopleSoftMockRouteBuilder(TMU_D1_BLUEPAY_INFO.getMockEndpoint(), helperClass) {
            @Override
            protected void buildMockRoute(ChoiceDefinition choiceDefinition) {
                choiceDefinition.when(and(not(method(helperClass, METHOD_IS_FAULT)),
                                not(method(helperClass, METHOD_IS_SERVER_ERROR))))
                        .bean(helperClass, METHOD_GET_SUCCESSFUL_CONTENT)
                        .when(method(helperClass, METHOD_IS_SERVER_ERROR))
                        .bean(helperClass, METHOD_GET_SERVER_ERROR_CONTENT)
                        .when(method(helperClass, METHOD_IS_FAULT))
                        .bean(helperClass, METHOD_GET_FAULT_CONTENT);
            }
        };
    }

}
