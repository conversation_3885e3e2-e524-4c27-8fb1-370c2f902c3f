package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentEnrollmentDropRouteHelper implements D1TMUPeopleSoftBasketRouteHelper {

    static final String ENTITY_COURSE_SECTION_LW = "courseSectionLW"

    public NodeChild mapInboundRequest(String body) {
        return parseXMLBody(body)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateSSRDropEnrollmentRequest(Exchange exchange) {
        def rootNode = exchange.message.getBody(NodeChild.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "SSR_DROP_ENROLLMENT_REQ"() {
                "ENROLL_REQ_HEADER"() {
                    buildDropEnrollmentRequestHeader(builder)
                    buildDropEnrollmentRequestDetail(builder, rootNode, exchange)
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private void buildDropEnrollmentRequestHeader(def builder) {
        builder."ENRL_REQUEST_ID"()
        builder."OPRID"()
        builder."ENRL_REQ_PROC_ST"()
        builder."ENRL_REQ_SOURCE"()
        builder."PROCESS_INSTANCE"('0')
        builder."DTTM_STAMP_SEC"()
        builder."SSR_IS_ADMIN"()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private void buildDropEnrollmentRequestDetail(def builder, NodeChild inboundRequest, Exchange exchange) {
        builder."ENROLL_REQUEST_DETAILS"() {
            def filteredEvents = extractEnrollmentEventsFromRequest(inboundRequest)
            if (filteredEvents.size() == 0) {
                "ENROLL_REQUEST_DETAIL"()
            } else {
                filteredEvents.each { node ->
                    "ENROLL_REQUEST_DETAIL"() {
                        "ENRL_REQUEST_ID"()
                        "ENRL_REQ_DETL_SEQ"()
                        "EMPLID"(extractEmplId(node))
                        "ACAD_CAREER"('CNED')
                        "INSTITUTION"('RYERU')
                        "STRM"(extractStrm(node))
                        "CLASS_NBR"(extractClassNbr(node))
                        "ENRL_REQ_ACTION"("D")
                        "ENRL_ACTION_REASON"('D1')
                        "ENRL_ACTION_DT"()
                        "UNT_TAKEN"('0')
                        "UNT_EARNED"('0')
                        "CRSE_COUNT"('0')
                        "REPEAT_CODE"()
                        "CRSE_GRADE_INPUT"()
                        "GRADING_BASIS_ENRL"('GRD')
                        "CLASS_PRMSN_NBR"('0')
                        "CLASS_NBR_CHG_TO"('0')
                        "DROP_CLASS_IF_ENRL"('0')
                        "CHG_TO_WL_NUM"('0')
                        "RELATE_CLASS_NBR_1"('0')
                        "RELATE_CLASS_NBR_2"('0')
                        "OVRD_CLASS_LIMIT"()
                        "OVRD_GRADING_BASIS"()
                        "OVRD_CLASS_UNITS"()
                        "OVRD_UNIT_LOAD"()
                        "OVRD_CLASS_LINKS"()
                        "OVRD_CLASS_PRMSN"()
                        "OVRD_REQUISITES"()
                        "OVRD_TIME_CNFLCT"()
                        "OVRD_CAREER"()
                        "WAIT_LIST_OKAY"('N')
                        "OVRD_ENRL_ACTN_DT"()
                        "OVRD_RQMNT_DESIG"()
                        "OVRD_SRVC_INDIC"()
                        "OVRD_APPT"()
                        "INSTRUCTOR_ID"()
                        "ENRL_REQ_DETL_STAT"()
                        "RQMNT_DESIGNTN"()
                        "RQMNT_DESIGNTN_OPT"()
                        "RQMNT_DESIGNTN_GRD"()
                        "TSCRPT_NOTE_ID"()
                        "TSCRPT_NOTE_EXISTS"()
                        "OPRID"()
                        "DTTM_STAMP_SEC"()
                        "START_DT"()
                        "ACAD_PROG"()
                        "SCC_ENTITY_INST_ID"(extractNodeText(node['objectId']))
                    }
                }
            }
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        boolean isFault = isFaultResponse(resp)
        NodeChildren respDetail = isFault ? resp['SCC_FAULT_RESP'].detail.MSGS.MSG :
                resp.detail.MSGS.MSG
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            mkp.yieldUnescaped mapInnerResult(respDetail)
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChildren response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            response.each { msgNode ->
                {
                    def messageNode = msgNode as NodeChild
                    def msgSeverityTxt = extractNodeText msgNode['MESSAGE_SEVERITY'] as GPathResult
                    def isMsgPropExist = !(msgNode['PROPS']['PROP'].isEmpty())
                    def sccEntityInstId = isMsgPropExist ? extractNodeText(msgNode['PROPS']['PROP'][0]['SCC_ENTITY_INST_ID']) : ""
                    "result"() {
                        "status"(msgSeverityTxt == null ? STATUS_CODE_NO_RESULT_FOUND : (msgSeverityTxt != "E" ? STATUS_CODE_SUCCESS :
                                STATUS_CODE_ACTION_CANNOT_PROCEED))
                        "entity"(sccEntityInstId)
                        "entityType"(ENTITY_COURSE_SECTION + ":" + ENTITY_STUDENT)
                        "entityInXMLFormat"(false)
                        "errorMsg"() {
                            mkp.yieldUnescaped(extractNodeText(msgNode['DESCR']))
                        }
                    }
                }
            }
        }
        return xml.toString()
    }
}
