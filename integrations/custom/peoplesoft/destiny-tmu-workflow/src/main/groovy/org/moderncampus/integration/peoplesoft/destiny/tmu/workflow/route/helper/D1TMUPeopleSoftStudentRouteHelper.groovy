package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

trait D1TMUPeopleSoftStudentRouteHelper extends D1TMUPeopleSoftRouteHelper {

    static final DateTimeFormatter TMU_D1_FORMAT_DATE_OF_ENTRY = DateTimeFormatter.ofPattern("yyyy/MM/dd")
    static final String CANADIAN_CITIZEN = '0'
    static final String NOT_REPORTED = '5'

    NodeChildren extractStudent(NodeChild integrationMessage) {
        return integrationMessage['body']['student'] as NodeChildren
    }

    String extractSchoolPersonnelNumber(NodeChild integrationMessage) {
        return extractNodeText(integrationMessage['body']['student']['schoolPersonnelNumber'] as NodeChildren)
    }

    String getUdfFieldValue(NodeChildren udfValueNodes, String fieldName, String defaultValue = null) {
        GPathResult udfValueNode = udfValueNodes.find { it ->
            return hasUdfFieldName(it as NodeChild, fieldName)
        }
        String udfValue = extractNodeText(udfValueNode['udfFieldValue'] as GPathResult)
        return udfValue ?: defaultValue
    }

    boolean hasUdfFieldName(NodeChild udfValueNode, String fieldName) {
        return fieldName == extractNodeText(udfValueNode['udfFieldSpec']['udfFieldName'] as NodeChildren)
    }

    String extractDateOfEntry(NodeChildren udfValueNodes, DateTimeFormatter formatter) {
        LocalDate birthDate = extractD1Date(getUdfFieldValue(udfValueNodes, 'dateOfEntry'))
        return birthDate ? formatter.format(birthDate) : null
    }
}