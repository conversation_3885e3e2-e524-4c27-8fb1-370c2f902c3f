package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockSSRGetClassSectionRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error. The server encountered an unexpected condition which prevented it from fulfilling " +
            "the request: "

    def static final String SUCCESS_RESP_ITEM_META = "" +
            "     <root> " +
            " <CLASS_SECTION_RESULT>\n" +
            "        <SSR_CRS_SRCH_COUNT>0</SSR_CRS_SRCH_COUNT>\n" +
            "        <CLASS_SECTIONS>\n" +
            "            <SSR_CLASS_SECTION>\n" +
            "                <ACAD_CAREER>CNED</ACAD_CAREER>\n" +
            "                <ACAD_CAREER_LOVDescr>Continuing Education</ACAD_CAREER_LOVDescr>\n" +
            "                <ASSOCIATED_CLASS>1</ASSOCIATED_CLASS>\n" +
            "                <AVAILABLE_SEATS>33</AVAILABLE_SEATS>\n" +
            "                <CAMPUS>MAIN</CAMPUS>\n" +
            "                <CAMPUS_LOVDescr>Main Campus</CAMPUS_LOVDescr>\n" +
            "                <CATALOG_NBR> 110</CATALOG_NBR>\n" +
            "                <CLASS_NBR>2326</CLASS_NBR>\n" +
            "                <CLASS_SECTION>FB0</CLASS_SECTION>\n" +
            "                <CLASS_TYPE>E</CLASS_TYPE>\n" +
            "                <CLASS_TYPE_LOVDescr>Enrollment Section</CLASS_TYPE_LOVDescr>\n" +
            "                <CONSENT>N</CONSENT>\n" +
            "                <CONSENT_LOVDescr>No Special Consent Required</CONSENT_LOVDescr>\n" +
            "                <CRS_TOPIC_ID>0</CRS_TOPIC_ID>\n" +
            "                <CRSE_ID>025665</CRSE_ID>\n" +
            "                <CRSE_ID_LOVDescr>Data Org. for Data Analysts</CRSE_ID_LOVDescr>\n" +
            "                <CRSE_OFFER_NBR>2</CRSE_OFFER_NBR>\n" +
            "                <DESCR200>CIND  110 - FB0   Data Organization for Data Analysts</DESCR200>\n" +
            "                <DESCRLONG>This course provides a foundation in data management for data analysts. Topics include database " +
            "architectures, formation of queries, queries themselves, data warehousing, relational database systems, NoSQL, and responsibilities of" +
            " data management professionals.</DESCRLONG>\n" +
            "                <EFFDT>2018-05-07</EFFDT>\n" +
            "                <END_DT>2018-08-18</END_DT>\n" +
            "                <ENRL_CAP>35</ENRL_CAP>\n" +
            "                <ENRL_STAT>O</ENRL_STAT>\n" +
            "                <ENRL_STATUS_DESCR>Closed</ENRL_STATUS_DESCR>\n" +
            "                <ENRL_TOT>2</ENRL_TOT>\n" +
            "                <GRADING_BASIS>GRD</GRADING_BASIS>\n" +
            "                <GRADING_BASIS_LOVDescr>Graded</GRADING_BASIS_LOVDescr>\n" +
            "                <INSTITUTION>RYERU</INSTITUTION>\n" +
            "                <INSTITUTION_LOVDescr>Ryerson University</INSTITUTION_LOVDescr>\n" +
            "                <INSTRUCTION_MODE>CL</INSTRUCTION_MODE>\n" +
            "                <INSTRUCTION_MODE_LOVDescr>Classroom</INSTRUCTION_MODE_LOVDescr>\n" +
            "                <LOCATION>DOWNTOWN</LOCATION>\n" +
            "                <LOCATION_DESCR>Downtown</LOCATION_DESCR>\n" +
            "                <MIN_ENRL>0</MIN_ENRL>\n" +
            "                <PRINT_TOPIC>N</PRINT_TOPIC>\n" +
            "                <SCHEDULE_PRINT>Y</SCHEDULE_PRINT>\n" +
            "                <SCHEDULE_PRINT_LOVDescr>Yes</SCHEDULE_PRINT_LOVDescr>\n" +
            "                <SESSION_CODE>3RS</SESSION_CODE>\n" +
            "                <SESSION_CODE_LOVDescr>Summer - Regular</SESSION_CODE_LOVDescr>\n" +
            "                <SSR_COMPONENT>LEC</SSR_COMPONENT>\n" +
            "                <SSR_COMPONENT_LOVDescr>Lecture</SSR_COMPONENT_LOVDescr>\n" +
            "                <SSR_CRSE_ATTR_LONG>Advertised: course in both print and web calendars&#xd;\n" +
            "    &#xd;\n" +
            "    Certificate and degree credit&#xd;\n" +
            "    &#xd;\n" +
            "    39 Hours&#xd;\n" +
            "    &#xd;\n" +
            "    3 Hours&#xd;\n" +
            "    &#xd;\n" +
            "    Course is exempt from taxes&#xd;\n" +
            "    &#xd;\n" +
            "    13 Weeks</SSR_CRSE_ATTR_LONG>\n" +
            "                <SSR_DATE_LONG>6/25/2018 - 8/18/2018</SSR_DATE_LONG>\n" +
            "                <SSR_DROP_CONSENT>N</SSR_DROP_CONSENT>\n" +
            "                <SSR_DROP_CONSENT_LOVDescr>No Special Consent Required</SSR_DROP_CONSENT_LOVDescr>\n" +
            "                <START_DT>2018-06-25</START_DT>\n" +
            "                <STRM>1185</STRM>\n" +
            "                <STRM_LOVDescr>Spring/Summer 2018</STRM_LOVDescr>\n" +
            "                <SUBJECT>CIND</SUBJECT>\n" +
            "                <SUBJECT_LOVDescr>Industrial Eng.</SUBJECT_LOVDescr>\n" +
            "                <UNITS_RANGE>1 units</UNITS_RANGE>\n" +
            "                <WAIT_TOT>0</WAIT_TOT>\n" +
            "                <WAIT_CAP>0</WAIT_CAP>\n" +
            "                <COURSE_TITLE_LONG>Data Organization for Data Analysts</COURSE_TITLE_LONG>\n" +
            "                <STUDENT_LANGUAGE>NULL</STUDENT_LANGUAGE>\n" +
            "                <SHIFT>Array</SHIFT>\n" +
            "                <UK_LEVEL>NULL</UK_LEVEL>\n" +
            "                <STUDENT_GROUPING>NULL</STUDENT_GROUPING>\n" +
            "                <PLACEMENT_COURSE>Array</PLACEMENT_COURSE>\n" +
            "                <NBR_CAMPUS_OFFERED>Array</NBR_CAMPUS_OFFERED>\n" +
            "                <DATE_LAST_VALIDATED>Array</DATE_LAST_VALIDATED>\n" +
            "                <LATE_CLASSES>Array</LATE_CLASSES>\n" +
            "                <CLASS_COMPONENTS>\n" +
            "                    <CLASS_COMPONENT>\n" +
            "                        <SSR_COMPONENT>LEC</SSR_COMPONENT>\n" +
            "                        <SSR_COMPONENT_LOVDescr>Lecture</SSR_COMPONENT_LOVDescr>\n" +
            "                        <OPTIONAL_SECTION>N</OPTIONAL_SECTION>\n" +
            "                        <OPTIONAL_SECTION_LOVDescr>Required</OPTIONAL_SECTION_LOVDescr>\n" +
            "                    </CLASS_COMPONENT>\n" +
            "                </CLASS_COMPONENTS>\n" +
            "                <CLASS_MEETING_PATTERNS>\n" +
            "                    <CLASS_MEETING_PATTERN>\n" +
            "                        <CRSE_ID>025665</CRSE_ID>\n" +
            "                        <CRSE_OFFER_NBR>2</CRSE_OFFER_NBR>\n" +
            "                        <STRM>1185</STRM>\n" +
            "                        <SESSION_CODE>3RS</SESSION_CODE>\n" +
            "                        <SESSION_CODE_LOVDescr>Summer - Regular</SESSION_CODE_LOVDescr>\n" +
            "                        <CLASS_SECTION>FB0</CLASS_SECTION>\n" +
            "                        <CLASS_MTG_NBR>1</CLASS_MTG_NBR>\n" +
            "                        <MEETING_TIME_START>18:00:00.000000</MEETING_TIME_START>\n" +
            "                        <MEETING_TIME_END>21:00:00.000000</MEETING_TIME_END>\n" +
            "                        <MON>N</MON>\n" +
            "                        <TUES>Y</TUES>\n" +
            "                        <WED>N</WED>\n" +
            "                        <THURS>Y</THURS>\n" +
            "                        <FRI>N</FRI>\n" +
            "                        <SAT>N</SAT>\n" +
            "                        <SUN>N</SUN>\n" +
            "                        <START_DT>2018-06-25</START_DT>\n" +
            "                        <END_DT>2018-08-18</END_DT>\n" +
            "                        <CRS_TOPIC_ID>0</CRS_TOPIC_ID>\n" +
            "                        <STND_MTG_PAT>TR</STND_MTG_PAT>\n" +
            "                        <SCC_LATITUDE>0</SCC_LATITUDE>\n" +
            "                        <SCC_LONGITUDE>0</SCC_LONGITUDE>\n" +
            "                        <SSR_MTG_SCHED_LONG>TuTh 6:00PM - 9:00PM</SSR_MTG_SCHED_LONG>\n" +
            "                        <SSR_MTG_LOC_LONG>TBA</SSR_MTG_LOC_LONG>\n" +
            "                        <SSR_INSTR_LONG>Staff</SSR_INSTR_LONG>\n" +
            "                        <SSR_MTG_DT_LONG>2018-06-25 - 2018-08-18</SSR_MTG_DT_LONG>\n" +
            "                        <SSR_TOPIC_LONG>TBA</SSR_TOPIC_LONG>\n" +
            "                    </CLASS_MEETING_PATTERN>\n" +
            "                </CLASS_MEETING_PATTERNS>\n" +
            "                <CLASS_TEXT_BOOKS>\n" +
            "                    <CLASS_TEXT_BOOK>\n" +
            "                        <TEXTBOOK_MESSAGE>Textbooks to be determined</TEXTBOOK_MESSAGE>\n" +
            "                    </CLASS_TEXT_BOOK>\n" +
            "                </CLASS_TEXT_BOOKS>\n" +
            "                <ENROLLMENT_DETAILS>\n" +
            "                    <ENROLLMENT_INFORMATION>\n" +
            "                        <CLASS_ATTRIBUTES>Certificate and degree credit39 HoursCourse is exempt from taxes13 " +
            "Weeks</CLASS_ATTRIBUTES>\n" +
            "                    </ENROLLMENT_INFORMATION>\n" +
            "                </ENROLLMENT_DETAILS>\n" +
            "            </SSR_CLASS_SECTION>\n" +
            "        </CLASS_SECTIONS>\n" +
            "    </CLASS_SECTION_RESULT>" +
            "     </root>"


    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request.toString()) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('500')
    }

    def boolean isEmptyResponse(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('EMP')
    }

    def boolean isFault(NodeChild respRoot) {
        return getClassNbr(respRoot).endsWith('XX1')
    }

    def void getServerErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + getClassNbr(respRoot), 500)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEmptyResponseContent() throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "SSR_GET_CLASS_SECTION_RESP"() {
                "CLASS_SECTION_RESULT"() {
                    "SSR_CRS_SRCH_COUNT"("0")
                    "SSR_CRS_GEN_MSG"()
                    "CLASS_SECTIONS"()
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent() throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "SSR_GET_CLASS_SECTION_RESP"() {
                "IS_FAULT"("Y")
                "SCC_FAULT_RESP"() {
                    "detail"() {
                        "MSGS"() {
                            "MSG"() {
                                "ID"("14098-309")
                                "DESCR"("Tag STRM has an invalid value 0001. Check and try again.")
                                "MESSAGE_SEVERITY"("E")
                                "PROPS"()
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    private String getClassNbr(NodeChild child) {
        return extractNodeText(child['CLASS_SECTION_REQUEST']['CLASS_NBR'] as GPathResult, () -> '')
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def respRoot = exchange.message.getBody(NodeChild.class)
        def respMetaXML = buildRespMetaXMLNode(getClassNbr(respRoot))
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "SSR_GET_CLASS_SECTION_RESP"() {
                mkp.yield respMetaXML.'CLASS_SECTION_RESULT'[0]
                "ORIGINAL"(originalReq)
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild buildRespMetaXMLNode(String classNbr) {
        def successRespMeta = new XmlSlurper().parseText(SUCCESS_RESP_ITEM_META)
        def ssrClassSectionNode = successRespMeta['CLASS_SECTION_RESULT']['CLASS_SECTIONS']['SSR_CLASS_SECTION']
        if (classNbr.startsWith('2')) {
            ssrClassSectionNode.ENRL_CAP.replaceBody("35")
            ssrClassSectionNode.ENRL_STAT.replaceBody("C")
            ssrClassSectionNode.ENRL_TOT.replaceBody("35")
        } else if (!classNbr.startsWith('1')) {
            ssrClassSectionNode.ENRL_CAP.replaceBody("40")
            ssrClassSectionNode.ENRL_STAT.replaceBody("C")
        }
        return successRespMeta as NodeChild
    }
}
