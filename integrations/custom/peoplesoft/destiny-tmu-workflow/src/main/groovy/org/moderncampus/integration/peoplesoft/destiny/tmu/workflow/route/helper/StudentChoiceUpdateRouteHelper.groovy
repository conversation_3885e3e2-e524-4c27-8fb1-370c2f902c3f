package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ValidationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentChoiceUpdateRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftStudentChoiceRouteHelper {

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMUPutOptinOutRequest(Exchange exchange) throws Exception {
        def studentId = exchange.message.getBody(String.class)
        NodeChild request = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        GPathResult filteredEnrollmentEvents = exchange.getProperty(FILTERED_ENROLLMENT_EVENTS, GPathResult.class)
        NodeChild enrollmentEvent = !filteredEnrollmentEvents.isEmpty() ? filteredEnrollmentEvents[0] as NodeChild : null
        if (filteredEnrollmentEvents.size() > 1 || enrollmentEvent == null) {
            throw new ValidationException("A single enrollment event must be present for processing")
        }
        def applicableItemTypes = extractConditionalSpecialRequestsFromEvent(enrollmentEvent).collect { specialRequestNode ->
            return extractNodeText(specialRequestNode['associatedSpecialRequest']['specialRequestType'] as GPathResult)
        }
        def optedInItemTypes = enrollmentEvent['feeLWs']['feeLW'].findAll { it ->
            return !(it['fee']['specialRequestType'] as GPathResult).isEmpty() && (extractNodeText(it['fee']['specialRequestType'] as GPathResult))
        }.collect { feeLWNode ->
            return extractNodeText(feeLWNode['fee']['specialRequestType'] as GPathResult)
        }
        applicableItemTypes.removeAll(optedInItemTypes)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_PUT_OPTINOUT_REQUEST"() {
                "STUDENTID"(studentId)
                "ACAD_CAREER"('CNED')
                "STRM"(extractStrm(enrollmentEvent as NodeChild))
                applicableItemTypes.each { String itemType ->
                    "ITEM_TYPE"(itemType)
                }
                "D1_PASS_THROUGH"(extractNodeText(enrollmentEvent['objectId'] as GPathResult))
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String mapInnerResult(NodeChild response) {
        def isMajorFault = isFaultResponse(response, true, true)
        def isFault = isFaultResponse(response, true)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isMajorFault ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault ? mkp.yieldUnescaped(extractNodeText(response['FAULT']['MSGS']['MSG'][0]['ID'])) :
                        mkp.yieldUnescaped(extractNodeText(response['D1_PASS_THROUGH']))
            }
            "entityType"(ENTITY_COURSE_SECTION_LW)
            "entityInXMLFormat"(false)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }
}
