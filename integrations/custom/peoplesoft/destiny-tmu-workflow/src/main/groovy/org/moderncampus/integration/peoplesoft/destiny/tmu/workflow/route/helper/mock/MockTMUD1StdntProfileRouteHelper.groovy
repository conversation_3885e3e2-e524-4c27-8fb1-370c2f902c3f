package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1StdntProfileRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error. The server encountered an unexpected condition which prevented it from fulfilling " +
            "the request: "

    static final String RDS1 = "<root>\n" +
            "     <TITLE>Ms</TITLE>\n" +
            "     <FIRST_NAME>Virginia</FIRST_NAME>\n" +
            "     <LAST_NAME>Sara</LAST_NAME>\n" +
            "     <ADDRESS1>181 Loriann Road</ADDRESS1>\n" +
            "     <CITY>Mississauga</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>A1B2C3</POSTAL>\n" +
            "     <COUNTRY>CAN</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <BUS_PHONE>416/979-5000</BUS_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <BIRTHDATE>1994-01-28</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <RU_OEN_NBR>526621990</RU_OEN_NBR>\n" +
            "     <MYRYERSONID>virginia.sara</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>N</HAS_RESIDENCY_INFO>\n" +
            " </root>\n"
    static final String RDS2 = "<root>\n" +
            "     <TITLE>Ms</TITLE>\n" +
            "     <FIRST_NAME>Hester</FIRST_NAME>\n" +
            "     <LAST_NAME>Crystle</LAST_NAME>\n" +
            "     <ADDRESS1>380 Deena Road</ADDRESS1>\n" +
            "     <ADDRESS2>Basement</ADDRESS2>\n" +
            "     <CITY>Toronto</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>M6K 1G9</POSTAL>\n" +
            "     <COUNTRY>XYZ</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <LANG_CD>ENG</LANG_CD>\n" +
            "     <BIRTHDATE>1993-09-16</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <MYRYERSONID>hester.crystle</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>Y</HAS_RESIDENCY_INFO>\n" +
            " </root>\n"
    static final String RDS3 = " <root>\n" +
            "     <TITLE>Mr</TITLE>\n" +
            "     <FIRST_NAME>Cleveland</FIRST_NAME>\n" +
            "     <LAST_NAME>Odis</LAST_NAME>\n" +
            "     <ADDRESS1>864 Loriann Road</ADDRESS1>\n" +
            "     <CITY>North York</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>A1B2C3</POSTAL>\n" +
            "     <COUNTRY>CAN</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <BUS_PHONE>416/979-5000</BUS_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <BIRTHDATE>1995-07-25</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <RU_OEN_NBR>948336417</RU_OEN_NBR>\n" +
            "     <MYRYERSONID>cleveland.odis</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>N</HAS_RESIDENCY_INFO>\n" +
            " </root>"
    static final String RDS4 = "<root>\n" +
            "     <TITLE>Mr</TITLE>\n" +
            "     <FIRST_NAME>Klara</FIRST_NAME>\n" +
            "     <LAST_NAME>Johnny</LAST_NAME>\n" +
            "     <ADDRESS1>457 Sharon Road</ADDRESS1>\n" +
            "     <CITY>Brampton</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>A1B2C3</POSTAL>\n" +
            "     <COUNTRY>CAN</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <BUS_PHONE>416/979-5000</BUS_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <BIRTHDATE>1995-08-05</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <IS_OUT_OF_PROVINCE>Y</IS_OUT_OF_PROVINCE>\n" +
            "     <RU_OEN_NBR>862356128</RU_OEN_NBR>\n" +
            "     <MYRYERSONID>klara.johnny</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            " </root>\n"
    static final String RDS5 = " <root>\n" +
            "     <FIRST_NAME>Loris</FIRST_NAME>\n" +
            "     <LAST_NAME>Nina</LAST_NAME>\n" +
            "     <ADDRESS1>848 Lana Road</ADDRESS1>\n" +
            "     <ADDRESS2>Zaferanieh St.,</ADDRESS2>\n" +
            "     <CITY>Tehran</CITY>\n" +
            "     <COUNTRY>IRN</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <BIRTHDATE>1993-01-08</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <IS_OUT_OF_PROVINCE>Y</IS_OUT_OF_PROVINCE>\n" +
            "     <RU_OEN_NBR>689481422</RU_OEN_NBR>\n" +
            "     <MYRYERSONID>loris.nina</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            " </root>"
    static final String RDS6 = "<root>\n" +
            "     <TITLE></TITLE>\n" +
            "     <FIRST_NAME></FIRST_NAME>\n" +
            "     <LAST_NAME></LAST_NAME>\n" +
            "     <FIRST_NAME_CD></FIRST_NAME_CD>\n" +
            "     <LAST_NAME_CD></LAST_NAME_CD>\n" +
            "     <ADDRESS1></ADDRESS1>\n" +
            "     <ADDRESS2></ADDRESS2>\n" +
            "     <CITY></CITY>\n" +
            "     <PROVINCE></PROVINCE>\n" +
            "     <POSTAL></POSTAL>\n" +
            "     <COUNTRY></COUNTRY>\n" +
            "     <HOME_PHONE></HOME_PHONE>\n" +
            "     <BUS_PHONE></BUS_PHONE>\n" +
            "     <EXTENSION></EXTENSION>\n" +
            "     <EMAIL_ADDR></EMAIL_ADDR>\n" +
            "     <LANG_CD></LANG_CD>\n" +
            "     <BIRTHDATE></BIRTHDATE>\n" +
            "     <IS_DOMESTIC></IS_DOMESTIC>\n" +
            "     <ACAD_PROG></ACAD_PROG>\n" +
            "     <RU_OEN_NBR></RU_OEN_NBR>\n" +
            "     <RU_DESTINY_ID></RU_DESTINY_ID>\n" +
            "     <MYRYERSONID></MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID></RMS_CREDENTIAL_ID>\n" +
            " </root>\n"
    static final String RDS7 = " <root>\n" +
            "     <TITLE>Mr</TITLE>\n" +
            "     <FIRST_NAME>123</FIRST_NAME>\n" +
            "     <LAST_NAME>Johnny</LAST_NAME>\n" +
            "     <ADDRESS1>457 Sharon Road</ADDRESS1>\n" +
            "     <CITY>Brampton</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>A1B2C3</POSTAL>\n" +
            "     <HOME_PHONE>abcd</HOME_PHONE>\n" +
            "     <BUS_PHONE></BUS_PHONE>\n" +
            "     <EMAIL_ADDR><EMAIL></EMAIL_ADDR>\n" +
            "     <BIRTHDATE>1995-08-05</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <RU_OEN_NBR>862356128</RU_OEN_NBR>\n" +
            "     <MYRYERSONID>klara.johnny</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>Y</HAS_RESIDENCY_INFO>\n" +
            " </root>\n"
    static final String RDS8 = "<root>\n" +
            "     <TITLE>Ms</TITLE>\n" +
            "     <FIRST_NAME>Hester</FIRST_NAME>\n" +
            "     <LAST_NAME>Crystle</LAST_NAME>\n" +
            "     <ADDRESS1>380 Deena Road</ADDRESS1>\n" +
            "     <ADDRESS2>Basement</ADDRESS2>\n" +
            "     <CITY>Toronto</CITY>\n" +
            "     <PROVINCE>ON</PROVINCE>\n" +
            "     <POSTAL>08854</POSTAL>\n" +
            "     <COUNTRY>Canada</COUNTRY>\n" +
            "     <HOME_PHONE>416/979-5000</HOME_PHONE>\n" +
            "     <EMAIL_ADDR>hester.crystle</EMAIL_ADDR>\n" +
            "     <LANG_CD>ENG</LANG_CD>\n" +
            "     <BIRTHDATE>" + LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) + "</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>N</IS_DOMESTIC>\n" +
            "     <MYRYERSONID>hester.crystle</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>N</HAS_RESIDENCY_INFO>\n" +
            " </root>\n"
    static final String RDS9 = "<root>\n" +
            "     <TITLE>Ms</TITLE>\n" +
            "     <FIRST_NAME>Cara</FIRST_NAME>\n" +
            "     <LAST_NAME>Fredricks</LAST_NAME>\n" +
            "     <ADDRESS1></ADDRESS1>\n" +
            "     <ADDRESS2></ADDRESS2>\n" +
            "     <CITY></CITY>\n" +
            "     <PROVINCE></PROVINCE>\n" +
            "     <POSTAL></POSTAL>\n" +
            "     <COUNTRY></COUNTRY>\n" +
            "     <HOME_PHONE></HOME_PHONE>\n" +
            "     <EMAIL_ADDR></EMAIL_ADDR>\n" +
            "     <LANG_CD>ENG</LANG_CD>\n" +
            "     <BIRTHDATE>" + LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) + "</BIRTHDATE>\n" +
            "     <IS_DOMESTIC>Y</IS_DOMESTIC>\n" +
            "     <IS_OUT_OF_PROVINCE>N</IS_OUT_OF_PROVINCE>\n" +
            "     <MYRYERSONID>hester.crystle</MYRYERSONID>\n" +
            "     <RMS_CREDENTIAL_ID>12345</RMS_CREDENTIAL_ID>\n" +
            "     <HAS_RESIDENCY_INFO>N</HAS_RESIDENCY_INFO>\n" +
            " </root>\n"

    String mapRequest(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        return extractNodeText(rootNode['STUDENTID'] as NodeChildren)
    }

    def boolean isFault(String studentId) {
        return studentId != null && (studentId.endsWith("XX1") || studentId.endsWith("XX2"))
    }

    def boolean isFault1(String studentId) {
        return studentId != null && studentId.endsWith("XX1")
    }

    def boolean isFault2(String studentId) {
        return studentId != null && studentId.endsWith("XX2")
    }

    def boolean isEmptyResponse(String studentId) {
        return studentId != null && studentId.endsWith("EMP")
    }

    def boolean isServerError(String studentId) {
        return studentId != null && studentId.endsWith("500")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + studentId, 500)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFault1Content() throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_STDNT_PROFILE_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1004")
                            "DESCR"("Unable to decrypt STUDENT ID.")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFault2Content() throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_STDNT_PROFILE_FAULT"("xmlns:RU": "http://xmlns.ryerson.ca/ps/sas/services") {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1001")
                            "DESCR"("Student ID  tag is missing in the request message.")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEmptyResponseContent() throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_STDNT_PROFILE_RESPONSE"() {
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        String studentId = exchange.message.getBody(String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_STDNT_PROFILE_RESPONSE"() {
                "STUDENTID"(studentId)
                if (studentId.startsWith("1")) {
                    mkp.yield((new XmlSlurper().parseText(RDS1)).'*')
                } else if (studentId.startsWith("2")) {
                    mkp.yield((new XmlSlurper().parseText(RDS2)).'*')
                } else if (studentId.startsWith("3")) {
                    mkp.yield((new XmlSlurper().parseText(RDS3)).'*')
                } else if (studentId.startsWith("4")) {
                    mkp.yield((new XmlSlurper().parseText(RDS4)).'*')
                } else if (studentId.startsWith("5")) {
                    mkp.yield((new XmlSlurper().parseText(RDS5)).'*')
                } else if (studentId.startsWith("6")) {
                    mkp.yield((new XmlSlurper().parseText(RDS6)).'*')
                } else if (studentId.startsWith("7")) {
                    mkp.yield((new XmlSlurper().parseText(RDS7)).'*')
                } else if (studentId.startsWith("8")) {
                    mkp.yield((new XmlSlurper().parseText(RDS8)).'*')
                } else if (studentId.startsWith("9")) {
                    mkp.yield((new XmlSlurper().parseText(RDS9)).'*')
                } else {
                    mkp.yield((new XmlSlurper().parseText(RDS1)).'*')
                }
                "original"(originalReq)
            }
        }
        return xml.toString()
    }
}
