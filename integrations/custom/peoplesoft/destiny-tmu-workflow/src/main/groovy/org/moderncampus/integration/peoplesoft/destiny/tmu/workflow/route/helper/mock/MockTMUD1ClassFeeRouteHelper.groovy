package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1ClassFeeRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error: "

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith('ERR6.5')
    }

    def boolean isClientError(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith("ERR6.4")
    }

    def boolean isFault(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith('ERR6.1')
    }

    private String getClassNbr(NodeChild child) {
        return extractNodeText(child['CLASS_NBR'] as GPathResult, () -> '')
    }

    def void getServerErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + getClassNbr(respRoot))
    }

    def void getClientErrorContent(NodeChild respRoot) throws Exception {
        throw new ApplicationException(JsonOutput.toJson([message: 'MOCK_RU_D1_CLASS_FEE', data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "&lt;RU_D1_CLASS_FEE_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;&lt;" +
                "FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_CLASS_FEE_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(NodeChild request) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_CLASS_FEE_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            "ID"("1001-mock")
                            "DESCR"("This is a mock fault")
                            "MESSAGE_SEVERITY"("E")
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(NodeChild req) throws Exception {
        String classNbr = extractNodeText(req['CLASS_NBR'])
        def domesticFee = classNbr.startsWith('ERR6.2') && charAt(classNbr, 6) == '0' ? 0 : 654.32
        def internationalFee = classNbr.startsWith('ERR6.2') && charAt(classNbr, 7) == '0' ? 0 : 4321.78
        def outOfProvinceFee = classNbr.startsWith('ERR6.2') && charAt(classNbr, 8) == '0' ? 0 : 1265.18
        def hst = classNbr.startsWith('ERR6.2') && charAt(classNbr, 9) == '0' ? 0 : 262.59
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_CLASS_FEE_RESPONSE"() {
                "RESULT"("True")
                "CLASS_NBR"(["DOMESTIC_FEE"       : domesticFee, "INTERNATIONAL_FEE": internationalFee,
                             "OUT_OF_PROVINCE_FEE": outOfProvinceFee, "HST": hst], classNbr)
                "origin"("MOCK_RU_D1_CLASS_FEE")
            }
        }
        return xml.toString()
    }

    private String charAt(String classNbr, int index) {
        try {
            return classNbr.charAt(index)
        } catch (Exception e) {
            return ""
        }
    }
}
