package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockSSREnrValidateRouteHelper extends MockPeopleSoftCartServiceRouteHelper {

    static final String msgDescSuccess = "Ok to Add"

    def static final Map<String, String> msgDescErrorMap = [
            "ERR4.5001": "Internal Server Error (Mock)",
            "ERR4.4001": "Bad Request (Mock)",
            "ERR4.1001": "Student must be term activated and eligible to enroll",
            "ERR4.1002": "Student is withdrawn or cancelled",
            "ERR4.1003": "Student must have an active program for the term",
            "ERR4.1004": "Invalid EMPLID",
            "ERR4.1005": "Student must be in permitted academic career",
            "ERR4.2001": "Last date to enroll has passed",
            "ERR4.2002": "Enrollment into class requires an enrollment appointment",
            "ERR4.2003": "Class Full",
            "ERR4.3001": "Student is already enrolled",
            "ERR4.XXXX": "Unable to add this class - unknown error"
    ]

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "SSR_ENR_VALIDATE_RESP"() {
                "detail"() {
                    "MSGS"() {
                        GPathResult items = extractMessageFromRequest(request)
                        if (items.isEmpty()) {
                            "MSG"()
                        }
                        items.each { item ->
                            "MSG"() {
                                mkp.yield item.'*'
                            }
                        }
                    }
                }
            }
        }
        return new XmlSlurper().parseText(xml.toString()) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return hasErrorCode(respRoot, ["ERR4.5001"])
    }

    def boolean isClientError(NodeChild respRoot) {
        NodeChildren msgNodes = respRoot['detail']['MSGS']['MSG']['*'] as NodeChildren
        return msgNodes.size() <= 0 || hasErrorCode(respRoot, ['ERR4.4001'])
    }

    def void getServerErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR4.5001"], 500)
    }

    def void getClientErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR4.4001"], 400)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def respRoot = exchange.message.getBody(NodeChild.class)
        respRoot.detail.MSGS.MSG.replaceNode { node ->
            String emplId = (extractNodeText node.EMPLID as GPathResult) ?: null
            String classNbr = (extractNodeText node.CLASS_NBR as GPathResult) ?: null
            String errorCode = getErrorCode(node)
            String errorField = getErrorField(node)
            "MSG" {
                "ID"(classNbr + "-" + emplId)
                "DESCR"(errorCode ? msgDescErrorMap[errorCode] + " [" + errorCode + "]" : msgDescSuccess)
                "MESSAGE_SEVERITY"(errorCode ? 'E' : 'I')
                "PROPS"() {
                    "PROP"() {
                        "SCC_ENTITY_INST_ID"() {
                            mkp.yieldUnescaped node.SCC_ENTITY_INST_ID
                        }
                        "PROPNAME"("")
                    }
                }
            }
        }
        respRoot << {
            "ORIGINAL_MOCK"(originalReq)
        }
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return xml.toString()
    }

    private GPathResult extractMessageFromRequest(String originalReq) {
        def rootNode = new XmlSlurper().parseText(originalReq) as NodeChild
        return rootNode['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'] as GPathResult
    }

    @Override
    protected String getErrorField(NodeChild msgNode) {
        String emplId = extractNodeText msgNode['EMPLID'] as GPathResult
        String classNbr = extractNodeText msgNode['CLASS_NBR'] as GPathResult
        if (classNbr.startsWith("ERR4.400") || classNbr.startsWith("ERR4.500")) {
            return classNbr
        } else if (emplId.startsWith("ERR4.100")) {
            return emplId
        } else if (classNbr.startsWith("ERR4.300") && classNbr == emplId) {
            return classNbr
        } else if (classNbr.startsWith("ERR4.")) {
            return classNbr
        } else {
            return null
        }
    }

    @Override
    protected String makeErrorCode(String errField) {
        return errField.startsWith("ERR4.") && errField.length() > 7 && errField.substring(0, 9) in msgDescErrorMap.keySet() ?
                errField.substring(0, 9) : "ERR4.XXXX"
    }
}
