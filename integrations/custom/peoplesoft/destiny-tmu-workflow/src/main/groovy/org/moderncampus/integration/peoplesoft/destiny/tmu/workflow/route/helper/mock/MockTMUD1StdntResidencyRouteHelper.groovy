package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock


import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1StdntResidencyRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error. The server encountered an unexpected condition which prevented it from fulfilling " +
            "the request: "
    static final String STUDENT_ID_PREFIX = "55555"

    @CompileStatic(TypeCheckingMode.SKIP)
    def String mapRequest(String request) {
        def rootNode = new XmlSlurper().parseText(request) as NodeChild
        return extractNodeText(rootNode['STUDENTID'] as NodeChildren)
    }

    def boolean isFault(String studentId) {
        return studentId != null && (studentId.endsWith("XE1") || studentId.endsWith('XE2') || studentId.endsWith('XE3') || studentId.endsWith('XE4'))
    }

    def boolean isServerError(String studentId) {
        return studentId != null && studentId.endsWith("500")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + studentId, 500)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(String studentId) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_STDNT_RESIDENCY_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            if (studentId.endsWith('XE2')) {
                                "ID"("1003")
                                "DESCR"("RESIDENCY tag is missing in the request message.")
                                "MESSAGE_SEVERITY"("E")
                            } else if (studentId.endsWith('XE3')) {
                                "ID"("1001")
                                "DESCR"("Student ID  tag is missing in the request message.")
                                "MESSAGE_SEVERITY"("E")
                            } else if (studentId.endsWith('XE4')) {
                                "ID"("1006")
                                "DESCR"("RESIDENCY already exists")
                                "MESSAGE_SEVERITY"("I")
                            } else {
                                "ID"("1001")
                                "DESCR"("Invalid Student")
                                "MESSAGE_SEVERITY"("E")
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def studentId = exchange.message.getBody(String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_STDNT_RESIDENCY_RESPONSE"() {
                "STUDENTID"(studentId)
                "original"(originalReq)
            }
        }
        return xml.toString()
    }
}
