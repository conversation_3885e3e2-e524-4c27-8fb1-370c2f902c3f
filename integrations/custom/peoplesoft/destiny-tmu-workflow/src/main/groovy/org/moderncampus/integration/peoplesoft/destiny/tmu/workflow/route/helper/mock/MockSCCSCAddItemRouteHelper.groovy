package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockSCCSCAddItemRouteHelper extends MockPeopleSoftCartServiceRouteHelper {

    static final String msgDescSuccess = "Ok to Add"

    def static final Map<String, String> msgDescErrorMap = [
            "ERR2.5001": "The server encountered an unexpected condition which prevented it from fulfilling the request (Mock)",
            "ERR2.4001": "Bad Request (Mock)",
            "ERR2.1001": "Student already enrolled",
            "ERR2.1002": "Term invalid value",
            "ERR2.2001": "Class invalid value",
            "ERR2.3001": "Enrollment in Courses in Career:  are normally not allowed for those in Career: Continuing Education",
            "ERR2.XXXX": "Unable to add this class - unknown error"
    ]

    def static final Map<String, String> errorCodeToFieldMap = [
            "ERR2.1002": "STRM",
            "ERR2.2001": "CLASS_NBR",
            "ERR2.3001": "CLASS_NBR"
    ]

    def static final List<String> validationErrorCodes = ["ERR2.1002", "ERR2.2001", "ERR2.3001"]

    def static final String SUCCESS_RESP_ITEM_META = "" +
            "     <root> " +
            "     <INSTITUTION_LOVDescr>Ryerson University</INSTITUTION_LOVDescr>\n" +
            "     <ACAD_CAREER_LOVDescr>Continuing Education</ACAD_CAREER_LOVDescr>\n" +
            "     <STRM_LOVDescr>Spring/Summer 2018</STRM_LOVDescr>\n" +
            "     <CLASS_NBR_LOVDescr>CIND 110 - Data Org. for Data Analysts</CLASS_NBR_LOVDescr>\n" +
            "     <WAIT_LIST_OKAY>N</WAIT_LIST_OKAY>\n" +
            "     <CLASS_PRMSN_NBR>0</CLASS_PRMSN_NBR>\n" +
            "     <GRADING_BASIS_ENRL>GRD</GRADING_BASIS_ENRL>\n" +
            "     <GRADING_BASIS_ENRL_LOVDescr>Graded</GRADING_BASIS_ENRL_LOVDescr>\n" +
            "     <UNT_TAKEN>1</UNT_TAKEN>\n" +
            "     <INSTRUCTOR_ID></INSTRUCTOR_ID>\n" +
            "     <RQMNT_DESIGNTN_OPT>N</RQMNT_DESIGNTN_OPT>\n" +
            "     <START_DT></START_DT>\n" +
            "     <CRSE_COUNT>1</CRSE_COUNT>\n" +
            "     <ACAD_PROG></ACAD_PROG>\n" +
            "     <ACAD_PROG_LOVDescr></ACAD_PROG_LOVDescr>\n" +
            "     <RELATE_CLASS_NBR_1>0</RELATE_CLASS_NBR_1>\n" +
            "     <RELATE_CLASS_NBR_2>0</RELATE_CLASS_NBR_2>\n" +
            "     <SCC_ROW_ADD_DTTM>05/31/2018 20:29:08.000000</SCC_ROW_ADD_DTTM>\n" +
            "     <QUANTITY>0</QUANTITY>\n" +
            "     <RQMNT_DESIGNTN></RQMNT_DESIGNTN>\n" +
            "     <RQMNT_DESIGNTN_LOVDescr></RQMNT_DESIGNTN_LOVDescr>\n" +
            "     <RQMNT_DESG_DESCRFORMAL></RQMNT_DESG_DESCRFORMAL>\n" +
            "     <ENRL_REQ_DETL_STAT>P</ENRL_REQ_DETL_STAT>\n" +
            "     <ENRL_REQ_DETL_STAT_LOVDescr>Pending</ENRL_REQ_DETL_STAT_LOVDescr>\n" +
            "     <DTTM_STAMP_SEC>05/31/2018 20:29:07.000000</DTTM_STAMP_SEC>\n" +
            "     <SSR_VALIDATE>N</SSR_VALIDATE>\n" +
            "     <SSR_VALIDATN_DTTM></SSR_VALIDATN_DTTM>\n" +
            "     <SCC_ROW_ADD_OPRID>W500981563</SCC_ROW_ADD_OPRID>\n" +
            "     <SCC_ROW_UPD_OPRID>W500981563</SCC_ROW_UPD_OPRID>\n" +
            "     <SCC_ROW_UPD_DTTM>05/31/2018 20:29:08.000000</SCC_ROW_UPD_DTTM>\n" +
            "     <SSR_INSTR_LONG>Staff</SSR_INSTR_LONG>\n" +
            "     <CLASS_SECTIONS>\n" +
            "         <SSR_CLASS_SECTION>\n" +
            "             <ACAD_CAREER>CNED</ACAD_CAREER>\n" +
            "             <ACAD_CAREER_LOVDescr>Continuing Education</ACAD_CAREER_LOVDescr>\n" +
            "             <ASSOCIATED_CLASS>1</ASSOCIATED_CLASS>\n" +
            "             <AVAILABLE_SEATS>34</AVAILABLE_SEATS>\n" +
            "             <CAMPUS>MAIN</CAMPUS>\n" +
            "             <CAMPUS_LOVDescr>Main Campus</CAMPUS_LOVDescr>\n" +
            "             <CATALOG_NBR> 110</CATALOG_NBR>\n" +
            "             <CLASS_NBR>2326</CLASS_NBR>\n" +
            "             <CLASS_SECTION>FB0</CLASS_SECTION>\n" +
            "             <CLASS_TYPE>E</CLASS_TYPE>\n" +
            "             <CLASS_TYPE_LOVDescr>Enrollment Section</CLASS_TYPE_LOVDescr>\n" +
            "             <COMBINED_SECTION></COMBINED_SECTION>\n" +
            "             <COMBINED_SECTION_LOVDescr></COMBINED_SECTION_LOVDescr>\n" +
            "             <CONSENT>N</CONSENT>\n" +
            "             <CONSENT_LOVDescr>No Special Consent Required</CONSENT_LOVDescr>\n" +
            "             <CRS_TOPIC_ID>0</CRS_TOPIC_ID>\n" +
            "             <CRS_TOPIC_DESCR></CRS_TOPIC_DESCR>\n" +
            "             <CRSE_ID>025665</CRSE_ID>\n" +
            "             <CRSE_ID_LOVDescr>Data Org. for Data Analysts</CRSE_ID_LOVDescr>\n" +
            "             <CRSE_OFFER_NBR>2</CRSE_OFFER_NBR>\n" +
            "             <DESCR200>CIND 110 - FB0 Data Organization for Data Analysts</DESCR200>\n" +
            "             <DESCRLONG>This course provides a foundation in data management for data analysts. Topics include database architectures," +
            " formation of queries, queries themselves, data warehousing, relational database systems, NoSQL, and responsibilities of data " +
            "management professionals.</DESCRLONG>\n" +
            "             <EFFDT>2018-05-07</EFFDT>\n" +
            "             <END_DT>2018-08-18</END_DT>\n" +
            "             <ENRL_CAP>36</ENRL_CAP>\n" +
            "             <ENRL_STAT>O</ENRL_STAT>\n" +
            "             <ENRL_STATUS_DESCR>Open</ENRL_STATUS_DESCR>\n" +
            "             <ENRL_TOT>2</ENRL_TOT>\n" +
            "             <GRADING_BASIS>GRD</GRADING_BASIS>\n" +
            "             <GRADING_BASIS_LOVDescr>Graded</GRADING_BASIS_LOVDescr>\n" +
            "             <INSTITUTION>RYERU</INSTITUTION>\n" +
            "             <INSTITUTION_LOVDescr>Ryerson University</INSTITUTION_LOVDescr>\n" +
            "             <INSTRUCTION_MODE>CL</INSTRUCTION_MODE>\n" +
            "             <INSTRUCTION_MODE_LOVDescr>Classroom</INSTRUCTION_MODE_LOVDescr>\n" +
            "             <LOCATION>DOWNTOWN</LOCATION>\n" +
            "             <LOCATION_DESCR>Downtown</LOCATION_DESCR>\n" +
            "             <MIN_ENRL>0</MIN_ENRL>\n" +
            "             <PRINT_TOPIC>N</PRINT_TOPIC>\n" +
            "             <SCHEDULE_PRINT>Y</SCHEDULE_PRINT>\n" +
            "             <SCHEDULE_PRINT_LOVDescr>Yes</SCHEDULE_PRINT_LOVDescr>\n" +
            "             <SESSION_CODE>3RS</SESSION_CODE>\n" +
            "             <SESSION_CODE_LOVDescr>Summer - Regular</SESSION_CODE_LOVDescr>\n" +
            "             <SSR_CLASSNOTE_LONG></SSR_CLASSNOTE_LONG>\n" +
            "             <SSR_COMPONENT>LEC</SSR_COMPONENT>\n" +
            "             <SSR_COMPONENT_LOVDescr>Lecture</SSR_COMPONENT_LOVDescr>\n" +
            "             <SSR_CRSE_ATTR_LONG>\n" +
            "                 Advertised: course in both print and web calendars\n" +
            "                 \n" +
            "                 Certificate and degree credit\n" +
            "                 \n" +
            "                 39 Hours\n" +
            "                 \n" +
            "                 3 Hours\n" +
            "                 \n" +
            "                 Course is exempt from taxes\n" +
            "                 \n" +
            "             13 Weeks</SSR_CRSE_ATTR_LONG>\n" +
            "             <SSR_CRSE_TYPOFF_CD></SSR_CRSE_TYPOFF_CD>\n" +
            "             <SSR_DATE_LONG>6/25/2018 - 8/18/2018</SSR_DATE_LONG>\n" +
            "             <SSR_DROP_CONSENT>N</SSR_DROP_CONSENT>\n" +
            "             <SSR_DROP_CONSENT_LOVDescr>No Special Consent Required</SSR_DROP_CONSENT_LOVDescr>\n" +
            "             <SSR_GBL_NOTE_LONG></SSR_GBL_NOTE_LONG>\n" +
            "             <SSR_REQUISITE_LONG></SSR_REQUISITE_LONG>\n" +
            "             <START_DT>2018-06-25</START_DT>\n" +
            "             <STRM>1185</STRM>\n" +
            "             <STRM_LOVDescr>Spring/Summer 2018</STRM_LOVDescr>\n" +
            "             <SUBJECT>CIND</SUBJECT>\n" +
            "             <SUBJECT_LOVDescr>Industrial Eng.</SUBJECT_LOVDescr>\n" +
            "             <UNITS_RANGE>1 units</UNITS_RANGE>\n" +
            "             <WAIT_TOT>0</WAIT_TOT>\n" +
            "             <WAIT_CAP>0</WAIT_CAP>\n" +
            "             <COURSE_TITLE_LONG>Data Organization for Data Analysts</COURSE_TITLE_LONG>\n" +
            "             <STUDENT_LANGUAGE>NULL</STUDENT_LANGUAGE>\n" +
            "             <SHIFT>Array</SHIFT>\n" +
            "             <UK_LEVEL>NULL</UK_LEVEL>\n" +
            "             <STUDENT_GROUPING>NULL</STUDENT_GROUPING>\n" +
            "             <PLACEMENT_COURSE>Array</PLACEMENT_COURSE>\n" +
            "             <NBR_CAMPUS_OFFERED>Array</NBR_CAMPUS_OFFERED>\n" +
            "             <DATE_LAST_VALIDATED>Array</DATE_LAST_VALIDATED>\n" +
            "             <LATE_CLASSES>Array</LATE_CLASSES>\n" +
            "             <CLASS_COMBINED_SECTION></CLASS_COMBINED_SECTION>\n" +
            "             <CLASS_COMPONENTS>\n" +
            "                 <CLASS_COMPONENT>\n" +
            "                     <SSR_COMPONENT>LEC</SSR_COMPONENT>\n" +
            "                     <SSR_COMPONENT_LOVDescr>Lecture</SSR_COMPONENT_LOVDescr>\n" +
            "                     <OPTIONAL_SECTION>N</OPTIONAL_SECTION>\n" +
            "                     <OPTIONAL_SECTION_LOVDescr>Required</OPTIONAL_SECTION_LOVDescr>\n" +
            "                     <CLASS_COMPONENT_SECTIONS></CLASS_COMPONENT_SECTIONS>\n" +
            "                 </CLASS_COMPONENT>\n" +
            "             </CLASS_COMPONENTS>\n" +
            "             <CLASS_MEETING_PATTERNS>\n" +
            "                 <CLASS_MEETING_PATTERN>\n" +
            "                     <CRSE_ID>025665</CRSE_ID>\n" +
            "                     <CRSE_OFFER_NBR>2</CRSE_OFFER_NBR>\n" +
            "                     <STRM>1185</STRM>\n" +
            "                     <SESSION_CODE>3RS</SESSION_CODE>\n" +
            "                     <SESSION_CODE_LOVDescr>Summer - Regular</SESSION_CODE_LOVDescr>\n" +
            "                     <CLASS_SECTION>FB0</CLASS_SECTION>\n" +
            "                     <CLASS_MTG_NBR>1</CLASS_MTG_NBR>\n" +
            "                     <FACILITY_ID></FACILITY_ID>\n" +
            "                     <FACILITY_ID_LOVDescr></FACILITY_ID_LOVDescr>\n" +
            "                     <MEETING_TIME_START>18:00:00.000000</MEETING_TIME_START>\n" +
            "                     <MEETING_TIME_END>21:00:00.000000</MEETING_TIME_END>\n" +
            "                     <MON>N</MON>\n" +
            "                     <TUES>Y</TUES>\n" +
            "                     <WED>N</WED>\n" +
            "                     <THURS>Y</THURS>\n" +
            "                     <FRI>N</FRI>\n" +
            "                     <SAT>N</SAT>\n" +
            "                     <SUN>N</SUN>\n" +
            "                     <START_DT>2018-06-25</START_DT>\n" +
            "                     <END_DT>2018-08-18</END_DT>\n" +
            "                     <CRS_TOPIC_ID>0</CRS_TOPIC_ID>\n" +
            "                     <CRS_TOPIC_ID_LOVDescr></CRS_TOPIC_ID_LOVDescr>\n" +
            "                     <DESCR></DESCR>\n" +
            "                     <STND_MTG_PAT>TR</STND_MTG_PAT>\n" +
            "                     <BLDG_CD></BLDG_CD>\n" +
            "                     <SCC_LATITUDE>0</SCC_LATITUDE>\n" +
            "                     <SCC_LONGITUDE>0</SCC_LONGITUDE>\n" +
            "                     <SSR_MTG_SCHED_LONG>TuTh 6:00PM - 9:00PM</SSR_MTG_SCHED_LONG>\n" +
            "                     <SSR_MTG_LOC_LONG>TBA</SSR_MTG_LOC_LONG>\n" +
            "                     <SSR_INSTR_LONG>Staff</SSR_INSTR_LONG>\n" +
            "                     <SSR_MTG_DT_LONG>2018-06-25 - 2018-08-18</SSR_MTG_DT_LONG>\n" +
            "                     <SSR_TOPIC_LONG>TBA</SSR_TOPIC_LONG>\n" +
            "                     <CLASS_INSTRUCTORS></CLASS_INSTRUCTORS>\n" +
            "                 </CLASS_MEETING_PATTERN>\n" +
            "             </CLASS_MEETING_PATTERNS>\n" +
            "             <CLASS_TEXT_BOOKS>\n" +
            "                 <CLASS_TEXT_BOOK>\n" +
            "                     <SSR_CLS_TXB_NONE></SSR_CLS_TXB_NONE>\n" +
            "                     <SSR_CLS_TXB_STATUS></SSR_CLS_TXB_STATUS>\n" +
            "                     <SSR_CLS_TXB_STATUS_LOVDescr></SSR_CLS_TXB_STATUS_LOVDescr>\n" +
            "                     <SSR_CLS_TXB_TEXT></SSR_CLS_TXB_TEXT>\n" +
            "                     <TEXTBOOK_MESSAGE>Textbooks to be determined</TEXTBOOK_MESSAGE>\n" +
            "                     <SSR_CLS_TXB_NONE_DESCR></SSR_CLS_TXB_NONE_DESCR>\n" +
            "                     <TEXT_BOOKS_DETAILS></TEXT_BOOKS_DETAILS>\n" +
            "                 </CLASS_TEXT_BOOK>\n" +
            "             </CLASS_TEXT_BOOKS>\n" +
            "             <ENROLLMENT_DETAILS>\n" +
            "                 <ENROLLMENT_INFORMATION>\n" +
            "                     <ADD_CONSENT></ADD_CONSENT>\n" +
            "                     <ADD_CONSENT_LOVDescr></ADD_CONSENT_LOVDescr>\n" +
            "                     <DROP_CONSENT></DROP_CONSENT>\n" +
            "                     <DROP_CONSENT_LOVDescr></DROP_CONSENT_LOVDescr>\n" +
            "                     <ENROLLMENT_REQUIREMENTS></ENROLLMENT_REQUIREMENTS>\n" +
            "                     <REQUIREMENT_DESIGNATION></REQUIREMENT_DESIGNATION>\n" +
            "                     <CLASS_ATTRIBUTES>Certificate and degree credit39 HoursCourse is exempt from taxes13 Weeks</CLASS_ATTRIBUTES>\n" +
            "                 </ENROLLMENT_INFORMATION>\n" +
            "             </ENROLLMENT_DETAILS>\n" +
            "         </SSR_CLASS_SECTION>\n" +
            "     </CLASS_SECTIONS>\n" +
            "     </root>"


    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "SCC_SC_ADDITEM_RESP"() {
                "detail"() {
                    "MSGS"() {
                        GPathResult items = extractMessageFromRequest(request)
                        if (items.isEmpty()) {
                            "MSG"()
                        }
                        items.each { item ->
                            "MSG"() {
                                mkp.yield item.'*'
                            }
                        }
                    }
                }
            }
        }
        return new XmlSlurper().parseText(xml.toString()) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return hasErrorCode(respRoot, ["ERR2.5"])
    }

    def boolean isClientError(NodeChild respRoot) {
        NodeChildren msgNodes = respRoot['detail']['MSGS']['MSG']['*'] as NodeChildren
        return msgNodes.size() <= 0 || hasErrorCode(respRoot, ['ERR2.4'])
    }

    def boolean isValidationError(NodeChild respRoot) {
        return hasErrorCode(respRoot, ['ERR2.1', 'ERR2.2', 'ERR2.3', 'ERR2.X'])
    }

    def void getServerErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR2.5001"], 500)
    }

    def void getClientErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR2.4001"], 400)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getValidationErrorContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def respRoot = exchange.message.getBody(NodeChild.class)
        respRoot.detail.MSGS.MSG.replaceNode { node ->
            String strm = (extractNodeText node.STRM as GPathResult) ?: null
            String classNbr = (extractNodeText node.CLASS_NBR as GPathResult) ?: null
            String errorCode = getErrorCode(node)
            String errorField = getErrorField(node)
            "MSG"() {
                "ID"(classNbr + "-" + strm)
                "DESCR"(errorCode ? msgDescErrorMap[errorCode] + " [" + errorCode + "]" : msgDescSuccess)
                "MESSAGE_SEVERITY"(errorCode ? 'E' : 'I')
                "PROPS"() {
                    "PROP"() {
                        "SCC_ENTITY_INST_ID"() {
                            mkp.yieldUnescaped node.SCC_ENTITY_INST_ID
                        }
                        "PROPNAME"(errorField && validationErrorCodes.indexOf(errorField) > 0 ? errorCodeToFieldMap[errorField] : null)
                    }
                }
            }
        }
        respRoot.replaceNode {
            "SCC_SC_ADDITEM_RESP"() {
                "SCC_FAULT_RESP" {
                    mkp.yield respRoot.detail
                }
                "IS_FAULT"("Y")
                "ORIGINAL_MOCK"(originalReq)
            }
        }
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def respRoot = exchange.message.getBody(NodeChild.class)
        def respMetaXML = new XmlSlurper().parseText(SUCCESS_RESP_ITEM_META)
        respRoot.detail.MSGS.MSG.replaceNode { node ->
            "ITEM" {
                mkp.yield node.'*'
                mkp.yield respMetaXML.'*'
            }
        }
        respRoot.detail.replaceNode {
            "ITEMS" {
                mkp.yield respRoot.detail.MSGS.MSG
            }
        }
        respRoot.replaceNode {
            "SCC_SC_ADDITEM_RESP"() {
                "SHOPPING_CART"() {
                    "COURSE_SHOP_CART"() {
                        mkp.yield respRoot.detail
                    }
                }
                "ORIGINAL_MOCK"(originalReq)
            }
        }
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return xml.toString()
    }

    protected String getErrorField(NodeChild msgNode) {
        String strm = extractNodeText msgNode['STRM'] as GPathResult
        String classNbr = extractNodeText msgNode['CLASS_NBR'] as GPathResult
        if (!strm) {
            return "ERR2.1002"
        } else if (!classNbr) {
            return "ERR2.2001"
        } else if (classNbr.startsWith("ERR2.4") || classNbr.startsWith('ERR2.5')) {
            return classNbr
        } else if (strm.startsWith('ERR2.1')) {
            return strm
        } else if ('ERR2.2001' == classNbr) {
            return classNbr
        } else if (classNbr.startsWith("ERR2.3") && classNbr == strm) {
            return classNbr
        } else {
            return null
        }
    }

    protected String makeErrorCode(String errField) {
        return errField.startsWith("ERR2.") && errField.length() > 8 && errField.substring(0, 9) in msgDescErrorMap.keySet() ?
                errField.substring(0, 9) : "ERR2.XXXX"
    }

    private GPathResult extractMessageFromRequest(String originalReq) {
        def rootNode = new XmlSlurper().parseText(originalReq) as NodeChild
        return rootNode['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'] as GPathResult
    }
}
