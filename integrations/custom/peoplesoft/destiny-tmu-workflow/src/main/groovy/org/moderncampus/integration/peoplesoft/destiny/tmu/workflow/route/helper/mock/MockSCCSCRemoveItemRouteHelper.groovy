package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockSCCSCRemoveItemRouteHelper {

    def static final Map<String, String> msgDescErrorMap = [
            "ERR.5001": "Internal Server Error (Mock)",
            "ERR.4001": "Bad Request (Mock)"
    ]

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(Exchange exchange) {
        def builder = new StreamingMarkupBuilder()
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        def request = exchange.message.getBody(String.class)
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "SCC_SC_REMOVEITEM_RESP"() {
                "SHOPPING_CART"() {
                    "COURSE_SHOP_CART"() {
                        "ITEMS"() {
                            GPathResult items = extractCartContents(request)
                            if (items.isEmpty()) {
                                "ITEM"()
                            }
                            items.each { item ->
                                "ITEM"() {
                                    mkp.yield item.'*'
                                }
                            }
                        }
                        "SCC_SHOP_CART_TYPE"(extractNodeText(extractShopCartType(request)))
                    }
                }
                "ORIGINAL_MOCK"(originalReq)
            }
        }
        return new XmlSlurper().parseText(xml.toString()) as NodeChild
    }

    def boolean isServerError(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith('RI_ERR500')
    }

    def boolean isClientError(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith('RI_ERR400')
    }

    def boolean isActionFailure(NodeChild respRoot) {
        return getClassNbr(respRoot).startsWith('RI_ERR700')
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getActionFailureContent(NodeChild respRoot) throws Exception {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(NodeChild respRoot) throws Exception {
        respRoot['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'].replaceNode {
            "ITEM"()
        }
        respRoot['SHOPPING_CART']['COURSE_SHOP_CART']['SCC_SHOP_CART_TYPE'].replaceNode {}
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.yield respRoot
        }
        return xml.toString()
    }

    def void getServerErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR.5001"], 500)
    }

    def void getClientErrorContent() throws Exception {
        throw new ApplicationException(msgDescErrorMap["ERR.4001"], 400)
    }

    private String getClassNbr(NodeChild respRoot) {
        return extractNodeText((respRoot['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'] as NodeChildren)[0]['CLASS_NBR'] as NodeChildren)
    }

    private GPathResult extractCartContents(String originalReq) {
        def rootNode = new XmlSlurper().parseText(originalReq) as NodeChild
        return rootNode['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'] as GPathResult
    }

    private GPathResult extractShopCartType(String originalReq) {
        def rootNode = new XmlSlurper().parseText(originalReq) as NodeChild
        return rootNode['SHOPPING_CART']['COURSE_SHOP_CART']['SCC_SHOP_CART_TYPE'] as GPathResult
    }
}
