package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper


import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.helper.GroovyXMLSupport.extractTrimmedText

@Component
@CompileStatic
class StudentLookupRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftStudentRouteHelper, D1TMUPeopleSoftRouteHelper {

    static final DateTimeFormatter TMU_D1_FORMAT_BIRTH_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    static final DateTimeFormatter TMU_D1_FORMAT_DATE_OF_ENTRY = DateTimeFormatter.ofPattern("yyyy/MM/dd")
    static final String PARSED_ORIGINAL_REQ = "parsedOriginal"

    String mapInboundRequest(Exchange exchange) {
        NodeChild request = parseXMLBody(exchange.message.getBody(String.class))
        exchange.setProperty(PARSED_ORIGINAL_REQ, request)
        return extractBirthDate(request)
    }

    String extractBirthDate(NodeChild integrationMessage) {
        LocalDate birthDate = extractD1Date(extractNodeText(integrationMessage['body']['student']['birthDate'] as NodeChildren))
        return birthDate ? TMU_D1_FORMAT_BIRTH_DATE.format(birthDate) : null
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMUCreateWebIdRequest(Exchange exchange) {
        NodeChild original = exchange.getProperty(PARSED_ORIGINAL_REQ)
        NodeChildren studentReq = extractStudent(original)
        String birthDate = exchange.message.getBody(String.class)
        NodeChildren udfValueNodes = studentReq['udfValues']['udfValue']
        NodeChildren addressNodes = studentReq['addresses']['address']
        NodeChildren telephoneNodes = studentReq['telephones']['telephone']
        NodeChildren emailNodes = studentReq['emails']['email']
        GPathResult preferredAddress = getPreferredAddressNode(addressNodes)
        GPathResult preferredEmail = getPreferredEmailNode(emailNodes)
        GPathResult homeTelephone = getTelephoneNode(telephoneNodes, 'Home')
        String residency = getUdfFieldValue(udfValueNodes, "citizenshipStatus")
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_CREATEWEBID_REQUEST"() {
                "RU_PREV_ATT"(mapYN(getUdfFieldValue(udfValueNodes, "attendanceQuestion")))
                "RU_PREV_YR"(getUdfFieldValue(udfValueNodes, "attendanceYear"))
                "TITLE"(extractNodeText(studentReq['salutationCode']))
                "FIRST_NAME"(extractTrimmedText(extractNodeText(studentReq['firstName1']), 30))
                "MIDDLE_NAME"(extractTrimmedText(extractNodeText(studentReq['firstName2']), 30))
                "LAST_NAME"(extractTrimmedText(extractNodeText(studentReq['lastName']), 30))
                "FIRST_NAME_CD"()
                "LAST_NAME_CD"(extractNodeText(studentReq['otherNames']))
                "ADDRESS1"(extractTrimmedText(extractNodeText(preferredAddress['street1']), 55))
                "ADDRESS2"(extractTrimmedText(extractNodeText(preferredAddress['street2']), 55))
                "CITY"(extractTrimmedText(extractNodeText(preferredAddress['city']), 30))
                "PROVINCE"('Canada' == extractNodeText(preferredAddress['country']) ? extractNodeText(preferredAddress['provinceState']) : "")
                "HOMECOUNTRY"(extractNodeText(studentReq['externalSystemData']))
                "POSTAL"(formatZip(extractNodeText(preferredAddress['postalZip'])))
                "HOME_PHONE"(getTelephone(telephoneNodes, 'Home'))
                "BUS_PHONE"(getTelephone(telephoneNodes, 'Office'))
                "EMAIL_ADDR"(extractNodeText(preferredEmail['emailAddress']))
                "ACCEPT_PB"('Y')
                "MAR_STATUS"()
                "LANG_CD"()
                "SEX"(getUdfFieldValue(udfValueNodes, "studentGender", 'U'))
                "BIRTHDATE"(birthDate)
                "RESIDENCY"(residency)
                "ENTRY_DATE"((CANADIAN_CITIZEN == residency || NOT_REPORTED == residency) ? '' : extractDateOfEntry(udfValueNodes,
                        TMU_D1_FORMAT_BIRTH_DATE))
                "COUNTRY"('Canada' == extractNodeText(preferredAddress['country']) ? (CANADIAN_CITIZEN == residency ? '' :
                        getUdfFieldValue(udfValueNodes, "countryOfCitizenship")) : 'XYZ')
                "IP"('NOIP')
                "EXTENSION"(extractNodeText(homeTelephone['telephoneExt']))
                "RU_OEN_NBR"(getUdfFieldValue(udfValueNodes, "oenNumber"))
                "OPTION"('REG')
                "ACAD_PROG"(getUdfFieldValue(udfValueNodes, "certificate", 'NOCER'))
                "RU_CERT_APPR_CD"(getUdfFieldValue(udfValueNodes, "certificateApprovalCode", '0'))
                "STUDENTID_VERIFY"(getUdfFieldValue(udfValueNodes, "existingStudentNumber"))
                "RU_DESTINY_ID"(extractNodeText(studentReq['studentNumber']))
            }
        }
        return xml.toString()
    }

    private GPathResult getPreferredAddressNode(NodeChildren addressNodes) {
        return addressNodes.find { it ->
            "true" == extractNodeText(it['preferred'] as NodeChildren)
        }
    }

    private String formatZip(String zip) {
        return zip?.replaceFirst("\\s", "")
    }

    private GPathResult getTelephoneNode(NodeChildren telephoneNodes, String typeCode) {
        return telephoneNodes.find { it ->
            extractNodeText(it['typeCode'] as NodeChildren) == typeCode
        }
    }

    private String getTelephone(NodeChildren telephoneNodes, String typeCode) {
        GPathResult telephoneNode = getTelephoneNode(telephoneNodes, typeCode)
        return telephoneNode.isEmpty() ? '' : "(" + extractNodeText(telephoneNode['areaCode'] as GPathResult) + ")" +
                extractNodeText(telephoneNode['telephoneNumber'] as GPathResult)
    }

    private GPathResult getPreferredEmailNode(NodeChildren emailNodes) {
        return emailNodes.find { it ->
            "true" == extractNodeText(it['preferred'] as NodeChildren)
        }
    }

    private String mapYN(String value) {
        return "Yes" == value ? "Y" : "N"
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response, true)
        String studentId = extractNodeText(response['STUDENTID'])
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault ? '' : mkp.yieldUnescaped(getEntityContent(studentId))
            }
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(String studentId) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "student"() {
                "schoolPersonnelNumber"(studentId)
            }
        }
        return xml.toString()
    }
}
