package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum DestinyTMUPSRouteIds implements IRouteId {

    V1_D1_TMU_PEOPLESOFT_STUDENT_TOKEN_RETRIEVE(Constants.STUDENT_TOKEN_RETRIEVE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_VALIDATE(Constants.STUDENT_ENROLLMENT_VALIDATE),

    V1_D1_TMU_PEOPLESOFT_SHOPPING_CART_ITEM_REMOVE(Constants.SHOPPING_CART_ITEM_REMOVE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_CREATE(Constants.STUDENT_ENROLLMENT_CREATE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_DROP(Constants.STUDENT_ENROLLMENT_DROP),

    V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_RETRIEVE(Constants.SECTION_AVAILABILITY_RETRIEVE),

    V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_VALIDATE(Constants.SECTION_AVAILABILITY_VALIDATE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_ACCOUNT_SETTLEMENT(Constants.STUDENT_ACCOUNT_SETTLEMENT),

    V1_D1_TMU_PEOPLESOFT_STUDENT_PROFILE_RETRIEVE(Constants.STUDENT_PROFILE_RETRIEVE),

    V1_D1_TMU_PEOPLESOFT_SECTION_CAPACITY_RESERVE(Constants.SECTION_CAPACITY_RESERVE),

    V1_D1_TMU_PEOPLESOFT_SECTION_FEE_RETRIEVE(Constants.SECTION_FEE_RETRIEVE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_LOOKUP(Constants.STUDENT_LOOKUP),

    V1_D1_TMU_PEOPLESOFT_STUDENT_RESIDENCY_NOTIFY(Constants.STUDENT_RESIDENCY_NOTIFY),

    V1_D1_TMU_PEOPLESOFT_STUDENT_CERTIFICATE_ENROLL(Constants.STUDENT_CERTIFICATE_ENROLL),

    V1_D1_TMU_PEOPLESOFT_STUDENT_STATUS_VALIDATE(Constants.STUDENT_STATUS_VALIDATE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_RETRIEVE(Constants.STUDENT_CHOICE_RETRIEVE),

    V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_UPDATE(Constants.STUDENT_CHOICE_UPDATE),

    V1_D1_TMU_PEOPLESOFT_MOCK_BLUEPAY_INFO(Constants.MOCK_BLUEPAY_INFO);

    String contextPath;

    String id;

    DestinyTMUPSRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(
                new String[]{VERSION_1, DESTINY_SYSTEM_ID,
                        org.moderncampus.integration.constants.Constants.SCHOOL_ID_TMU, PEOPLESOFT_SYSTEM_ID,
                        contextPath});
    }
}
