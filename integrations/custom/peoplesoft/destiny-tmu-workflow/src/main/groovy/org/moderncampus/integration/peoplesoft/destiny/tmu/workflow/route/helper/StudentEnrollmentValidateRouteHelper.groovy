package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component("d1TMUPSEnrollmentValidateRouteHelper")
@CompileStatic
class StudentEnrollmentValidateRouteHelper implements D1TMUPeopleSoftBasketRouteHelper {

    static final String ACTION_RESULT_TYPE_VALIDATE_ENROLLMENT = "VALIDATE"
    static final String ENTITY_COURSE_SECTION_LW = "courseSectionLW"
    protected static final String PARSED_ORIGINAL_REQ = "parsedOriginal"
    protected static final String COURSE_SHOP_CART_TYPE = "COURSE"

    public void mapInboundRequest(Exchange exchange) {
        String originalReq = exchange.message.getBody(String.class)
        def rootNode = parseXMLBody(originalReq)
        exchange.setProperty(PARSED_ORIGINAL_REQ, rootNode)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateSSCSCAddItemRequest(Exchange exchange) {
        def rootNode = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "SCC_SC_ADDITEM_REQ"() {
                "SHOPPING_CART"() {
                    "COURSE_SHOP_CART"() {
                        "SCC_SHOP_CART_TYPE"(COURSE_SHOP_CART_TYPE)
                        buildAddItemsRequest(builder, rootNode)
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private void buildAddItemsRequest(def builder, NodeChild inboundRequest) {
        builder."ITEMS"() {
            def filteredEvents = extractEnrollmentEventsFromRequest(inboundRequest)
            if (filteredEvents.size() == 0) {
                "ITEM"()
            } else {
                filteredEvents.each { node ->
                    "ITEM"() {
                        "SCC_ENTITY_INST_ID"(extractEmplId(node) + ":" + extractClassNbr(node))
                        "EMPLID"(extractEmplId(node))
                        "INSTITUTION"("RYERU")
                        "ACAD_CAREER"("CNED")
                        "STRM"(extractStrm(node))
                        "CLASS_NBR"(extractClassNbr(node))
                        "WAIT_LIST_OKAY"("N")
                        "CLASS_PRMSN_NBR"()
                        "GRADING_BASIS_ENRL"("GRD")
                        "UNT_TAKEN"()
                        "INSTRUCTOR_ID"()
                        "RQMNT_DESIGNTN_OPT"()
                        "START_DT"()
                        "CRSE_COUNT"()
                        "ACAD_PROG"()
                        "RELATE_CLASS_NBR_1"()
                        "RELATE_CLASS_NBR_2"()
                    }
                }
            }
        }
    }

    protected void validateSCCSCAddItemResponse(Exchange exchange) {
        def originalReq = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        def addItemResponse = exchange.message.getBody(NodeChild.class)
        GPathResult originalEnrollEvents = extractEnrollmentEventsFromRequest(originalReq)
        originalEnrollEvents.eachWithIndex { event, index ->
            int idx = -1
            def originalStrm = extractStrm(event as NodeChild)
            def originalClassNbr = extractClassNbr(event as NodeChild)
            addItemResponse['SHOPPING_CART']['COURSE_SHOP_CART']['ITEMS']['ITEM'].each { item ->
                String requestTerm = extractNodeText(item['STRM'] as GPathResult)
                String requestClassNbr = extractNodeText(item['CLASS_NBR'] as GPathResult)
                if (originalStrm == requestTerm &&
                        originalClassNbr == requestClassNbr) {
                    idx = index
                }
            }
            if (idx < 0) {
                throw new ApplicationException("SCC_SC_ADDITEM Response missing item for Enrollment Event with Term: " + originalStrm +
                        " and Class Nbr: " + originalClassNbr)
            }
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateSSREnrValidateRequest(Exchange exchange) {
        def originalReq = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "SSR_ENR_VALIDATE_REQ"() {
                "SHOPPING_CART"() {
                    "COURSE_SHOP_CART"() {
                        "SCC_SHOP_CART_TYPE"(COURSE_SHOP_CART_TYPE)
                        buildValidateItemsRequest(builder, originalReq)
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private void buildValidateItemsRequest(def builder, NodeChild inboundRequest) {
        builder."ITEMS"() {
            def filteredEvents = extractEnrollmentEventsFromRequest(inboundRequest)
            if (filteredEvents.size() == 0) {
                "ITEM"()
            } else {
                filteredEvents.each { node ->
                    "ITEM"() {
                        "SCC_ENTITY_INST_ID"(extractClassNbr(node) + ":" + extractEmplId(node))
                        "EMPLID"(extractEmplId(node))
                        "INSTITUTION"("RYERU")
                        "ACAD_CAREER"("CNED")
                        "STRM"(extractStrm(node))
                        "CLASS_NBR"(extractClassNbr(node))
                        "WAIT_LIST_OKAY"("N")
                        "CLASS_PRMSN_NBR"("0")
                        "GRADING_BASIS_ENRL"()
                        "UNT_TAKEN"("0")
                        "INSTRUCTOR_ID"()
                        "RQMNT_DESIGNTN_OPT"()
                        "START_DT"()
                        "CRSE_COUNT"("0")
                        "ACAD_PROG"()
                        "RELATE_CLASS_NBR_1"("0")
                        "RELATE_CLASS_NBR_2"("0")
                        "SSR_VALIDATIN_MODE"("N")
                    }
                }
            }
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        boolean isFault = isFaultResponse(resp)
        NodeChildren respDetail = isFault ? resp['SCC_FAULT_RESP'].detail.MSGS.MSG :
                resp.detail.MSGS.MSG
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            mkp.yieldUnescaped mapInnerResult(respDetail)
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChildren response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            response.each { msgNode ->
                {
                    def messageNode = msgNode as NodeChild
                    def msgSeverityTxt = extractNodeText msgNode['MESSAGE_SEVERITY'] as GPathResult
                    def isMsgPropExist = msgNode['PROPS']['PROP'].isEmpty()
                    def sccEntityInstId = isMsgPropExist ? extractNodeText(msgNode['PROPS']['PROP'][0]['SCC_ENTITY_INST_ID']) : ""
                    "result"() {
                        "status"(msgSeverityTxt == null ? STATUS_CODE_NO_RESULT_FOUND : (msgSeverityTxt != "E" ? STATUS_CODE_SUCCESS :
                                STATUS_CODE_ACTION_CANNOT_PROCEED))
                        "entity"(sccEntityInstId)
                        "entityType"(ENTITY_COURSE_SECTION + ":" + ENTITY_STUDENT)
                        "entityInXMLFormat"(false)
                        "errorMsg"() {
                            msgNode['DESCR'].isEmpty() ? '' : mkp.yieldUnescaped(extractNodeText(msgNode['DESCR']))
                        }
                    }
                }
            }
        }
        return xml.toString()
    }
}
