package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder;

import static org.moderncampus.integration.route.support.RouteSupport.buildDirectRouteURI;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.ChoiceDefinition;
import org.apache.camel.model.RouteDefinition;

public abstract class D1TMUPeopleSoftMockRouteBuilder extends RouteBuilder {

    static final String METHOD_MAP_REQUEST = "mapRequest";

    String id;

    Class<?> helperClass;

    public D1TMUPeopleSoftMockRouteBuilder(String id, Class<?> helperClass) {
        this.id = id;
        this.helperClass = helperClass;
    }

    @Override
    public void configure() throws Exception {
        String routeUri = buildDirectRouteURI(id);
        RouteDefinition routeDefinition = from(routeUri).routeId(id);
        routeDefinition.bean(helperClass, METHOD_MAP_REQUEST);
        ChoiceDefinition choiceDefinition = routeDefinition.choice();
        buildMockRoute(choiceDefinition);
        choiceDefinition.end();
    }

    protected abstract void buildMockRoute(ChoiceDefinition choiceDefinition);
}
