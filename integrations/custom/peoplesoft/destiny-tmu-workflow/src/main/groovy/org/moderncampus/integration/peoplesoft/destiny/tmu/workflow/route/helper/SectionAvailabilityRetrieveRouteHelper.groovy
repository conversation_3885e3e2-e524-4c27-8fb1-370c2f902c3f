package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class SectionAvailabilityRetrieveRouteHelper implements D1TMUPeopleSoftRouteHelper, D1TMUPeopleSoftSectionRouteHelper {

    static final String AVAILABILITY_CODE_AVAILABLE = "available"
    static final String AVAILABILITY_CODE_FULL = "full"
    static final String AVAILABILITY_CODE_CANCELLED = "canceled"
    static final String AVAILABILITY_CODE_CLOSED = "enrollment_closed"

    public NodeChildren mapInboundRequest(String body) {
        return extractCourseSectionProfileFromRequest(parseXMLBody(body))
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateSSRGetClassSectionRequest(Exchange exchange) {
        def sectionProfile = exchange.message.getBody(NodeChildren.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "SSR_GET_CLASS_SECTION_REQ"() {
                "CLASS_SECTION_REQUEST"() {
                    "CRSE_ID"()
                    "CRSE_OFFER_NBR"()
                    "STRM"(extractStrm(sectionProfile))
                    "SESSION_CODE"()
                    "CLASS_SECTION"()
                    "SSR_SEARCH_MODE"('N')
                    "CLASS_NBR"(extractClassNbr(sectionProfile))
                    "ACAD_CAREER"()
                    "ACAD_PROG"()
                }
            }
        }
        return xml.toString()
    }

    boolean isErrorResponse(NodeChild responseNode) {
        NodeChildren classSectionsNode = responseNode['CLASS_SECTION_RESULT']['CLASS_SECTIONS']['*'] as NodeChildren
        return classSectionsNode.isEmpty()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        boolean isFault = isFaultResponse(resp, true)
        boolean isError = isErrorResponse(resp)
        def respDetail = isFault ? resp : resp['CLASS_SECTION_RESULT']['CLASS_SECTIONS']['SSR_CLASS_SECTION']
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            mkp.yieldUnescaped mapInnerResult(respDetail, isFault, isError)
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(def response, boolean isFault, boolean isError) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "result"() {
                "status"(isFault ? STATUS_CODE_UNKNOWN_ERROR : (isError ? STATUS_CODE_NO_RESULT_FOUND : STATUS_CODE_SUCCESS))
                "entity"()
                "entityType"()
                "entityInXMLFormat"(false)
                "errorMsg"() {
                    isFault ? mkp.yieldUnescaped(getFaultMsgs(response as NodeChild)) : (isError ? mkp.yieldUnescaped('No section found') : '')
                }
                "detail"() {
                    (isError || isFault) ? '' : mkp.yieldUnescaped(getSectionAvailability(response[0] as NodeChild))
                }
            }
        }
        return xml.toString()
    }

    def String getSectionAvailability(NodeChild sectionNode) {
        return isSectionOpen(sectionNode) ? AVAILABILITY_CODE_AVAILABLE : (isSectionFull(sectionNode) ? AVAILABILITY_CODE_FULL :
                AVAILABILITY_CODE_CANCELLED)
    }

    def boolean isSectionOpen(NodeChild sectionNode) {
        return 'O' == extractNodeText(sectionNode['ENRL_STAT'] as GPathResult)
    }

    def boolean isSectionFull(NodeChild sectionNode) {
        Integer enrlCap = extractNodeText(sectionNode['ENRL_CAP'] as GPathResult) as Integer
        Integer enrlTot = extractNodeText(sectionNode['ENRL_TOT'] as GPathResult) as Integer
        return enrlCap != 0 && enrlTot >= enrlCap
    }

    def String getFaultMsgs(NodeChild resp) {
        return D1TMUPeopleSoftRouteHelper.super.getFaultMsgs(resp,
                resp['SCC_FAULT_RESP']['detail']['MSGS']['MSG'] as NodeChildren)
    }
}
