package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentCertificateEnrollRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftStudentRouteHelper, D1TMUPeopleSoftRouteHelper {

    static final String PARSED_ORIGINAL_REQ = "parsedOriginal"
    static final DateTimeFormatter TMU_D1_FORMAT_DATE_OF_ENTRY = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    String mapInboundRequest(Exchange exchange) {
        def body = exchange.message.getBody(String.class)
        NodeChild request = parseXMLBody(body)
        exchange.setProperty(PARSED_ORIGINAL_REQ, request)
        validateRequest(body, request)
        return extractSchoolPersonnelNumber(request)
    }

    def void validateRequest(String originalReq, NodeChild parsedRequest) {
        def student = extractStudent(parsedRequest)
        if (student.isEmpty() || student.size() > 1) {
            def invalidRequestException = [error     : "Invalid Request",
                                           reason    : "Request must contain exactly 1 student.",
                                           original  : originalReq,
                                           resolution: "Modify request to contain exactly 1 student."]
            throw new ApplicationException(JsonOutput.toJson(invalidRequestException), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMURegAndTranRequest(Exchange exchange) {
        NodeChild original = exchange.getProperty(PARSED_ORIGINAL_REQ)
        NodeChildren studentReq = extractStudent(original)
        String studentId = exchange.message.getBody(String.class)
        NodeChildren udfValueNodes = studentReq['udfValues']['udfValue']
        String residency = getUdfFieldValue(udfValueNodes, "citizenshipStatus")
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_REGANDTRAN_REQUEST"() {
                "STUDENTID"(studentId)
                "OPTION"("REG")
                "ACAD_PROG"(getUdfFieldValue(udfValueNodes, "certificate", "NOCER"))
                "RU_CERT_APPR_CD"(getUdfFieldValue(udfValueNodes, "certificateApprovalCode", "0"))
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response)
        String studentId = extractNodeText(response['STUDENTID'])
        String result = isFault ? "" : extractNodeText(response['RESULT'])
        String intStatus = (isFault || "False" == result) ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS
        String message = extractNodeText(response['MESSAGE'])
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(intStatus)
            "entity"(studentId)
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(false)
            "errorMsg"(STATUS_CODE_SUCCESS == intStatus ? "" : message)
        }
        return xml.toString()
    }
}
