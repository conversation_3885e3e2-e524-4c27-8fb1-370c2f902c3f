package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@CompileStatic
@Component
class MockTMUD1BluePayInfoRouteHelper {

    @CompileStatic(TypeCheckingMode.SKIP)
    def NodeChild mapRequest(String request) {
        return new XmlSlurper().parseText(request) as NodeChild
    }

    private String extractTerm(NodeChild resp) {
        return extractNodeText(resp['STRM'] as GP<PERSON><PERSON><PERSON><PERSON>, () -> '')
    }

    private String extractStudentId(NodeChild resp) {
        return extractNodeText(resp['STUDENTID'] as GPathResult)
    }

    private String extractTranType(NodeChild resp) {
        return extractNodeText(resp['RU_TRAN_TYPE'] as GPathResult, () -> '')
    }

    def boolean isFault(NodeChild resp) {
        String term = extractTerm(resp)
        String tranType = extractTranType(resp)
        return (term.endsWith("XX1") || term.endsWith("XX2") || term.endsWith("XX3") || term.endsWith("XX4") || term.endsWith("XX5")
                || term.endsWith("XX6") || term.endsWith("XX7") || term.endsWith("XX8") || term.endsWith("XX9") || term.endsWith("XX10")
                || term.endsWith("XX11") || (term.endsWith("XX12") && 'C' == tranType) || (term.endsWith("XX13") && 'W' == tranType))
    }

    def boolean isServerError(NodeChild resp) {
        String term = extractTerm(resp)
        return term.endsWith("401")
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException("", 401)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getFaultContent(NodeChild resp) throws Exception {
        String term = extractTerm(resp)
        String tranType = extractTranType(resp)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "RU_D1_BLUEPAY_INFO_FAULT"() {
                "IS_FAULT"("Y")
                "FAULT"() {
                    "MSGS"() {
                        "MSG"() {
                            if (term.endsWith('XX2')) {
                                "ID"("1007")
                                "DESCR"("RU_TRAN_TYPE can be S,P,W or C")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX3')) {
                                "ID"("1014")
                                "DESCR"("RU_REF_NUM already exists with TRANS TYPE C or W")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX4')) {
                                "ID"("1004")
                                "DESCR"("Both STUDENTID and RU_DESTINY_ID can not be blank")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX5')) {
                                "ID"("1001")
                                "DESCR"("RU_MESSAGE_HASH can not be blank")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX6') || (term.endsWith('XX12') && 'C' == tranType) || (term.endsWith('XX13')
                                    && 'W' == tranType)) {
                                "ID"("1018")
                                "DESCR"("Invalid date.")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX8')) {
                                "ID"("1010")
                                "DESCR"("Unable to decrypt RU_TOKEN.")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX9')) {
                                "ID"("1000")
                                "DESCR"("STRM can not be blank")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX10')) {
                                "ID"("1003")
                                "DESCR"("SCC_ROW_UPD_DTTM can not be blank if RU_RET_AUTH_RESULT is A")
                                "MESSAGE_SEVERITY"("E")
                            } else if (term.endsWith('XX11')) {
                                "ID"("1008")
                                "DESCR"("Unable to decrypt STUDENT ID.")
                                "MESSAGE_SEVERITY"("E")
                            } else {
                                "ID"("1013")
                                "DESCR"("RU_REF_NUM already exists with TRANS TYPE S or P")
                                "MESSAGE_SEVERITY"("E")
                            }
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        NodeChild originalReq = new XmlSlurper().parseText(exchange.unitOfWork.originalInMessage.getBody(String.class)) as NodeChild
        def body = exchange.message.body as NodeChild
        def studentId = extractStudentId(body)
        def respStudentId = studentId?.trim()?.length() > 0 ? 22 : 0
        def refNumber = extractNodeText(body['RU_REF_NUM'] as GPathResult)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            mkp.declareNamespace("": "http://xmlns.ryerson.ca/ps/sas/services")
            "RU_D1_BLUEPAY_INFO_RESPONSE"() {
                "STUDENTID"(respStudentId)
                "RU_REF_NUM"(refNumber)
                "original"() {
                    mkp.declareNamespace("": "http://xmlns.ryerson.ca/ps/sas/schemas/RU_D1_BLUEPAY_INFO_REQUEST.V1")
                    mkp.yield(originalReq)
                }
            }
        }
        return xml.toString()
    }
}
