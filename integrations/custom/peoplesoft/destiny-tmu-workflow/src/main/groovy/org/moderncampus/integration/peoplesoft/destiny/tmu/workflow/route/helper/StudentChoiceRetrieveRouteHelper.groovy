package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.apache.commons.lang3.tuple.Pair
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentChoiceRetrieveRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftStudentChoiceRouteHelper {

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMUGetOptinOutRequest(Exchange exchange) {
        def studentId = exchange.message.getBody(String.class)
        NodeChild request = exchange.getProperty(PARSED_ORIGINAL_REQ, NodeChild.class)
        GPathResult filteredEnrollmentEvents = exchange.getProperty(FILTERED_ENROLLMENT_EVENTS, GPathResult.class)
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_GET_OPTINOUT_REQUEST"() {
                "STUDENTID"(studentId)
                "ACAD_CAREER"('CNED')
                filteredEnrollmentEvents.each { event ->
                    "STRM"(extractStrm(event as NodeChild))
                }
                "D1_PASS_THROUGH"(constructD1PassThrough(filteredEnrollmentEvents))
            }
        }
        return xml.toString()
    }

    private String constructD1PassThrough(GPathResult filteredEnrollmentEvents) {
        Map<String, List<String>> passThroughMap = filteredEnrollmentEvents.collectEntries { it ->
            {
                String key = extractStrm(it as NodeChild) + ":" + extractSectionId(it as NodeChild)
                List<String> value = extractConditionalSpecialRequestsFromEvent(it as NodeChild).collect { specialRequestNode ->
                    return extractNodeText(specialRequestNode['associatedSpecialRequest']['specialRequestType'] as GPathResult)
                }
                return [(key): value]
            }
        } as Map<String, List<String>>
        return JsonOutput.toJson(passThroughMap)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response, true, true)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_ACTION_CANNOT_PROCEED : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault ? '' : mkp.yieldUnescaped(getEntityContent(response))
            }
            "entityType"(VALUE_MAP)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String getEntityContent(NodeChild respRoot) {
        def d1PassThroughMap = getD1PassThroughMap(respRoot)
        def eligibleTermEntries = findEligibleTermEntries(respRoot)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "map"() {
                "entries"() {
                    (getStudentChoices(d1PassThroughMap, eligibleTermEntries) + getMissingChoices(d1PassThroughMap, eligibleTermEntries))
                            .each { choiceEntryMap ->
                                choiceEntryMap.each { choiceEntryMapEntry ->
                                    "entry"() {
                                        "key"(choiceEntryMapEntry.key)
                                        "value"(choiceEntryMapEntry.value)
                                    }
                                }
                            }
                }
            }
        }
        return xml.toString()
    }

    private Map<String, List<String>> getD1PassThroughMap(NodeChild respRoot) {
        return new JsonSlurper().parseText(extractNodeText(respRoot['D1_PASS_THROUGH'] as GPathResult)) as Map<String, List<String>>
    }

    private List<Map<String, String>> getStudentChoices(Map<String, List<String>> d1PassThroughMap, Map<String,
            Pair<String, String>> eligibleTermEntries) {
        def eligibleTerms = eligibleTermEntries.keySet().collect {
            return it.split(":")[0]
        } as Set<String>
        return d1PassThroughMap.findAll { entry ->
            return eligibleTerms.contains(entry.key.split(":")[0])
        }.collect { entry ->
            return entry.value.collectEntries { value ->
                def termEntry = eligibleTermEntries[entry.key.split(":")[0] + ":" + value]
                def key = makeReturnKey(entry.getKey(), value)
                def val = !termEntry ? "UNDEFINED" :
                        (extractNodeText(termEntry.key['ISEDITABLE'] as GPathResult) == 'Y' ? "NOT_SELECTED" : (termEntry.value["@OPTOUT"] == "N" ?
                                "OPTED_IN" : "OPTED_OUT"))
                return [(key): val]
            }
        } as List<Map<String, String>>
    }

    private List<Map<String, String>> getMissingChoices(Map<String, List<String>> d1PassThroughMap, Map<String,
            Pair<String, String>> eligibleTermEntries) {
        List<Pair<String, String>> itemTypesThatDoesNotExistInRequest = eligibleTermEntries.collect { entry ->
            def strm = entry.getKey().split(":")[0]
            def itemType = entry.getKey().split(":")[1]
            Map<String, List<String>> passThroughMap = d1PassThroughMap.findAll { it ->
                (it.key.split(":")[0] == strm) && (!it.value.contains(itemType))
            }
            return passThroughMap.collect { passThroughMapEntry ->
                Pair.of(passThroughMapEntry.key, itemType)
            }
        }.flatten() as List<Pair<String, String>>
        return itemTypesThatDoesNotExistInRequest.collect { itemTypePair ->
            return [(makeReturnKey(itemTypePair.key, itemTypePair.value)): "UNDEFINED"] as Map<String, String>
        }
    }

    private Map<String, Pair<NodeChild, NodeChild>> findEligibleTermEntries(NodeChild resp) {
        Map<String, Pair<NodeChild, NodeChild>> entries = (resp['STRMS'] as NodeChildren).collectEntries { strmsNode ->
            return (strmsNode['ITEM_TYPE'] as NodeChildren).collectEntries { it ->
                [(extractNodeText(strmsNode['STRM'] as NodeChildren) + ":" + extractNodeText(it as GPathResult)): Pair.of(strmsNode, it)]
            }
        } as Map<String, Pair<NodeChild, NodeChild>>
        return entries
    }

    private String makeReturnKey(String termAndSectionRef, String itemTypeRef) {
        return termAndSectionRef + ":" + itemTypeRef
    }
}
