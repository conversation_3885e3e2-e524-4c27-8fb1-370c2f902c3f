package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.mock

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText
import static org.moderncampus.integration.helper.GroovyXMLSupport.extractTrimmedText

@CompileStatic
@Component
class MockTMUD1RegAndTranRouteHelper {

    static final String SERVER_ERROR_MSG = "Mock server error: "
    static final String STUDENT_ID_PREFIX = "55555"
    static final Map<String, Map<String, String>> RESPONSE_MAP = [
            "1010"   : ["result"   : "False",
                        "message"  : "You are already registered in a certificate program. (25000,381)",
                        "rmsStatus": "",
                        "rmsUrl"   : ""],
            "1020"   : ["result"   : "True",
                        "message"  : "Term activated only, Student already has active NOCER program (0,0)",
                        "rmsStatus": "error",
                        "rmsUrl"   : "Potential duplicate found."],
            "success": ["result"   : "True",
                        "message"  : "Registered in Non Certificate Program Certificate (25000,382)",
                        "rmsStatus": "success",
                        "rmsUrl"   : "https://destinysolutions.com"]
    ] as Map<String, Map<String, String>>

    String mapRequest(String body) {
        def rootNode = new XmlSlurper().parseText(body) as NodeChild
        String studentId = extractNodeText(rootNode['STUDENTID'] as NodeChildren)
        return studentId && studentId.length() > 14 ? studentId.substring(14) : studentId
    }

    def boolean isServerError(String studentId) {
        return studentId?.startsWith('ERR5100')
    }

    def boolean isClientError(String studentId) {
        return studentId?.startsWith('ERR4100')
    }

    def void getServerErrorContent(String studentId) throws Exception {
        throw new ApplicationException(SERVER_ERROR_MSG + studentId)
    }

    def void getClientErrorContent() throws Exception {
        throw new ApplicationException(JsonOutput.toJson([data: "[&lt;?xml version=&quot;1.0&quot;?&gt;\n" +
                "&lt;RU_D1_STDNT_STATUS_FAULT xmlns=&quot;http://xmlns.ryerson.ca/ps/sas/services&quot;&gt;&lt;IS_FAULT&gt;Y&lt;/IS_FAULT&gt;&lt;" +
                "FAULT&gt;&lt;MSGS&gt;&lt;MSG&gt;&lt;ID&gt;1002&lt;/ID&gt;&lt;DESCR&gt;Unable to decrypt STUDENT ID.&lt;/DESCR&gt;&lt;" +
                "MESSAGE_SEVERITY&gt;E&lt;/MESSAGE_SEVERITY&gt;&lt;/MSG&gt;&lt;/MSGS&gt;&lt;/FAULT&gt;&lt;/RU_D1_STDNT_STATUS_FAULT&gt;"]), 404)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getSuccessfulContent(Exchange exchange) throws Exception {
        String studentId = exchange.message.getBody(String.class)
        def responseMapEntry = RESPONSE_MAP.getOrDefault(extractTrimmedText(studentId, 4), RESPONSE_MAP["success"])
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_REGANDTRAN_RESPONSE"() {
                "STUDENTID"(studentId)
                "RESULT"(responseMapEntry.result)
                "MESSAGE"(responseMapEntry.message)
                "RMS_STATUS"(responseMapEntry.rmsStatus)
                "RMS_URL"(responseMapEntry.rmsUrl)
                "origin"("MOCK_RU_D1_REGANDTRAN_RESPONSE")
            }
        }
        return xml.toString()
    }
}
