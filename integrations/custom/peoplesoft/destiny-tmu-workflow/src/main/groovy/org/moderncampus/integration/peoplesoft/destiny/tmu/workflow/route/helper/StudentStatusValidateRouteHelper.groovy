package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentStatusValidateRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftBasketRouteHelper {

    static final Map<String, String> STDNT_STATUS_ERROR_MSG_MAP = [
            SC0000: "",
            FC9999: "Unknown error",
            FC5000: "Student is not CNED eligible",
            FC9997: "Student status could not be found"
    ]

    static final Map<String, String> REGANDTRAN_ERROR_MSG_MAP = [
            SC0000: "",
            FC5000: "Student could not be enrolled in a certificate program",
            FC9997: "Student enrollment status could not be found"
    ]

    public String mapInboundRequest(String body) {
        NodeChild inboundReq = parseXMLBody(body)
        GPathResult filteredEnrollmentEvents = extractEnrollmentEventsFromRequest(inboundReq, true, true)
        List<String> uniqueStudentIdList = extractUniqueStudentIdsFromEvent(filteredEnrollmentEvents)
        validateRequest(body, uniqueStudentIdList)
        return uniqueStudentIdList[0]
    }

    private void validateRequest(String originalReq, List<String> uniqueStudentIdList) {
        if (uniqueStudentIdList && uniqueStudentIdList.size() != 1) {
            def invalidRequestException = [error     : "Invalid Request",
                                           reason    : "Request must contain exactly 1 student across all enrollments.",
                                           original  : originalReq,
                                           resolution: "Modify request to contain exactly 1 student across all enrollments."]
            throw new ApplicationException(JsonOutput.toJson(invalidRequestException), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMUStudentStatusRequest(String studentId) {
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_STDNT_STATUS_REQUEST"() {
                "STUDENTID"(studentId)
            }
        }
        return xml.toString()
    }

    def String extractStudentId(NodeChild studentStatusResp) {
        return extractNodeText(studentStatusResp['STUDENTID'] as GPathResult)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMURegAndTranRequest(String studentId) {
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_REGANDTRAN_REQUEST"() {
                "STUDENTID"(studentId)
                "OPTION"("REG")
                "ACAD_PROG"("NOCER")
                "RU_CERT_APPR_CD"("0")
            }
        }
        return xml.toString()
    }

    def boolean isStudentCertRegistrationRequired(NodeChild studentStatusResp) {
        return isResultFound(studentStatusResp) && !hasActiveUgrd(studentStatusResp) && !hasActiveCned(studentStatusResp)
    }

    private boolean hasActiveUgrd(NodeChild response) {
        GPathResult result = (response['CAREER_INFO'] as NodeChildren).find { it ->
            "UGRD" == extractNodeText(it['ACAD_CAREER'] as GPathResult) && "AC" == extractNodeText(it['PROG_STATUS'] as GPathResult)
        }
        return !result.isEmpty()
    }

    private boolean hasActiveCned(NodeChild response) {
        GPathResult result = (response['CAREER_INFO'] as NodeChildren).find { it ->
            "CNED" == extractNodeText(it['ACAD_CAREER'] as GPathResult) && "AC" == extractNodeText(it['PROG_STATUS'] as GPathResult)
        }
        return !result.isEmpty()
    }

    private boolean isResultFound(NodeChild response) {
        return !((response['STUDENTID'] as GPathResult).isEmpty()) && !((response['CAREER_INFO'] as GPathResult).isEmpty())
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    public String mapInnerResult(NodeChild response) {
        if (response.name() == 'RU_D1_STDNT_STATUS_RESPONSE') {
            return mapStdntStatusRespToResult(response)
        } else if (response.name() == 'RU_D1_REGANDTRAN_RESPONSE') {
            return mapRegAndTranRespToResult(response)
        }

        throw new RuntimeException("Invalid response found for mapping. Response root: " + response.name())
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String mapStdntStatusRespToResult(NodeChild response) {
        def isResultFound = isResultFound(response)
        def isEnrollmentEligible = hasActiveCned(response)
        def isEnrollmentCannotProceed = hasActiveUgrd(response)
        def statusStr = !isResultFound ? STATUS_CODE_NO_RESULT_FOUND : (isEnrollmentCannotProceed ? STATUS_CODE_ACTION_CANNOT_PROCEED :
                STATUS_CODE_SUCCESS)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(statusStr)
            "studentId"(extractNodeText(response['STUDENTID']))
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(false)
            "errorMsg"(STDNT_STATUS_ERROR_MSG_MAP[statusStr])
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String mapRegAndTranRespToResult(NodeChild response) {
        def isResultFound = !response['STUDENTID'].isEmpty() && !response['RESULT'].isEmpty()
        def isEnrollmentCannotProceed = 'true' != extractNodeText(response['RESULT'], () -> '').toLowerCase()
        def isFault = isFaultResponse(response)
        def statusStr = !isResultFound ? STATUS_CODE_NO_RESULT_FOUND : (isEnrollmentCannotProceed ? STATUS_CODE_ACTION_CANNOT_PROCEED :
                STATUS_CODE_SUCCESS)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(statusStr)
            "entity"(isFault ? "" : extractNodeText(response['STUDENTID']))
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(false)
            "errorMsg"(REGANDTRAN_ERROR_MSG_MAP[statusStr])
        }
        return xml.toString()
    }
}
