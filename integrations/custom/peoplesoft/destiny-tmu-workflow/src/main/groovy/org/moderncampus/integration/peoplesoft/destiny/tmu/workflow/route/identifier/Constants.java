package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier;

public class Constants {

    public static final String STUDENT_TOKEN_RETRIEVE = "studentTokenRetrieve";

    public static final String STUDENT_ENROLLMENT_VALIDATE = "studentEnrollmentValidate";

    public static final String SHOPPING_CART_ITEM_REMOVE = "shoppingCartItemRemove";

    public static final String STUDENT_ENROLLMENT_CREATE = "studentEnrollmentCreate";

    public static final String STUDENT_ENROLLMENT_DROP = "studentEnrollmentDrop";

    public static final String SECTION_AVAILABILITY_RETRIEVE = "sectionAvailabilityRetrieve";

    public static final String SECTION_AVAILABILITY_VALIDATE = "sectionAvailabilityValidate";

    public static final String STUDENT_ACCOUNT_SETTLEMENT = "studentAccountSettlement";

    public static final String STUDENT_PROFILE_RETRIEVE = "studentProfileRetrieve";

    public static final String SECTION_CAPACITY_RESERVE = "sectionCapacityReserve";

    public static final String SECTION_FEE_RETRIEVE = "sectionFeeRetrieve";

    public static final String STUDENT_LOOKUP = "studentLookup";

    public static final String STUDENT_RESIDENCY_NOTIFY = "studentResidenceNotify";

    public static final String STUDENT_CERTIFICATE_ENROLL = "studentCertificateEnroll";

    public static final String STUDENT_STATUS_VALIDATE = "studentStatusValidate";

    public static final String STUDENT_CHOICE_RETRIEVE = "studentChoiceRetrieve";

    public static final String STUDENT_CHOICE_UPDATE = "studentChoiceUpdate";

    public static final String MOCK_BLUEPAY_INFO = "mockBluePayInfo";
}
