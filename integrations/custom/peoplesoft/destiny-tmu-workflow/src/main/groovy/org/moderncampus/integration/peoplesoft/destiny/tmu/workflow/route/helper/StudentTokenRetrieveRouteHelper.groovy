package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.constants.Constants
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class StudentTokenRetrieveRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper, D1TMUPeopleSoftRouteHelper,
        D1TMUPeopleSoftStudentRouteHelper {

    public String mapInboundRequest(String body) {
        def rootNode = parseXMLBody(body)
        return extractSchoolPersonnelNumber(rootNode)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMUTokenRequest(String input) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_TOKEN_REQUEST"() {
                "STUDENTID"(input)
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped makeOriginalResponse(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        boolean isFault = isFault(response)
        boolean isResultFound = isResultFound(response)
        String psToken = extractPSToken(response)
        def xml = builder.bind {
            "status"(isFault || !isResultFound ? STATUS_CODE_UNKNOWN_ERROR : STATUS_CODE_SUCCESS)
            "entity"() {
                isFault || !isResultFound ? '' : mkp.yieldUnescaped(getEntityContent(psToken))
            }
            "entityType"(VALUE_MAP)
            "entityInXMLFormat"(!(isFault || !isResultFound))
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : (!isResultFound ? mkp.yieldUnescaped('PS_TOKEN value is either missing or ' +
                        'empty') : '')
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(String psToken) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "map"() {
                "entries"() {
                    "entry"() {
                        "key"(Constants.D1_PS_TOKEN_KEY)
                        "value"(psToken)
                    }
                }
            }
        }
        return xml.toString()
    }

    def boolean isFault(NodeChild response) {
        return isFaultResponse(response, true)
    }

    def boolean isResultFound(NodeChild rootNode) {
        NodeChildren tokenExpr = rootNode['PS_TOKEN'] as NodeChildren
        return !tokenExpr.isEmpty() && extractNodeText(tokenExpr).size() > 0
    }

    def String extractPSToken(NodeChild rootNode) {
        NodeChildren tokenExpr = rootNode['PS_TOKEN'] as NodeChildren
        return extractNodeText(tokenExpr)
    }

    private String makeOriginalResponse(String response) {
        String originalResp = generateOriginalResp(response)
        def psTokenElementPattern = /<PS_TOKEN>(.*?)<\/PS_TOKEN>/
        def matcher = (originalResp =~ psTokenElementPattern)
        if (matcher.find()) {
            // Obfuscate the value
            def obfuscatedValue = "**********"

            // Replace the original value in the XML with the obfuscated value
            return originalResp.replace(matcher.group(1), obfuscatedValue)
        }
        return originalResp
    }
}
