package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.moderncampus.integration.exception.ApplicationException
import org.moderncampus.integration.route.dto.RouteExecutorResult
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class SectionAvailabilityValidateRouteHelper implements D1TMUPeopleSoftRouteHelper {

    static final String AVAILABILITY_CODE_FULL = "full"
    static final String AVAILABILITY_CODE_CANCELLED = "canceled"

    @CompileStatic(TypeCheckingMode.SKIP)
    String mapInboundRequest(String req) {
        NodeChild request = parseXMLBody(req)
        NodeChildren sections = request['body']['transactionBasket']['enrollmentEvents']['enrollmentEvent']['enrollCourseSection']
        if (sections.isEmpty() || sections.size() > 1) {
            throw new ApplicationException("Basket must contain a single enrollment section event to continue processing", 400)
        }
        def markupBuilder = new StreamingMarkupBuilder()
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "courseSectionProfile"() {
                        mkp.yield sections['*']
                    }
                }
            }
        }
        return xml.toString()
    }

    public String parseRouteResponse(RouteExecutorResult result) {
        return result.results as String
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(String resp) {
        NodeChild sectionRetrieveResp = parseXMLBody(resp)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            mkp.yieldUnescaped mapInnerResult(sectionRetrieveResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(def response) {
        NodeChildren results = response['body']['integrationActionResult']['results']['result'] as NodeChildren
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            results.each { resultNode ->
                "result"() {
                    "status"(isUnknownError(resultNode) ? STATUS_CODE_UNKNOWN_ERROR : (isSectionFull(resultNode) ?
                            STATUS_CODE_OPERATION_NO_LONGER_VALID : (isSectionCancelled(resultNode)
                            ? STATUS_CODE_ACTION_CANNOT_PROCEED : extractNodeText(resultNode['status'] as GPathResult))))
                    "entity"()
                    "entityType"(ENTITY_COURSE_SECTION + ":" + ENTITY_COURSE_SECTION_LW)
                    "entityInXMLFormat"(false)
                    "errorMsg"(extractNodeText(resultNode['errorMsg']))
                    "action"()
                }
            }
        }
        return xml.toString()
    }

    def boolean isSectionFull(NodeChild resultNode) {
        return extractNodeText(resultNode['detail'] as GPathResult) == AVAILABILITY_CODE_FULL
    }

    def boolean isSectionCancelled(NodeChild resultNode) {
        return extractNodeText(resultNode['detail'] as GPathResult) == AVAILABILITY_CODE_CANCELLED
    }

    def boolean isUnknownError(NodeChild resultNode) {
        return extractNodeText(resultNode['status'] as GPathResult) == STATUS_CODE_UNKNOWN_ERROR
    }

    def boolean isError(NodeChild resultNode) {
        return !(resultNode['error'] as GPathResult).isEmpty()
    }
}
