package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.beans;

import static org.apache.camel.support.builder.PredicateBuilder.not;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.constants.Constants.D1_TMU_PEOPLESOFT_COMPONENT_SCHEME;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName.*;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier.DestinyTMUPSCommonRouteIds.V1_D1_TMU_PEOPLESOFT_HEALTH;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier.DestinyTMUPSRouteIds.*;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Collection;
import java.util.Map;

import org.apache.camel.builder.DataFormatBuilderFactory;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.health.HealthCheck;
import org.apache.camel.model.ChoiceDefinition;
import org.apache.camel.model.ProcessorDefinition;
import org.apache.camel.model.RouteDefinition;
import org.apache.camel.model.dataformat.Base64DataFormat;
import org.apache.camel.model.dataformat.CryptoDataFormat;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.health.IDefaultIntEndpointHealthCheckInvoker;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftBaseConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.D1TMUPeopleSoftEndpointConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftEnrollmentCreateConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftEnrollmentDropConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftEnrollmentValidateConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftSectionAvailabilityRetrieveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftSectionCapacityReserveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftSectionFeeRetrieveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentAccountSettlementConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentCertificateEnrollConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentChoiceRetrieveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentChoiceUpdateConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentLookupConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentProfileRetrieveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentResidencyNotifyConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftStudentStatusValidateConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.component.config.D1TMUPeopleSoftTokenRetrieveConfiguration;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder.CustomEncryptionRouteBuilder;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.builder.D1TMUPeopleSoftRouteBuilder;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.SectionAvailabilityRetrieveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.SectionAvailabilityValidateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.SectionCapacityReserveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.SectionFeeRetrieveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.ShoppingCartRemoveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentAccountSettlementRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentCertificateEnrollRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentChoiceRetrieveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentChoiceUpdateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentEnrollmentCreateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentEnrollmentDropRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentEnrollmentValidateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentLookupRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentProfileRetrieveRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentResidencyNotifyRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentStatusValidateRouteHelper;
import org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper.StudentTokenRetrieveRouteHelper;
import org.moderncampus.integration.route.builder.BaseHealthCheckRouteBuilder;
import org.moderncampus.integration.route.builder.BaseRouteBuilder;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanDefinitions {

    static final String AES_CBC_PKCS_5_PADDING = "AES/CBC/PKCS5Padding";
    static final String METHOD_GENERATE_D1_TMU_TOKEN_REQUEST = "generateD1TMUTokenRequest";
    static final String METHOD_GENERATE_SCC_SC_ADDITEM_REQUEST = "generateSSCSCAddItemRequest";
    static final String METHOD_GENERATE_SSR_ENR_VALIDATE_REQUEST = "generateSSREnrValidateRequest";
    static final String METHOD_GENERATE_SCC_SC_REMOVEITEM_REQUEST = "generateSCCSCRemoveItemRequest";
    static final String METHOD_VALIDATE_SCC_SC_ADDITEM_RESPONSE = "validateSCCSCAddItemResponse";
    static final String METHOD_GENERATE_SSR_ADD_ENROLLMENT_REQUEST = "generateSSRAddEnrollmentRequest";
    static final String METHOD_GENERATE_SSR_DROP_ENROLLMENT_REQUEST = "generateSSRDropEnrollmentRequest";
    static final String METHOD_GENERATE_SSR_GET_CLASS_SECTION_REQUEST = "generateSSRGetClassSectionRequest";
    static final String METHOD_GENERATE_D1_TMU_SETTLEMENT_REQUEST = "generateD1TMUSettlementRequest";
    static final String METHOD_GENERATE_D1_TMU_STUDENT_PROFILE_REQUEST = "generateD1TMUStudentProfileRequest";
    static final String METHOD_GENERATE_D1_TMU_CLASS_CAPADJUST_REQUEST = "generateD1TMUClassCapAdjustRequest";
    static final String METHOD_GENERATE_D1_TMU_CLASS_FEE_REQUEST = "generateD1TMUClassFeeRequest";
    static final String METHOD_GENERATE_D1_TMU_CREATEWEBID_REQUEST = "generateD1TMUCreateWebIdRequest";
    static final String METHOD_GENERATE_D1_TMU_STUDENT_RESIDENCY_REQUEST = "generateD1TMUStudentResidencyRequest";
    static final String METHOD_GENERATE_D1_TMU_REGANDTRAN_REQUEST = "generateD1TMURegAndTranRequest";
    static final String METHOD_GENERATE_D1_TMU_STUDENT_STATUS_REQUEST = "generateD1TMUStudentStatusRequest";
    static final String METHOD_GENERATE_D1_TMU_GET_OPTINOUT_REQUEST = "generateD1TMUGetOptinOutRequest";
    static final String METHOD_GENERATE_D1_TMU_PUT_OPTINOUT_REQUEST = "generateD1TMUPutOptinOutRequest";
    static final String METHOD_IS_FAULT_RESPONSE = "isFaultResponse";
    static final String METHOD_PARSE_XML_BODY = "parseXMLBody";
    static final String METHOD_PARSE_ROUTE_RESPONSE = "parseRouteResponse";
    static final String METHOD_HANDLE_EMPTY_CONTENT = "handleEmptyContent";
    static final String METHOD_IS_STUDENT_CERT_REGISTRATION_REQUIRED = "isStudentCertRegistrationRequired";
    static final String METHOD_EXTRACT_STUDENT_ID = "extractStudentId";
    static final String METHOD_GET_PADDED_VALUE = "getPaddedValue";
    static final String HEALTH_CHECK_ID = "peoplesoft:destiny:custom:tmu";

    IntegrationRequestContext requestContext;

    @Bean
    public DataFormatBuilderFactory dataFormatBuilderFactory() {
        return new DataFormatBuilderFactory();
    }

    @Bean
    public CryptoDataFormat getD1TMUPeopleSoftTokenEncryptFormat(DataFormatBuilderFactory factory) {
        CryptoDataFormat.Builder dataFormatBuilder = factory.crypto()
                .algorithm(AES_CBC_PKCS_5_PADDING).shouldAppendHMAC(false);
        return dataFormatBuilder.end();
    }

    @Bean
    public Base64DataFormat getUrlUnsafeBase64DataFormat(DataFormatBuilderFactory factory) {
        return factory.base64().urlSafe(false).end();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentTokenRetrieve(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentTokenRetrieveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_TOKEN_RETRIEVE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftTokenRetrieveConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_TOKEN_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_TOKEN_SO.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftTokenRetrieveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentEnrollmentValidate(
            StudentEnrollmentValidateRouteHelper d1TMUPSEnrollmentValidateRouteHelper) {
        StudentEnrollmentValidateRouteHelper helper = d1TMUPSEnrollmentValidateRouteHelper;
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_VALIDATE.getId(),
                helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .bean(helper, METHOD_GENERATE_SCC_SC_ADDITEM_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SCC_SC_ADDITEM.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftEnrollmentValidateConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY)
                        .bean(helper, METHOD_VALIDATE_SCC_SC_ADDITEM_RESPONSE)
                        .choice()
                        .when(not(method(helper, METHOD_IS_FAULT_RESPONSE + "(${body},false, false)")))
                        .bean(helper, METHOD_GENERATE_SSR_ENR_VALIDATE_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SSR_ENR_VALIDATE.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftEnrollmentValidateConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY)
                        .process(RouteSupport::setupAsyncRouteInvoke)
                        .wireTap(buildDirectRouteURI(V1_D1_TMU_PEOPLESOFT_SHOPPING_CART_ITEM_REMOVE.getId()))
                        .end();
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftShoppingCartRemove(ShoppingCartRemoveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_SHOPPING_CART_ITEM_REMOVE.getId(), helper) {

            @Override
            public boolean isValidate() {
                return false;
            }

            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .bean(helper, METHOD_GENERATE_SCC_SC_REMOVEITEM_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SCC_SC_REMOVEITEM.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftEnrollmentValidateConfiguration.class)))));
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentEnrollmentCreate(
            StudentEnrollmentCreateRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_CREATE.getId(),
                helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .bean(helper, METHOD_GENERATE_SSR_ADD_ENROLLMENT_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SSR_ADD_ENROLLMENT.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftEnrollmentCreateConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentEnrollmentDrop(
            StudentEnrollmentDropRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_ENROLLMENT_DROP.getId(),
                helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .bean(helper, METHOD_GENERATE_SSR_DROP_ENROLLMENT_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SSR_DROP_ENROLLMENT.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftEnrollmentDropConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftSectionAvailabilityRetrieve(
            SectionAvailabilityRetrieveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_RETRIEVE.getId(),
                helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .bean(helper, METHOD_GENERATE_SSR_GET_CLASS_SECTION_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                SSR_GET_CLASS_SECTION.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftSectionAvailabilityRetrieveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftSectionAvailabilityValidate(
            SectionAvailabilityValidateRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_VALIDATE.getId(),
                helper) {

            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition
                        .toD(buildDirectRouteURI(V1_D1_TMU_PEOPLESOFT_SECTION_AVAILABILITY_RETRIEVE.getId()))
                        .bean(helper, METHOD_PARSE_ROUTE_RESPONSE);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentAccountSettlement(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentAccountSettlementRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_ACCOUNT_SETTLEMENT.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentAccountSettlementConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_SETTLEMENT_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_SETTLEMENT.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentAccountSettlementConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentProfileRetrieve(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentProfileRetrieveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_PROFILE_RETRIEVE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentProfileRetrieveConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_STUDENT_PROFILE_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_STDNT_PROFILE.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentProfileRetrieveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY)
                        .bean(helper, METHOD_HANDLE_EMPTY_CONTENT);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftSectionCapacityReserve(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            SectionCapacityReserveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_SECTION_CAPACITY_RESERVE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftSectionCapacityReserveConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_CLASS_CAPADJUST_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_CLASS_CAPADJUST.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftSectionCapacityReserveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftSectionFeeRetrieve(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            SectionFeeRetrieveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_SECTION_FEE_RETRIEVE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftSectionFeeRetrieveConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_CLASS_FEE_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_CLASS_FEE.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftSectionFeeRetrieveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentLookup(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentLookupRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_LOOKUP.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentLookupConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_CREATEWEBID_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_CREATEWEBID.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentLookupConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentResidencyNotify(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentResidencyNotifyRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_RESIDENCY_NOTIFY.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentResidencyNotifyConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_STUDENT_RESIDENCY_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_STDNT_RESIDENCY.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentResidencyNotifyConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentCertificateEnroll(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentCertificateEnrollRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_CERTIFICATE_ENROLL.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                buildCertEnrollRouteDef(definition, encryptionRouteBuilder, helper,
                        D1TMUPeopleSoftStudentCertificateEnrollConfiguration.class);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentStatusValidate(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentStatusValidateRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_STATUS_VALIDATE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                definition.bean(helper, METHOD_GET_PADDED_VALUE);
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentStatusValidateConfiguration.class,
                        definition, true);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_STUDENT_STATUS_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_STDNT_STATUS.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentStatusValidateConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
                //cert registration conditional - when deemed required
                ChoiceDefinition studentRegChoiceDef = definition
                        .choice()
                        .when((method(helper, METHOD_IS_STUDENT_CERT_REGISTRATION_REQUIRED)))
                        .bean(helper, METHOD_EXTRACT_STUDENT_ID);
                buildCertEnrollRouteDef(studentRegChoiceDef, encryptionRouteBuilder, helper,
                        D1TMUPeopleSoftStudentStatusValidateConfiguration.class);
            }
        };
    }

    private void buildCertEnrollRouteDef(ProcessorDefinition<?> definition,
            CustomEncryptionRouteBuilder encryptionRouteBuilder, Object helper, Class<?> pipelineConfigClass) {
        definition.bean(helper, METHOD_GET_PADDED_VALUE);
        //encrypt token when configured
        encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class, pipelineConfigClass,
                definition, true);
        definition
                .bean(helper, METHOD_GENERATE_D1_TMU_REGANDTRAN_REQUEST)
                .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                        TMU_D1_REGANDTRAN.name(), routeQueryParamStr(
                                Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                        parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                        D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                        parameterBeanRef(pipelineConfigClass)))))
                .bean(helper, METHOD_PARSE_XML_BODY);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentChoiceRetrieve(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentChoiceRetrieveRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_RETRIEVE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentChoiceRetrieveConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_GET_OPTINOUT_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_GET_OPTINOUT.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentChoiceRetrieveConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftStudentChoiceUpdate(CustomEncryptionRouteBuilder encryptionRouteBuilder,
            StudentChoiceUpdateRouteHelper helper) {
        return new D1TMUPeopleSoftRouteBuilder(V1_D1_TMU_PEOPLESOFT_STUDENT_CHOICE_UPDATE.getId(), helper) {
            @Override
            protected void buildCustomRoute(RouteDefinition definition) {
                //encrypt token when configured
                encryptionRouteBuilder.buildRoute(helper, D1TMUPeopleSoftBaseConfiguration.class,
                        D1TMUPeopleSoftStudentChoiceUpdateConfiguration.class,
                        definition);
                definition
                        .bean(helper, METHOD_GENERATE_D1_TMU_PUT_OPTINOUT_REQUEST)
                        .toD(buildRouteURI(D1_TMU_PEOPLESOFT_COMPONENT_SCHEME,
                                TMU_D1_PUT_OPTINOUT.name(), routeQueryParamStr(
                                        Map.of(D1TMUPeopleSoftEndpointConfiguration.BASE_CONFIG_PARAM,
                                                parameterBeanRef(D1TMUPeopleSoftBaseConfiguration.class),
                                                D1TMUPeopleSoftEndpointConfiguration.PIPELINE_CONFIG_PARAM,
                                                parameterBeanRef(
                                                        D1TMUPeopleSoftStudentChoiceUpdateConfiguration.class)))))
                        .bean(helper, METHOD_PARSE_XML_BODY);
            }
        };
    }

    @Bean
    @Lazy(value = false)
    @Profile("!test & !prod")
    public RouteBuilder v1D1TMUPeopleSoftMockBluePayInfo() {
        return new BaseRouteBuilder(V1_D1_TMU_PEOPLESOFT_MOCK_BLUEPAY_INFO.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.to(buildDirectRouteURI(TMU_D1_BLUEPAY_INFO.getMockEndpoint()));
            }
        };
    }

    @Bean
    public IDefaultIntEndpointHealthCheckInvoker v1D1TMUPeopleSoftHealthCheckInvoker(
            IntegrationRequestContext requestContext) {
        return new IDefaultIntEndpointHealthCheckInvoker() {

            @Override
            public String getHealthCheckId() {
                return HEALTH_CHECK_ID;
            }

            @Override
            public IntegrationRequestContext getTenantRequestContext() {
                return requestContext;
            }
        };
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1D1TMUPeopleSoftHealthRoute(D1TMUPeopleSoftBaseConfiguration baseConfiguration,
            IntegrationRequestContext requestContext, IDefaultIntEndpointHealthCheckInvoker healthCheckInvoker) {
        return new BaseHealthCheckRouteBuilder(V1_D1_TMU_PEOPLESOFT_HEALTH.getId()) {
            @Override
            protected void buildHealthRoute(RouteDefinition routeDefinition) {
                routeDefinition.process((exchange) -> {
                    String host = baseConfiguration.getHost();
                    Collection<HealthCheck.Result> healthResult = healthCheckInvoker.healthCheck(host);
                    exchange.getMessage().setBody(healthResult);
                });
            }
        };
    }
}
