package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonBuilder
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

@Component
@CompileStatic
class SectionCapacityReserveRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftBasketRouteHelper {

    static final Map<String, String> REVERSE_ENRL_ACTION = [
            E: "D",
            D: "E"
    ]

    static final Map<String, String> ACTIVITY_CODE_TO_ENRL_ACTION = [
            Sale    : "E",
            Refund  : "D",
            Exchange: "D"
    ]

    static final String PASS_THROUGH_FORMAT_REGEX = /\{("\w+":"\w+")(,"\w+":"\w+")*\}/

    def String mapInboundRequest(String body) {
        NodeChild parsedReq = parseXMLBody(body)
        validateRequest(body, parsedReq)
        def enrollmentEvents = extractEnrollmentEventsFromRequest(parsedReq, false)
        return extractClassNbr(enrollmentEvents[0] as NodeChild)
    }

    private void validateRequest(String request, NodeChild parsedRequest) {
        def enrollmentEvents = extractEnrollmentEventsFromRequest(parsedRequest, false)
        if (enrollmentEvents.isEmpty() || enrollmentEvents.size() > 1) {
            def emptyExceptionDetail = [error     : "Invalid Request",
                                        reason    : "Request must contain exactly one enrollment event",
                                        original  : request,
                                        resolution: "Review and correct payload"]
            throw new ApplicationException(JsonOutput.toJson(emptyExceptionDetail), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String generateD1TMUClassCapAdjustRequest(Exchange exchange) {
        def originalReq = exchange.unitOfWork.originalInMessage.getBody(String.class)
        String classNbr = exchange.message.getBody(String.class)
        def parsedReq = parseXMLBody(originalReq)
        def markupBuilder = new StreamingMarkupBuilder()
        def enrollmentEvents = extractEnrollmentEventsFromRequest(parsedReq, false)
        def enrollmentEvent = enrollmentEvents[0] as NodeChild
        String enrlAction = getHeaderFromRequest(exchange, 'ENRL_ACTION', getEnrlActionFromEnrollmentEvent(enrollmentEvent))
        markupBuilder.encoding = 'UTF-8'
        def xml = markupBuilder.bind { builder ->
            mkp.xmlDeclaration()
            "RU_D1_CLASS_CAPADJUST_REQUEST"() {
                "ACAD_CAREER"('CNED')
                "STRM"(extractStrm(enrollmentEvent))
                "CLASS_NBR"(classNbr)
                "ENRL_ACTION"(enrlAction)
                "D1_PASS_THROUGH"(new JsonBuilder(entity: extractNodeText(enrollmentEvent['objectId']), action: enrlAction).toString())
            }
        }
        return xml.toString()
    }

    private String mapEnrlAction(NodeChild enrollmentEvent) {
        return ACTIVITY_CODE_TO_ENRL_ACTION[extractNodeText(enrollmentEvent['activityCode'] as NodeChildren)]
    }

    private String reverseEnrlAction(String action) {
        return REVERSE_ENRL_ACTION[action]
    }

    private String getEnrlActionFromEnrollmentEvent(NodeChild enrollmentEvent) {
        return "true" == extractNodeText(enrollmentEvent['voided'] as NodeChildren) ? reverseEnrlAction(mapEnrlAction(enrollmentEvent)) :
                mapEnrlAction(enrollmentEvent)
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapInnerResult(NodeChild response) {
        def isFault = isFaultResponse(response, true)
        def passThroughStr = isFault ? parsePassThrough(getFaultIds(response)[0]) :
                parsePassThrough(extractNodeText(response['D1_PASS_THROUGH']))
        String passThroughEntity = getPassThroughEntity(passThroughStr)
        String passThroughAction = getPassThroughAction(passThroughStr)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_UNKNOWN_ERROR : (!isResultFound(response) ? STATUS_CODE_NO_RESULT_FOUND : (isResultTrue(response) ?
                    STATUS_CODE_SUCCESS : STATUS_CODE_ACTION_CANNOT_PROCEED)))
            "entity"(passThroughEntity)
            "entityType"(ENTITY_COURSE_SECTION_LW)
            "entityInXMLFormat"(false)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultDescriptions(response)) : ''
            }
            "action"(passThroughAction)
        }
        return xml.toString()
    }

    private boolean isResultFound(NodeChild response) {
        return !(response['RESULT'] as NodeChildren).isEmpty()
    }

    private boolean isResultTrue(NodeChild response) {
        return isResultFound(response) && Boolean.valueOf(extractNodeText(response['RESULT'] as NodeChildren))
    }

    private List<String> getFaultIds(NodeChild rootNode) {
        return (rootNode['FAULT']['MSGS']['MSG'] as NodeChildren).collect { it ->
            {
                NodeChild msgNode = it as NodeChild
                extractNodeText(msgNode['ID'] as NodeChildren)
            }
        } as List<String>
    }

    private String getFaultDescriptions(NodeChild rootNode) {
        return (rootNode['FAULT']['MSGS']['MSG'] as NodeChildren).collect { it ->
            {
                NodeChild msgNode = it as NodeChild
                extractNodeText(msgNode['DESCR'] as NodeChildren)
            }
        }.join(",")
    }

    private String getPassThroughEntity(Object passThrough) {
        return passThrough ? passThrough['entity'] : null
    }

    private String getPassThroughAction(Object passThrough) {
        return passThrough ? passThrough['action'] : null
    }

    private Object parsePassThrough(String value) {
        return value =~ PASS_THROUGH_FORMAT_REGEX ? new JsonSlurper().parseText(value) : null
    }

}
