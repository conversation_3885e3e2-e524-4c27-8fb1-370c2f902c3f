package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import org.apache.camel.Exchange
import org.moderncampus.integration.exception.ApplicationException
import org.springframework.stereotype.Component

import static org.moderncampus.integration.helper.GroovyXMLSupport.*

@Component
@CompileStatic
class StudentProfileRetrieveRouteHelper implements D1TMUPeopleSoftEncryptionRouteHelper,
        D1TMUPeopleSoftRouteHelper, D1TMUPeopleSoftStudentRouteHelper {

    static final List<String> SALUTATION_CODES = [
            "",
            "Mr",
            "Mrs",
            "Miss",
            "Ms",
            "Dr",
            "Professor",
            "Rev",
            "Fr"
    ]

    static final Map<String, String> TELEPHONE_CODES = [
            HOME  : "Home",
            OFFICE: "Office"
    ]

    static final Map<String, String> ADDRESS_CODES = [
            HOME: "Home"
    ]

    static final Map<String, Integer> TELEPHONE_INDEX = [
            HOME  : 0,
            OFFICE: 1
    ]

    static final Map<String, Integer> TELEPHONE_SUB_INDEX = [
            AREA_CODE: 0,
            NUMBER   : 1
    ]

    static final Map<String, String> STUDENT_CATEGORY = [
            INTERNATIONAL           : "International",
            DOMESTIC                : "Domestic",
            DOMESTIC_OUT_OF_PROVINCE: "Domestic Out of Province"
    ]

    static final String RESIDENCY_FLAG = "HasResidency"
    static final String PHONE_SANITIZE_REGEX = /[\/-]/
    static boolean IS_EMAIL_PREFERRED = true
    static boolean IS_ADDRESS_PREFERRED = true

    String mapInboundRequest(String body) {
        NodeChild request = parseXMLBody(body)
        validateRequest(body, request)
        return extractSchoolPersonnelNumber(request)
    }

    def void validateRequest(String originalReq, NodeChild parsedRequest) {
        def student = parsedRequest['body']['student'] as GPathResult
        if (student.isEmpty() || student.size() > 1) {
            def invalidRequestException = [error     : "Invalid Request",
                                           reason    : "Request must contain exactly 1 student across all enrollments.",
                                           original  : originalReq,
                                           resolution: "Modify request to contain exactly 1 student across all enrollments."]
            throw new ApplicationException(JsonOutput.toJson(invalidRequestException), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String generateD1TMUStudentProfileRequest(String studentId) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "RU_D1_STDNT_PROFILE_REQUEST"() {
                "ACAD_CAREER"('CNED')
                "STRM"()
                "STUDENTID"(studentId)
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    void handleEmptyContent(Exchange exchange) {
        def response = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.yieldUnescaped generateOriginalResp(originalResp)
        }
        if (response.isEmpty() || response.'*'.isEmpty()) {
            def emptyExceptionDetail = [error     : "Empty Response",
                                        reason    : "Invalid token value passed",
                                        original  : xml.toString(),
                                        resolution: "The token may have expired. Retry request or review token generation"]
            throw new ApplicationException(JsonOutput.toJson(emptyExceptionDetail), 400)
        }
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    public String mapOutboundResponse(Exchange exchange) {
        def resp = exchange.message.getBody(NodeChild.class)
        String originalResp = exchange.getProperty(ORIGINAL_BODY, String.class)
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def xml = builder.bind {
            mkp.xmlDeclaration()
            "integrationMessage"() {
                "body"() {
                    "integrationActionResult"() {
                        "status"(STATUS_CODE_SUCCESS)
                        "results"() {
                            "result"() {
                                mkp.yieldUnescaped mapInnerResult(resp)
                            }
                        }
                        "response"() {
                            mkp.yieldUnescaped generateOriginalResp(originalResp)
                        }
                    }
                }
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    private String mapInnerResult(NodeChild response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        boolean isFault = isFaultResponse(response, true)
        def xml = builder.bind {
            "status"(isFault ? STATUS_CODE_UNKNOWN_ERROR : STATUS_CODE_SUCCESS)
            if (!isFault) {
                "entity"() {
                    mkp.yieldUnescaped(getEntityContent(response))
                }
            } else {
                mkp.yield response.'*'
            }
            "entityType"(ENTITY_STUDENT)
            "entityInXMLFormat"(!isFault)
            "errorMsg"() {
                isFault ? mkp.yieldUnescaped(getFaultMsgs(response)) : ''
            }
        }
        return xml.toString()
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def String getEntityContent(NodeChild response) {
        def builder = new StreamingMarkupBuilder()
        builder.encoding = 'UTF-8'
        def salutationCodeStr = extractNodeText(response['TITLE'])
        def isDomestic = extractNodeText(response['IS_DOMESTIC'])
        def isOutOfProvince = extractNodeText(response['IS_OUT_OF_PROVINCE'])
        def birthDateStr = extractNodeText(response['BIRTHDATE'])
        def residencyFlag = extractNodeText(response['HAS_RESIDENCY_INFO'])
        def xml = builder.bind {
            "student"() {
                "salutationCode"(SALUTATION_CODES.indexOf(salutationCodeStr) > 0 ? SALUTATION_CODES[SALUTATION_CODES.indexOf(salutationCodeStr)] : '')
                "firstName1"(extractTrimmedText(extractNodeText(response['FIRST_NAME']), 35))
                "lastName"(extractTrimmedText(extractNodeText(response['LAST_NAME']), 35))
                "otherNames"(extractTrimmedText(extractNodeText(response['LAST_NAME_CD']), 256))
                "addresses"() {
                    "address"() {
                        "typeCode"(ADDRESS_CODES.HOME)
                        "preferred"(IS_ADDRESS_PREFERRED)
                        "street1"(extractTrimmedText(extractNodeText(response['ADDRESS1']), 100))
                        "street2"(extractTrimmedText(extractNodeText(response['ADDRESS2']), 100))
                        "city"(extractTrimmedText(extractNodeText(response['CITY']), 40))
                        "provinceState"(extractTrimmedText(extractNodeText(response['PROVINCE']), 60))
                        "postalZip"(extractTrimmedText(extractNodeText(response['POSTAL']), 20))
                        "country"(extractTrimmedText(extractNodeText(response['COUNTRY']), 70))
                    }
                }
                "telephones"() {
                    "telephone"() {
                        "typeCode"(TELEPHONE_CODES.HOME)
                        "areaCode"(getTelephone(response, TELEPHONE_INDEX.HOME, TELEPHONE_SUB_INDEX.AREA_CODE))
                        "telephoneNumber"(getTelephone(response, TELEPHONE_INDEX.HOME, TELEPHONE_SUB_INDEX.NUMBER))
                    }
                    "telephone"() {
                        "typeCode"(TELEPHONE_CODES.OFFICE)
                        "areaCode"(getTelephone(response, TELEPHONE_INDEX.OFFICE, TELEPHONE_SUB_INDEX.AREA_CODE))
                        "telephoneNumber"(getTelephone(response, TELEPHONE_INDEX.OFFICE, TELEPHONE_SUB_INDEX.NUMBER))
                        "telephoneExt"(extractTrimmedText(extractNodeText(response['EXTENSION']), 8))
                    }
                }
                "emails"() {
                    "email"() {
                        "preferred"(IS_EMAIL_PREFERRED)
                        "emailAddress"(extractTrimmedText(extractNodeText(response['EMAIL_ADDR']), 70))
                    }
                }
                "studentCategories"() {
                    "studentCategory"() {
                        "category"(isDomestic == 'N' ? STUDENT_CATEGORY.INTERNATIONAL : (isDomestic == 'Y' ? (isOutOfProvince == 'Y' ?
                                STUDENT_CATEGORY.DOMESTIC_OUT_OF_PROVINCE : STUDENT_CATEGORY.DOMESTIC) : ""))
                    }
                }
                "birthDate"(birthDateStr ? serializeD1Date(birthDateStr) : "")
                "netId"(extractNodeText(response['RMS_CREDENTIAL_ID']))
                "externalSystemData"(residencyFlag == 'Y' ? RESIDENCY_FLAG : "")
            }
        }
        return xml.toString()
    }

    private String getTelephoneProp(String number, Integer subtype) {
        return TELEPHONE_SUB_INDEX.AREA_CODE == subtype ? extractTrimmedText(number, 3)
                : (TELEPHONE_SUB_INDEX.NUMBER == subtype ? extractTrimmedText(number, 20, 3) : "")
    }

    private String getTelephone(NodeChild response, Integer type, Integer subtype) {
        String homePhone = extractNodeText(response['HOME_PHONE'] as GPathResult, () -> '')
        String busPhone = extractNodeText(response['BUS_PHONE'] as GPathResult, () -> '')
        return TELEPHONE_INDEX.HOME == type ? getTelephoneProp(extractSanitizedText(homePhone, PHONE_SANITIZE_REGEX), subtype) :
                TELEPHONE_INDEX.OFFICE == type ? getTelephoneProp(extractSanitizedText(busPhone, PHONE_SANITIZE_REGEX), subtype) : ""
    }
}
