package org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.helper

import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild
import groovy.xml.slurpersupport.NodeChildren

import static org.moderncampus.integration.helper.GroovyXMLSupport.extractNodeText

trait D1TMUPeopleSoftSectionRouteHelper {

    NodeChildren extractCourseSectionProfileFromRequest(NodeChild enrollment) {
        return enrollment['body']['courseSectionProfile'] as NodeChildren
    }

    String extractClassNbr(NodeChildren sectionProfile) {
        return extractNodeText(sectionProfile['campusFisId'] as GPathResult)
    }

    String extractStrm(NodeChildren sectionProfile) {
        return extractNodeText(sectionProfile['associatedSemester']['campusId'] as GPathResult)
    }

}