package peoplesoft.custom.route.test;

import static org.moderncampus.integration.peoplesoft.destiny.tmu.component.endpoint.resources.D1TMUPSServiceName.*;
import static org.moderncampus.integration.peoplesoft.destiny.tmu.workflow.route.identifier.DestinyTMUPSRouteIds.V1_D1_TMU_PEOPLESOFT_STUDENT_TOKEN_RETRIEVE;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Objects;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.test.spring.junit5.CamelSpringBootTest;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import peoplesoft.custom.route.test.config.TmuCustomRouteConfigConfig;

@CamelSpringBootTest
@EnableAutoConfiguration
@SpringBootTest(classes = {TmuCustomRouteConfigConfig.class})
@ActiveProfiles("dev")
@SpringJUnitWebConfig
public class TMUCustomRouteTest {

    @Autowired
    ProducerTemplate producerTemplate;

    @Autowired
    CamelContext context;

    void invokeEndpoint(String routeId, String inputFileName) throws InterruptedException, IOException {
        String routeUri = RouteSupport.buildDirectRouteURI(routeId);
        Endpoint endpoint = context.getEndpoint(routeUri);
        File file = new File(Objects.requireNonNull(getClass().getClassLoader().getResource(
                inputFileName)).getPath());
        String xml = Files.readString(file.toPath(), StandardCharsets.UTF_8);
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        exchange.getMessage().setBody(xml);
        Exchange response = producerTemplate.send(routeUri, exchange);
        System.out.println("Response is : " + response.getMessage().getBody(String.class));
    }

    @Test
    public void testStudentTokenRetrieve() throws InterruptedException, IOException {
        invokeEndpoint(V1_D1_TMU_PEOPLESOFT_STUDENT_TOKEN_RETRIEVE.getId(), "IntegrationMessageStudent.xml");
    }

    @Test
    public void testMockSCCSCAddItem() throws InterruptedException, IOException {
        invokeEndpoint(SCC_SC_ADDITEM.getMockEndpoint(), "SCC_SC_ADD_ITEM_REQ.xml");
    }

    @Test
    public void testMockSSREnrValidate() throws InterruptedException, IOException {
        invokeEndpoint(SSR_ENR_VALIDATE.getMockEndpoint(), "SSR_ENR_VALIDATE_REQ.xml");
    }

    @Test
    public void testMockSSCSCRemoteItem() throws InterruptedException, IOException {
        invokeEndpoint(SCC_SC_REMOVEITEM.getMockEndpoint(), "SCC_SC_REMOVE_ITEM_REQ.xml");
    }

    @Test
    public void testMockSSRAddEnrollment() throws InterruptedException, IOException {
        invokeEndpoint(SSR_ADD_ENROLLMENT.getMockEndpoint(), "SSR_ADD_ENROLLMENT_REQ.xml");
    }

    @Test
    public void testMockSSRDropEnrollment() throws InterruptedException, IOException {
        invokeEndpoint(SSR_DROP_ENROLLMENT.getMockEndpoint(), "SSR_DROP_ENROLLMENT_REQ.xml");
    }

    @Test
    public void testMockSSRGetClassSection() throws InterruptedException, IOException {
        invokeEndpoint(SSR_GET_CLASS_SECTION.getMockEndpoint(), "SSR_GET_CLASS_SECTION_REQ.xml");
    }

    @Test
    public void testMockTMUD1Settlement() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_SETTLEMENT.getMockEndpoint(), "TMU_D1_SETTLEMENT_REQ.xml");
    }

    @Test
    public void testMockTMUD1StdntProfile() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_STDNT_PROFILE.getMockEndpoint(), "TMU_D1_STDNT_PROFILE_REQ.xml");
    }

    @Test
    public void testMockTMUD1ClassCapAdjust() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_CLASS_CAPADJUST.getMockEndpoint(), "TMU_D1_CLASS_CAPADJUST_REQ.xml");
    }

    @Test
    public void testMockTMUD1ClassFee() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_CLASS_FEE.getMockEndpoint(), "TMU_D1_CLASS_FEE_REQ.xml");
    }

    @Test
    public void testMockTMUD1CreateWebId() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_CREATEWEBID.getMockEndpoint(), "TMU_D1_CREATEWEBID_REQ.xml");
    }

    @Test
    public void testMockTMUD1StdntResidency() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_STDNT_RESIDENCY.getMockEndpoint(), "TMU_D1_STDNT_RESIDENCY_REQ.xml");
    }

    @Test
    public void testMockTMUD1RegAndTran() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_REGANDTRAN.getMockEndpoint(), "TMU_D1_REGANDTRAN_REQ.xml");
    }

    @Test
    public void testMockTMUD1StdntStatus() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_STDNT_STATUS.getMockEndpoint(), "TMU_D1_STDNT_STATUS_REQ.xml");
    }

    @Test
    public void testMockTMUD1GetOptinOut() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_GET_OPTINOUT.getMockEndpoint(), "TMU_D1_GET_OPTINOUT_REQ.xml");
    }

    @Test
    public void testMockTMUD1PutOptinOut() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_PUT_OPTINOUT.getMockEndpoint(), "TMU_D1_PUT_OPTINOUT_REQ.xml");
    }

    @Test
    public void testMockTMUD1BluePayInfo() throws InterruptedException, IOException {
        invokeEndpoint(TMU_D1_BLUEPAY_INFO.getMockEndpoint(), "TMU_D1_BLUEPAY_INFO_REQ.xml");
    }
}
