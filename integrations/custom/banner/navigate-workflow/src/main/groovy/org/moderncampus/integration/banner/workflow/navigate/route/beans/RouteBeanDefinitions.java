package org.moderncampus.integration.banner.workflow.navigate.route.beans;

import static org.moderncampus.integration.banner.component.constants.Constants.BANNER_COMPONENT_SCHEME;
import static org.moderncampus.integration.banner.workflow.navigate.route.identifier.BannerRouteIds.*;
import static org.moderncampus.integration.banner.workflow.navigate.transform.helper.NavigateEthosBannerReadTransformHelper.*;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.List;
import java.util.Map;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.banner.component.BannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.endpoint.EndpointType;
import org.moderncampus.integration.banner.component.internal.APIResource;
import org.moderncampus.integration.banner.workflow.navigate.transform.helper.NavigateEthosBannerReadTransformHelper;
import org.moderncampus.integration.banner.workflow.route.builder.EthosBannerGetAllRouteBuilder;
import org.moderncampus.integration.banner.workflow.route.helper.EthosBannerAssociationResolver;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.route.builder.BaseRouteBuilder;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("navigateBannerRouteBeanDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanDefinitions {

    NavigateEthosBannerReadTransformHelper ethosBannerTransformFunctions;

    DefaultListAggregationStrategy aggregationStrategy;

    EthosBannerAssociationResolver associationResolver;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1NavigateBannerGetTerms() {
        return EthosBannerGetAllRouteBuilder.builder(V1_NAVIGATE_BANNER_GET_TERMS.getId(),
                        ethosBannerTransformFunctions, METHOD_MAP_TERM, aggregationStrategy, APIResource.ADMINISTRATIVE_PERIODS)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1NavigateBannerGetAcademicPrograms() {
        return EthosBannerGetAllRouteBuilder.builder(V1_NAVIGATE_BANNER_GET_ACADEMIC_PROGRAMS.getId(),
                ethosBannerTransformFunctions, METHOD_MAP_ACADEMIC_PROGRAM, aggregationStrategy,
                APIResource.ACADEMIC_PROGRAMS).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1NavigateBannerGetCourses() {
        return EthosBannerGetAllRouteBuilder.builder(V1_NAVIGATE_BANNER_GET_COURSES.getId(),
                        ethosBannerTransformFunctions, METHOD_MAP_COURSE, aggregationStrategy, APIResource.COURSES)
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(APIResource.SUBJECTS, APIResource.EDUCATIONAL_INSTITUTION_UNITS,
                        APIResource.COURSE_TITLE_TYPES)).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1NavigateBannerGetSections() {
        return new BaseRouteBuilder(V1_NAVIGATE_BANNER_GET_SECTIONS.getId(), null) {
            @Override
            protected void buildRouteActions(RouteDefinition routeDefinition) {
                routeDefinition.toD(buildRouteURI(BANNER_COMPONENT_SCHEME,
                        EndpointType.ETHOS_API + ":" + APIResource.SECTIONS + ":"
                                + "/", routeQueryParamStr(
                                Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                        parameterBeanRef(BannerConnectionConfiguration.class),
                                        Constants.MODEL_CLASS_PARAM, JsonNode.class.getName()))));
            }
        };
    }

}
