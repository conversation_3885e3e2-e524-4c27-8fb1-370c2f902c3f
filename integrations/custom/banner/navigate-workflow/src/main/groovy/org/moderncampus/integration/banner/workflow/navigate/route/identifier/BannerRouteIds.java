package org.moderncampus.integration.banner.workflow.navigate.route.identifier;

import static org.moderncampus.integration.Constants.*;
import static org.moderncampus.integration.route.identifier.Constants.*;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum BannerRouteIds implements IRouteId {

    V1_NAVIGATE_BANNER_GET_TERMS(GET_TERMS),

    V1_NAVIGATE_BANNER_GET_COURSES(GET_COURSES),

    V1_NAVIGATE_BANNER_GET_SECTIONS(GET_SECTIONS),

    V1_NAVIGATE_BANNER_GET_ACADEMIC_PROGRAMS(GET_ACADEMIC_PROGRAMS);

    String contextPath;

    String id;

    BannerRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, NAVIGATE_SYSTEM_ID, "*", BANNER_SYSTEM_ID, contextPath});
    }
}
