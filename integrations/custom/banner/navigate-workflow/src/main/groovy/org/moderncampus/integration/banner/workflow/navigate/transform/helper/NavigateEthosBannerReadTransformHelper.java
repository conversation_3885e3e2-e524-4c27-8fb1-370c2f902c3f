package org.moderncampus.integration.banner.workflow.navigate.transform.helper;

import org.apache.camel.Exchange;
import org.moderncampus.integration.banner.workflow.navigate.transform.NavigateEthosBannerReadTransforms;
import org.moderncampus.integration.navigate.dto.NavigateAcademicProgram;
import org.moderncampus.integration.navigate.dto.NavigateCourse;
import org.moderncampus.integration.navigate.dto.NavigateTerm;
import org.moderncampus.integration.transform.TransformContext;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class NavigateEthosBannerReadTransformHelper {

    public static final String METHOD_MAP_TERM = "mapTerm";
    public static final String METHOD_MAP_ACADEMIC_PROGRAM = "mapAcademicProgram";
    public static final String METHOD_MAP_COURSE = "mapCourse";

    NavigateEthosBannerReadTransforms readTransforms;

    NavigateTerm mapTerm(String body) {
        return readTransforms.getNavTermTransformer().transform(null, body);
    }

    NavigateAcademicProgram mapAcademicProgram(String body) {
        return readTransforms.getNavAcadProgTransformer().transform(null, body);
    }

    NavigateCourse mapCourse(Exchange exchange) {
        TransformContext ctx = new TransformContext(exchange.getProperties());
        return readTransforms.getNavCourseTransformer().transform(ctx, exchange.getMessage().getBody(String.class));
    }

}
