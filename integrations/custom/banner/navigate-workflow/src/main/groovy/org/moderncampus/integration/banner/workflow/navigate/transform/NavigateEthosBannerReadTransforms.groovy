package org.moderncampus.integration.banner.workflow.navigate.transform

import groovy.json.JsonSlurper
import org.moderncampus.integration.banner.component.internal.APIResource
import org.moderncampus.integration.banner.workflow.route.Constants
import org.moderncampus.integration.navigate.dto.NavigateAcademicProgram
import org.moderncampus.integration.navigate.dto.NavigateCourse
import org.moderncampus.integration.navigate.dto.NavigateTerm
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.DateTimeFormatters.mapDateTime

@Component
class NavigateEthosBannerReadTransforms {

    ITransformer<String, NavigateTerm> navTermTransformer = (ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        NavigateTerm navTerm = new NavigateTerm()
        navTerm.with {
            code = rootNode['code']
            name = rootNode['title']
            startDate = mapDateTime(rootNode['startOn'] as String)?.toLocalDate()
            endDate = mapDateTime(rootNode['endOn'] as String)?.toLocalDate()
        }
        navTerm.dynamicProperties.put("category", rootNode['type'])
        return navTerm
    }

    ITransformer<String, NavigateAcademicProgram> navAcadProgTransformer = (ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        NavigateAcademicProgram navAcadProg = new NavigateAcademicProgram()
        if (rootNode['status'] != 'active') {
            return null
        }
        navAcadProg.with {
            code = rootNode['code']
            title = rootNode['title']
        }
        return navAcadProg
    }

    ITransformer<String, NavigateCourse> navCourseTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        Map<APIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        NavigateCourse navCourse = new NavigateCourse()
        navCourse.with {
            id = rootNode['id']
            code = ethosAssocCacheMap[APIResource.SUBJECTS]?[rootNode['subject']['id']]?['abbreviation']
            number = rootNode['number']
            rootNode['titles'].findAll {
                def courseTitleTypeObj = ethosAssocCacheMap[APIResource.COURSE_TITLE_TYPES]?[it['type']['id']]
                return courseTitleTypeObj && courseTitleTypeObj['code'] == 'short'
            }.each {
                def courseTitleTypeObj = ethosAssocCacheMap[APIResource.COURSE_TITLE_TYPES]?[it['type']['id']]
                title = it['value']
            }
            if (rootNode['owningInstitutionUnits']) {
                String institutionId = (rootNode['owningInstitutionUnits'] as List)[0]['institutionUnit']['id']
                faculty = ethosAssocCacheMap[APIResource.EDUCATIONAL_INSTITUTION_UNITS]?[institutionId]?['title']
            }
            description = rootNode['description']
            if (rootNode['credits'] as List) {
                creditWeight = (rootNode['credits'] as List)[0]['minimum'] as Integer
            }
        }
        return navCourse
    }
}
