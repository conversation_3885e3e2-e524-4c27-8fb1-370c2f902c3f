package org.moderncampus.integration.banner.webservice.schema.navigate;

import java.util.List;
import java.util.Map;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.navigate.dto.NavigateTerm;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

public class NavigateSchemas {

    @Schema(name = "Get Terms Response")
    public static class GetTermsResponse extends IntegrationResponse<List<? extends NavigateTerm>> {

        @Override
        @Schema(type = "array",
                implementation = BannerNavTerm.class
        )
        public List<? extends NavigateTerm> getData() {
            return super.getData();
        }
    }

    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Schema(name = "NavigateTerm")
    public static class BannerNavTerm extends NavigateTerm {

        Map<String, AnyValue> extendedFields;

        @JsonProperty("dynamicProperties")
        @Schema(description = "Extended Fields that are included in the response. "
                + " \n **Additional Field Mappings**: "
                + " \n When mapped from the ethos API (administrative-periods), the 'type' field is mapped to category")
        public Map<String, AnyValue> getExtendedFields() {
            return extendedFields;
        }

    }

    @Schema(name = "AnyValue", description = "Extended Field Value Type (any)")
    public static class AnyValue {

    }

}
