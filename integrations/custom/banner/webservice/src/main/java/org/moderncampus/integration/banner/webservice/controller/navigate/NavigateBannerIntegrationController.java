package org.moderncampus.integration.banner.webservice.controller.navigate;

import static org.moderncampus.integration.Constants.*;

import java.util.List;
import java.util.Map;

import org.moderncampus.integration.banner.webservice.schema.navigate.NavigateSchemas;
import org.moderncampus.integration.banner.webservice.service.navigate.NavigateBannerIntegrationService;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.navigate.dto.NavigateAcademicProgram;
import org.moderncampus.integration.navigate.dto.NavigateTerm;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RestController
@RequestMapping("integration/" + VERSION_1 + "/" + NAVIGATE_SYSTEM_ID + "/{schoolId}/"
        + BANNER_SYSTEM_ID)
@Tag(name = "Navigate Banner Integration API", description = "Manages integration requests from Navigate to Banner.")
@Validated
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "apiToken")
public class NavigateBannerIntegrationController {

    static final String TERMS = "terms";
    static final String COURSES = "courses";
    static final String SECTIONS = "sections";
    static final String ACADEMIC_PROGRAMS = "academic-programs";

    NavigateBannerIntegrationService service;

    @Operation(summary = "Get all Terms From Banner")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful retrieval of terms",
                    content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = NavigateSchemas.GetTermsResponse.class))}),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = TERMS)
    public IntegrationResponse<List<NavigateTerm>> getTerms(
            @ParameterObject IntegrationRequestContext requestContextWS) throws Exception {
        return service.getTerms();
    }

    @Operation(summary = "Get all Courses From Banner")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = COURSES)
    public IntegrationResponse<ObjectNode> getCourses(
            @ParameterObject IntegrationRequestContext requestContextWS) throws Exception {
        return service.getCourses();
    }

    @Operation(summary = "Search Courses On Banner")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = COURSES + "/query")
    @Parameter(name = "TERM_ID", schema = @Schema(type = "string"), required = false)
    public IntegrationResponse<ObjectNode> searchCourses(
            @ParameterObject IntegrationRequestContext requestContextWS,
            @RequestParam(required = false) Map<String, Object> requestParams) throws Exception {
        return service.searchCourses(requestParams);
    }

    @Operation(summary = "Get all Sections From Banner")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = SECTIONS)
    public IntegrationResponse<ObjectNode> getSections(
            @ParameterObject IntegrationRequestContext requestContextWS) throws Exception {
        return service.getSections();
    }

    @Operation(summary = "Get all Academic Programs From Banner")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful retrieval of Academic Programs",
                    content = {
                            @Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = ACADEMIC_PROGRAMS)
    public IntegrationResponse<List<NavigateAcademicProgram>> getAcademicPrograms(
            @ParameterObject IntegrationRequestContext requestContextWS) throws Exception {
        return service.getAcademicPrograms();
    }
}
