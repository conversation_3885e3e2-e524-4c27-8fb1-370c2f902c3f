package org.moderncampus.integration.banner.webservice.service.navigate;

import static org.moderncampus.integration.banner.workflow.navigate.route.identifier.BannerRouteIds.*;

import java.util.List;
import java.util.Map;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.navigate.dto.NavigateAcademicProgram;
import org.moderncampus.integration.navigate.dto.NavigateTerm;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class NavigateBannerIntegrationService extends BaseIntegrationService {

    public IntegrationResponse<List<NavigateTerm>> getTerms() throws Exception {

        return executeRoute(V1_NAVIGATE_BANNER_GET_TERMS, null, null);
    }

    public IntegrationResponse<List<NavigateAcademicProgram>> getAcademicPrograms() throws Exception {

        return executeRoute(V1_NAVIGATE_BANNER_GET_ACADEMIC_PROGRAMS, null, null);
    }

    public IntegrationResponse<ObjectNode> getCourses() throws Exception {
        return executeRoute(V1_NAVIGATE_BANNER_GET_COURSES, null, null);
    }

    public IntegrationResponse<ObjectNode> searchCourses(Map<String, Object> requestParams) throws Exception {
        return searchEntities(new BasePaginationRequest(), V1_NAVIGATE_BANNER_GET_COURSES, requestParams, null);
    }

    public IntegrationResponse<ObjectNode> getSections() throws Exception {
        return executeRoute(V1_NAVIGATE_BANNER_GET_SECTIONS, null, null);
    }

}
