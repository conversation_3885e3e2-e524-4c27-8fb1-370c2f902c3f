package org.moderncampus.integration.webservice.core.colleague.course;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleagueCourseIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("colleague.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("colleague.cloud.integration.auth.useHttp", () -> true);
        registry.add("colleague.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypes,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneAvailabilities,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypes,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypes,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypes,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypes,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatuses,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatuses,
            @Value("classpath:colleague/ethos/EthosGeSectionGradeTypesResp.json") Resource ethosSectionGradeTypes
            )
            throws Exception {

        String strippedToken = COLLEAGUE_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities",
                ethosGetEmergencyContactPhoneAvailabilities));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypes));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-grade-types", ethosSectionGradeTypes));

    }

    @Test
    void colleaguePostCourse(@Value("classpath:colleague/ethos/EthosPostCourseResp.json") Resource ethosPostCourseResp,
            @Value("classpath:colleague/ethos/EthosPostCourseReq.json") Resource ethosPostCourseReq,
            @Value("classpath:colleague/CreateSingleCourseReq.json") Resource createSingleCourseReq,
            @Value("classpath:colleague/CreateSingleCourseResp.json") Resource createSingleCourseResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/courses", ethosPostCourseResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v16+json")));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/courses").contentType("application/json")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createSingleCourseReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSingleCourseResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/courses")).withRequestBody(
                equalToJson(asString(ethosPostCourseReq))));
    }

    @Test
    void colleagueGetCourseById(
            @Value("classpath:colleague/ethos/EthosGetCourseById.json") Resource ethosGetCourseByIdResp,
            @Value("classpath:colleague/GetCourseById.json") Resource getCourseByIdResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(GET, ".*/courses/49aef0f9-f017-4cf8-b2c0-d260da026d72", ethosGetCourseByIdResp));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/courses/49aef0f9-f017-4cf8-b2c0-d260da026d72")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getCourseByIdResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleaguePatchCourse(@Value("classpath:colleague/ethos/EthosPutCourseResp.json") Resource ethosPutCourseResp,
            @Value("classpath:colleague/ethos/EthosGetForPutCourseResp.json") Resource ethosGetCourseResp,
            @Value("classpath:colleague/ethos/EthosPutCourseReq.json") Resource ethosPutCourseReq,
            @Value("classpath:colleague/UpdateSingleCourseReq.json") Resource updateSingleCourseReq,
            @Value("classpath:colleague/UpdateSingleCourseResp.json") Resource updateSingleCourseResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(PUT, ".*/courses/0330adc7-3aa2-4dc5-9b77-af3b8f045b71", ethosPutCourseResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/courses/0330adc7-3aa2-4dc5-9b77-af3b8f045b71", ethosGetCourseResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.patch("/uapi/integration/v1/courses/0330adc7-3aa2-4dc5-9b77-af3b8f045b71")
                                .contentType("application/json")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateSingleCourseReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSingleCourseResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/courses/0330adc7-3aa2-4dc5-9b77-af3b8f045b71")).withRequestBody(
                equalToJson(asString(ethosPutCourseReq))));
    }


}
