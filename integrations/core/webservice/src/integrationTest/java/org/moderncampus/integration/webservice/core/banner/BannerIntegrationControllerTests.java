package org.moderncampus.integration.webservice.core.banner;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class BannerIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${banner.bearer.token}")
    private String BANNER_BEARER_TOKEN;

    static final String GUID_PATTERN = "[a-zA-Z0-9]{8}(-[a-zA-Z0-9]{4}){3}-[a-zA-Z0-9]{12}";
    static final String BREADCRUMB_PATTERN = "[a-zA-Z0-9]{15}-[a-zA-Z0-9]{16}";

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("banner.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("banner.cloud.integration.auth.useHttp", () -> true);
        registry.add("banner.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(@Value("classpath:banner/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:banner/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:banner/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:banner/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetAdminInsMethodResp,
            @Value("classpath:banner/ethos/EthosGetSectionStatusesResp.json") Resource ethosGetSectionStatusesResp,
            @Value("classpath:banner/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp)
            throws Exception {

        String strippedToken = BANNER_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetAdminInsMethodResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-statuses", ethosGetSectionStatusesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
    }


    @Test
    void bannerAcademicPeriodBadRequestInvalidDateParameters() throws Exception {
        this.mockMvc.perform(
                        get("/uapi/integration/v1/academic-periods?startOn=2025-02-25")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    void bannerAcademicPeriodSearchAll(
            @Value("classpath:banner/ethos/EthosGetAcademicPeriodsResp.json") Resource ethosGetAcademicPeriodsResp,
            @Value("classpath:banner/GetAcademicPeriodsResp.json") Resource getAcademicPeriodsResponse
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/academic-periods.*", ethosGetAcademicPeriodsResp));

        this.mockMvc.perform(get("/uapi/integration/v1/academic-periods")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getAcademicPeriodsResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void bannerGetInstructors(
            @Value("classpath:banner/ethos/EthosGetPersonsInstructorsResp.json") Resource ethosGetPersonsResp,
            @Value("classpath:banner/ethos/EthosGetInstructorResp_1.json") Resource ethosGetInstructorResp1,
            @Value("classpath:banner/ethos/EthosGetInstructorResp_2.json") Resource ethosGetInstructorResp2,
            @Value("classpath:banner/ethos/EthosGetInstructorResp_3.json") Resource ethosGetInstructorResp3,
            @Value("classpath:banner/GetInstructorsResp.json") Resource getInstructorsResp) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/persons\\?.*", ethosGetPersonsResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/instructors\\?.*17ce089b-918e-42f6-bf23-25ed27d796b0.*", ethosGetInstructorResp1));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/instructors\\?.*8b3fed38-9bef-4ec5-8b70-386cf96f681f.*", ethosGetInstructorResp2));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/instructors\\?.*969c08aa-5e67-4e28-9d08-122db7d16e51.*", ethosGetInstructorResp3));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/instructors?pageOffset=0&pageSize=3").header(AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getInstructorsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void bannerGetInstructorById(
            @Value("classpath:banner/ethos/EthosGetPersonInstructorResp.json") Resource ethosGetPersonInstructorResp,
            @Value("classpath:banner/ethos/EthosGetInstructorResp_3.json") Resource ethosGetInstructorResp3,
            @Value("classpath:banner/GetSingleInstructorResp.json") Resource getSectionScheduleByIdResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/persons/969c08aa-5e67-4e28-9d08-122db7d16e51",
                ethosGetPersonInstructorResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/instructors\\?.*969c08aa-5e67-4e28-9d08-122db7d16e51.*", ethosGetInstructorResp3));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/instructors/969c08aa-5e67-4e28-9d08-122db7d16e51").header(
                                AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionScheduleByIdResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

    }

    @Test
    void bannerGetOrganizationalUnits(
            @Value("classpath:banner/ethos/EthosGetEducationalInstitutionUnitsResp.json") Resource ethosGetUnitsResp,
            @Value("classpath:banner/GetOrganizationalUnitsResp.json") Resource getOrgUnitsResp) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/educational-institution-units", ethosGetUnitsResp));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/organizational-units").header(AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getOrgUnitsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }


    @Test
    void bannerGetHealthCheck(
            @Value("classpath:banner/ethos/EthosGetEducationalInstitutionUnitsResp.json") Resource ethosGetUnitsResp,
            @Value("classpath:banner/GetHealthCheckResp.json") Resource getHealthCheckResp) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/educational-institution-units", ethosGetUnitsResp));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/health-check").header(AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getHealthCheckResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

}
