package org.moderncampus.integration.webservice.core.colleague.person;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleaguePersonIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("colleague.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("colleague.cloud.integration.auth.useHttp", () -> true);
        registry.add("colleague.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypes,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneAvailabilities,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypes,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypes,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypes,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypes,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatuses,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatuses,
            @Value("classpath:colleague/ethos/EthosGeSectionGradeTypesResp.json") Resource ethosSectionGradeTypes
    )
            throws Exception {

        String strippedToken = COLLEAGUE_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities",
                ethosGetEmergencyContactPhoneAvailabilities));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypes));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-grade-types", ethosSectionGradeTypes));

    }

    @Test
    void colleaguePatchPerson(
            @Value("classpath:colleague/ethos/EthosPostPersonResp.json") Resource ethosPostPersonResp,
            @Value("classpath:colleague/ethos/EthosPutPersonResp.json") Resource ethosPutPersonResp,
            @Value("classpath:colleague/ethos/EthosPutPersonReq.json") Resource ethosPutPersonReq,
            @Value("classpath:colleague/UpdatePersonReq.json") Resource updatePersonReq,
            @Value("classpath:colleague/UpdatePersonResp.json") Resource updatePersonResp,
            @Value("classpath:colleague/ethos/EthosPutPerson-EthosGetAddressByIdResp.json") Resource ethosGetAddressByIdForPutResp,
            @Value("classpath:colleague/ethos/EthosPutAddressReq.json") Resource ethosPutAddressReq,
            @Value("classpath:colleague/ethos/EthosPutAddressResp.json") Resource ethosPutAddressResp,
            @Value("classpath:colleague/ethos/EthosPutPerson-EthosPostPersonEmergencyContactReq.json") Resource ethosPostEmergencyContactReq,
            @Value("classpath:colleague/ethos/EthosPutPerson-EthosPostPersonEmergencyContactResp.json") Resource ethosPostEmergencyContactResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(GET, ".*/persons/bbea55c6-b321-4783-90cf-09d55a080000", ethosPostPersonResp));
        wireMockServer.stubFor(
                makeRequest(PUT, ".*/persons/bbea55c6-b321-4783-90cf-09d55a080000", ethosPutPersonResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/addresses/e8d0c80e-7120-4151-971a-52eae5f63391", ethosGetAddressByIdForPutResp));
        wireMockServer.stubFor(
                makeRequest(PUT, ".*/addresses/e8d0c80e-7120-4151-971a-52eae5f63391", ethosPutAddressResp));
        wireMockServer.stubFor(
                delete(urlMatching(".*/person-emergency-contacts/53687ebf-ff0e-45be-99a4-e15ddeda4c1d"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );
        wireMockServer.stubFor(makeRequest(POST, ".*/person-emergency-contacts", ethosPostEmergencyContactResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .patch("/uapi/integration/v1/persons/bbea55c6-b321-4783-90cf-09d55a080000")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updatePersonReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updatePersonResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/persons/bbea55c6-b321-4783-90cf-09d55a080000")).withRequestBody(
                equalToJson(asString(ethosPutPersonReq))));

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/addresses/e8d0c80e-7120-4151-971a-52eae5f63391")).withRequestBody(
                equalToJson(asString(ethosPutAddressReq))));
        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/person-emergency-contacts/53687ebf-ff0e-45be-99a4-e15ddeda4c1d")));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/person-emergency-contacts")).withRequestBody(
                equalToJson(asString(ethosPostEmergencyContactReq))));
    }

    @Test
    void colleaguePostPersonEmergencyContact(
            @Value("classpath:colleague/ethos/EthosPostPersonEmergencyContactResp.json") Resource ethosPostPersonEmergencyContactResp,
            @Value("classpath:colleague/ethos/EthosPostPersonEmergencyContactReq.json") Resource ethosPersonEmergencyContactReq,
            @Value("classpath:colleague/CreatePersonEmergencyContactReq.json") Resource createPersonEmergencyContactReq,
            @Value("classpath:colleague/CreatePersonEmergencyContactResp.json") Resource createPersonEmergencyContactResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/person-emergency-contacts", ethosPostPersonEmergencyContactResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/person-emergency-contacts")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createPersonEmergencyContactReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createPersonEmergencyContactResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/person-emergency-contacts")).withRequestBody(
                equalToJson(asString(ethosPersonEmergencyContactReq))));
    }

    @Test
    void colleagueDeletePersonEmergencyContact(
            @Value("classpath:colleague/DeletePersonEmergencyContactResp.json") Resource apiResponse)
            throws Exception {

        wireMockServer.stubFor(
                delete(urlMatching(".*/person-emergency-contacts/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );

        this.mockMvc.perform(
                        MockMvcRequestBuilders.delete(
                                        "/uapi/integration/v1/person-emergency-contacts/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(apiResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/person-emergency-contacts/09352284-8e81-4a6a-add8-4bcfeae126e1")));
    }

    @Test
    void colleaguePostPerson(
            @Value("classpath:colleague/ethos/EthosPostPersonResp.json") Resource ethosPostPersonResp,
            @Value("classpath:colleague/ethos/EthosPostPersonReq.json") Resource ethosPersonReq,
            @Value("classpath:colleague/CreatePersonReq.json") Resource createPersonReq,
            @Value("classpath:colleague/CreatePersonResp.json") Resource createPersonResp,
            @Value("classpath:colleague/ethos/EthosPostPerson-EmergencyContactResp.json") Resource ethosPostEmergencyResp,
            @Value("classpath:colleague/ethos/EthosPostPerson-EmergencyContactReq.json") Resource ethosPostEmergencyReq
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/persons", ethosPostPersonResp));
        wireMockServer.stubFor(makeRequest(POST, ".*/person-emergency-contacts", ethosPostEmergencyResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/persons")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createPersonReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createPersonResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/persons")).withRequestBody(
                equalToJson(asString(ethosPersonReq))));

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/person-emergency-contacts")).withRequestBody(
                equalToJson(asString(ethosPostEmergencyReq))));
    }

    @Test
    void colleaguePostCheckDuplicatePerson(
            @Value("classpath:colleague/ethos/EthosPostPersonResp.json") Resource ethosPostPersonResp,
            @Value("classpath:colleague/ethos/EthosPostCheckDuplicatePersonReq.json") Resource ethosPersonReq,
            @Value("classpath:colleague/CreatePersonReq.json") Resource createPersonReq,
            @Value("classpath:colleague/CreatePersonResp.json") Resource createPersonResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/persons", ethosPostPersonResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/person-duplicate-check")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createPersonReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createPersonResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/persons")).withRequestBody(
                equalToJson(asString(ethosPersonReq))));
    }

    @Test
    void colleaguePostOrganization(
            @Value("classpath:colleague/ethos/EthosPostOrgResp.json") Resource ethosPostOrganizationResp,
            @Value("classpath:colleague/ethos/EthosPostOrganizationReq.json") Resource ethosOrganizationReq,
            @Value("classpath:colleague/CreateOrganizationReq.json") Resource createOrganizationReq,
            @Value("classpath:colleague/CreateOrganizationResp.json") Resource createOrganizationResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/organizations", ethosPostOrganizationResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/organizations")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createOrganizationReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createOrganizationResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/organizations")).withRequestBody(
                equalToJson(asString(ethosOrganizationReq))));
    }

    @Test
    void colleaguePatchOrganization(
            @Value("classpath:colleague/ethos/EthosPutOrgResp.json") Resource ethosPutOrganizationResp,
            @Value("classpath:colleague/ethos/EthosGetOrgResp.json") Resource ethosGetOrganizationResp,
            @Value("classpath:colleague/ethos/EthosPutOrganizationReq.json") Resource ethosPutOrganizationReq,
            @Value("classpath:colleague/UpdateOrganizationReq.json") Resource updateOrganizationReq,
            @Value("classpath:colleague/UpdateOrganizationResp.json") Resource updateOrganizationResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(PUT, ".*/organizations/e28f655c-17bd-42db-a433-280cddc915cc", ethosPutOrganizationResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/organizations/e28f655c-17bd-42db-a433-280cddc915cc", ethosGetOrganizationResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .patch("/uapi/integration/v1/organizations/e28f655c-17bd-42db-a433-280cddc915cc")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateOrganizationReq))
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateOrganizationResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(WireMock.urlMatching(".*/organizations/e28f655c-17bd-42db-a433-280cddc915cc"))
                .withRequestBody(equalToJson(asString(ethosPutOrganizationReq))));
    }

    @Test
    void colleagueGetPerson(
            @Value("classpath:colleague/ethos/EthosGetPersonBySearchPersonIdResp.json") Resource ethosPersons,
            @Value("classpath:colleague/GetPersonByPersonResp.json") Resource getAddressResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(GET, ".*/persons.*", ethosPersons));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/uapi/integration/v1/persons?colleaguePersonId=1000007")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getAddressResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

}
