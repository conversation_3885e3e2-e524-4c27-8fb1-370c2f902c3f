package org.moderncampus.integration.webservice.core.banner.course;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class BannerCourseIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${banner.bearer.token}")
    private String BANNER_BEARER_TOKEN;

    static final String GUID_PATTERN = "[a-zA-Z0-9]{8}(-[a-zA-Z0-9]{4}){3}-[a-zA-Z0-9]{12}";
    static final String BREADCRUMB_PATTERN = "[a-zA-Z0-9]{15}-[a-zA-Z0-9]{16}";

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("banner.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("banner.cloud.integration.auth.useHttp", () -> true);
        registry.add("banner.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(@Value("classpath:banner/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:banner/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:banner/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:banner/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetAdminInsMethodResp,
            @Value("classpath:banner/ethos/EthosGetSectionStatusesResp.json") Resource ethosGetSectionStatusesResp,
            @Value("classpath:banner/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp)
            throws Exception {

        String strippedToken = BANNER_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetAdminInsMethodResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-statuses", ethosGetSectionStatusesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
    }

    @Test
    void bannerPostCourse(@Value("classpath:banner/ethos/EthosPostCourseResp.json") Resource ethosPostCourseResp,
            @Value("classpath:banner/ethos/EthosPostCourseReq.json") Resource ethosPostCourseReq,
            @Value("classpath:banner/CreateSingleCourseReq.json") Resource createSingleCourseReq,
            @Value("classpath:banner/CreateSingleCourseResp.json") Resource createSingleCourseResp) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/courses", ethosPostCourseResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/courses").contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(createSingleCourseReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSingleCourseResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/courses")).withRequestBody(
                equalToJson(asString(ethosPostCourseReq))));
    }

    @Test
    void bannerPatchCourse(@Value("classpath:banner/ethos/EthosPutCourseResp.json") Resource ethosPutCourseResp,
            @Value("classpath:banner/ethos/EthosGetForPutCourseResp.json") Resource ethosGetCourseResp,
            @Value("classpath:banner/ethos/EthosPutCourseReq.json") Resource ethosPutCourseReq,
            @Value("classpath:banner/UpdateSingleCourseReq.json") Resource updateSingleCourseReq,
            @Value("classpath:banner/UpdateSingleCourseResp.json") Resource updateSingleCourseResp) throws Exception {

        wireMockServer.stubFor(makeRequest(PUT, ".*/courses/6cf33fd6-0f2e-4a77-9b70-9ea6aa9021e2", ethosPutCourseResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/courses/6cf33fd6-0f2e-4a77-9b70-9ea6aa9021e2", ethosGetCourseResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.patch("/uapi/integration/v1/courses/6cf33fd6-0f2e-4a77-9b70-9ea6aa9021e2")
                                .contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(updateSingleCourseReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSingleCourseResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/courses/6cf33fd6-0f2e-4a77-9b70-9ea6aa9021e2")).withRequestBody(
                equalToJson(asString(ethosPutCourseReq))));
    }

}
