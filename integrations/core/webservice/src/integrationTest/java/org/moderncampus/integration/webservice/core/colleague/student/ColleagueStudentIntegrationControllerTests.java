package org.moderncampus.integration.webservice.core.colleague.student;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleagueStudentIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("colleague.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("colleague.cloud.integration.auth.useHttp", () -> true);
        registry.add("colleague.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypes,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneAvailabilities,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypes,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypes,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypes,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypes,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatuses,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatuses,
            @Value("classpath:colleague/ethos/EthosGeSectionGradeTypesResp.json") Resource ethosSectionGradeTypes
            )
            throws Exception {

        String strippedToken = COLLEAGUE_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities",
                ethosGetEmergencyContactPhoneAvailabilities));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypes));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-grade-types", ethosSectionGradeTypes));

    }

    @Test
    void colleaguePostStudentCharges(
            @Value("classpath:colleague/ethos/EthosPostStudentChargeResp.json") Resource ethosPostStudentChargeResp,
            @Value("classpath:colleague/ethos/EthosPostStudentChargeReq.json") Resource ethosStudentChargeReq,
            @Value("classpath:colleague/CreateStudentChargeReq.json") Resource createStudentChargeReq,
            @Value("classpath:colleague/CreateStudentChargeResp.json") Resource createStudentChargeResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/student-charges", ethosPostStudentChargeResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/student-charges")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createStudentChargeReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createStudentChargeResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/student-charges")).withRequestBody(
                equalToJson(asString(ethosStudentChargeReq))));
    }

    @Test
    void colleaguePostStudentPayment(
            @Value("classpath:colleague/ethos/EthosPostStudentPaymentResp.json") Resource ethosPostStudentPaymentResp,
            @Value("classpath:colleague/ethos/EthosPostStudentPaymentReq.json") Resource ethosStudentPaymentReq,
            @Value("classpath:colleague/CreateStudentPaymentReq.json") Resource createStudentPaymentReq,
            @Value("classpath:colleague/CreateStudentPaymentResp.json") Resource createStudentPaymentResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/student-payments", ethosPostStudentPaymentResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/student-payments")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createStudentPaymentReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createStudentPaymentResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/student-payments")).withRequestBody(
                equalToJson(asString(ethosStudentPaymentReq))));
    }

    @Test
    void colleaguePostStudentEnrollment(
            @Value("classpath:colleague/ethos/EthosPostStudentEnrollmentResp.json") Resource ethosPostStudentEnrollmentResp,
            @Value("classpath:colleague/ethos/EthosPostStudentEnrollmentReq.json") Resource ethosStudentEnrollmentReq,
            @Value("classpath:colleague/CreateStudentEnrollmentReq.json") Resource createStudentEnrollmentReq,
            @Value("classpath:colleague/CreateStudentEnrollmentResp.json") Resource createStudentEnrollmentResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/section-registrations", ethosPostStudentEnrollmentResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/student-enrollments")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createStudentEnrollmentReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createStudentEnrollmentResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-registrations")).withRequestBody(
                equalToJson(asString(ethosStudentEnrollmentReq))));
    }

    @Test
    void colleaguePutStudentEnrollment(
            @Value("classpath:colleague/ethos/EthosPutStudentEnrollmentResp.json") Resource ethosPutStudentEnrollmentResp,
            @Value("classpath:colleague/ethos/EthosPutStudentEnrollmentReq.json") Resource ethosStudentEnrollmentReq,
            @Value("classpath:colleague/UpdateStudentEnrollmentReq.json") Resource updateStudentEnrollmentReq,
            @Value("classpath:colleague/UpdateStudentEnrollmentResp.json") Resource updateStudentEnrollmentResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(PUT, ".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000",
                ethosPutStudentEnrollmentResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000",
                ethosPutStudentEnrollmentResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/student-enrollments/bbea55c6-b321-4783-90cf-09d55a080000")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateStudentEnrollmentReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateStudentEnrollmentResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000")).withRequestBody(
                equalToJson(asString(ethosStudentEnrollmentReq))));
    }


    @Test
    void colleaguePutStudentEnrollmentDropped(
            @Value("classpath:colleague/ethos/EthosPutStudentEnrollmentDroppedResp.json") Resource ethosPutStudentEnrollmentDroppedResp,
            @Value("classpath:colleague/ethos/EthosPutStudentEnrollmentDroppedReq.json") Resource ethosStudentEnrollmentDroppedReq,
            @Value("classpath:colleague/UpdateStudentEnrollmentDroppedReq.json") Resource updateStudentEnrollmentDroppedReq
    ) throws Exception {

        // Setup WireMock stubs for dropped enrollment scenario
        wireMockServer.stubFor(makeRequest(PUT, ".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000",
                ethosPutStudentEnrollmentDroppedResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000",
                ethosPutStudentEnrollmentDroppedResp));

        // Execute the PUT request for dropped enrollment
        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/student-enrollments/bbea55c6-b321-4783-90cf-09d55a080000")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                        .content(asString(updateStudentEnrollmentDroppedReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty());


        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000"))
                .withRequestBody(equalToJson(asString(ethosStudentEnrollmentDroppedReq)))
                .withRequestBody(matchingJsonPath("$.status.registrationStatus", equalTo("notRegistered")))
                .withRequestBody(matchingJsonPath("$.status.sectionRegistrationStatusReason", equalTo("dropped"))));
    }


    @Test
    void verifyBugFixForDroppedEnrollmentStatusMapping() throws Exception {
        // Test data for dropped enrollment
        String droppedEnrollmentRequest = """
            {
              "data": {
                "person": "29f654fd-1f22-4d37-886c-d1c357df2b98",
                "academicLevel": "178fd725-b762-4fce-a46c-ffb554203b96",
                "section": "746181eb-4473-4cc9-92e3-3f5716b6de0f",
                "originalDate": "2025-06-01",
                "enrollmentStatus": "D",
                "enrollmentStatusDate": "2025-06-06"
              }
            }
            """;

        // Expected response from Colleague for dropped enrollment
        String expectedColleagueResponse = """
            {
              "id" : "bbea55c6-b321-4783-90cf-09d55a080000",
              "registrant" : { "id" : "29f654fd-1f22-4d37-886c-d1c357df2b98" },
              "section" : { "id" : "746181eb-4473-4cc9-92e3-3f5716b6de0f" },
              "academicLevel" : { "id" : "178fd725-b762-4fce-a46c-ffb554203b96" },
              "originallyRegisteredOn" : "2025-06-01",
              "statusDate" : "2025-06-06",
              "status" : {
                "registrationStatus" : "notRegistered",
                "sectionRegistrationStatusReason" : "dropped",
                "detail" : { "id" : "7aa5a27e-a7a4-44d6-8c83-a07c8fc53550" }
              }
            }
            """;

        // Setup WireMock to return the expected response
        wireMockServer.stubFor(put(urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(expectedColleagueResponse)));

        wireMockServer.stubFor(get(urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(expectedColleagueResponse)));

        // Execute the request
        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/student-enrollments/bbea55c6-b321-4783-90cf-09d55a080000")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                        .content(droppedEnrollmentRequest)
                ).andDo(print())
                .andExpect(status().isOk());


        wireMockServer.verify(putRequestedFor(
                urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000"))
                .withRequestBody(matchingJsonPath("$.status.registrationStatus", equalTo("notRegistered")))
                .withRequestBody(matchingJsonPath("$.status.sectionRegistrationStatusReason", equalTo("dropped")))
                .withRequestBody(matchingJsonPath("$.status.detail.id", equalTo("7aa5a27e-a7a4-44d6-8c83-a07c8fc53550"))));


        wireMockServer.verify(0, putRequestedFor(
                urlMatching(".*/section-registrations/bbea55c6-b321-4783-90cf-09d55a080000"))
                .withRequestBody(matchingJsonPath("$.status.registrationStatus", equalTo("notRegistered")))
                .withRequestBody(matchingJsonPath("$.status.sectionRegistrationStatusReason", equalTo("notRegistered"))));
    }

    @Test
    void colleaguePutStudent(
            @Value("classpath:colleague/ethos/EthosPutStudentResp.json") Resource ethosPutStudentResp,
            @Value("classpath:colleague/ethos/EthosPutStudentReq.json") Resource ethosStudentReq,
            @Value("classpath:colleague/UpdateStudentReq.json") Resource updateStudentReq,
            @Value("classpath:colleague/UpdateStudentResp.json") Resource updateStudentResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(PUT, ".*/students/8e2d86e5-fe3d-14a7-ed49-8f49e81a07d9", ethosPutStudentResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/students/8e2d86e5-fe3d-14a7-ed49-8f49e81a07d9", ethosPutStudentResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/students/8e2d86e5-fe3d-14a7-ed49-8f49e81a07d9")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateStudentReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateStudentResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/students/8e2d86e5-fe3d-14a7-ed49-8f49e81a07d9")).withRequestBody(
                equalToJson(asString(ethosStudentReq))));
    }

}
