package org.moderncampus.integration.webservice.core;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.intellij.lang.annotations.Language;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import com.github.tomakehurst.wiremock.http.HttpHeaders;

public class IntegrationControllerTestsHelper {

    public static String asString(Resource resource) throws IOException {
        return resource.getContentAsString(StandardCharsets.UTF_8);
    }

    /**
     * Builds a WireMock {@link MappingBuilder} with the specified HTTP method, URL regex, optional body, headers, and
     * status. This is the main method used to define the full request/response mapping.
     *
     * @param method     the HTTP method to match
     * @param urlRegex   the regular expression for the URL
     * @param body       the optional response body
     * @param headers    the optional response headers
     * @param httpStatus the optional HTTP status to return
     * @return a configured {@link MappingBuilder}
     */
    public static MappingBuilder makeRequest(
            RequestMethod method, @Language("RegExp") String urlRegex, String body,
            Map<String, String> headers, HttpStatus httpStatus) {

        List<HttpHeader> headerList = new ArrayList<>();
        if (headers == null || headers.isEmpty()) {
            headerList.add(new HttpHeader("Content-Type", "application/json"));
        }
        if (headers != null) {
            headers.forEach((key, value) -> headerList.add(new HttpHeader(key, value)));
        }
        ResponseDefinitionBuilder responseBuilder = aResponse().withHeaders(new HttpHeaders(headerList));
        if (body != null) {
            responseBuilder.withBody(body);
        }
        if (httpStatus != null) {
            responseBuilder.withStatus(httpStatus.value());
        }
        return WireMock.request(method.toString(), WireMock.urlMatching(urlRegex))
                .willReturn(responseBuilder);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with only method and URL.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex) {
        return makeRequest(method, urlRegex, null, null, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL and
     * body.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex, String body) {
        return makeRequest(method, urlRegex, body, null, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL and body
     * as a {@link Resource}.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex,
            Resource bodyResource) throws IOException {
        return makeRequest(method, urlRegex, asString(bodyResource), null, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL and
     * headers.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex,
            Map<String, String> headers) {
        return makeRequest(method, urlRegex, null, headers, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL, body and
     * headers.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex,
            String body, Map<String, String> headers) {
        return makeRequest(method, urlRegex, body, headers, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL, body
     * {@link Resource} and headers.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex,
            Resource bodyResource, Map<String, String> headers) throws IOException {
        return makeRequest(method, urlRegex, asString(bodyResource), headers, null);
    }

    /**
     * Helper method for {@link #makeRequest(RequestMethod, String, String, Map, HttpStatus)} with method, URL and
     * status.
     */
    public static MappingBuilder makeRequest(RequestMethod method, @Language("RegExp") String urlRegex,
            HttpStatus status) {
        return makeRequest(method, urlRegex, null, null, status);
    }

}
