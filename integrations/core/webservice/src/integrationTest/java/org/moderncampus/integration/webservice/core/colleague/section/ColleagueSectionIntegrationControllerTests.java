package org.moderncampus.integration.webservice.core.colleague.section;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleagueSectionIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("colleague.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("colleague.cloud.integration.auth.useHttp", () -> true);
        registry.add("colleague.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypes,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneAvailabilities,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypes,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypes,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypes,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypes,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatuses,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatuses,
            @Value("classpath:colleague/ethos/EthosGeSectionGradeTypesResp.json") Resource ethosSectionGradeTypes
            )
            throws Exception {

        String strippedToken = COLLEAGUE_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities",
                ethosGetEmergencyContactPhoneAvailabilities));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypes));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-grade-types", ethosSectionGradeTypes));

    }

    @Test
    void colleaguePostSection(
            @Value("classpath:colleague/ethos/EthosPostSectionReq.json") Resource ethosPostSectionReq,
            @Value("classpath:colleague/ethos/EthosPostSectionResp.json") Resource ethosPostSectionResp,
            @Value("classpath:colleague/CreateSectionReq.json") Resource createSectionReq,
            @Value("classpath:colleague/CreateSectionResp.json") Resource createSectionResp,
            @Value("classpath:colleague/ethos/EthosPostSectionScheduleReq.json") Resource ethosPostScheduleReq,
            @Value("classpath:colleague/ethos/EthosPostSectionSchedulesResp.json") Resource ethosPostSectionSchedulesResp,
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorResp.json") Resource ethosPostSectionInstructorResp,
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorReq.json") Resource ethosPostSectionInstructorReq,
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListResp.json") Resource ethosPostSectionCrossListResp,
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListReq.json") Resource ethosPostSectionCrossListReq
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/sections", ethosPostSectionResp));
        wireMockServer.stubFor(makeRequest(POST, ".*/section-instructors", ethosPostSectionInstructorResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));
        wireMockServer.stubFor(makeRequest(POST, ".*/instructional-events", ethosPostSectionSchedulesResp));
        wireMockServer.stubFor(makeRequest(POST, ".*/section-crosslists", ethosPostSectionCrossListResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/sections")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createSectionReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSectionResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/sections")).withRequestBody(
                equalToJson(asString(ethosPostSectionReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/instructional-events")).withRequestBody(
                equalToJson(asString(ethosPostScheduleReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-instructors")).withRequestBody(
                equalToJson(asString(ethosPostSectionInstructorReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-crosslists")).withRequestBody(
                equalToJson(asString(ethosPostSectionCrossListReq))));
    }

    @Test
    void colleaguePatchSection(
            @Value("classpath:colleague/ethos/EthosPutSectionReq.json") Resource ethosPutSectionReq,
            @Value("classpath:colleague/ethos/EthosPutSectionResp.json") Resource ethosPutSectionResp,
            @Value("classpath:colleague/UpdateSectionReq.json") Resource updateSectionReq,
            @Value("classpath:colleague/UpdateSectionResp.json") Resource updateSectionResp,
            @Value("classpath:colleague/ethos/EthosPostSectionScheduleReq.json") Resource ethosPostScheduleReq,
            @Value("classpath:colleague/ethos/EthosPostSectionSchedulesResp.json") Resource ethosPostSectionSchedulesResp,
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorResp.json") Resource ethosPostSectionInstructorResp,
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorReq.json") Resource ethosPostSectionInstructorReq,
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListResp.json") Resource ethosPostSectionCrossListResp,
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListReq.json") Resource ethosPostSectionCrossListReq,
            @Value("classpath:colleague/ethos/EthosResourceEmptyResp.json") Resource ethosEmptyResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(PUT, ".*/sections/65a98d56-c7ce-4402-80e5-511bc61ce32c", ethosPutSectionResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/sections/65a98d56-c7ce-4402-80e5-511bc61ce32c", ethosPutSectionResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-instructors.*", ethosEmptyResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));
        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-events.*", ethosEmptyResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-crosslists.*", ethosEmptyResp));
        wireMockServer.stubFor(makeRequest(POST, ".*/section-instructors", ethosPostSectionInstructorResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));
        wireMockServer.stubFor(makeRequest(POST, ".*/instructional-events", ethosPostSectionSchedulesResp));
        wireMockServer.stubFor(makeRequest(POST, ".*/section-crosslists", ethosPostSectionCrossListResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .patch("/uapi/integration/v1/sections/65a98d56-c7ce-4402-80e5-511bc61ce32c")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateSectionReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSectionResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/sections/65a98d56-c7ce-4402-80e5-511bc61ce32c")).withRequestBody(
                equalToJson(asString(ethosPutSectionReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/instructional-events")).withRequestBody(
                equalToJson(asString(ethosPostScheduleReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-instructors")).withRequestBody(
                equalToJson(asString(ethosPostSectionInstructorReq))));
        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-crosslists")).withRequestBody(
                equalToJson(asString(ethosPostSectionCrossListReq))));
    }

    @Test
    void colleaguePostSectionSchedule(
            @Value("classpath:colleague/ethos/EthosPostSectionScheduleReq.json") Resource ethosPostScheduleReq,
            @Value("classpath:colleague/ethos/EthosPostSectionSchedulesResp.json") Resource ethosPostSectionSchedulesResp,
            @Value("classpath:colleague/CreateSectionScheduleReq.json") Resource createScheduleReq,
            @Value("classpath:colleague/CreateSectionScheduleResp.json") Resource createScheduleResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/instructional-events", ethosPostSectionSchedulesResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/section-schedules")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createScheduleReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createScheduleResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/instructional-events")).withRequestBody(
                equalToJson(asString(ethosPostScheduleReq))));
    }

    @Test
    void colleagueGetSectionInstructorAssignments(
            @Value("classpath:colleague/ethos/EthosGetSectionInstructors.json") Resource ethosGetSectionInstructorsResp,
            @Value("classpath:colleague/GetSectionInstructorsResp.json") Resource getSectionInstructorsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/section-instructors", ethosGetSectionInstructorsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/section-instructor-assignments")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionInstructorsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }


    @Test
    void colleaguePostSectionInstructorAssignment(
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorResp.json") Resource ethosPostSectionInstructorResp,
            @Value("classpath:colleague/ethos/EthosPostSectionInstructorReq.json") Resource ethosPostSectionInstructorReq,
            @Value("classpath:colleague/CreateSingleSectionInstructorReq.json") Resource createSingleSectionInstructorReq,
            @Value("classpath:colleague/CreateSingleSectionInstructorResp.json") Resource createSingleSectionInstructorResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/section-instructors", ethosPostSectionInstructorResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/section-instructor-assignments")
                                .contentType("application/json")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                                .content(asString(createSingleSectionInstructorReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSingleSectionInstructorResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-instructors")).withRequestBody(
                equalToJson(asString(ethosPostSectionInstructorReq))));
    }

    @Test
    void colleaguePostSectionCrossList(
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListResp.json") Resource ethosPostSectionCrossListResp,
            @Value("classpath:colleague/ethos/EthosPostSectionCrossListReq.json") Resource ethosPostSectionCrossListReq,
            @Value("classpath:colleague/CreateSectionCrossListReq.json") Resource createSectionCrossListReq,
            @Value("classpath:colleague/CreateSectionCrossListResp.json") Resource createSectionCrossListResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/section-crosslists", ethosPostSectionCrossListResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/section-cross-lists")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createSectionCrossListReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSectionCrossListResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-crosslists")).withRequestBody(
                equalToJson(asString(ethosPostSectionCrossListReq))));
    }

    @Test
    void colleagueGetSectionCrossList(
            @Value("classpath:colleague/ethos/EthosGetSectionCrossListResp.json") Resource ethosGetSectionCrossListResp,
            @Value("classpath:colleague/GetSectionCrossListResp.json") Resource getSectionCrossListResp
    ) throws Exception {
        wireMockServer.stubFor(makeRequest(GET, ".*/section-crosslists", ethosGetSectionCrossListResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/uapi/integration/v1/section-cross-lists")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionCrossListResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueDeleteSectionSchedule(
            @Value("classpath:colleague/DeleteSectionScheduleResp.json") Resource apiResponse)
            throws Exception {

        wireMockServer.stubFor(
                delete(urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );

        this.mockMvc.perform(
                        MockMvcRequestBuilders.delete(
                                        "/uapi/integration/v1/section-schedules/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(apiResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1")));
    }

    @Test
    void colleagueDeleteSectionCrossList(
            @Value("classpath:colleague/DeleteSectionCrossListResp.json") Resource apiResponse)
            throws Exception {

        wireMockServer.stubFor(
                delete(urlMatching(".*/section-crosslists/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );

        this.mockMvc.perform(
                        MockMvcRequestBuilders.delete(
                                        "/uapi/integration/v1/section-cross-lists/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(apiResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/section-crosslists/09352284-8e81-4a6a-add8-4bcfeae126e1")));
    }

    @Test
    void colleagueDeleteSectionInstructorAssignment() throws Exception {

        String uuid = "cead1e7c-8a36-4a6c-95a4-99c2c0a7d6e4";
        wireMockServer.stubFor(makeRequest(DELETE, ".*/section-instructors/" + uuid, HttpStatus.NO_CONTENT));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .delete("/uapi/integration/v1/section-instructor-assignments/" + uuid)
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(WireMock.urlMatching(".*/section-instructors/" + uuid)));
    }

    @Test
    void bannerSectionScheduleSearchBySection(
            @Value("classpath:banner/ethos/EthosGetInstructionalEventsBySearchResp.json") Resource ethosGetInstructionalEventsBySearch,
            @Value("classpath:banner/GetSearchSectionScheduleResp.json") Resource getSectionScheduleBySearchResponse)
            throws Exception {
        String sectionUuid = "25078cb6-f4fb-4cc0-80e1-74877d472fb0";

        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-events.*", ethosGetInstructionalEventsBySearch));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/section-schedules?sectionId=" + sectionUuid).header(
                                AUTHORIZATION,
                                COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionScheduleBySearchResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

}
