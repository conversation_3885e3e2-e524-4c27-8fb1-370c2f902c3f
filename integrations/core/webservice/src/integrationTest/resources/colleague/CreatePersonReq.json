{"data": {"names": [{"preferred": true, "type": {"id": "3578614a-1ca1-4ea6-8edd-58a73f2c4d38"}, "title": "Titulo", "firstName": "<PERSON>", "middleName": "Middle", "lastName": "<PERSON><PERSON>", "suffix": "Mr"}], "dateOfBirth": "1993-02-02", "emails": [{"preferred": true, "type": {"id": "952e31de-de75-4a40-bf41-60ab2a1e36b1"}, "address": "<EMAIL>"}], "phones": [{"preferred": true, "type": {"id": "94a059ec-d472-451f-ae21-57b14802a64b"}, "number": "************", "extension": "123"}], "gender": "male", "genderIdentity": "ccab3f24-cf5f-4c89-9c33-342124d19df7", "pronouns": "c003493f-8e83-4d75-9c18-0168cdc03e44", "ethnicity": {"id": "6bfaf529-ac47-410c-887f-ed540b9ce6a2"}, "roles": [{"role": "student", "effStart": "1991-01-01", "effEnd": "1991-01-01"}], "races": [{"id": "ea405ed1-07f4-4c4e-9385-88c3620aa4eb"}], "veteranStatus": "67fb9262-a0b3-40f1-9c9e-cf0b6d581f03", "credentials": [{"type": "taxIdentificationNumber", "value": "100-01-0031", "effStart": "1992-01-01", "effEnd": "2020-01-01"}], "citizenshipCountry": "USA", "citizenshipStatus": "60581cd5-eed1-40fe-ba12-efa143a6e85f", "addresses": [{"preferred": true, "type": {"id": "81090915-e445-4bec-892c-ca51e8134dfa"}, "addressLine1": "Street one", "addressLine2": "splitted", "addressLine3": "in front of something", "addressLine4": "passing the shore", "postalCode": 90003, "country": {"countryCode": "USA", "countryName": "United States of America"}, "state": {"stateCode": "US-IL", "stateName": "Illinois"}, "city": "Chicago", "effStart": "2021-01-01", "effEnd": "2025-05-01"}], "emergencyContacts": [{"fullName": "<PERSON>", "firstName": "<PERSON>", "middleName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phones": [{"availability": "DAYTIME", "countryCode": "+1", "number": "5551234567", "extension": "123"}], "isEmerContact": true, "isMissPerContact": false, "relationship": {"type": "father"}}]}}