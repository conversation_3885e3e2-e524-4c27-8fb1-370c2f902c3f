[{"credentials": [{"type": "bannerSourcedId", "value": "9538"}, {"type": "bannerUserName", "value": "pabbey"}, {"type": "bannerUdcId", "value": "8FAD820880D14870E0401895DC21425E"}, {"type": "elevateId", "value": "206911"}, {"type": "bannerId", "value": "N00015316"}, {"type": "ssn", "value": "N00015316"}], "dateOfBirth": "1970-01-01", "gender": "male", "genderMarker": {"id": "0b1085a8-ba80-4001-85ab-902d462c622d"}, "id": "17ce089b-918e-42f6-bf23-25ed27d796b0", "names": [{"firstName": "Parent", "fullName": "Parent Abbey", "lastName": "Abbey", "preference": "preferred"}], "privacyStatus": {"privacyCategory": "unrestricted"}, "races": [{"race": {"id": "5194f0d1-9ca8-463e-ae6e-89a3e7574b46"}, "reporting": [{"country": {"code": "USA", "racialCategory": "white"}}]}], "roles": [{"endOn": "2999-05-15T04:00:00Z", "role": "instructor", "startOn": "2009-01-01T05:00:00Z"}, {"endOn": "2999-05-15T04:00:00Z", "role": "advisor", "startOn": "2009-01-01T05:00:00Z"}]}, {"citizenshipStatus": {"category": "citizen", "detail": {"id": "9de466e9-6ba9-4823-b623-8fe212670a58"}}, "credentials": [{"type": "bannerSourcedId", "value": "16192"}, {"type": "bannerUserName", "value": "sabderra"}, {"type": "bannerUdcId", "value": "06B8BD4A558BFFEAE0501895DC217497"}, {"type": "elevateId", "value": "206903"}, {"type": "bannerId", "value": "205100002"}, {"type": "ssn", "value": "205100002"}], "dateOfBirth": "1998-05-04", "emails": [{"address": "sab<PERSON><PERSON><PERSON>@test.test", "preference": "primary", "type": {"detail": {"id": "0e7235e9-ae56-4e0b-bd74-1b892401f97e"}, "emailType": "personal"}}, {"address": "sab<PERSON><PERSON><PERSON>@test.test", "type": {"detail": {"id": "7364d68b-a5f0-4dbe-8b41-7c76d5c4ddde"}, "emailType": "personal"}}], "gender": "male", "genderMarker": {"id": "0b1085a8-ba80-4001-85ab-902d462c622d"}, "id": "8b3fed38-9bef-4ec5-8b70-386cf96f681f", "maritalStatus": {"detail": {"id": "394ca14e-2270-4ecb-b679-504008aeb7d7"}, "maritalCategory": "single"}, "names": [{"firstName": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preference": "preferred"}, {"firstName": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": {"category": "favored"}}, {"fullName": "<PERSON><PERSON>", "type": {"category": "legal"}}], "privacyStatus": {"privacyCategory": "unrestricted"}, "races": [{"race": {"id": "5194f0d1-9ca8-463e-ae6e-89a3e7574b46"}, "reporting": [{"country": {"code": "USA", "racialCategory": "white"}}]}], "religion": {"id": "058dfe9b-6c28-4cbb-853b-57365df4d9d3"}, "roles": [{"role": "student", "startOn": "2014-09-03T04:00:00Z"}, {"endOn": "2999-05-15T04:00:00Z", "role": "instructor", "startOn": "1900-01-01T05:00:00Z"}, {"endOn": "2999-05-15T04:00:00Z", "role": "advisor", "startOn": "1900-01-01T05:00:00Z"}]}, {"citizenshipStatus": {"category": "citizen", "detail": {"id": "9de466e9-6ba9-4823-b623-8fe212670a58"}}, "credentials": [{"type": "bannerSourcedId", "value": "46422"}, {"type": "bannerUserName", "value": "shawki"}, {"type": "bannerUdcId", "value": "322B6D222CEA3D00E053DD211895C0C3"}, {"type": "elevateId", "value": "459844"}, {"type": "bannerId", "value": "SABDERRA"}, {"type": "ssn", "value": "SABDERRA"}], "dateOfBirth": "1990-06-30", "emails": [{"address": "<EMAIL>", "preference": "primary", "type": {"detail": {"id": "a12c39d7-6696-4699-8db3-7872a3367919"}, "emailType": "school"}}], "gender": "male", "genderMarker": {"id": "0b1085a8-ba80-4001-85ab-902d462c622d"}, "id": "969c08aa-5e67-4e28-9d08-122db7d16e51", "names": [{"firstName": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preference": "preferred"}], "phones": [{"number": "5406328585", "type": {"detail": {"id": "5b0f5387-0f4b-438a-8b9f-594493d054ac"}, "phoneType": "business"}}, {"number": "5409695232", "type": {"detail": {"id": "83056c05-a6d8-470d-ab1c-a5a04332fcb1"}, "phoneType": "home"}}, {"number": "5406328585", "type": {"detail": {"id": "5d7cf4c4-993b-46fc-9099-a8aae92dad4e"}, "phoneType": "school"}}], "privacyStatus": {"privacyCategory": "unrestricted"}, "races": [{"race": {"id": "5194f0d1-9ca8-463e-ae6e-89a3e7574b46"}, "reporting": [{"country": {"code": "USA", "racialCategory": "white"}}]}], "roles": [{"endOn": "2999-05-15T04:00:00Z", "role": "instructor", "startOn": "2023-01-03T05:00:00Z"}, {"endOn": "2999-05-15T04:00:00Z", "role": "advisor", "startOn": "2023-01-03T05:00:00Z"}, {"role": "employee", "startOn": "2023-01-01T05:00:00Z"}]}]