package org.moderncampus.integration.webservice.core.dto.request;

import org.moderncampus.integration.dto.core.MCStudentPayment;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateMCStudentPaymentRequest extends MCStudentPayment {

    @Override
    @Hidden
    @Null(message = "Id value cannot be specified on a create")
    public String getId() {
        return super.getId();
    }

}
