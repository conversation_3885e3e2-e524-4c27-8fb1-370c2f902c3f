package org.moderncampus.integration.webservice.core.dto.request;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMCSectionRequest {

    @Parameter(in = ParameterIn.QUERY)
    String keyword;

    @Parameter(in = ParameterIn.QUERY)
    String code;

    @Parameter(in = ParameterIn.QUERY)
    String number;

    @Parameter(in = ParameterIn.QUERY)
    LocalDate startOn;

    @Parameter(in = ParameterIn.QUERY)
    LocalDate endOn;

    @Parameter(in = ParameterIn.QUERY)
    String academicPeriod;

    @Parameter(in = ParameterIn.QUERY)
    String course;

    @Parameter(in = ParameterIn.QUERY)
    String site;

    @Parameter(in = ParameterIn.QUERY)
    List<String> academicLevels;

    @Parameter(in = ParameterIn.QUERY)
    List<String> orgUnits;

    @Parameter(in = ParameterIn.QUERY)
    String status;

    public Map<String, Object> getSearchCriteria() {
        return new HashMap<String, Object>() {{
            put("keyword", keyword);
            put("code", code);
            put("number", number);
            put("startOn", startOn);
            put("endOn", endOn);
            put("academicPeriod", academicPeriod);
            put("course", course);
            put("site", site);
            put("academicLevels", academicLevels);
            put("orgUnits", orgUnits);
            put("status", status);
        }};
    }
}
