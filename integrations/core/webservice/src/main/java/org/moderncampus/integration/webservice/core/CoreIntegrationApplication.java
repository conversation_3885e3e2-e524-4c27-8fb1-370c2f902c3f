package org.moderncampus.integration.webservice.core;

import org.moderncampus.integration.webservice.BaseIntegrationApplication;
import org.moderncampus.integration.webservice.config.SharedAppConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.servers.Server;

@OpenAPIDefinition(
        servers = @Server(url = "/"),
        info = @Info(title = "Modern Campus Integration Connect APIs", version = "v1")
)
@SecurityScheme(name = "bearerAuth", type = SecuritySchemeType.HTTP, bearerFormat = "JWT", scheme = "bearer", in = SecuritySchemeIn.HEADER)
@SpringBootApplication
@ComponentScan(lazyInit = true, basePackages = {"org.moderncampus.integration.*"})
@EnableCaching
@Import(SharedAppConfiguration.class)
public class CoreIntegrationApplication extends BaseIntegrationApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(CoreIntegrationApplication.class);
        doBaseSetup(application);
        application.run(args);
    }
}
