package org.moderncampus.integration.webservice.core.service;

import static org.moderncampus.integration.route.identifier.Constants.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.core.MCAcademicLevel;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCHealthCheck;
import org.moderncampus.integration.dto.core.MCInstructionalMethod;
import org.moderncampus.integration.dto.core.MCCombinedInstructor;
import org.moderncampus.integration.dto.core.MCInstructor;
import org.moderncampus.integration.dto.core.MCLocation;
import org.moderncampus.integration.dto.core.MCOrganizationalUnit;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact;
import org.moderncampus.integration.dto.core.MCRoom;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCSubject;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCFinalGradeRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCOrganizationRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCPersonRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionCrossListGroupRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentChargeRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentEnrollmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentPaymentRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCAcademicPeriodRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCPersonRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionCrossListGroupsRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionCrossListsRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionScheduleRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMcSectionInstructorAssignmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCFinalGradeRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCOrganizationRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCPersonRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionCrossListGroupRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionCrossListRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCStudentEnrollmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCStudentRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCCourseResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCFinalGradeResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCOrganizationResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCPersonEmergencyContactResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCPersonResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionCrossListGroupResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionCrossListResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionInstructorResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionScheduleResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentChargeResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentEnrollmentResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentPaymentResponse;
import org.moderncampus.integration.webservice.core.util.RouteIdGenerator;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CoreIntegrationService extends BaseIntegrationService {

    IIntegrationRequestContext requestContext;

    public IntegrationResponse<List<MCOrganizationalUnit>> getOrganizationalUnits() throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ORGANIZATIONAL_UNITS));
    }

    public IntegrationResponse<MCOrganizationalUnit> getOrganizationalUnit(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ORGANIZATIONAL_UNIT), id, null);
    }

    public IntegrationResponse<List<MCSubject>> getSubjects() throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SUBJECTS));
    }

    public IntegrationResponse<MCSubject> getSubject(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SUBJECT), id, null);
    }

    public IntegrationResponse<List<MCInstructionalMethod>> getInstructionalMethods(
            BasePaginationRequest paginationRequest) throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_INSTRUCTIONAL_METHODS), null);
    }

    public IntegrationResponse<MCInstructionalMethod> getInstructionalMethod(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_INSTRUCTIONAL_METHOD), id, null);
    }

    public IntegrationResponse<List<MCLocation>> getLocations() throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_LOCATIONS));
    }

    public IntegrationResponse<MCLocation> getLocation(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_LOCATION), id, null);
    }

    public IntegrationResponse<List<MCAcademicLevel>> getAcademicLevels() throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ACADEMIC_LEVELS));
    }

    public IntegrationResponse<MCAcademicLevel> getAcademicLevel(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ACADEMIC_LEVEL), id, null);
    }

    public IntegrationResponse<List<MCRoom>> getRooms() throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ROOMS));
    }

    public IntegrationResponse<MCRoom> getRoom(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ROOM), id, null);
    }

    public IntegrationResponse<MCSection> getSection(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SECTION), id, null);
    }

    public IntegrationResponse<CreateMCSectionResponse> createSection(MCSection section) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_SECTION), section, null);
    }

    public IntegrationResponse<ObjectNode> updateSection(String id, MCSection section) throws Exception {
        section.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_SECTION), section, null);
    }

    public IntegrationResponse<List<MCSection>> getSections(BasePaginationRequest paginationRequest,
            SearchMCSectionRequest searchMCSectionRequest) throws Exception {
        return searchEntities(paginationRequest, RouteIdGenerator.generateRouteId(requestContext, GET_SECTIONS),
                searchMCSectionRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<MCSectionSchedule> getSectionSchedule(String sectionScheduleId)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_SCHEDULE),
                sectionScheduleId, null);
    }

    public IntegrationResponse<List<MCSectionSchedule>> getSectionSchedules(BasePaginationRequest paginationRequest,
            SearchMCSectionScheduleRequest searchMCSectionScheduleRequest)
            throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_SCHEDULES),
                searchMCSectionScheduleRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<CreateMCSectionScheduleResponse> createSectionSchedule(MCSectionSchedule sectionSchedule)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_SECTION_SCHEDULE), sectionSchedule,
                null);
    }

    public IntegrationResponse<ObjectNode> updateSectionSchedule(String id, MCSectionSchedule sectionSchedule) throws Exception {
        sectionSchedule.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_SECTION_SCHEDULE), sectionSchedule, null);
    }

    public IntegrationResponse<CreateMCCourseResponse> createCourse(MCCourse course) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_COURSE), course, null);
    }

    public IntegrationResponse<ObjectNode> updateCourse(String id, MCCourse course) throws Exception {
        course.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_COURSE), course, null);
    }

    public IntegrationResponse<ObjectNode> deleteSectionSchedule(String sectionScheduleId) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, DELETE_SECTION_SCHEDULE), sectionScheduleId, null);
    }

    public IntegrationResponse<MCCourse> getCourse(String courseId) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_COURSE), courseId, null);
    }

    public IntegrationResponse<List<MCAcademicPeriod>> getAcademicPeriods(BasePaginationRequest paginationRequest,
            SearchMCAcademicPeriodRequest periodRequest)
            throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_ACADEMIC_PERIODS),
                periodRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<List<MCAcademicPeriod>> getAcademicPeriod(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ACADEMIC_PERIOD), id, null);
    }

    public IntegrationResponse<List<MCCombinedInstructor>> getInstructors(BasePaginationRequest paginationRequest)
            throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_INSTRUCTORS),
                null, null);
    }

    public IntegrationResponse<MCInstructor> getInstructor(String instructorId) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_INSTRUCTOR), instructorId, null);
    }

    public IntegrationResponse<List<MCSectionInstructorAssignment>> getSectionInstructorAssignments(
            BasePaginationRequest paginationRequest,
            SearchMcSectionInstructorAssignmentRequest searchRequest) throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_INSTRUCTOR_ASSIGNMENTS),
                searchRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<MCSectionInstructorAssignment> getSectionInstructorAssignment(String sectionInstructorAssignmentId)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_INSTRUCTOR_ASSIGNMENT),
                sectionInstructorAssignmentId, null);
    }

    public IntegrationResponse<CreateMCSectionInstructorResponse> createSectionInstructorAssignment(
            MCSectionInstructorAssignment instructor) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_SECTION_INSTRUCTOR_ASSIGNMENT),
                instructor, null);
    }

    public IntegrationResponse<ObjectNode> deleteSectionInstructorAssignment(String sectionInstructorId)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, DELETE_SECTION_INSTRUCTOR_ASSIGNMENT),
                sectionInstructorId, null);
    }

    public IntegrationResponse<ObjectNode> updateSectionInstructorAssignment(String id, MCSectionInstructorAssignment instructor)
            throws Exception {
        instructor.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT),
                instructor, null);
    }

    public IntegrationResponse<CreateMCSectionCrossListResponse> createSectionCrossList(MCSectionCrossList crossList)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_SECTION_CROSS_LIST), crossList,
                null);
    }

    public IntegrationResponse<ObjectNode> deleteSectionCrossList(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, DELETE_SECTION_CROSS_LIST), id, null);
    }

    public IntegrationResponse<List<MCSectionCrossList>> getSectionCrossLists(BasePaginationRequest paginationRequest,
            SearchMCSectionCrossListsRequest crossListsRequest) throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_CROSS_LISTS),
                crossListsRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<MCAddress> getAddress(String addressId) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ADDRESS), addressId, null);
    }

    public IntegrationResponse<CreateMCPersonEmergencyContactResponse> createPersonEmergencyContact(
            MCPersonEmergencyContact emergencyContact) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_PERSON_EMERGENCY_CONTACT),
                emergencyContact, null);
    }

    public IntegrationResponse<ObjectNode> deletePersonEmergencyContact(String emergencyContactId) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, DELETE_PERSON_EMERGENCY_CONTACT), emergencyContactId, null);
    }

    public IntegrationResponse<CreateMCPersonResponse> createPerson(CreateMCPersonRequest personRequest) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_PERSON), personRequest, null);
    }

    public IntegrationResponse<CreateMCPersonResponse> checkDuplicatePerson(CreateMCPersonRequest personRequest)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CHECK_DUPLICATE_PERSON), personRequest,
                null);
    }

    public IntegrationResponse<CreateMCStudentChargeResponse> createStudentCharge(
            CreateMCStudentChargeRequest chargeRequest) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_STUDENT_CHARGE), chargeRequest,
                null);
    }

    public IntegrationResponse<CreateMCStudentPaymentResponse> createStudentPayment(
            CreateMCStudentPaymentRequest request) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_STUDENT_PAYMENT), request, null);
    }

    public IntegrationResponse<MCHealthCheck> healthCheck() throws Exception{
        MCHealthCheck healthCheck = new MCHealthCheck();
        IntegrationResponse<MCOrganizationalUnit> sisResponse = executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_ORGANIZATIONAL_UNITS));
        healthCheck.setHealthCheckMessage("Connectivity verified.");
        healthCheck.setHealthCheckPass(true);
        IntegrationResponse<MCHealthCheck> response = new IntegrationResponse<>();
        response.setData(healthCheck);
        response.setMeta(sisResponse.getMeta());
        return response;
    }

    public IntegrationResponse<CreateMCStudentEnrollmentResponse> createStudentEnrollment(
            CreateMCStudentEnrollmentRequest request) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_STUDENT_ENROLLMENT), request, null);
    }

    public IntegrationResponse<ObjectNode> updateStudentEnrollments(String id, UpdateMCStudentEnrollmentRequest studentEnrollment)
            throws Exception {
        studentEnrollment.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_STUDENT_ENROLLMENT),
                studentEnrollment, null);
    }

    public IntegrationResponse<ObjectNode> updateStudent(String id, UpdateMCStudentRequest student)
            throws Exception {
        student.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_STUDENT),
                student, null);
    }

    public IntegrationResponse<ObjectNode> updatePerson(String id, UpdateMCPersonRequest person) throws Exception {
        person.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_PERSON), person, null);
    }

    public IntegrationResponse<CreateMCFinalGradeResponse> createFinalGrade(
            CreateMCFinalGradeRequest request) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_FINAL_GRADE), request, null);
    }

    public IntegrationResponse<ObjectNode> updateFinalGrade(String id, UpdateMCFinalGradeRequest finalGradeUpdate)
            throws Exception {
        finalGradeUpdate.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_FINAL_GRADE), finalGradeUpdate,
                null);
    }

    public IntegrationResponse<CreateMCSectionCrossListGroupResponse> createSectionCrossListGroups(
            CreateMCSectionCrossListGroupRequest request) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_SECTION_CROSS_LIST_GROUP),
                request, null);
    }

    public IntegrationResponse<ObjectNode> updateSectionCrossListGroups(String compositeId,
            UpdateMCSectionCrossListGroupRequest request) throws Exception {
        request.setId(compositeId);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_SECTION_CROSS_LIST_GROUP), request,
                null);
    }

    public IntegrationResponse<ObjectNode> updateCourseSectionInformation(MCCourseSectionInformation request)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_COURSE_SECTION_INFORMATION),
                request, null);
    }

    public IntegrationResponse<CreateMCOrganizationResponse> createOrganization(CreateMCOrganizationRequest request)
            throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, CREATE_ORGANIZATION), request, null);
    }

    public IntegrationResponse<ObjectNode> updateOrganization(String id, UpdateMCOrganizationRequest organization)
            throws Exception {
        organization.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_ORGANIZATION), organization, null);
    }

    public IntegrationResponse<ObjectNode> getSectionCrossListGroups(BasePaginationRequest paginationRequest,
            SearchMCSectionCrossListGroupsRequest searchRequest) throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_CROSS_LIST_GROUPS),
                searchRequest.getSearchCriteria(), null);
    }

    public IntegrationResponse<MCSectionCrossListGroup> getSectionCrossListGroup(String id) throws Exception {
        String[] compositeKey = id.split("\\|");
        Map<String, Object> searchCriteria = new HashMap<>();
        searchCriteria.put("xlstGroup", compositeKey[0]);
        searchCriteria.put("termCode", compositeKey[1]);
        return searchEntities(new BasePaginationRequest(),
                RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_CROSS_LIST_GROUP), searchCriteria, null);
    }

    public IntegrationResponse<ObjectNode> updateSectionCrossList(String id,
            UpdateMCSectionCrossListRequest sectionCrossList) throws Exception {
        sectionCrossList.setId(id);
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, UPDATE_SECTION_CROSS_LIST),
                sectionCrossList, null);
    }

    public IntegrationResponse<MCSectionCrossList> sectionCrossList(String id) throws Exception {
        return executeRoute(RouteIdGenerator.generateRouteId(requestContext, GET_SECTION_CROSS_LIST), id, null);
    }
    public IntegrationResponse<List<MCPerson>> getPersons(BasePaginationRequest paginationRequest,
            SearchMCPersonRequest searchMCPersonRequest) throws Exception {
        return searchEntities(paginationRequest,
                RouteIdGenerator.generateRouteId(requestContext, GET_PERSONS),
                searchMCPersonRequest.getSearchCriteria(), null);
    }
}
