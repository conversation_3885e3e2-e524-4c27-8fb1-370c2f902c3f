package org.moderncampus.integration.webservice.core.security;

import java.security.interfaces.RSAPublicKey;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;

@Configuration
public class DevWebSecurityConfig {

    @Value("${jwt.public.key}")
    RSAPublicKey rsaPublicKey;

    @Bean
    @Profile("dev")
    public JwtDecoder jwtDecoder() {
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withPublicKey(rsaPublicKey).build();
        OAuth2TokenValidator<Jwt> noExpiryValidator = jwt -> OAuth2TokenValidatorResult.success();

        jwtDecoder.setJwtValidator(noExpiryValidator); // disables all validation, or customize
        return jwtDecoder;
    }

}
