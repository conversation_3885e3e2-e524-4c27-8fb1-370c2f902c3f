package org.moderncampus.integration.webservice.core.dto.request;

import java.util.List;

import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCountry;
import org.moderncampus.integration.dto.core.MCCounty;
import org.moderncampus.integration.dto.core.MCEmail;
import org.moderncampus.integration.dto.core.MCOrganization;
import org.moderncampus.integration.dto.core.MCPhone;
import org.moderncampus.integration.dto.core.MCRoleCredential;
import org.moderncampus.integration.dto.core.MCState;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateMCOrganizationRequest extends MCOrganization {

    @Override
    @NotNull(message = "Id value must be specified on an update")
    @Schema(hidden = true)
    public String getId() {
        return super.getId();
    }

    @Override
    public List<UpdateMCPhoneRequest> getPhones() {
        return (List<UpdateMCPhoneRequest>) super.getPhones();
    }

    @Override
    public List<UpdateMCEmailRequest> getEmails() {
        return (List<UpdateMCEmailRequest>) super.getEmails();
    }

    @Override
    public List<UpdateMCRoleCredentialRequest> getCredentials() {
        return (List<UpdateMCRoleCredentialRequest>) super.getCredentials();
    }

    public static class UpdateMCPhoneRequest extends MCPhone {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a update")
        public String getId() {
            return super.getId();
        }
    }

    public static class UpdateMCEmailRequest extends MCEmail {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a update")
        public String getId() {
            return super.getId();
        }
    }

    @Override
    public List<UpdateMCAddressRequest> getAddresses() {
        return (List<UpdateMCAddressRequest>) super.getAddresses();
    }


    public static class UpdateMCRoleCredentialRequest extends MCRoleCredential {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a update")
        public String getId() {
            return super.getId();
        }
    }

    public static class UpdateMCStateRequest extends MCState {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class UpdateMCCountyRequest extends MCCounty {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class UpdateMCCountryRequest extends MCCountry {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class UpdateMCAddressRequest extends MCAddress {

        @Nullable
        @Override
        public String getId() {
            return super.getId();
        }

        @Override
        public UpdateMCCountryRequest getCountry() {
            return (UpdateMCCountryRequest) super.getCountry();
        }

        @Override
        public UpdateMCCountyRequest getCounty() {
            return (UpdateMCCountyRequest) super.getCounty();
        }

        @Override
        public UpdateMCStateRequest getState() {
            return (UpdateMCStateRequest) super.getState();
        }
    }

}
