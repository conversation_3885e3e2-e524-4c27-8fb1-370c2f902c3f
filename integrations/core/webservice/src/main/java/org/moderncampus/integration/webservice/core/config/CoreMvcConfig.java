package org.moderncampus.integration.webservice.core.config;

import org.moderncampus.integration.webservice.core.interceptor.CoreIntegrationRouteInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CoreMvcConfig implements WebMvcConfigurer {

    CoreIntegrationRouteInterceptor integrationRouteInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(integrationRouteInterceptor);
    }
}
