package org.moderncampus.integration.webservice.core.dto.request;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import org.moderncampus.integration.dto.core.MCStudentCharge;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateMCStudentChargeRequest extends MCStudentCharge {

    @Schema(hidden = true, requiredMode = REQUIRED)
    @Null(message = "Id cannot be specified on a create")
    String id;

}
