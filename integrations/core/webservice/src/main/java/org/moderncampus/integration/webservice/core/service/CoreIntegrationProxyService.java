package org.moderncampus.integration.webservice.core.service;

import static org.moderncampus.integration.constants.Constants.RESPONSE_HEADERS;
import static org.moderncampus.integration.constants.Constants.RESPONSE_STATUS_CODE;

import java.util.Map;
import java.util.Set;

import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.dto.base.IntegrationProxyRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.core.util.RouteIdGenerator;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CoreIntegrationProxyService extends BaseIntegrationService {

    static final String PROXY_RESOURCE = "proxy";
    static final Set<String> DISALLOWED_OUT_HTTP_HEADERS = Set.of("Content-Length", "Transfer-Encoding");

    IIntegrationRequestContext requestContext;

    public ResponseEntity<String> doProxyCall(String path, Map<String, String> headers, String body, String httpMethod)
            throws Exception {
        IntegrationProxyRequest request = new IntegrationProxyRequest();
        request.setProxyPath(path);
        request.setData(body);
        request.setHeaders(headers);
        request.setHttpMethod(httpMethod);
        IntegrationResponse<String> response = executeRoute(RouteIdGenerator.generateRouteId(requestContext,
                PROXY_RESOURCE), request, Set.of(RESPONSE_STATUS_CODE, RESPONSE_HEADERS));
        Map<String, Object> responseHeaders = (Map<String, Object>) response.getMeta().getMetaProps()
                .get(RESPONSE_HEADERS);
        HttpHeaders httpHeaders = new HttpHeaders();
        responseHeaders.forEach((key, value) -> {
            if (!DISALLOWED_OUT_HTTP_HEADERS.contains(key)) {
                httpHeaders.add(key, value.toString());
            }
        });
        return ResponseEntity.status((Integer) response.getMeta().getMetaProps().get(RESPONSE_STATUS_CODE))
                .headers(httpHeaders).body(response.getData());
    }

}
