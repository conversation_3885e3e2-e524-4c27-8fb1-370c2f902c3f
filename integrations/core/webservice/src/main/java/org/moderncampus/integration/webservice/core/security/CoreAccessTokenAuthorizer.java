package org.moderncampus.integration.webservice.core.security;

import static org.moderncampus.integration.Constants.DEST_SYSTEM_ID;
import static org.moderncampus.integration.Constants.SOURCE_SYSTEM_ID;

import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.exception.EntityNotFoundException;
import org.moderncampus.integration.persistence.repository.OAuthRegistration;
import org.moderncampus.integration.persistence.repository.OAuthRegistrationRepository;
import org.moderncampus.integration.tenants.service.TenantApiTokenService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.stereotype.Service;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CoreAccessTokenAuthorizer implements AuthorizationManager<RequestAuthorizationContext> {

    static final String TEST_TENANT = "test";
    static final String SOURCE_SYSTEM_CLAIM = "sourceSystemClaim";
    static final String DEST_SYSTEM_CLAIM = "destSystemClaim";
    static final String SCHOOL_ID_CLAIM = "schoolIdClaim";
    static final String API_KEY_CLAIM = "apiKeyClaim";

    TenantApiTokenService tokenService;
    OAuthRegistrationRepository repository;
    String activeProfiles;
    IntegrationRequestContext requestContext;

    public CoreAccessTokenAuthorizer(TenantApiTokenService tokenService,
            @Value("${spring.profiles.active:}") String activeProfiles, OAuthRegistrationRepository repository,
            IntegrationRequestContext requestContext) {
        this.repository = repository;
        this.activeProfiles = activeProfiles;
        this.requestContext = requestContext;
        this.tokenService = tokenService;
    }

    public boolean isTokenValid(Authentication authentication) {
        Authentication auth = authentication;

        Jwt jwt = (Jwt) auth.getPrincipal();

        String apiToken = jwt.getClaimAsString(API_KEY_CLAIM);
        String tenant = jwt.getClaimAsString(SCHOOL_ID_CLAIM);
        String sourceSystemId = jwt.getClaimAsString(SOURCE_SYSTEM_CLAIM);
        String destSystemId = jwt.getClaimAsString(DEST_SYSTEM_CLAIM);

        if (StringUtils.isNotBlank(sourceSystemId) && StringUtils.isNotBlank(destSystemId)) {
            requestContext.setSourceSystem(sourceSystemId);
            requestContext.setDestSystem(destSystemId);
            requestContext.setSchool(tenant);

            if (!activeProfiles.contains("dev")) {
                boolean isApiTokenValid = !StringUtils.isEmpty(apiToken) && tokenService.isValidToken(tenant, apiToken);
                if (!isApiTokenValid) {
                    log.info("Invalid API Key");
                    throw new BadCredentialsException("");
                }
            }

            return true;
        }

        AuthorizationDecision decision = check(() -> authentication, null);
        return decision != null && decision.isGranted();
    }

    @Override
    public AuthorizationDecision check(Supplier<Authentication> authentication, RequestAuthorizationContext object) {
        Authentication auth = authentication.get();

        Jwt jwt = (Jwt) auth.getPrincipal();
        String clientId = jwt.getSubject();

        OAuthRegistration registrationId = new OAuthRegistration();
        registrationId.setClientId(clientId);
        registrationId.setId(clientId);
        String tenant;
        String sourceSystem = jwt.getClaimAsString(SOURCE_SYSTEM_ID);
        String destSystem = jwt.getClaimAsString(DEST_SYSTEM_ID);
        if (!activeProfiles.contains("dev")) {
            try {
                OAuthRegistration registration = repository.getById(registrationId);
                if (registration == null) {
                    return handleInvalidRegistration();
                }
                tenant = registration.getClientName();
            } catch (EntityNotFoundException e) {
                return handleInvalidRegistration();
            }
        } else {
            tenant = TEST_TENANT;
        }

        requestContext.setSourceSystem(sourceSystem);
        requestContext.setDestSystem(destSystem);
        requestContext.setSchool(tenant);

        return new AuthorizationDecision(true);
    }

    private AuthorizationDecision handleInvalidRegistration() {
        log.info("Invalid Auth Registration");
        throw new InternalAuthenticationServiceException("");
    }
}
