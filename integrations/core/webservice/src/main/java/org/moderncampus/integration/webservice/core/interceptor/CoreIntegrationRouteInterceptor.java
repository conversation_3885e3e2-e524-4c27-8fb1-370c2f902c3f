package org.moderncampus.integration.webservice.core.interceptor;

import static org.springframework.web.servlet.HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CoreIntegrationRouteInterceptor implements HandlerInterceptor {

    IIntegrationRequestContext requestContext;

    @Override
    @SuppressWarnings("unchecked")
    public boolean preHandle(HttpServletRequest hsr, HttpServletResponse hsr1, Object handler)
            throws Exception {

        URI uri = new URI(hsr.getRequestURL().toString());
        List<String> pathSegments = List.of(uri.getPath().split("/"));

        Optional.ofNullable(hsr.getAttribute(URI_TEMPLATE_VARIABLES_ATTRIBUTE))
                .map(Map.class::cast)
                .ifPresent(pathParameters -> {
                    setRequestContextFields(pathSegments);
                });
        return true;
    }

    private void setRequestContextFields(List<String> pathSegments) {
        if (pathSegments != null && pathSegments.size() >= 5) {
            String version = pathSegments.get(3);
            setupContext(version);
        }
    }

    private void setupContext(String version) {
        String schoolId = requestContext.getSchool();
        String sourceSystemId = requestContext.getSourceSystem();
        String destinationSystemId = requestContext.getDestSystem();
        requestContext.setVersion(version);
    }
}
