package org.moderncampus.integration.webservice.core.dto.request;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMcSectionInstructorAssignmentRequest {

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Section UUID", nullable = true)
    String section;

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Instructor UUID", nullable = true)
    String instructor;

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Section Schedule UUID", nullable = true)
    String sectionSchedule;

    @AssertTrue(message = "Exactly one of section, instructor, or sectionSchedule must be provided.")
    public boolean isOnlyOnSearchParameter() {
        return Stream.of(section, instructor, sectionSchedule)
                .filter(Objects::nonNull)
                .count() <= 1;
    }

    public Map<String, Object> getSearchCriteria() {
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("section", section);
        criteria.put("instructor", instructor);
        criteria.put("sectionSchedule", sectionSchedule);
        return criteria;
    }

}
