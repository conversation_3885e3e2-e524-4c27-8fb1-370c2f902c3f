package org.moderncampus.integration.webservice.core.dto.request;

import java.util.List;

import org.moderncampus.integration.dto.core.MCSection;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateMCSectionRequest extends MCSection {

    @Override
    @NotNull(message = "Id value must be specified on an update")
    @Schema(hidden = true)
    public String getId() {
        return super.getId();
    }

    @Override
    public List<CreateMCSectionRequest.CreateMCSectionInstructorAssignmentRequestFromSection> getInstructors() {
        return (List<CreateMCSectionRequest.CreateMCSectionInstructorAssignmentRequestFromSection>) super.getInstructors();
    }

    @Override
    public List<CreateMCSectionRequest.CreateMCSectionScheduleRequestFromSection> getSectionSchedules() {
        return (List<CreateMCSectionRequest.CreateMCSectionScheduleRequestFromSection>) super.getSectionSchedules();
    }

    @Override
    public List<CreateMCSectionCrossListRequest> getCrossLists() {
        return (List<CreateMCSectionCrossListRequest>) super.getCrossLists();
    }
}
