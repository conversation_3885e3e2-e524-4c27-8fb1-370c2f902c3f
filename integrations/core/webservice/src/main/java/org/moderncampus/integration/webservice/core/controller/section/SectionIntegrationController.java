package org.moderncampus.integration.webservice.core.controller.section;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import java.util.List;

import org.moderncampus.integration.dto.base.IntegrationRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionCrossListGroupRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionCrossListRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionInstructorAssignmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCSectionScheduleRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionCrossListGroupsRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionCrossListsRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCSectionScheduleRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMcSectionInstructorAssignmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionCrossListGroupRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionCrossListRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionInstructorAssignmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCSectionScheduleRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionCrossListGroupResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionCrossListResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionInstructorResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCSectionScheduleResponse;
import org.moderncampus.integration.webservice.core.service.CoreIntegrationService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class SectionIntegrationController {

    static final String SECTIONS = "sections";
    static final String SECTION_SCHEDULES = "section-schedules";
    static final String GET_SECTION = SECTIONS + "/" + "{id}";
    static final String GET_SECTION_SCHEDULE = SECTION_SCHEDULES + "/" + "{id}";
    static final String DELETE_SECTION_SCHEDULE = SECTION_SCHEDULES + "/" + "{id}";
    static final String UPDATE_SECTION_SCHEDULE = SECTION_SCHEDULES + "/" + "{id}";
    static final String UPDATE_SECTION = SECTIONS + "/" + "{id}";
    static final String SECTION_INSTRUCTOR_ASSIGNMENTS = "section-instructor-assignments";
    static final String GET_SECTION_INSTRUCTOR_ASSIGNMENT = SECTION_INSTRUCTOR_ASSIGNMENTS + "/" + "{id}";
    static final String DELETE_SECTION_INSTRUCTOR_ASSIGNMENT = SECTION_INSTRUCTOR_ASSIGNMENTS + "/" + "{id}";
    static final String UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT = SECTION_INSTRUCTOR_ASSIGNMENTS + "/" + "{id}";
    static final String SECTION_CROSS_LISTS = "section-cross-lists";
    static final String PUT_SECTION_CROSS_LISTS = SECTION_CROSS_LISTS + "/" + "{id}";
    static final String SECTION_CROSS_LIST = SECTION_CROSS_LISTS + "/" + "{id}";
    static final String DELETE_SECTION_CROSS_LIST = SECTION_CROSS_LISTS + "/" + "{id}";
    static final String SECTION_CROSS_LIST_GROUPS = "section-cross-list-groups";
    static final String GET_SECTION_CROSS_LIST_GROUP = SECTION_CROSS_LIST_GROUPS + "/" + "{id}";
    static final String PUT_SECTION_CROSS_LIST_GROUP = SECTION_CROSS_LIST_GROUPS + "/" + "{compositeId}";

    protected CoreIntegrationService service;

    @Operation(summary = "Get Section From External System", tags = {"Section"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_SECTION)
    public IntegrationResponse<MCSection> getSection(@PathVariable String id) throws Exception {
        return service.getSection(id);
    }

    @Operation(summary = "Create Section In External System", tags = {"Section"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = SECTIONS)
    public IntegrationResponse<CreateMCSectionResponse> createSection(
            @RequestBody IntegrationRequest<CreateMCSectionRequest> section) throws Exception {
        return service.createSection(section.getData());
    }

    @Operation(summary = "Update Section In External System", tags = {"Section"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_SECTION)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> patchUpdateSection(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCSectionRequest> section)
            throws Exception {
        return service.updateSection(id, section.getData());
    }

    @Operation(summary = "Get Sections From External System", tags = {"Section"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Courses"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = SECTIONS)
    public IntegrationResponse<List<MCSection>> getSections(
            @ParameterObject BasePaginationRequest paginationRequest,
            @ParameterObject SearchMCSectionRequest searchMCSectionRequest)
            throws Exception {
        return service.getSections(paginationRequest, searchMCSectionRequest);
    }

    @Operation(summary = "Create Section Schedule In External System", tags = {"Section Schedule"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section Schedule created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = SECTION_SCHEDULES)
    public IntegrationResponse<CreateMCSectionScheduleResponse> createSectionSchedule(
            @RequestBody IntegrationRequest<CreateMCSectionScheduleRequest> sectionSchedule) throws Exception {
        return service.createSectionSchedule(sectionSchedule.getData());
    }

    @Operation(summary = "Get Section Schedules From External System", tags = {"Section Schedule"},
            description = """
                    ### Query Parameter Restriction
                    You can only provide **one** of the following query parameters:
                    
                    - sectionId
                    - recurrenceStartOn
                    - recurrenceEndOn
                    
                    If more than **one** parameter is sent, the request will **fail**."""
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Schedule was found"),
            @ApiResponse(responseCode = "400", description = "Probably you sent more than one permitted query parameters.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = SECTION_SCHEDULES)
    public IntegrationResponse<List<MCSectionSchedule>> getSectionSchedules(
            @ParameterObject @Valid BasePaginationRequest paginationRequest,
            @ParameterObject @Valid SearchMCSectionScheduleRequest searchMCSectionScheduleRequest) throws Exception {
        return service.getSectionSchedules(paginationRequest, searchMCSectionScheduleRequest);
    }

    @Operation(summary = "Get Section Schedule From External System", tags = {"Section Schedule"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Schedule was found"),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = GET_SECTION_SCHEDULE)
    public IntegrationResponse<MCSectionSchedule> getSectionSchedule(@PathVariable String id)
            throws Exception {
        return service.getSectionSchedule(id);
    }

    @Operation(summary = "Update Section Schedule In External System", tags = {"Section Schedule"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section schedule updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_SECTION_SCHEDULE)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> updateSectionSchedule(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCSectionScheduleRequest> sectionSchedule)
            throws Exception {
        return service.updateSectionSchedule(id, sectionSchedule.getData());
    }

    @Operation(summary = "Delete Section Schedule From External System", tags = {"Section Schedule"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Schedule was deleted"),
            @ApiResponse(responseCode = "400", description = "Bad request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "403", description = "Permission Denied.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "404", description = "Not Found.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @DeleteMapping(path = DELETE_SECTION_SCHEDULE)
    public IntegrationResponse<ObjectNode> deleteSectionSchedule(@PathVariable String id) throws Exception {
        return service.deleteSectionSchedule(id);
    }

    @Operation(summary = "Get Section Instructor Assignment From External System", tags = {
            "Section Instructor Assignment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Instructor Assignment was found"),
            @ApiResponse(responseCode = "400", description = "Bad request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "403", description = "Permission Denied.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "404", description = "Not Found.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = GET_SECTION_INSTRUCTOR_ASSIGNMENT)
    public IntegrationResponse<MCSectionInstructorAssignment> getSectionInstructorAssignment(@PathVariable String id) throws Exception {
        return service.getSectionInstructorAssignment(id);
    }

    @Operation(summary = "Get All Section Instructor Assignment From External System", tags = {
            "Section Instructor Assignment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Instructor Assignments were found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = SECTION_INSTRUCTOR_ASSIGNMENTS)
    public IntegrationResponse<List<MCSectionInstructorAssignment>> getSectionInstructorAssignments(
            @ParameterObject @Valid BasePaginationRequest paginationRequest,
            @ParameterObject @Valid SearchMcSectionInstructorAssignmentRequest searchRequest)
            throws Exception {
        return service.getSectionInstructorAssignments(paginationRequest, searchRequest);
    }

    @Operation(summary = "Create Section Instructor Assignment In External System", tags = {
            "Section Instructor Assignment"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section Instructor created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = SECTION_INSTRUCTOR_ASSIGNMENTS)
    public IntegrationResponse<CreateMCSectionInstructorResponse> createSectionInstructorAssignment(
            @RequestBody IntegrationRequest<CreateMCSectionInstructorAssignmentRequest> instructor) throws Exception {
        return service.createSectionInstructorAssignment(instructor.getData());
    }

    @Operation(summary = "Update Section Instructor Assignment In External System", tags = {
            "Section Instructor Assignment"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section instructor assignment updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> updateSectionInstructorAssignment(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCSectionInstructorAssignmentRequest> sectionInstructor)
            throws Exception {
        return service.updateSectionInstructorAssignment(id, sectionInstructor.getData());
    }

    @Operation(summary = "Delete Section Instructor Assignment From External System", tags = {
            "Section Instructor Assignment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Instructor Assignment was deleted"),
            @ApiResponse(responseCode = "400", description = "Bad request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "403", description = "Permission Denied.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "404", description = "Not Found.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @DeleteMapping(path = DELETE_SECTION_INSTRUCTOR_ASSIGNMENT)
    public IntegrationResponse<ObjectNode> deleteSectionInstructorAssignment(@PathVariable String id) throws Exception {
        return service.deleteSectionInstructorAssignment(id);
    }

    @Operation(summary = "Create Section CrossList In External System", tags = {"Section Cross-List"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section CrossList created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = SECTION_CROSS_LISTS)
    public IntegrationResponse<CreateMCSectionCrossListResponse> createSectionCrossList(
            @RequestBody IntegrationRequest<CreateMCSectionCrossListRequest> request) throws Exception {
        return service.createSectionCrossList(request.getData());
    }

    @Operation(summary = "Update Section CrossList In External System", tags = {"Section Cross-List"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Section CrossList updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PutMapping(path = PUT_SECTION_CROSS_LISTS)
    public IntegrationResponse<ObjectNode> updateSectionCrossList(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCSectionCrossListRequest> sectionCrossList
    ) throws Exception {
        return service.updateSectionCrossList(id,sectionCrossList.getData());
    }


    @Operation(summary = "Delete Section Cross-Lists From External System", tags = {"Section Cross-List"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Cross-List was deleted"),
            @ApiResponse(responseCode = "400", description = "Bad request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "403", description = "Permission Denied.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "404", description = "Not Found.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @DeleteMapping(path = DELETE_SECTION_CROSS_LIST)
    public IntegrationResponse<ObjectNode> deleteSectionCrossList(@PathVariable String id) throws Exception {
        return service.deleteSectionCrossList(id);
    }

    @Operation(summary = "Get All Section CrossList From External System", tags = {"Section Cross-List"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section CrossListS WERE found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = SECTION_CROSS_LISTS)
    public IntegrationResponse<List<MCSectionCrossList>> getSectionCrossLists(
            @ParameterObject BasePaginationRequest paginationRequest,
            @ParameterObject SearchMCSectionCrossListsRequest crossListsRequest)
            throws Exception {
        return service.getSectionCrossLists(paginationRequest, crossListsRequest);
    }

    @Operation(summary = "Get Section CrossList From External System", tags = {"Section Cross-List"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = SECTION_CROSS_LIST)
    public IntegrationResponse<MCSectionCrossList> getSectionCrossList(@PathVariable String id)
            throws Exception {
        return service.sectionCrossList(id);
    }

    @Operation(summary = "Create Section Cross-List Group In External System", tags = {"Section Cross-List Group"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Cross-List Group was created"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PostMapping(path = SECTION_CROSS_LIST_GROUPS)
    public IntegrationResponse<CreateMCSectionCrossListGroupResponse> createSectionCrossListGroups(
            @RequestBody IntegrationRequest<CreateMCSectionCrossListGroupRequest> request) throws Exception {
        return service.createSectionCrossListGroups(request.getData());
    }

    @Operation(summary = "Update Section Cross-List Group In External System", tags = {"Section Cross-List Group"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Cross-List Group was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PutMapping(path = PUT_SECTION_CROSS_LIST_GROUP)
    public IntegrationResponse<ObjectNode> putSectionCrossListGroups(
            @PathVariable @Parameter(description = "Composite ID in the format `groupCode|termCode`. Example: `A1|202520`.")
            String compositeId,
            @RequestBody IntegrationRequest<UpdateMCSectionCrossListGroupRequest> request
    ) throws Exception {
        return service.updateSectionCrossListGroups(compositeId, request.getData());
    }

    @Operation(summary = "Get Section Cross-List Group In External System", tags = {"Section Cross-List Group"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Section Cross-List Group was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = SECTION_CROSS_LIST_GROUPS)
    public IntegrationResponse<ObjectNode> getSectionCrossListGroups(
            @ParameterObject BasePaginationRequest paginationRequest,
            @ParameterObject SearchMCSectionCrossListGroupsRequest searchMCSectionCrossListGroups
    ) throws Exception {
        return service.getSectionCrossListGroups(paginationRequest, searchMCSectionCrossListGroups);
    }

    @Operation(summary = "Get Section Cross-List Group In External System", tags = {"Section Cross-List Group"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_SECTION_CROSS_LIST_GROUP)
    public IntegrationResponse<MCSectionCrossListGroup> getSectionCrossListGroup(@PathVariable String id) throws Exception {
        return service.getSectionCrossListGroup(id);
    }

}
