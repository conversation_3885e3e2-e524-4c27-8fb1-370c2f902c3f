package org.moderncampus.integration.webservice.core.dto.request;


import java.util.HashMap;
import java.util.Map;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.constraints.Pattern;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMCAcademicPeriodRequest {

    @Parameter(in = ParameterIn.QUERY,
            description = """
                    Supported operators are $gte or the default of equals ($eq) if operator is not specified.
                    
                    Returns all academic periods for the start on date passed
                    
                    Date in UTC ISO 8601 format.
                    
                    Examples:
                    
                    - 2016-03-07T00:00:00
                    - $eq:2016-03-07T00:00:00
                    - $gte:2016-03-07T00:00:00
                    """)
    @Pattern(
            regexp = "^(\\$eq:|\\$gte:)?\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$",
            message = "Invalid format. Use `YYYY-MM-DDTHH:MM:SS`, `$eq:YYYY-MM-DDTHH:MM:SS`, or `$gte:YYYY-MM-DDTHH:MM:SS`"
    )
    String startOn;

    @Parameter(in = ParameterIn.QUERY,
            description = """
                    Supported operators are $lte or the default of equals ($eq) if operator is not specified.
                    
                    Returns all academic periods for the end on date passed
                    
                    Date in UTC ISO 8601 format.
                    
                    Examples:
                    
                    - 2016-03-07T00:00:00
                    - $eq:2016-03-07T00:00:00
                    - $lte:2016-03-07T00:00:00
                    """)
    @Pattern(
            regexp = "^(\\$eq:|\\$lte:)?\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$",
            message = "Invalid format. Use `YYYY-MM-DDTHH:MM:SS`, `$eq:YYYY-MM-DDTHH:MM:SS`, or `$lte:YYYY-MM-DDTHH:MM:SS`"
    )
    String endOn;

    public Map<String, Object> getSearchCriteria() {
        return new HashMap<>() {{
            put("startOn", startOn);
            put("endOn", endOn);
        }};
    }

}
