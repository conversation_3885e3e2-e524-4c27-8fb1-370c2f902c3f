package org.moderncampus.integration.webservice.core.dto.request;

import org.moderncampus.integration.dto.core.MCSectionCrossList;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateMCSectionCrossListRequest extends MCSectionCrossList {

    @Override
    @Hidden
    public String getId() {
        return super.getId();
    }

}
