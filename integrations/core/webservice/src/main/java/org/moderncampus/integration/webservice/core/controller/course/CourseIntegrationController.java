package org.moderncampus.integration.webservice.core.controller.course;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import org.moderncampus.integration.dto.base.IntegrationRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCCourseRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCCourseRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCCourseResponse;
import org.moderncampus.integration.webservice.core.service.CoreIntegrationService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class CourseIntegrationController {

    static final String COURSES = "courses";
    static final String GET_COURSES = COURSES + "/" + "{id}";
    static final String UPDATE_COURSE = COURSES + "/" + "{id}";

    protected CoreIntegrationService service;

    @Operation(summary = "Get Course From External System", tags = {"Course"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_COURSES)
    public IntegrationResponse<MCCourse> getCourse(@PathVariable String id) throws Exception {
        return service.getCourse(id);
    }

    @Operation(summary = "Update Course In External System", tags = {"Course"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Course updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_COURSE)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> updateCourse(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCCourseRequest> course)
            throws Exception {
        return service.updateCourse(id, course.getData());
    }

    @Operation(summary = "Create Course In External System", tags = {"Course"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Course created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = COURSES)
    public IntegrationResponse<CreateMCCourseResponse> createCourse(
            @RequestBody IntegrationRequest<CreateMCCourseRequest> course) throws Exception {
        return service.createCourse(course.getData());
    }

}
