package org.moderncampus.integration.webservice.core.dto.request;

import java.util.List;

import org.moderncampus.integration.dto.core.MCPerson;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateMCPersonRequest extends MCPerson {

    @Override
    @NotNull(message = "Id value must be specified on an update")
    @Schema(hidden = true)
    public String getId() {
        return super.getId();
    }

    @Override
    public List<CreateMCPersonRequest.CreateMCPhoneRequest> getPhones() {
        return (List<CreateMCPersonRequest.CreateMCPhoneRequest>) super.getPhones();
    }

    @Override
    public List<CreateMCPersonRequest.CreateMCEmailRequest> getEmails() {
        return (List<CreateMCPersonRequest.CreateMCEmailRequest>) super.getEmails();
    }

    @Override
    public List<CreateMCPersonRequest.CreateMCPersonAlternativeCredentialRequest> getAlternativeCredentials() {
        return (List<CreateMCPersonRequest.CreateMCPersonAlternativeCredentialRequest>) super.getAlternativeCredentials();
    }

    @Override
    public List<CreateMCPersonRequest.CreateMCRoleCredentialRequest> getCredentials() {
        return (List<CreateMCPersonRequest.CreateMCRoleCredentialRequest>) super.getCredentials();
    }
}
