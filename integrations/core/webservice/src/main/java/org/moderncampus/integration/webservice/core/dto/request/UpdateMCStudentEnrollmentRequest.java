package org.moderncampus.integration.webservice.core.dto.request;

import org.moderncampus.integration.dto.core.MCStudentEnrollment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateMCStudentEnrollmentRequest extends MCStudentEnrollment {

    @Override
    @NotNull(message = "Id value must be specified on an update")
    @Schema(hidden = true)
    public String getId() {
        return super.getId();
    }
}
