package org.moderncampus.integration.webservice.core.dto.request;

import java.util.List;

import org.moderncampus.integration.dto.core.MCSection;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateMCSectionRequest extends MCSection {

    @Override
    @Hidden
    @Null(message = "Id value cannot be specified on a create")
    public String getId() {
        return super.getId();
    }

    @Override
    public List<CreateMCSectionInstructorAssignmentRequestFromSection> getInstructors() {
        return (List<CreateMCSectionInstructorAssignmentRequestFromSection>) super.getInstructors();
    }

    @Override
    public List<CreateMCSectionScheduleRequestFromSection> getSectionSchedules() {
        return (List<CreateMCSectionScheduleRequestFromSection>) super.getSectionSchedules();
    }

    @Override
    public List<CreateMCSectionCrossListRequest> getCrossLists() {
        return (List<CreateMCSectionCrossListRequest>) super.getCrossLists();
    }

    public static class CreateMCSectionInstructorAssignmentRequestFromSection extends
            CreateMCSectionInstructorAssignmentRequest {

        @Override
        @Hidden
        @Null(message = "Instructor Section Id value cannot be specified on a create")
        public String getSectionId() {
            return super.getSectionId();
        }

        @Override
        @Hidden
        @Null(message = "Instructor Section Schedules value cannot be specified on a create")
        public List<MCSectionInstructorSchedule> getSectionSchedules() {
            return super.getSectionSchedules();
        }
    }

    public static class CreateMCSectionScheduleRequestFromSection extends CreateMCSectionScheduleRequest {

        @Override
        @Hidden
        @Null(message = "Section Schedule Section value cannot be specified on a create")
        public String getSection() {
            return super.getSection();
        }
    }
}
