package org.moderncampus.integration.webservice.core.util;

import static org.moderncampus.integration.Constants.BANNER_SYSTEM_ID;
import static org.moderncampus.integration.Constants.COLLEAGUE_SYSTEM_ID;

import org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds;
import org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.route.identifier.IRouteId;

import com.google.common.base.CaseFormat;

public class RouteIdGenerator {

    static String makeRouteId(IIntegrationRequestContext requestContext, String resource) {
        return requestContext.getVersion().toUpperCase() + "_" + requestContext.getDestSystem().toUpperCase() + "_"
                + resource.toUpperCase();
    }

    public static IRouteId generateRouteId(IIntegrationRequestContext requestContext, String resource) {
        try {
            String routeIdResource = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, resource);
            if (requestContext.getDestSystem() != null && BANNER_SYSTEM_ID.equals(requestContext.getDestSystem())) {
                return CoreBannerRouteIds.valueOf(makeRouteId(requestContext, routeIdResource));
            } else if (requestContext.getDestSystem() != null && COLLEAGUE_SYSTEM_ID.equals(
                    requestContext.getDestSystem())) {
                return CoreColleagueRouteIds.valueOf(makeRouteId(requestContext, routeIdResource));
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
