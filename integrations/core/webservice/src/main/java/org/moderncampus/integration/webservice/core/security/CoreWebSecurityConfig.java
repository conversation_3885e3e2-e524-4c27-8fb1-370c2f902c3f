package org.moderncampus.integration.webservice.core.security;

import java.security.interfaces.RSAPublicKey;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.DefaultHttpSecurityExpressionHandler;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

@Configuration
@EnableWebSecurity
public class CoreWebSecurityConfig {

    @Value("${jwt.public.key}")
    RSAPublicKey rsaPublicKey;

    @Bean
    public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http, ApplicationContext context)
            throws Exception {

        http.csrf(AbstractHttpConfigurer::disable);

        DefaultHttpSecurityExpressionHandler expressionHandler = new DefaultHttpSecurityExpressionHandler();
        expressionHandler.setApplicationContext(context);
        WebExpressionAuthorizationManager authManager = new WebExpressionAuthorizationManager(
                "@coreAccessTokenAuthorizer.isTokenValid(authentication)");
        authManager.setExpressionHandler(expressionHandler);


        http.sessionManagement(configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http
                .securityMatcher("/uapi/integration/**")
                .anonymous(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(configurer -> {
                    configurer.requestMatchers(
                                    new AntPathRequestMatcher("/uapi/integration/*/**"))
                            .access(authManager);
                    configurer.anyRequest().permitAll();
                })
                .oauth2ResourceServer((oauth2) -> {
                    oauth2.jwt(jwt -> jwt.decoder(jwtDecoder()));
                    oauth2.authenticationEntryPoint(new CoreAccessTokenInvalidEntryPoint());
                });

        return http.build();
    }

    @Bean
    @Profile("!dev")
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withPublicKey(rsaPublicKey).build();
    }

}
