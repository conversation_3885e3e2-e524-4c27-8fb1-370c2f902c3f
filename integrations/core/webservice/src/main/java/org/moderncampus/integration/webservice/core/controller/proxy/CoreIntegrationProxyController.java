package org.moderncampus.integration.webservice.core.controller.proxy;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import java.util.Map;

import org.moderncampus.integration.webservice.core.service.CoreIntegrationProxyService;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
@Hidden
public class CoreIntegrationProxyController {

    static final String PROXY_RESOURCE = "proxy";
    static final String PROXY_PATH = PROXY_RESOURCE + "/**";

    CoreIntegrationProxyService service;

    @Operation(summary = "Get Proxy to External System APIs", tags = {"Proxy APIs"})
    @GetMapping(path = PROXY_PATH)
    public ResponseEntity<String> getProxy(
            @ParameterObject IntegrationRequestContext requestContextWS, HttpServletRequest request,
            @RequestHeader Map<String, String> headers) throws Exception {
        return service.doProxyCall(request.getRequestURL().toString().split("/proxy/")[1], headers, null, null);
    }

    @Operation(summary = "Post Proxy to External System APIs", tags = {"Proxy APIs"})
    @PostMapping(path = PROXY_PATH)
    public ResponseEntity<String> postProxy(
            @ParameterObject IntegrationRequestContext requestContextWS, HttpServletRequest request,
            @RequestBody String body,
            @RequestHeader Map<String, String> headers) throws Exception {
        return service.doProxyCall(request.getRequestURL().toString().split("/proxy/")[1], headers, body,
                HttpMethod.POST.name());
    }

    @Operation(summary = "Put Proxy to External System APIs", tags = {"Proxy APIs"})
    @PutMapping(path = PROXY_PATH)
    public ResponseEntity<String> putProxy(
            @ParameterObject IntegrationRequestContext requestContextWS, HttpServletRequest request,
            @RequestBody String body,
            @RequestHeader Map<String, String> headers) throws Exception {
        return service.doProxyCall(request.getRequestURL().toString().split("/proxy/")[1], headers, body,
                HttpMethod.PUT.name());
    }
}
