package org.moderncampus.integration.webservice.core.controller.person;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import org.moderncampus.integration.dto.base.IntegrationRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCPersonEmergencyContactRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCPersonRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCPersonRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCPersonEmergencyContactResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCPersonResponse;
import org.moderncampus.integration.webservice.core.service.CoreIntegrationService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class PersonIntegrationController {

    static final String BY_ID = "/{id}";
    static final String PERSONS = "persons";
    static final String PERSON_EMERGENCY_CONTACTS = "person-emergency-contacts";
    static final String PERSONS_DUPLICATE_CHECK = "person-duplicate-check";
    static final String DELETE_PERSON_EMERGENCY_CONTACT = PERSON_EMERGENCY_CONTACTS + BY_ID;
    static final String UPDATE_PERSON = PERSONS + "/" + "{id}";

    protected CoreIntegrationService service;

    @Operation(summary = "Create Person Emergency Contact In External System", tags = {"Person Emergency Contact"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Person Emergency Contact created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = PERSON_EMERGENCY_CONTACTS)
    public IntegrationResponse<CreateMCPersonEmergencyContactResponse> createPersonEmergencyContact(
            @RequestBody IntegrationRequest<CreateMCPersonEmergencyContactRequest> emergencyRequest) throws Exception {
        return service.createPersonEmergencyContact(emergencyRequest.getData());
    }

    @Operation(summary = "Delete Person Emergency Contact From External System", tags = {"Person Emergency Contact"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Emergency Contact was deleted"),
            @ApiResponse(responseCode = "400", description = "Bad request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "403", description = "Permission Denied.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "404", description = "Not Found.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @DeleteMapping(path = DELETE_PERSON_EMERGENCY_CONTACT)
    public IntegrationResponse<ObjectNode> deletePersonEmergencyContact(@PathVariable String id) throws Exception {
        return service.deletePersonEmergencyContact(id);
    }

    @Operation(summary = "Create Person In External System", tags = {"Person"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Person was created"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500")
    })
    @PostMapping(path = PERSONS)
    public IntegrationResponse<CreateMCPersonResponse> createPerson(
            @RequestBody IntegrationRequest<CreateMCPersonRequest> request) throws Exception {
        return service.createPerson(request.getData());
    }

    @Operation(summary = "Check Duplicate Person In External System", tags = {"Person"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Person was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500")
    })
    @PostMapping(path = PERSONS_DUPLICATE_CHECK)
    public IntegrationResponse<CreateMCPersonResponse> checkDuplicatePerson(
            @RequestBody IntegrationRequest<CreateMCPersonRequest> request) throws Exception {
        return service.checkDuplicatePerson(request.getData());
    }

    @Operation(summary = "Update Person In External System", tags = {"Person"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Person updated in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PatchMapping(path = UPDATE_PERSON)
    @ResponseStatus(HttpStatus.OK)
    public IntegrationResponse<ObjectNode> updatePerson(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCPersonRequest> person)
            throws Exception {
        return service.updatePerson(id, person.getData());
    }
}
