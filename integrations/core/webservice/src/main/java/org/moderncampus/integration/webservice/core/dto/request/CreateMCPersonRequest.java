package org.moderncampus.integration.webservice.core.dto.request;

import java.util.List;

import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCountry;
import org.moderncampus.integration.dto.core.MCCounty;
import org.moderncampus.integration.dto.core.MCEmail;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPhone;
import org.moderncampus.integration.dto.core.MCRoleCredential;
import org.moderncampus.integration.dto.core.MCState;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateMCPersonRequest extends MCPerson {

    @Override
    @Hidden
    @Null(message = "Id value cannot be specified on a create")
    public String getId() {
        return super.getId();
    }

    @Override
    public List<CreateMCAddressRequest> getAddresses() {
        return (List<CreateMCAddressRequest>) super.getAddresses();
    }

    @Override
    public List<CreateMCPhoneRequest> getPhones() {
        return (List<CreateMCPhoneRequest>) super.getPhones();
    }

    @Override
    public List<CreateMCEmailRequest> getEmails() {
        return (List<CreateMCEmailRequest>) super.getEmails();
    }

    @Override
    public List<CreateMCPersonAlternativeCredentialRequest> getAlternativeCredentials() {
        return (List<CreateMCPersonAlternativeCredentialRequest>) super.getAlternativeCredentials();
    }

    @Override
    public List<CreateMCRoleCredentialRequest> getCredentials() {
        return (List<CreateMCRoleCredentialRequest>) super.getCredentials();
    }

    @Override
    public List<CreateMCPersonEmergencyContactRequest> getEmergencyContacts() {
        return (List<CreateMCPersonEmergencyContactRequest>) super.getEmergencyContacts();
    }

    public static class CreateMCStateRequest extends MCState {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCCountyRequest extends MCCounty {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCCountryRequest extends MCCountry {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCAddressRequest extends MCAddress {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }

        @Override
        public CreateMCCountryRequest getCountry() {
            return (CreateMCCountryRequest) super.getCountry();
        }

        @Override
        public CreateMCCountyRequest getCounty() {
            return (CreateMCCountyRequest) super.getCounty();
        }

        @Override
        public CreateMCStateRequest getState() {
            return (CreateMCStateRequest) super.getState();
        }
    }

    public static class CreateMCPhoneRequest extends MCPhone {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCEmailRequest extends MCEmail {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCRoleCredentialRequest extends MCRoleCredential {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

    public static class CreateMCPersonAlternativeCredentialRequest extends MCPersonAlternativeCredential {

        @Override
        @Hidden
        @Null(message = "Id value cannot be specified on a create")
        public String getId() {
            return super.getId();
        }
    }

}
