package org.moderncampus.integration.webservice.core.security;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class CoreAccessTokenInvalidEntryPoint implements AuthenticationEntryPoint {

    private static final String INVALID_AUTH_TOKEN = "Invalid Auth token";

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException authException) throws IOException, ServletException {

        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, INVALID_AUTH_TOKEN);
    }

}
