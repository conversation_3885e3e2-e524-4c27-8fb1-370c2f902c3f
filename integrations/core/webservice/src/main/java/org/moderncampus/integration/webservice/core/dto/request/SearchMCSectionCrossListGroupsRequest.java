package org.moderncampus.integration.webservice.core.dto.request;

import java.util.HashMap;
import java.util.Map;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMCSectionCrossListGroupsRequest {

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Term code", nullable = true)
    String termCode;

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "xlstGroup", nullable = true)
    String xlstGroup;

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Max Enrollment", nullable = true)
    Integer maxEnrl;

    public Map<String, Object> getSearchCriteria() {
        return new HashMap<>() {{
            put("termCode", termCode);
            put("xlstGroup", xlstGroup);
            put("maxEnrl", maxEnrl);
        }};
    }

}
