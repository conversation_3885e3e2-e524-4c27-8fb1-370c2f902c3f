package org.moderncampus.integration.webservice.core.dto.request;

import java.util.HashMap;
import java.util.Map;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMCPersonRequest {

    @Parameter(in = ParameterIn.QUERY)
    @Schema(description = "Colleague Person Id", nullable = true)
    String colleaguePersonId;

    public Map<String, Object> getSearchCriteria() {
        return new HashMap<>() {{
            put("colleaguePersonId", colleaguePersonId);
        }};
    }

}
