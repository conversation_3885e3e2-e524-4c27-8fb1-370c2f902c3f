package org.moderncampus.integration.webservice.core.controller.student;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import org.moderncampus.integration.dto.base.IntegrationRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentChargeRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentEnrollmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCStudentPaymentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCStudentEnrollmentRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCStudentRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentChargeResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentEnrollmentResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCStudentPaymentResponse;
import org.moderncampus.integration.webservice.core.service.CoreIntegrationService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class StudentIntegrationController {

    static final String STUDENTS = "students";
    static final String PUT_STUDENTS = STUDENTS + "/" + "{id}" ;
    static final String STUDENT_CHARGES = "student-charges";
    static final String STUDENT_PAYMENTS = "student-payments";
    static final String STUDENT_ENROLLMENTS = "student-enrollments";
    static final String UPDATE_STUDENT_ENROLLMENTS = STUDENT_ENROLLMENTS + "/" + "{id}";

    protected CoreIntegrationService service;

    @Operation(summary = "Create Student Charge In External System", tags = {"Student Charge"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Student Charge was created"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500")
    })
    @PostMapping(path = STUDENT_CHARGES)
    public IntegrationResponse<CreateMCStudentChargeResponse> createStudentCharge(
            @RequestBody IntegrationRequest<CreateMCStudentChargeRequest> chargeRequest) throws Exception {
        return service.createStudentCharge(chargeRequest.getData());
    }

    @Operation(summary = "Create Student Payment In External System", tags = {"Student Payment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Student Payment was created"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PostMapping(path = STUDENT_PAYMENTS)
    public IntegrationResponse<CreateMCStudentPaymentResponse> createStudentPayment(
            @RequestBody IntegrationRequest<CreateMCStudentPaymentRequest> request) throws Exception {
        return service.createStudentPayment(request.getData());
    }

    @Operation(summary = "Create Student Enrollment In External System", tags = {"Student Enrollment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Student Enrollment was created"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PostMapping(path = STUDENT_ENROLLMENTS)
    public IntegrationResponse<CreateMCStudentEnrollmentResponse> createStudentEnrollment(
            @RequestBody IntegrationRequest<CreateMCStudentEnrollmentRequest> request) throws Exception{
        return service.createStudentEnrollment(request.getData());
    }

    @Operation(summary = "Update Student Enrollment In External System", tags = {"Student Enrollment"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Student Enrollment was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PutMapping(path = UPDATE_STUDENT_ENROLLMENTS)
    public IntegrationResponse<ObjectNode> putStudentEnrollment(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCStudentEnrollmentRequest> studentEnroll)
            throws Exception {
        return service.updateStudentEnrollments(id, studentEnroll.getData());
    }

    @Operation(summary = "Update Student In External System", tags = {"Student"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Student was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PutMapping(path = PUT_STUDENTS)
    public IntegrationResponse<ObjectNode> putStudents(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCStudentRequest> student)
            throws Exception {
        return service.updateStudent(id, student.getData());
    }
}
