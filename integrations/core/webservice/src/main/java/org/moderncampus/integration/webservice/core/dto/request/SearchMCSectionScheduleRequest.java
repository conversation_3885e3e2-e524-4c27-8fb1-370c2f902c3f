package org.moderncampus.integration.webservice.core.dto.request;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.constraints.AssertTrue;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchMCSectionScheduleRequest {

    @Parameter(in = ParameterIn.QUERY,
            description = "**Unique parameter** Id of section.")
    String sectionId;

    @Parameter(in = ParameterIn.QUERY,
            description =
                    "**Unique parameter** Start date for recurrence. It can be expressed in two ways:<br/><br/>" +
                            "ISO_OFFSET_DATE_TIME: 2004-08-15T15:00:00-04:00<br/>" +
                            "ISO_LOCAL_DATE_TIME: In UTC: 2004-08-15T19:00:00<br/><br/>")
    String recurrenceStartOn;

    @Parameter(in = ParameterIn.QUERY,
            description =
                    "**Unique parameter** Start date for recurrence. It can be expressed in two ways:<br/><br/>" +
                            "ISO_OFFSET_DATE_TIME: 2004-12-15T16:30:00-05:00<br/>" +
                            "ISO_LOCAL_DATE_TIME: In UTC: 2004-08-15T21:30:00s<br/><br/>")
    String recurrenceEndOn;

    @AssertTrue(message = "Only one search parameter is allowed for Section Schedule Request")
    public boolean isOnlySearchParameter() {
        return Stream.of(sectionId, recurrenceStartOn, recurrenceEndOn)
                .filter(Objects::nonNull)
                .count() <= 1;
    }

    public Map<String, Object> getSearchCriteria() {
        return new HashMap<>() {{
            put("sectionId", sectionId);
            put("recurrenceStartOn", recurrenceStartOn);
            put("recurrenceEndOn", recurrenceEndOn);
        }};
    }

}
