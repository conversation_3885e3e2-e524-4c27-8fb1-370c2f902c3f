spring.config.import=integration-common.properties
# springdoc properties
springdoc.swagger-ui.path=/swagger/uapi/
springdoc.api-docs.path=/swagger/uapi/docs
server.error.include-message=always
# Security
jwt.private.key=classpath:app.key
jwt.public.key=classpath:app.pub
route.results.append.log=true
# Disable condition evaluation delta logging
logging.level.org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener=WARN
# banner ellucian hub connection properties
banner.cloud.integration.host=
banner.cloud.integration.auth.auth-token=
# colleague ellucian hub connection properties
colleague.cloud.integration.host=
colleague.cloud.integration.auth.auth-token=