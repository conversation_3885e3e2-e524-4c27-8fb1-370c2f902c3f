package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.V1_COLLEAGUE_UPDATE_PERSON_ADDRESS;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ColleagueEthosPersonAddressAssociationHandler extends ColleagueEthosPersonAssociationHandler {

    IRouteExecutor routeExecutor;

    @Override
    protected void handle(String useCase, MCPerson request, String personId) throws Exception {
        List<? extends MCAddress> addresses = request.getAddresses();
        if (isUpdatePerson(useCase) && !CollectionUtils.isEmpty(addresses)) {
            for (MCAddress address : addresses) {
                if (StringUtils.isNotEmpty(address.getId())) {
                    RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_UPDATE_PERSON_ADDRESS,
                            address);
                }
            }
        }
    }
}
