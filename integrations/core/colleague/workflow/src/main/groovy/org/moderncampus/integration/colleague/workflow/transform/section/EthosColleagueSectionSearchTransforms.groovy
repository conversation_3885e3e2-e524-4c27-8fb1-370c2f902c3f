package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.BaseEllucianSearchTransforms
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Component
@CompileStatic
class EthosColleagueSectionSearchTransforms extends BaseEllucianSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = exchange.getProperty(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        def sectionStatuses = ethosAssocCacheMap[ColleagueEthosAPIResource.SECTION_STATUSES] as Map<String, Map>

        def keyword = searchCriteria["keyword"]
        if(keyword){
            queryParams["keywordSearch"] = JsonOutput.toJson(["keywordSearch": keyword])
        }

        def code = searchCriteria["code"]
        if (code) {
            queryParams["criteria"] = JsonOutput.toJson(["code": code])
        }

        def number = searchCriteria["number"]
        if (number) {
            queryParams["criteria"] = JsonOutput.toJson(["number": number])
        }

        LocalDate startOn = searchCriteria["startOn"] as LocalDate
        if (startOn) {
            queryParams["criteria"] = JsonOutput.toJson(["startOn": startOn.format(DateTimeFormatter.ISO_LOCAL_DATE)])
        }

        LocalDate endOn = searchCriteria["endOn"] as LocalDate
        if (endOn) {
            queryParams["criteria"] = JsonOutput.toJson(["endOn": endOn.format(DateTimeFormatter.ISO_LOCAL_DATE)])
        }

        def academicPeriod = searchCriteria["academicPeriod"]
        if (academicPeriod) {
            queryParams["criteria"] = JsonOutput.toJson(["academicPeriod": ["id": academicPeriod]])
        }

        List<String> academicLevels = searchCriteria["academicLevels"] as List
        if (academicLevels) {
            def aLvls = academicLevels.collect { ["id": it] }
            queryParams["criteria"] = JsonOutput.toJson(["academicLevels": aLvls])
        }

        def course = searchCriteria["course"]
        if (course) {
            queryParams["criteria"] = JsonOutput.toJson(["course": ["id": course]])
        }

        def site = searchCriteria["site"]
        if (site) {
            queryParams["criteria"] = JsonOutput.toJson(["site": ["id": site]])
        }

        String orgUnit = searchCriteria["orgUnit"]
        if (orgUnit) {
            def orgUnits = orgUnit.split(",").each { return ["institutionUnit": ["id": it]] }
            queryParams["criteria"] = JsonOutput.toJson(["owningInstitutionUnits": orgUnits])
        }

        def status = searchCriteria["status"]
        if (status) {
            def statusName = sectionStatuses.find { it.key == status }?.value?.category
            queryParams["criteria"] = JsonOutput.toJson(["status": ["category": statusName]])
        }

    }
}
