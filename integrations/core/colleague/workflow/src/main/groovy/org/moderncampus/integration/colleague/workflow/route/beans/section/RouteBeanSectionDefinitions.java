package org.moderncampus.integration.colleague.workflow.route.beans.section;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;
import static org.moderncampus.integration.transform.IExtSystemReadTransformer.*;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.MulticastDefinition;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosAssocResolver;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosSectionCrossListsAssocHandler;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosSectionInstructorAssocHandler;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosSectionSchedulesAssocHandler;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueReadTransformer;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueWriteTransformer;
import org.moderncampus.integration.colleague.workflow.transform.section.EthosColleagueSectionCrossListSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.section.EthosColleagueSectionInstructorAssignmentSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.section.EthosColleagueSectionScheduleSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.section.EthosColleagueSectionSearchTransforms;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanSectionDefinitions")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanSectionDefinitions {

    EthosColleagueWriteTransformer writeTransformHelper;
    EthosColleagueReadTransformer readTransformer;
    ColleagueEthosAssocResolver associationResolver;
    ObjectMapper mapper;

    public RouteBeanSectionDefinitions(
            EthosColleagueWriteTransformer writeTransformHelper,
            EthosColleagueReadTransformer readTransformer,
            @Qualifier("colleagueEthosAssocResolver") ColleagueEthosAssocResolver associationResolver,
            ObjectMapper mapper) {
        this.writeTransformHelper = writeTransformHelper;
        this.readTransformer = readTransformer;
        this.associationResolver = associationResolver;
        this.mapper = mapper;
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateSection(
            ColleagueEthosSectionSchedulesAssocHandler sectionSchedulesAssocHandler,
            ColleagueEthosSectionInstructorAssocHandler sectionInstructorAssocHandler,
            ColleagueEthosSectionCrossListsAssocHandler crossListsAssocHandler) {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_SECTION.getId(),
                        ColleagueEthosAPIResource.SECTIONS, writeTransformHelper,
                        METHOD_MAP_TO_SECTION_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_SECTION_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_TITLE_TYPES,
                        ColleagueEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                        ColleagueEthosAPIResource.CREDIT_CATEGORIES,
                        ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_CREATE_SECTION).postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(sectionSchedulesAssocHandler);
                    multicastDefinition.bean(sectionInstructorAssocHandler);
                    multicastDefinition.bean(crossListsAssocHandler);
                })).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateSection(
            ColleagueEthosSectionSchedulesAssocHandler sectionSchedulesAssocHandler,
            ColleagueEthosSectionInstructorAssocHandler sectionInstructorAssocHandler,
            ColleagueEthosSectionCrossListsAssocHandler crossListsAssocHandler) {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_SECTION.getId(),
                        ColleagueEthosAPIResource.SECTIONS, writeTransformHelper,
                        METHOD_MAP_TO_SECTION_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_TITLE_TYPES,
                        ColleagueEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                        ColleagueEthosAPIResource.CREDIT_CATEGORIES,
                        ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION).postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(sectionSchedulesAssocHandler);
                    multicastDefinition.bean(sectionInstructorAssocHandler);
                    multicastDefinition.bean(crossListsAssocHandler);
                })).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSection(
            EthosColleagueSectionSearchTransforms sectionSearchTransforms,
            ColleagueEthosSectionSchedulesAssocHandler sectionSchedulesAssocHandler,
            ColleagueEthosSectionInstructorAssocHandler sectionInstructorAssocHandler,
            ColleagueEthosSectionCrossListsAssocHandler crossListsAssocHandler) {
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_SECTION.getId(),
                        ColleagueEthosAPIResource.SECTIONS, readTransformer, METHOD_MAP_TO_SECTION)
                .associationResolver(associationResolver).associationPreFetchList(
                        List.of(ColleagueEthosAPIResource.SECTION_TITLE_TYPES,
                                ColleagueEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                                ColleagueEthosAPIResource.SECTION_STATUSES,
                                ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_GET_SECTION)
                .postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(sectionSchedulesAssocHandler);
                    multicastDefinition.bean(sectionInstructorAssocHandler);
                    multicastDefinition.bean(crossListsAssocHandler);
                }))
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSections(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueSectionSearchTransforms sectionSearchTransforms,
            ColleagueEthosSectionSchedulesAssocHandler sectionSchedulesAssocHandler,
            ColleagueEthosSectionInstructorAssocHandler sectionInstructorAssocHandler,
            ColleagueEthosSectionCrossListsAssocHandler crossListsAssocHandler) {
        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_SECTIONS.getId(),
                        ColleagueEthosAPIResource.SECTIONS, readTransformer,
                        METHOD_MAP_TO_SECTION, aggregationStrategy
                )
                .searchCriteriaBuilder(sectionSearchTransforms)
                .associationResolver(associationResolver).associationPreFetchList(
                        List.of(ColleagueEthosAPIResource.SECTION_TITLE_TYPES,
                                ColleagueEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                                ColleagueEthosAPIResource.SECTION_STATUSES,
                                ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_GET_SECTIONS)
                .postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(sectionSchedulesAssocHandler);
                    multicastDefinition.bean(sectionInstructorAssocHandler);
                    multicastDefinition.bean(crossListsAssocHandler);
                }))
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateSectionSchedule() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_SECTION_SCHEDULE.getId(),
                        ColleagueEthosAPIResource.SECTION_SCHEDULES, writeTransformHelper,
                        METHOD_MAP_TO_SECTION_SCHEDULE_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_SECTION_SCHEDULE_CREATE_RESPONSE
                ).useCase(EllucianConstants.USE_CASE_CREATE_SECTION_SCHEDULE)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateSectionInstructorAssignment() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(
                        V1_COLLEAGUE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                        ColleagueEthosAPIResource.SECTION_INSTRUCTOR, writeTransformHelper,
                        METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_SECTION_INSTRUCTOR_ASSIGNMENT_CREATE_RESPONSE
                )
                .useCase(EllucianConstants.USE_CASE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSectionInstructorAssignment(DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueSectionInstructorAssignmentSearchTransforms searchTransforms) {
        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_SECTION_INSTRUCTOR_ASSIGNMENTS.getId(),
                        ColleagueEthosAPIResource.SECTION_INSTRUCTOR, readTransformer,
                        METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT, aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateSectionCrossList() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_SECTION_CROSS_LIST.getId(),
                ColleagueEthosAPIResource.SECTION_CROSS_LIST, writeTransformHelper,
                METHOD_MAP_TO_SECTION_CROSS_LIST_WRITE_REQUEST, writeTransformHelper,
                METHOD_MAP_FROM_SECTION_CROSS_LIST_CREATE_RESPONSE
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateSectionCrossList() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_SECTION_CROSS_LIST.getId(),
                        ColleagueEthosAPIResource.SECTION_CROSS_LIST, writeTransformHelper,
                        METHOD_MAP_TO_SECTION_CROSS_LIST_WRITE_REQUEST, mapper)
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION_CROSS_LIST)
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_CROSS_LIST))
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueDeleteSectionSchedule() {
        return ColleagueEthosRouteBuilderSupport.deleteByIdBuilder(V1_COLLEAGUE_DELETE_SECTION_SCHEDULE.getId(),
                ColleagueEthosAPIResource.SECTION_SCHEDULES,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueDeleteSectionCrossList() {
        return ColleagueEthosRouteBuilderSupport.deleteByIdBuilder(V1_COLLEAGUE_DELETE_SECTION_CROSS_LIST.getId(),
                ColleagueEthosAPIResource.SECTION_CROSS_LIST,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSectionCrossList() {
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_SECTION_CROSS_LIST.getId(),
                        ColleagueEthosAPIResource.SECTION_CROSS_LIST, readTransformer, METHOD_MAP_TO_SECTION_CROSS_LIST)
                .associationResolver(associationResolver)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSectionCrossLists(DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueSectionCrossListSearchTransforms searchTransforms) {
        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_SECTION_CROSS_LISTS.getId(),
                        ColleagueEthosAPIResource.SECTION_CROSS_LIST, readTransformer, METHOD_MAP_TO_SECTION_CROSS_LIST,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueDeleteSectionInstructorAssignment() {
        return ColleagueEthosRouteBuilderSupport.deleteByIdBuilder(
                V1_COLLEAGUE_DELETE_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                ColleagueEthosAPIResource.SECTION_INSTRUCTOR,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSectionSchedule(){
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_SECTION_SCHEDULE.getId(),
                    ColleagueEthosAPIResource.SECTION_SCHEDULES, readTransformer, METHOD_MAP_TO_SECTION_SCHEDULE)
            .associationResolver(associationResolver)
            .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetSectionSchedules(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueSectionScheduleSearchTransforms searchTransforms) {

        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_SECTION_SCHEDULES.getId(),
                        ColleagueEthosAPIResource.SECTION_SCHEDULES, readTransformer, METHOD_MAP_TO_SECTION_SCHEDULE,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateStudentEnrollment() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_STUDENT_ENROLLMENT.getId(),
                        ColleagueEthosAPIResource.SECTION_REGISTRATION, writeTransformHelper,
                        METHOD_MAP_TO_STUDENT_ENROLLMENT_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_STUDENT_ENROLLMENT_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_REGISTRATION_STATUSES)).build();
    }

}
