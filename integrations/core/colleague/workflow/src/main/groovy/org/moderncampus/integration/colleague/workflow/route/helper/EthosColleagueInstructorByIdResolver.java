package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * Resolver for instructor GET-by-ID endpoint that validates both person existence and instructor role.
 * 
 * This resolver runs BEFORE the main PERSONS call to:
 * 1. Validate that the person ID exists in PERSONS
 * 2. Validate that the person is an instructor in INSTRUCTORS
 * 3. Provide appropriate error messages for each case
 * 
 * Flow:
 * 1. Extract person ID from exchange body
 * 2. Call PERSONS/{id} to validate person exists
 * 3. Call INSTRUCTORS with criteria {"instructor":"<person-id>"} to validate instructor role
 * 4. If person doesn't exist: Let the normal 404 from PERSONS flow through
 * 5. If person exists but not instructor: Throw "No instructor was found for id '<person-id>'"
 * 6. If both exist: Set instructor data and continue with normal flow
 */
@Component
@Qualifier("instructorByIdResolver")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorByIdResolver extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorByIdResolver.class);

    public EthosColleagueInstructorByIdResolver(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        // Extract person ID from the exchange body (set by the framework)
        String personId = exchange.getIn().getBody(String.class);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        logger.debug("Validating instructor role for person ID: {}", personId);

        try {
            // Validate instructor role by calling INSTRUCTORS endpoint
            Exchange instructorExchange = exchange.copy();
            buildInstructorSearchCriteria(personId, instructorExchange);

            List<Map<String, ?>> instructorResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS, instructorExchange);

            if (instructorResponse == null || instructorResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Filter results to only include instructors that match the person ID
            List<Map<String, ?>> filteredResponse = instructorResponse.stream()
                .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                .collect(java.util.stream.Collectors.toList());

            if (filteredResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed after filtering: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Set the instructor association data for use by the transform
            exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);

            logger.debug("Instructor validation successful for person ID: {}. Found {} instructor records.",
                        personId, filteredResponse.size());

        } catch (ApplicationException e) {
            // Re-throw application exceptions as-is
            throw e;
        } catch (Exception e) {
            // Check if this is a 404 error from INSTRUCTORS endpoint
            if (e.getMessage() != null && e.getMessage().contains("404")) {
                // Convert INSTRUCTORS 404 to instructor-specific message
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed due to 404: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // For other exceptions, let them bubble up
            logger.debug("Exception during instructor validation for person ID {}: {}", personId, e.getMessage());
            throw e;
        }
    }
    
    /**
     * Build search criteria for INSTRUCTORS endpoint using person ID
     */
    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        
        // Build criteria as specified: {"instructor":"<person-id>"}
        Map<String, Object> criteria = Map.of("instructor", personId);
        
        entityRequest.setCriteria(criteria);
        newExchange.getMessage().setBody(entityRequest);
        
        logger.debug("Built instructor search criteria: {}", criteria);
    }
    
    /**
     * Extract person ID from instructor data structure
     */
    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }


}
