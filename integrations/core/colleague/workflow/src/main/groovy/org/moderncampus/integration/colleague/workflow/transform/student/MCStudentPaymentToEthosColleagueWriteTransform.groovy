package org.moderncampus.integration.colleague.workflow.transform.student

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCStudentPayment
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCStudentPaymentToEthosColleagueWriteTransform extends BaseTransformer<MCStudentPayment, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext context, MCStudentPayment studentPayment) {
        Map<String, ?> request = [:]

        commonWriteTransform.mapId(request)
        mapFields(request, studentPayment)

        return JsonOutput.toJson(request)
    }

    private void mapFields(Map<String, Object> request, MCStudentPayment studentPayment) {

        if (isValueExist(studentPayment.person)) {
            request.student = ['id': studentPayment.person]
        }

        if (isValueExist(studentPayment.fundingSource)) {
            request.fundingSource = ['id': studentPayment.fundingSource]
        }

        if (isValueExist(studentPayment.fundingDestination)) {
            request.fundingDestination = ['id': studentPayment.fundingDestination]
        }

        if (isValueExist(studentPayment.academicPeriod)) {
            request.academicPeriod = ['id': studentPayment.academicPeriod]
        }

        if (isValueExist(studentPayment.paymentType)) {
            request.paymentType = studentPayment.paymentType
        }

        if (isValueExist(studentPayment.paymentDate)) {
            request.paidOn = DateTimeFormatter.ISO_LOCAL_DATE.format(studentPayment.paymentDate)
        }


        if (isValueExist(studentPayment.paymentAmount) && isValueExist(studentPayment.currency)) {
            request.amount = [
                    'value'   : studentPayment.paymentAmount,
                    'currency': studentPayment.currency]
        }

        if (isValueExist(studentPayment.description)) {
            request.overrideDescription = studentPayment.description
        }

        if (isValueExist(studentPayment.comments)) {
            request.comments = [studentPayment.comments]
        }

        if (isValueExist(studentPayment.reportingDetails)) {
            request.reportingDetail = [
                    'usage'       : studentPayment.reportingDetails.type,
                    'originatedOn': DateTimeFormatter.ISO_LOCAL_DATE.format(studentPayment.reportingDetails.originDate)
            ]
        }
    }

}
