package org.moderncampus.integration.colleague.workflow.route.beans.course;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;
import static org.moderncampus.integration.transform.IExtSystemReadTransformer.METHOD_MAP_TO_COURSE;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosAssocResolver;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueReadTransformer;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueWriteTransformer;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanCourseDefinitions")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanCourseDefinitions {

    EthosColleagueWriteTransformer writeTransformHelper;
    EthosColleagueReadTransformer readTransformer;
    ColleagueEthosAssocResolver associationResolver;
    ObjectMapper mapper;

    public RouteBeanCourseDefinitions(
            EthosColleagueWriteTransformer writeTransformHelper,
            EthosColleagueReadTransformer readTransformer,
            @Qualifier("colleagueEthosAssocResolver") ColleagueEthosAssocResolver associationResolver,
            ObjectMapper mapper) {
        this.writeTransformHelper = writeTransformHelper;
        this.readTransformer = readTransformer;
        this.associationResolver = associationResolver;
        this.mapper = mapper;
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateCourse() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_COURSE.getId(),
                        ColleagueEthosAPIResource.COURSES, writeTransformHelper,
                        METHOD_MAP_TO_COURSE_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_COURSE_CREATE_RESPONSE
                )
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.COURSE_TITLE_TYPES,
                        ColleagueEthosAPIResource.CREDIT_CATEGORIES,
                        ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_CREATE_COURSE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateCourse() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_COURSE.getId(),
                        ColleagueEthosAPIResource.COURSES, writeTransformHelper,
                        METHOD_MAP_TO_COURSE_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.COURSE_TITLE_TYPES,
                        ColleagueEthosAPIResource.CREDIT_CATEGORIES,
                        ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_UPDATE_COURSE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetCourse() {
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_COURSE.getId(),
                        ColleagueEthosAPIResource.COURSES, readTransformer,
                        METHOD_MAP_TO_COURSE)
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.COURSE_TITLE_TYPES,
                        ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS)).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueOrganization() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_ORGANIZATION.getId(),
                        ColleagueEthosAPIResource.ORGANIZATIONS, writeTransformHelper,
                        METHOD_MAP_TO_ORGANIZATION_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_ORGANIZATION_CREATE_RESPONSE
                )
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(
                        ColleagueEthosAPIResource.ADDRESS_TYPES,
                        ColleagueEthosAPIResource.PHONE_TYPES,
                        ColleagueEthosAPIResource.EMAIL_TYPES
                ))
                .useCase(EllucianConstants.USE_CASE_CREATE_ORGANIZATION).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateOrganization() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_ORGANIZATION.getId(),
                        ColleagueEthosAPIResource.ORGANIZATIONS, writeTransformHelper,
                        METHOD_MAP_TO_ORGANIZATION_WRITE_REQUEST, mapper
                )
                .associationResolver(associationResolver)
                .associationPreFetchList(List.of(
                        ColleagueEthosAPIResource.ADDRESS_TYPES,
                        ColleagueEthosAPIResource.PHONE_TYPES,
                        ColleagueEthosAPIResource.EMAIL_TYPES
                ))
                .useCase(EllucianConstants.USE_CASE_UPDATE_ORGANIZATION)
                .build();
    }

}
