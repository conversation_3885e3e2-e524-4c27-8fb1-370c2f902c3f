package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.NIL_GUID
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionInstructorToEthosColleagueSectionInstructorWriteTransform extends BaseTransformer<MCSectionInstructorAssignment, String> {

    @Override
    protected String doTransform(TransformContext ctx, MCSectionInstructorAssignment sectionInstructor) {
        Map<String, ?> requestRoot = [:]

        if (USE_CASE_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            requestRoot = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            mapId(requestRoot, sectionInstructor)
        }

        mapSectionId(requestRoot, sectionInstructor)
        mapSectionScheduleId(requestRoot, sectionInstructor)
        mapInstructorId(requestRoot, sectionInstructor)
        mapInstructionalMethod(requestRoot, sectionInstructor)
        mapInstructorRole(requestRoot, sectionInstructor)
        mapWorkloadHrs(requestRoot, sectionInstructor)
        mapPercentageResponsible(requestRoot, sectionInstructor)
        mapAssignmentStartOn(requestRoot, sectionInstructor)
        mapAssignmentEndOn(requestRoot, sectionInstructor)

        return JsonOutput.toJson(requestRoot)
    }

    private void mapId(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        requestRoot.put("id", NIL_GUID)
    }

    private void mapSectionId(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.sectionId)) {
            requestRoot.put("section", ["id": sectionInstructor.sectionId])
        }
    }

    private void mapSectionScheduleId(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.sectionSchedules)) {
            requestRoot.put("instructionalEvents", sectionInstructor.sectionSchedules.collect {
                ["id": it["id"]]
            })
        }
    }

    private void mapInstructorId(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.instructorId)) {
            requestRoot.put("instructor", ["id": sectionInstructor.instructorId])
        }
    }

    private void mapInstructionalMethod(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.instructionalMethod)) {
            requestRoot.put("instructionalMethod", ["id": sectionInstructor.instructionalMethod])
        }
    }

    private void mapInstructorRole(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.instructorRole)) {
            requestRoot.put("instructorRole", sectionInstructor.instructorRole)
        }
    }

    private void mapWorkloadHrs(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.workloadHrs)) {
            requestRoot.put("workLoad", sectionInstructor.workloadHrs)
        }
    }

    private void mapPercentageResponsible(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.percentageResponsible)) {
            requestRoot.put("responsibilityPercentage", sectionInstructor.percentageResponsible)
        }
    }

    private void mapAssignmentStartOn(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.assignmentStartOn)) {
            requestRoot.put("workStartOn", DateTimeFormatter.ISO_LOCAL_DATE.format(sectionInstructor.assignmentStartOn))
        }
    }

    private void mapAssignmentEndOn(Map<String, ?> requestRoot, MCSectionInstructorAssignment sectionInstructor) {
        if (isValueExist(sectionInstructor.assignmentEndOn)) {
            requestRoot.put("workEndOn", DateTimeFormatter.ISO_LOCAL_DATE.format(sectionInstructor.assignmentEndOn))
        }
    }

}
