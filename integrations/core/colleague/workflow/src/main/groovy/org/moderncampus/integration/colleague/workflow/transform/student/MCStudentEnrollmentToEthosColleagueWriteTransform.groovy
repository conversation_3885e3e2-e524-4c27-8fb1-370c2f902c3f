package org.moderncampus.integration.colleague.workflow.transform.student

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCStudentEnrollment
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_STUDENT_ENROLLMENT
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCStudentEnrollmentToEthosColleagueWriteTransform extends BaseTransformer<MCStudentEnrollment, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCStudentEnrollment studentEnrollment) {
        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> sectionRegistrationStatuses = ethosAssocCacheMap[ColleagueEthosAPIResource.SECTION_REGISTRATION_STATUSES] as Map<String, Map>
        Map<String, ?> request = [:]

        if (USE_CASE_UPDATE_STUDENT_ENROLLMENT == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            request = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            commonWriteTransform.mapId(request)
        }

        mapFields(request, studentEnrollment)
        mapStatus(request, studentEnrollment, sectionRegistrationStatuses)

        return JsonOutput.toJson(request)
    }

    protected void mapFields(Map<String, Object> request, MCStudentEnrollment studentEnrollment) {

        if (isValueExist(studentEnrollment.person)) {
            request.registrant = ['id': studentEnrollment.person]
        }

        if (isValueExist(studentEnrollment.section)) {
            request.section = ['id': studentEnrollment.section]
        }

        if (isValueExist(studentEnrollment.academicLevel)) {
            request.academicLevel = ['id': studentEnrollment.academicLevel]
        }

        if (isValueExist(studentEnrollment.originalDate)) {
            request.originallyRegisteredOn = DateTimeFormatter.ISO_LOCAL_DATE.format(studentEnrollment.originalDate)
        }

        if (isValueExist(studentEnrollment.enrollmentStatusDate)) {
            request.statusDate = DateTimeFormatter.ISO_LOCAL_DATE.format(studentEnrollment.enrollmentStatusDate)
        }

    }

    protected void mapStatus(Map<String, Object> request, MCStudentEnrollment studentEnrollment, Map<String, Map> statuses) {
        String statusCode = studentEnrollment.enrollmentStatus
        def status = statuses.find { it.value?['code'] == statusCode }
        if (!status) return

        request.status = [
                'registrationStatus'             : status.value?['status']?['registrationStatus'],
                'sectionRegistrationStatusReason': status.value?['status']?['sectionRegistrationStatusReason'],
                'detail'                         : ['id': status.value?['id']]
        ]
    }

}
