package org.moderncampus.integration.colleague.workflow.transform.student

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCStudentCharge
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCStudentChargeToEthosColleagueWriteTransform extends BaseTransformer<MCStudentCharge, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext context, MCStudentCharge studentCharge) {
        Map<String, ?> request = [:]

        commonWriteTransform.mapId(request)

        mapFields(request, studentCharge)
        mapReportingDetail(request, studentCharge)

        return JsonOutput.toJson(request)
    }

    private void mapFields(Map<String, Object> request, MCStudentCharge charge) {

        if (isValueExist(charge.person)) {
            request.student = ['id': charge.person]
        }

        if (isValueExist(charge.fundingSource)) {
            request.fundingSource = ['id': charge.fundingSource]
        }

        if (isValueExist(charge.fundingDestination)) {
            request.fundingDestination = ['id': charge.fundingDestination]
        }

        if (isValueExist(charge.academicPeriod)) {
            request.academicPeriod = ['id': charge.academicPeriod]
        }

        if (isValueExist(charge.chargeableOn)) {
            request.chargeableOn = DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(charge.chargeableOn)
        }

        if (isValueExist(charge.chargeAmount) && isValueExist(charge.currency)) {
            request.chargedAmount = ['amount': [
                    'value'   : charge.chargeAmount,
                    'currency': charge.currency]
            ]
        }

        if (isValueExist(charge.description)) {
            request.overrideDescription = charge.description
        }

        if (isValueExist(charge.comments)) {
            request.comments = [charge.comments]
        }

    }

    private void mapReportingDetail(Map<String, Object> request, MCStudentCharge charge) {
        if (!isValueExist(charge.reportingDetails)) return
        def detail = charge.reportingDetails
        def map = [:]


        if (isValueExist(detail.type)) {
            map['usage'] = charge.reportingDetails.type
        }

        if (isValueExist(detail.originDate)) {
            map['originatedOn'] = DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(charge.reportingDetails.originDate)
        }

        if (isValueExist(detail.activityStartDate) && isValueExist(detail.activityEndDate)) {
            map['activityDates'] = [
                    startOn: DateTimeFormatter.ISO_LOCAL_DATE.format(detail.activityStartDate),
                    endOn  : DateTimeFormatter.ISO_LOCAL_DATE.format(detail.activityEndDate)
            ]
        }

        request.reportingDetail = map
    }

}
