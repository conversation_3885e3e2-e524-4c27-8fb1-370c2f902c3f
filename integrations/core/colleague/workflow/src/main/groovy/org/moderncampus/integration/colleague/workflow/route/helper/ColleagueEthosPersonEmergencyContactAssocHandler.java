package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.V1_COLLEAGUE_CREATE_PERSON_EMERGENCY_CONTACT;
import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.V1_COLLEAGUE_DELETE_PERSON_EMERGENCY_CONTACT;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ColleagueEthosPersonEmergencyContactAssocHandler extends ColleagueEthosPersonAssociationHandler {

    IRouteExecutor routeExecutor;

    @Override
    protected void handle(String useCase, MCPerson request, String personId) throws Exception {
        List<? extends MCPersonEmergencyContact> contactsAssoc = request.getEmergencyContacts();
        if (contactsAssoc != null) {
            for (MCPersonEmergencyContact contact : contactsAssoc) {
                if (isCreatePerson(useCase)) {
                    associatePersonToEmergencyContact(personId, contact);
                    RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_CREATE_PERSON_EMERGENCY_CONTACT,
                            contact);
                } else if (isUpdatePerson(useCase)) {
                    if (StringUtils.isNotBlank(contact.getId())) {
                        RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_DELETE_PERSON_EMERGENCY_CONTACT,
                                contact.getId());
                        contact.setId(null);
                    }
                    associatePersonToEmergencyContact(personId, contact);
                    RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_CREATE_PERSON_EMERGENCY_CONTACT,
                            contact);
                }
            }
        }
    }

    private void associatePersonToEmergencyContact(String personId, MCPersonEmergencyContact contact) {
        contact.setPerson(personId);
    }

}
