package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionCrossList
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource.SECTION_CROSS_LIST
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION_CROSS_LIST
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionCrossListToEthosColleagueWriteTransform extends BaseTransformer<MCSectionCrossList, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCSectionCrossList crossList) {
        Map<String, ?> request = [:]

        if (USE_CASE_UPDATE_SECTION_CROSS_LIST == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
            Map<String, Map> mapExistingCrossLists = ethosAssocCacheMap[SECTION_CROSS_LIST] as Map<String, Map>
            mapExistingCrossLists.remove(crossList.id)
            validateSectionsForUpdate(crossList.sections, mapExistingCrossLists)
            commonWriteTransform.mapId(request, crossList.id)
        } else {
            commonWriteTransform.mapId(request)
        }

        mapMaxCapacity(request, crossList)
        mapSections(request, crossList)

        return JsonOutput.toJson(request)
    }

    private void mapSections(Map<String, Object> request, MCSectionCrossList crossList) {
        if (!isValueExist(crossList.getSections())) return

        request.sections = crossList.sections.collect {
            Map<String, Object> obj = ['section': ['id': it.id]] as Map<String, Object>

            if (isValueExist(it.primary)) {
                obj['type'] = "primary"
            }
            return obj
        }

    }

    private void mapMaxCapacity(Map<String, Object> request, MCSectionCrossList crossList) {
        if (isValueExist(crossList.getMaxCapacity())) {
            request.maxEnrollment = crossList.getMaxCapacity()
        }
    }

    private void validateSectionsForUpdate(List<MCSectionCrossList.CrossListSection> newSections,Map<String, Map> existingSectionsMap) {
        if (newSections == null || newSections.size() < 2) {
            throw new IllegalArgumentException("At least two sections must be specified.")
        }

        Set<String> idSet = [] as Set
        // Check the existing sections first
        if (existingSectionsMap != null) {
            def sections = existingSectionsMap.entrySet().collect { it.value.sections }
            def sectionIds = sections*.collect { it['section']?['id'] }.findAll().flatten() as List<String>

            sectionIds.each {
                if (!idSet.add(it)) {
                    throw new IllegalArgumentException("Section with ID ${it} is already present in another crosslist grouping.")
                }
            }
        }

        // Then check the new sections
        newSections?.each { MCSectionCrossList.CrossListSection section ->
            if (!idSet.add(section.getId())) {
                throw new IllegalArgumentException("Section with ID ${section.getId()} is already present in another crosslist grouping.")
            }
        }
    }

}
