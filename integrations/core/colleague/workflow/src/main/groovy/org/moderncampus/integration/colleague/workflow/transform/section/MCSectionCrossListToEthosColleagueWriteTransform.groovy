package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionCrossList
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionCrossListToEthosColleagueWriteTransform extends BaseTransformer<MCSectionCrossList, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext context, MCSectionCrossList crossList) {

        Map<String, ?> request = [:]

        commonWriteTransform.mapId(request)
        mapMaxCapacity(request, crossList)
        mapSections(request, crossList)

        return JsonOutput.toJson(request)
    }

    private void mapSections(Map<String, Object> request, MCSectionCrossList crossList) {
        if (!isValueExist(crossList.getSections())) return

        request.sections = crossList.sections.collect {
            Map<String, Object> obj = ['section': ['id': it.id]] as Map<String, Object>

            if (isValueExist(it.primary)) {
                obj['type'] = "primary"
            }
            return obj
        }

    }

    private void mapMaxCapacity(Map<String, Object> request, MCSectionCrossList crossList) {
        if (isValueExist(crossList.getMaxCapacity())) {
            request.maxEnrollment = crossList.getMaxCapacity()
        }
    }

}
