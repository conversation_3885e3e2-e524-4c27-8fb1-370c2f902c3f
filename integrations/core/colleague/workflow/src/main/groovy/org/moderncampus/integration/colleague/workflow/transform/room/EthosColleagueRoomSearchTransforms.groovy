package org.moderncampus.integration.colleague.workflow.transform.room


import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.colleague.workflow.transform.common.search.BaseEthosColleagueSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueRoomSearchTransforms extends BaseEthosColleagueSearchTransforms {
    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) { }
}
