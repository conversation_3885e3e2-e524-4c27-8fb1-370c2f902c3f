package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.colleague.workflow.transform.common.search.BaseEthosColleagueSearchTransforms
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.springframework.stereotype.Component

@Component("ethosColleagueInstructorPersonSearchTransforms")
@CompileStatic
class EthosColleaguePersonSearchTransforms extends BaseEthosColleagueSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        def useCase = exchange.getProperty(org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE)
        if (EllucianConstants.USE_CASE_FETCH_INSTRUCTORS == useCase) {
            queryParams["criteria"] = JsonOutput.toJson(["roles": [["role": "instructor"]]])
        }
    }
}
