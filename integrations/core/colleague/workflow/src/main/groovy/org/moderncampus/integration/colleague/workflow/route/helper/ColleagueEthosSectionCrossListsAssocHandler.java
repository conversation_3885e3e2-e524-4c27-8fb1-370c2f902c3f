package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ColleagueEthosSectionCrossListsAssocHandler extends ColleagueEthosSectionAssociationHandler {

    IRouteExecutor routeExecutor;

    @Override
    protected void handle(String useCase, MCSection request, String sectionId) throws Exception {
        List<? extends MCSectionCrossList> crossListAssocs = request.getCrossLists();
        if (!CollectionUtils.isEmpty(crossListAssocs) && isCreateSection(useCase)) {
            createCrossLists(sectionId, crossListAssocs);
        } else if (isUpdateSection(useCase) && crossListAssocs != null) {
            List<MCSectionCrossList> existingCrossLists = retrieveAllCrossLists(sectionId);
            deleteCrossLists(existingCrossLists);
            createCrossLists(sectionId, crossListAssocs);
        }
    }

    @Override
    protected void handleGet(List<MCSection> mcSections) throws Exception {
        List<MCSection> crossListed = mcSections.stream().filter(MCSection::getCrossListed).toList();
        for (MCSection mcSection : crossListed) {
            List<MCSectionCrossList> crossLists = retrieveAllCrossLists(mcSection.getId());
            mcSection.setCrossLists(crossLists);
        }
    }

    private void deleteCrossLists(List<MCSectionCrossList> existingCrossLists) throws Exception {
        if (existingCrossLists != null && !existingCrossLists.isEmpty()) {
            for (MCSectionCrossList existingCrossList : existingCrossLists) {
                RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_DELETE_SECTION_CROSS_LIST,
                        existingCrossList.getId());
            }
        }
    }

    private List<MCSectionCrossList> retrieveAllCrossLists(String sectionId) throws Exception {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(Map.of("section", sectionId));
        RouteExecutorResult result = RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_GET_SECTION_CROSS_LISTS,
                entityRequest);
        return (List<MCSectionCrossList>) result.getResults();
    }

    private void createCrossLists(String sectionId, List<? extends MCSectionCrossList> crossListAssocs)
            throws Exception {
        for (MCSectionCrossList crossListAssoc : crossListAssocs) {
            addSectionToCrossListAssoc(crossListAssoc, sectionId);
            RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_CREATE_SECTION_CROSS_LIST, crossListAssoc);
        }
    }

    private void addSectionToCrossListAssoc(@NonNull MCSectionCrossList crossListAssoc, String sectionId) {
        MCSectionCrossList.CrossListSection sectionCrossListSection = new MCSectionCrossList.CrossListSection();
        sectionCrossListSection.setId(sectionId);
        List<MCSectionCrossList.CrossListSection> sectionCrossListSectionAssoc = crossListAssoc.getSections();
        if (CollectionUtils.isEmpty(sectionCrossListSectionAssoc)) {
            crossListAssoc.setSections(new ArrayList<>() {{
                add(sectionCrossListSection);
            }});
        } else if (sectionCrossListSectionAssoc.stream().noneMatch(
                sectionCrossListAssoc -> sectionCrossListAssoc.getId()
                        .equals(sectionId))) {
            Optional<MCSectionCrossList.CrossListSection> nilSectionCrossList = sectionCrossListSectionAssoc.stream()
                    .filter(
                            sectionCrossListAssoc -> sectionCrossListAssoc.getId()
                                    .equals(sectionId)).findFirst();
            if (nilSectionCrossList.isPresent()) {
                nilSectionCrossList.get().setId(sectionId);
            } else {
                List<MCSectionCrossList.CrossListSection> orderedCrossListSectionAssoc = new LinkedList<>(
                        sectionCrossListSectionAssoc);
                orderedCrossListSectionAssoc.addFirst(sectionCrossListSection);
                crossListAssoc.setSections(orderedCrossListSectionAssoc);
            }
        }
    }
}
