package org.moderncampus.integration.colleague.workflow.route.beans.student;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosAssocResolver;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueWriteTransformer;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanStudentDefinitions")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanStudentDefinitions {

    EthosColleagueWriteTransformer writeTransformHelper;
    ColleagueEthosAssocResolver associationResolver;
    ObjectMapper mapper;

    public RouteBeanStudentDefinitions(
            EthosColleagueWriteTransformer writeTransformHelper,
            @Qualifier("colleagueEthosAssocResolver") ColleagueEthosAssocResolver associationResolver,
            ObjectMapper mapper) {
        this.writeTransformHelper = writeTransformHelper;
        this.associationResolver = associationResolver;
        this.mapper = mapper;
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateStudentCharge() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_STUDENT_CHARGE.getId(),
                ColleagueEthosAPIResource.STUDENT_CHARGE, writeTransformHelper,
                METHOD_MAP_TO_STUDENT_CHARGE_WRITE_REQUEST, writeTransformHelper,
                METHOD_MAP_FROM_STUDENT_CHARGE_CREATE_RESPONSE
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateStudentPayment() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_STUDENT_PAYMENT.getId(),
                ColleagueEthosAPIResource.STUDENT_PAYMENTS, writeTransformHelper,
                METHOD_MAP_TO_STUDENT_PAYMENT_WRITE_REQUEST, writeTransformHelper,
                METHOD_MAP_FROM_STUDENT_PAYMENT_CREATE_RESPONSE
        ).build();
    }


    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateStudentEnrollment() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_STUDENT_ENROLLMENT.getId(),
                        ColleagueEthosAPIResource.SECTION_REGISTRATION,
                        writeTransformHelper,
                        METHOD_MAP_TO_STUDENT_ENROLLMENT_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_REGISTRATION_STATUSES))
                .useCase(EllucianConstants.USE_CASE_UPDATE_STUDENT_ENROLLMENT).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateStudent() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_STUDENT.getId(),
                ColleagueEthosAPIResource.STUDENTS, writeTransformHelper,
                METHOD_MAP_TO_STUDENT_WRITE_REQUEST, mapper).build();
    }

}
