package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.List;

import org.apache.camel.Exchange;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.exception.ApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Instructor association resolver specifically for GET-1 (get instructor by ID) endpoint.
 * This resolver validates that a person is actually an instructor before allowing the request to proceed.
 * 
 * Flow:
 * 1. Extract person ID from the request path
 * 2. Call INSTRUCTORS endpoint with criteria {"instructor":"<person-id>"}
 * 3. If empty array returned, throw error: "No instructor was found for id '<person-id>'"
 * 4. If records found, set instructor association data and allow request to proceed
 */
@Component
@Qualifier("instructorByIdAssocResolver")
public class EthosColleagueInstructorByIdAssocResolver implements IEllucianAssociationResolver {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorByIdAssocResolver.class);

    public EthosColleagueInstructorByIdAssocResolver() {
        logger.info("EthosColleagueInstructorByIdAssocResolver initialized successfully");
    }

    @Override
    public void resolveAssociations(List<IEllucianCloudAPIResource> apiResources, Exchange exchange) throws Exception {
        // Extract person ID from the exchange - it should be in the body during preFetchAssociations
        String personId = exchange.getIn().getBody(String.class);

        // Debug logging
        System.out.println("=== DEBUG: EthosColleagueInstructorByIdAssocResolver ===");
        System.out.println("DEBUG: API Resources: " + apiResources);
        System.out.println("DEBUG: Extracted person ID: '" + personId + "'");
        System.out.println("DEBUG: Exchange body: '" + exchange.getIn().getBody() + "'");
        System.out.println("DEBUG: Exchange headers: " + exchange.getIn().getHeaders());
        System.out.println("DEBUG: Exchange properties: " + exchange.getProperties());
        System.out.println("=== END DEBUG ===");

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        logger.info("Instructor validation association resolver executed for person ID: {}", personId);

        // For now, just log and continue - we'll implement the actual validation later
        // The goal is to first make sure this resolver is being called
    }
}
