package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCPersonEmergencyContactToEthosColleagueWriteTransform extends BaseTransformer<MCPersonEmergencyContact, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCPersonEmergencyContact emergencyContact) {
        Map<String, ?> request = [:]

        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> contactTypesById = ethosAssocCacheMap[ColleagueEthosAPIResource.EMERGENCY_CONTACT_TYPES] as Map<String, Map>
        Map<String, Map> phoneAvailabilitiesById = ethosAssocCacheMap[ColleagueEthosAPIResource.EMERGENCY_CONTACT_PHONE_AVAILABILITIES] as
                Map<String, Map>

        commonWriteTransform.mapId(request)
        mapPersonId(request, emergencyContact)

        request.contact = [:]
        mapContactNames(request, emergencyContact)
        mapContactPhones(request, emergencyContact, phoneAvailabilitiesById)
        mapTypes(request, emergencyContact, contactTypesById)
        mapRelationship(request, emergencyContact)

        return JsonOutput.toJson(request)
    }

    private void mapPersonId(Map<String, Object> request, MCPersonEmergencyContact emergencyContact) {
        if (isValueExist(emergencyContact.person)) {
            request.person = ['id': emergencyContact.person]
        }
    }

    private void mapContactNames(Map<String, Object> request, MCPersonEmergencyContact emergencyContact) {
        def name = [:]

        if (isValueExist(emergencyContact.fullName)) {
            name.fullName = emergencyContact.fullName
        }

        if (isValueExist(emergencyContact.firstName)) {
            name.firstName = emergencyContact.firstName
        }

        if (isValueExist(emergencyContact.middleName)) {
            name.middleName = emergencyContact.middleName
        }

        if (isValueExist(emergencyContact.lastName)) {
            name.lastName = emergencyContact.lastName
        }

        if (!name.isEmpty()) {
            request.contact['name'] = name
        }

    }

    private void mapTypes(Map<String, Object> request, MCPersonEmergencyContact emergencyContact, Map<String, Map> contactTypesById) {
        def codes = []
        if (isValueExist(emergencyContact.isEmerContact)) codes << 'EMER'
        if (isValueExist(emergencyContact.isMissPerContact)) codes << 'MISS'
        if (!codes) return

        def types = contactTypesById
                .findAll { _, value -> codes.contains(value['code']) }
                .collect { _, value -> [id: value['id']] }

        if (types) {
            request.contact['types'] = types
        }
    }

    private void mapContactPhones(Map<String, Object> request, MCPersonEmergencyContact emergencyContact,
                                  Map<String, Map> phoneAvailabilitiesById) {

        def phones = emergencyContact?.phones?.collect { mcPhone ->
            def map = [
                    countryCallingCode: mcPhone.countryCode,
                    number            : mcPhone.number,
                    extension         : mcPhone.extension
            ]

            if (mcPhone.availability) {
                def availability = phoneAvailabilitiesById.find {
                    (it.value['code'] == mcPhone.availability.code)
                }
                map.contactAvailability = [id: availability?.key]
            }
            return map
        }

        if (phones) {
            request.contact['phones'] = phones
        }

    }

    private void mapRelationship(Map<String, Object> request, MCPersonEmergencyContact emergencyContact) {
        if (isValueExist(emergencyContact.relationship?.type)) {
            request.contact['relationship'] = ['type': emergencyContact.relationship.type]
        }
    }
}
