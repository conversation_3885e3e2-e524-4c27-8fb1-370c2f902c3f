package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.exception.ApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Simple processor to validate that a person is an instructor before allowing GET-by-ID request to proceed.
 * 
 * This processor executes during the preFetchAssociations phase, before calling the PERSONS endpoint.
 * It validates that the requested person ID corresponds to an actual instructor.
 */
@Component
@Qualifier("instructorValidationProcessor")
public class InstructorValidationProcessor extends ColleagueEthosAssocResolver implements IEllucianAssociationResolver {

    private static final Logger logger = LoggerFactory.getLogger(InstructorValidationProcessor.class);

    public InstructorValidationProcessor(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
        logger.info("InstructorValidationProcessor initialized successfully");
    }

    @Override
    public void resolveAssociations(List<IEllucianCloudAPIResource> apiResources, Exchange exchange) throws Exception {
        // Extract person ID from the exchange body (available during preFetchAssociations)
        String personId = exchange.getIn().getBody(String.class);

        logger.debug("Validating instructor existence for person ID: {} with API resources: {}", personId, apiResources);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        // Create a new exchange for the INSTRUCTORS endpoint call
        Exchange newExchange = exchange.copy();
        buildInstructorSearchCriteria(personId, newExchange);

        // Call the INSTRUCTORS endpoint to validate instructor existence
        List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS, newExchange);

        if (parsedAssocResponse == null || parsedAssocResponse.isEmpty()) {
            String errorMessage = String.format("No instructor was found for id '%s'", personId);
            logger.warn("Instructor validation failed: {}", errorMessage);
            throw new ApplicationException(errorMessage, 404);
        }

        // Filter results to only include instructors that match the person ID
        List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
            .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
            .collect(java.util.stream.Collectors.toList());

        if (filteredResponse.isEmpty()) {
            String errorMessage = String.format("No instructor was found for id '%s'", personId);
            logger.warn("Instructor validation failed: {}", errorMessage);
            throw new ApplicationException(errorMessage, 404);
        }

        // Set the instructor association data for use by other processors
        exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);

        logger.debug("Instructor validation successful for person ID: {}. Found {} instructor records.",
                    personId, filteredResponse.size());
    }
    
    /**
     * Build search criteria for INSTRUCTORS endpoint using person ID
     */
    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        
        // Build criteria as specified in ticket: {"instructor":"<person-id>"}
        Map<String, Object> criteria = Map.of("instructor", personId);
        
        entityRequest.setCriteria(criteria);
        newExchange.getMessage().setBody(entityRequest);
        
        logger.debug("Built instructor search criteria: {}", criteria);
    }
    
    /**
     * Extract person ID from instructor data structure
     */
    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }
}
