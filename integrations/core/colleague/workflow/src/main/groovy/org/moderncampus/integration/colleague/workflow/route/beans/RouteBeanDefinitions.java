package org.moderncampus.integration.colleague.workflow.route.beans;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;
import static org.moderncampus.integration.transform.IExtSystemReadTransformer.*;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.METHOD_MAP_FROM_FINAL_GRADE_CREATE_RESPONSE;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.METHOD_MAP_TO_FINAL_GRADE_WRITE_REQUEST;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosAssocResolver;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueReadTransformer;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueWriteTransformer;
import org.moderncampus.integration.colleague.workflow.transform.acadlevel.EthosColleagueAcademicLevelSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.location.EthosColleagueLocationSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.room.EthosColleagueRoomSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.section.BasePaginationSearchTransforms;
import org.moderncampus.integration.colleague.workflow.transform.subject.EthosColleagueSubjectSearchTransforms;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanDefinitions")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanDefinitions {

    EthosColleagueWriteTransformer writeTransformHelper;
    EthosColleagueReadTransformer readTransformer;
    ColleagueEthosAssocResolver associationResolver;
    ObjectMapper mapper;

    public RouteBeanDefinitions(
            EthosColleagueWriteTransformer writeTransformHelper,
            EthosColleagueReadTransformer readTransformer,
            ColleagueEthosAssocResolver colleagueEthosAssocResolver,
            ObjectMapper mapper) {
        this.writeTransformHelper = writeTransformHelper;
        this.readTransformer = readTransformer;
        this.associationResolver = colleagueEthosAssocResolver;
        this.mapper = mapper;
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetAddress() {
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_ADDRESS.getId(),
                ColleagueEthosAPIResource.ADDRESSES, readTransformer,
                METHOD_MAP_TO_ADDRESS).build();
    }


    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetAllAcademicLevels(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueAcademicLevelSearchTransforms searchTransforms) {

        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_ACADEMIC_LEVELS.getId(),
                        ColleagueEthosAPIResource.ACADEMIC_LEVELS, readTransformer, METHOD_MAP_TO_ACADEMIC_LEVEL,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetAllSubjects(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueSubjectSearchTransforms searchTransforms) {

        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_SUBJECTS.getId(),
                        ColleagueEthosAPIResource.SUBJECTS, readTransformer, METHOD_MAP_TO_SUBJECT,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetAllRooms(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueRoomSearchTransforms searchTransforms) {

        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_ROOMS.getId(),
                        ColleagueEthosAPIResource.ROOMS, readTransformer, METHOD_MAP_TO_ROOM,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetAllLocations(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleagueLocationSearchTransforms searchTransforms) {

        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_LOCATIONS.getId(),
                        ColleagueEthosAPIResource.LOCATIONS, readTransformer, METHOD_MAP_TO_LOCATION,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreateFinalGrade() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_FINAL_GRADE.getId(),
                        ColleagueEthosAPIResource.FINAL_GRADES, writeTransformHelper,
                        METHOD_MAP_TO_FINAL_GRADE_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_FINAL_GRADE_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_GRADE_TYPES)).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdateFinalGrade() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_FINAL_GRADE.getId(),
                        ColleagueEthosAPIResource.FINAL_GRADES,
                        writeTransformHelper,
                        METHOD_MAP_TO_FINAL_GRADE_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.SECTION_GRADE_TYPES))
                .useCase(EllucianConstants.USE_CASE_UPDATE_FINAL_GRADE).isPatchImpl(false).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetInstructionalMethods(DefaultListAggregationStrategy aggregationStrategy,
            BasePaginationSearchTransforms paginationTransforms) {
        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_INSTRUCTIONAL_METHODS.getId(),
                        ColleagueEthosAPIResource.INSTRUCTIONAL_METHODS, readTransformer, METHOD_MAP_TO_INSTRUCTIONAL_METHOD,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(paginationTransforms).build();
    }

}
