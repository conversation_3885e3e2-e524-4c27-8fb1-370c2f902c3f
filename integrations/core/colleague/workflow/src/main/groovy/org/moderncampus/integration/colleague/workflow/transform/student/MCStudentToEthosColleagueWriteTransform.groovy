package org.moderncampus.integration.colleague.workflow.transform.student

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCStudent
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCStudentToEthosColleagueWriteTransform extends BaseTransformer<MCStudent, String> {

    @Override
    protected String doTransform(TransformContext ctx, MCStudent student) {

        Map<String, ?> studentReq = [:]

        mapId(studentReq, student)
        mapPerson(studentReq, student)
        mapStudentTypes(studentReq, student)
        mapResidencyTypes(studentReq, student)

        return JsonOutput.toJson(studentReq)
    }

    private void mapId(Map<String, ?> requestRoot, MCStudent student) {
        if (isValueExist(student.id)) {
            requestRoot.id = student.id
        }
    }

    private void mapPerson(Map<String, ?> requestRoot, MCStudent student) {
        if (isValueExist(student.person)) {
          requestRoot.person = ["id" : student.person]
        }
    }

    private void mapStudentTypes(Map<String, ?> requestRoot, MCStudent student) {
        if (isValueExist(student.studentTypes)) {
            def studentTypesArr = []
            student.studentTypes.collect {
                studentTypesArr.push(
                        [
                         "type":["id": it.studentType],
                         "startOn":ISO_LOCAL_DATE.format(it.effectiveStart)
                        ]
                )
            }
            requestRoot.put("types", studentTypesArr)
        }
    }

    private void mapResidencyTypes(Map<String, ?> requestRoot, MCStudent student) {
        if (isValueExist(student.residencies)) {
            def residenciesArr = []
            student.residencies.collect {
                residenciesArr.push(
                        [
                         "residency":["id": it.residencyType],
                         "startOn": DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(it.effectiveStart)
                        ]
                )
            }
            requestRoot.put("residencies", residenciesArr)
        }
    }
}
