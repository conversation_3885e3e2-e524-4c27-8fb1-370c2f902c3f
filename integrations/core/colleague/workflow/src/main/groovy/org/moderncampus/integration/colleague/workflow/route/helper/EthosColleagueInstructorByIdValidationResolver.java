package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * Instructor validation resolver specifically for GET-by-ID endpoint (/instructors/id={id}).
 * This resolver validates that a person is actually an instructor before allowing the request to proceed.
 *
 * Flow:
 * 1. Extract person ID from the request path parameter
 * 2. Call INSTRUCTORS endpoint with criteria {"instructor":"<person-id>"}
 * 3. If empty array returned, throw error: "No instructor was found for id '<person-id>'"
 * 4. If records found, set instructor association data and allow request to proceed to PERSONS endpoint
 */
@Component
@Qualifier("instructorByIdValidationResolver")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorByIdValidationResolver extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorByIdValidationResolver.class);

    public EthosColleagueInstructorByIdValidationResolver(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        // Extract person ID from the exchange body (set by the framework before this processor)
        String personId = exchange.getIn().getBody(String.class);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        logger.debug("Validating instructor existence for person ID: {}", personId);

        try {
            // Create a new exchange for the INSTRUCTORS endpoint call
            Exchange newExchange = exchange.copy();
            buildInstructorSearchCriteria(personId, newExchange);

            // Call the INSTRUCTORS endpoint to validate instructor existence
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS, newExchange);

            if (parsedAssocResponse == null || parsedAssocResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Filter results to only include instructors that match the person ID
            List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                .collect(java.util.stream.Collectors.toList());

            if (filteredResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed after filtering: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Set the instructor association data for use by the transform
            exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);

            logger.debug("Instructor validation successful for person ID: {}. Found {} instructor records.",
                        personId, filteredResponse.size());

        } catch (ApplicationException e) {
            // Re-throw application exceptions as-is
            throw e;
        } catch (Exception e) {
            // Log and wrap other exceptions
            logger.error("Error during instructor validation for person ID {}: {}", personId, e.getMessage(), e);
            throw new ApplicationException("Error validating instructor: " + e.getMessage(), 500);
        }
    }

    
    /**
     * Build search criteria for INSTRUCTORS endpoint using person ID
     */
    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        
        // Build criteria as specified in ticket: {"instructor":"<person-id>"}
        Map<String, Object> criteria = Map.of("instructor", personId);
        
        entityRequest.setCriteria(criteria);
        newExchange.getMessage().setBody(entityRequest);
        
        logger.debug("Built instructor search criteria: {}", criteria);
    }
    
    /**
     * Extract person ID from instructor data structure
     */
    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }
}
