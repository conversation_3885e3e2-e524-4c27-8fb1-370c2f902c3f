package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionSchedule
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.moderncampus.integration.transform.support.DaysOfWeek
import org.springframework.stereotype.Component

import java.time.ZonedDateTime

import static org.moderncampus.integration.transform.support.DateTimeFormatters.fromUtcDateTimeString
import static org.moderncampus.integration.transform.support.DateTimeFormatters.mapDate

@Component
@CompileStatic
class MCSectionScheduleTransforms {

    ITransformer<String, MCSectionSchedule> mcSectionScheduleTransformer = (TransformContext ctx, String body) -> {

        def rootNode = new JsonSlurper().parseText(body) as Map<String,Object>

        Optional<ZonedDateTime> startOn = Optional.ofNullable(rootNode?.recurrence?['timePeriod']?['startOn'] as String).map(dt -> fromUtcDateTimeString(dt))
        Optional<ZonedDateTime> endOn = Optional.ofNullable(rootNode?.recurrence?['timePeriod']?['endOn'] as String).map(dt -> fromUtcDateTimeString(dt))

        def sectionSchedule = new MCSectionSchedule(
                id: rootNode?.id,
                section: rootNode?.section?['id'],
                instructionalMethod: rootNode?.instructionalMethod?['id'],
                startTime: startOn.map { it.toOffsetDateTime().toOffsetTime() }.orElse(null),
                endTime: endOn.map { it.toOffsetDateTime().toOffsetTime() }.orElse(null),
                startDate: startOn.map { it.toLocalDate() }.orElse(null),
                endDate: endOn.map { it.toLocalDate() }.orElse(null),

                workload: rootNode?.workLoad as BigDecimal
        )

        List<String> roomIdArray = rootNode?.locations?['location']?['room']?['id'] as List<String>
        if(roomIdArray)
            sectionSchedule.room = roomIdArray?.get(0)

        if(rootNode?.approvals)
            sectionSchedule.approvalOverrides = rootNode.approvals.findAll { 'user'.equalsIgnoreCase(it['approvalEntity'] as String) }
                    .collect { new MCSectionSchedule.MCSectionScheduleApproval([ type:it['approvalType'] as String, approver:it['approvalEntity'] as String]) }

        def recurrence = rootNode?.recurrence
        if(recurrence) {
             recurrence.with {
                 sectionSchedule.recurrenceType = it['repeatRule']['type']
                 sectionSchedule.recurrenceInterval = it['repeatRule']['interval'] as Integer
                 sectionSchedule.recurrenceUntil = mapDate(it['repeatRule']['ends']?['date'] as String)

                 if('weekly'.equalsIgnoreCase(sectionSchedule.recurrenceType)) {
                     List<String> daysOfWeek = it['repeatRule']['daysOfWeek'] as List<String>

                     if (daysOfWeek) {
                         DaysOfWeek.values().each {
                             sectionSchedule[it.flagName()] = daysOfWeek.contains(it.lowerName())
                         }
                     }
                 }
                 if('monthly'.equalsIgnoreCase(sectionSchedule.recurrenceType)) {
                     def repeatBy = it['repeatRule']?['repeatBy']
                     if(repeatBy) {
                         if (repeatBy?['dayOfMonth'])
                            sectionSchedule.recurrenceByDayOfMo =repeatBy?['dayOfMonth'] as Integer
                         if(repeatBy?['dayOfWeek']) {
                             sectionSchedule.recurrenceByDayOfWk = repeatBy?['dayOfWeek']?['occurrence'] as Integer
                             def dayVariable = DaysOfWeek.from(repeatBy?['dayOfWeek']?['day'] as String)
                             DaysOfWeek.values().each {
                                 sectionSchedule[it.flagName()] = (it == dayVariable)
                             }
                         }
                     }
                 }
            }
        }

        return sectionSchedule
    }

}
