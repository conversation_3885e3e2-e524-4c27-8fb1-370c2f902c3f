package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.ellucian.workflow.transform.ethos.BaseEllucianSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleaguePersonSearchTransforms extends BaseEllucianSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def colleaguePersonId = searchCriteria["colleaguePersonId"] as String
        if (colleaguePersonId) {
            def criteria = [
                    credentials: [
                            [type: 'colleaguePersonId', value: colleaguePersonId]
                    ]
            ]
            queryParams["criteria"] = JsonOutput.toJson(criteria)
        }
    }

}
