package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_CREATE_PERSON;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_PERSON;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianCloudCommonAssociationHandler;
import org.moderncampus.integration.route.dto.RouteExecutorResult;

public abstract class ColleagueEthosPersonAssociationHandler extends EllucianCloudCommonAssociationHandler
        implements Processor {

    protected boolean isCreatePerson(String useCase) {
        return USE_CASE_CREATE_PERSON.equals(useCase);
    }

    protected boolean isUpdatePerson(String useCase) {
        return USE_CASE_UPDATE_PERSON.equals(useCase);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        MCPerson personWriteRequest = getOriginalRequest(exchange, MCPerson.class);
        RouteExecutorResult personWriteResponse = exchange.getMessage().getBody(RouteExecutorResult.class);
        String useCase = extractUseCase(exchange);
        String personId = extractObjectId(isCreatePerson(useCase), isUpdatePerson(useCase), personWriteRequest,
                personWriteResponse);

        if (personId != null && personWriteRequest != null) {
            handle(extractUseCase(exchange), personWriteRequest, personId);
        }
    }

    protected abstract void handle(String useCase, MCPerson request, String personId) throws Exception;

}
