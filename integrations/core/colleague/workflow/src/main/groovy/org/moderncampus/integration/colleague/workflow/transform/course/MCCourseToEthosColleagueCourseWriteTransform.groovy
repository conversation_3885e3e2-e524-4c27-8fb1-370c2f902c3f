package org.moderncampus.integration.colleague.workflow.transform.course

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCCourse
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_COURSE
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCCourseToEthosColleagueCourseWriteTransform extends BaseTransformer<MCCourse, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCCourse course) {
        Map<String, ?> requestRoot = [:]
        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> courseTitleTypeByIdMap = ethosAssocCacheMap[ColleagueEthosAPIResource.COURSE_TITLE_TYPES]
        Map<String, Map> creditCategoriesByIdMap = ethosAssocCacheMap[ColleagueEthosAPIResource.CREDIT_CATEGORIES]
        Map<String, Map> adminInstructionalMethodsTypeByIdMap = ethosAssocCacheMap[ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS]
        String loadedEntity = ctx.getContextProp(LOADED_ENTITY, String.class)

        if (USE_CASE_UPDATE_COURSE == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            requestRoot = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            mapId(requestRoot, course)
        }

        mapTitles(requestRoot, course, courseTitleTypeByIdMap)
        mapDescriptions(requestRoot, course)
        mapStartDate(requestRoot, course)
        mapEndDate(requestRoot, course)
        mapCode(requestRoot, course)
        mapNumber(requestRoot, course)
        mapCourseCategories(requestRoot, course)
        mapCredits(requestRoot, course, creditCategoriesByIdMap)
        mapSubjects(requestRoot, course)
        mapTopics(requestRoot, course)
        mapAcademicLevels(requestRoot, course)
        mapGradeSchemes(requestRoot, course)
        mapInstructionalMethods(requestRoot, course)
        mapHours(requestRoot, course, adminInstructionalMethodsTypeByIdMap)
        mapStatus(requestRoot, course)
        mapDepartments(requestRoot, course)
        mapStartTerm(requestRoot, course)
        mapCipCode(requestRoot, course)
        commonWriteTransform.mapEthosExtensions(requestRoot, course)

        return JsonOutput.toJson(requestRoot)
    }

    private void mapId(Map<String, ?> requestRoot, MCCourse course) {
        requestRoot.put("id", EllucianConstants.NIL_GUID)
    }

    private void mapTitles(Map<String, ?> requestRoot, MCCourse course, Map<String, Map> courseTitleTypeByIdMap) {
        if ((isValueExist(course.shortTitle)) || (isValueExist(course.longTitle))) {
            def titlesArr = []
            def courseTitleTypeByCodeMap = courseTitleTypeByIdMap.collectEntries {
                [(it.value['code']): it.value]
            } as TreeMap<String, Map>
            def courseTitleTypeByCodeMapInsCase = new TreeMap<String, Map>(String.CASE_INSENSITIVE_ORDER)
            courseTitleTypeByCodeMapInsCase.putAll(courseTitleTypeByCodeMap)
            if (isValueExist(course.shortTitle)) {
                String shortId = courseTitleTypeByCodeMapInsCase['short']?['id']
                if (isValueExist(shortId)) {
                    titlesArr.push([
                            "type" : [
                                    "id": shortId
                            ],
                            "value": course.shortTitle
                    ]) as List<Map<String, ?>>
                }
            }
            if (isValueExist(course.longTitle)) {
                String longId = courseTitleTypeByCodeMapInsCase['long']?['id']
                if (isValueExist(longId)) {
                    titlesArr.push([
                            "type" : [
                                    "id": longId
                            ],
                            "value": course.longTitle
                    ]) as List<Map<String, ?>>
                }
            }
            requestRoot.put("titles", titlesArr)
        }
    }

    private void mapDescriptions(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.description)) {
            requestRoot.put("description", course.description)
        }
    }

    private void mapStartDate(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.startOn)) {
            requestRoot.put("schedulingStartOn", DateTimeFormatter.ISO_LOCAL_DATE.format(course.startOn))
        }
    }

    private void mapEndDate(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.endOn)) {
            requestRoot.put("schedulingEndOn", DateTimeFormatter.ISO_LOCAL_DATE.format(course.endOn))
        }
    }

    private void mapCode(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.code)) {
            requestRoot.put("code", course.code)
        }
    }

    private void mapNumber(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.number)) {
            requestRoot.put("number", course.number)
        }
    }

    private void mapCourseCategories(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.courseCategories)) {
            requestRoot.put("categories", course.courseCategories.collect {
                ["id": it]
            })
        }
    }

    private void mapCredits(Map<String, ?> requestRoot, MCCourse course, Map<String, Map> creditCategoriesByIdMap) {
        if (isValueExist(course.creditType) && (isValueExist(course.creditsMin) || isValueExist(course.creditsMax) || isValueExist(course.ceuMin))) {
            def creditsArr = []
            String creditCategoryType = creditCategoriesByIdMap?[course.creditType]?["creditType"]
            String creditCategoryTypeId = course.creditType
            if (isValueExist(creditCategoryType) && (isValueExist(course.creditsMin) || isValueExist(course.creditsMax))) {
                Map<String, ?> creditMap = [
                        "creditCategory": [
                                "creditType": creditCategoryType,
                                "detail"    : [
                                        "id": creditCategoryTypeId
                                ]
                        ],
                        "measure"       : "credit",
                ]
                if (isValueExist(course.creditsMin)) {
                    creditMap['minimum'] = course.creditsMin
                }
                if (isValueExist(course.creditsMax)) {
                    creditMap['maximum'] = course.creditsMax
                }
                if (isValueExist(course.creditsIncrement)) {
                    creditMap['increment'] = course.creditsIncrement
                }
                creditsArr.push(creditMap)
            }
            if (isValueExist(creditCategoryType) && (isValueExist(course.ceuMin))) {
                Map<String, ?> creditMap = [
                        "creditCategory": [
                                "creditType": creditCategoryType,
                                "detail"    : [
                                        "id": creditCategoryTypeId
                                ]
                        ],
                        "measure"       : "ceu",
                ]
                if (isValueExist(course.ceuMin)) {
                    creditMap['minimum'] = course.ceuMin
                }
                if (isValueExist(course.creditsIncrement)) {
                    creditMap['increment'] = course.creditsIncrement
                }
                creditsArr.push(creditMap)
            }
            requestRoot.put("credits", creditsArr)
        }
    }

    private void mapSubjects(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.subjects)) {
            requestRoot.put("subject", ["id": course.subjects.isEmpty() ? "" : course.subjects[0]])
        }
    }

    private void mapAcademicLevels(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.academicLevels)) {
            requestRoot.put("academicLevels", course.academicLevels.collect {
                ["id": it]
            })
        }
    }

    private void mapGradeSchemes(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.gradeSchemes)) {
            def gradeSchemeArr = []
            course.gradeSchemes.collect {
                gradeSchemeArr.push(
                        [
                                "gradeScheme": ["id": it],
                                "usage"      : "default"
                        ]
                )
            }
            requestRoot.put("gradeSchemes", gradeSchemeArr)
        }
    }

    private void mapInstructionalMethods(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.instructionalMethods)) {
            def instMethArr = []
            course.instructionalMethods.collect {
                instMethArr.push(
                        [
                                "instructionalMethod": ["id": it.id]
                        ]
                )
            }
            requestRoot.put("instructionalMethodDetails", instMethArr)
        }
    }

    private void mapHours(Map<String, ?> requestRoot, MCCourse course, Map<String, Map> adminInstructionalMethodsByIdMap) {
        if (isValueExist(course.instructionalMethods)) {
            def adminInstructionalMethodsByInstMethodIdMap = adminInstructionalMethodsByIdMap.collectEntries {
                [it.value['instructionalMethod']['id'], it.value]
            }
            def hoursArr = null
            course.instructionalMethods.findAll {
                return isValueExist(it.hours)
            }.each {
                def adminInstMethodAssoc = adminInstructionalMethodsByInstMethodIdMap[it.id]
                if (isValueExist(adminInstMethodAssoc)) {
                    if (!isValueExist(hoursArr)) hoursArr = []
                    hoursArr.push(["administrativeInstructionalMethod": ["id": adminInstMethodAssoc['id']], "minimum": it.hours])
                }
            }
            if (isValueExist(hoursArr)) {
                requestRoot.put("hours", hoursArr)
            }
        }
    }

    private void mapStatus(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.status)) {
            requestRoot.put("status", ["id": course.status])
        }
    }

    private void mapDepartments(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.departments)) {
            requestRoot.put("owningInstitutionUnits", course.departments.collect {
                ["institutionUnit"    : ["id": it.id],
                 "ownershipPercentage": it.ownershipPercentage]
            })
        }
    }

    private void mapTopics(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.topics)) {
            requestRoot.put("topic", ["id": course.topics.isEmpty() ? "" : course.topics[0]])
        }
    }

    private void mapStartTerm(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.startTerm)) {
            requestRoot.put("administrativePeriod", ["id": course.startTerm])
        }
    }

    private void mapCipCode(Map<String, ?> requestRoot, MCCourse course) {
        if (isValueExist(course.cipCode)) {
            def cipCodeArr = []
            cipCodeArr.push(["cipCode": [
                    "id": course.cipCode
            ]])
            requestRoot.put("additionalClassifications", cipCodeArr)
        }
    }

}
