package org.moderncampus.integration.colleague.workflow.transform;

import java.util.List;

import org.apache.camel.Exchange;
import org.moderncampus.integration.colleague.workflow.transform.common.EthosColleagueReadTransforms;
import org.moderncampus.integration.colleague.workflow.transform.instructor.EthosColleagueInstructorReadTransforms;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionScheduleTransforms;
import org.moderncampus.integration.dto.core.MCAcademicLevel;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCombinedInstructor;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCInstructionalMethod;
import org.moderncampus.integration.dto.core.MCInstructor;
import org.moderncampus.integration.dto.core.MCLocation;
import org.moderncampus.integration.dto.core.MCOrganizationalUnit;
import org.moderncampus.integration.dto.core.MCRoom;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCSubject;
import org.moderncampus.integration.transform.IExtSystemReadTransformer;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueReadTransformer implements IExtSystemReadTransformer {

    EthosColleagueReadTransforms readTransforms;

    MCSectionScheduleTransforms sectionScheduleTransforms;

    EthosColleagueInstructorReadTransforms instructorReadTransforms;

    @Override
    public MCOrganizationalUnit mapToOrganizationalUnit(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSubject mapToSubject(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcSubjectTransformer(), exchange, body);
    }

    @Override
    public MCInstructionalMethod mapToInstructionalMethod(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcInstructionalMethod(), exchange, body);
    }

    @Override
    public MCLocation mapToLocation(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcLocationTransformer(), exchange, body);
    }

    @Override
    public MCAcademicLevel mapToAcademicLevel(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcAcademicLevelTransformer(), exchange, body);
    }

    @Override
    public MCRoom mapToRoom(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcRoomTransformer(), exchange, body);
    }

    @Override
    public MCSection mapToSection(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionSchedule mapToSectionSchedule(String body, Exchange exchange) throws Exception {
        return invokeTransform(sectionScheduleTransforms.getMcSectionScheduleTransformer(), exchange, body);
    }

    @Override
    public MCCourse mapToCourse(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcCourseTransformer(), exchange,
                exchange.getIn().getBody(String.class));
    }

    @Override
    public MCAcademicPeriod mapToAcademicPeriod(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCInstructor mapToInstructor(String body, Exchange exchange) throws Exception {
        return invokeTransform(instructorReadTransforms.getMcInstructorTransformer(), exchange, body);
    }

    @Override
    public MCSectionCrossList mapToSectionCrossList(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcSectionCrossListTransformer(), exchange, exchange.getIn().getBody(String.class));
    }

    @Override
    public MCSectionInstructorAssignment mapToSectionInstructorAssignment(String body, Exchange exchange)
            throws Exception {
        return invokeTransform(readTransforms.getMcSectionInstructorAssignmentTransformer(), exchange, exchange.getIn().getBody(String.class));
    }

    @Override
    public MCAddress mapToAddress(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcAddressTransformer(), exchange, exchange.getIn().getBody(String.class));
    }

    @Override
    public MCCourseSectionInformation mapToCourseSectionInformation(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionCrossListGroup mapToSectionCrossListGroup(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public List<MCSectionCrossListGroup> mapToSectionCrossListGroups(String body, Exchange exchange) throws Exception {
        return null;
    }

    public MCCombinedInstructor mapToCombinedInstructor(String body, Exchange exchange) throws Exception {
        return invokeTransform(instructorReadTransforms.getMcCombinedInstructorTransformer(), exchange, body);
    }
}
