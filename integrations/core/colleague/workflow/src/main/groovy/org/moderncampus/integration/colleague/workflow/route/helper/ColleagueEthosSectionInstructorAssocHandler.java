package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;

import java.util.HashMap;
import java.util.List;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ColleagueEthosSectionInstructorAssocHandler extends ColleagueEthosSectionAssociationHandler {

    IRouteExecutor routeExecutor;

    @Override
    protected void handle(String useCase, @NonNull MCSection sectionRequest, @NonNull String sectionId)
            throws Exception {
        List<? extends MCSectionInstructorAssignment> sectionInstructors = sectionRequest.getInstructors();
        if (!CollectionUtils.isEmpty(sectionInstructors) && isCreateSection(useCase)) {
            createSectionInstructors(sectionId, sectionInstructors);
        } else if (isUpdateSection(useCase) && sectionInstructors != null) {
            List<MCSectionInstructorAssignment> existingInstructors = retrieveAllSectionInstructors(sectionId, null);
            deleteSectionInstructors(existingInstructors, null, false);
            createSectionInstructors(sectionId, sectionInstructors);
        }
    }

    @Override
    protected void handleGet(List<MCSection> mcSections) throws Exception {
        List<MCSection> withSectionSchedules = mcSections.stream()
                .filter(mcSection -> !CollectionUtils.isEmpty(mcSection.getSectionSchedules())).toList();

        for (MCSection mcSection : withSectionSchedules) {
            List<MCSectionInstructorAssignment> mcInstructorAssigns = retrieveAllSectionInstructors(
                    mcSection.getId(), null);
            mcSection.setInstructors(mcInstructorAssigns);
        }
    }

    public void deleteSectionInstructorsForSchedule(String scheduleId) throws Exception {
        List<MCSectionInstructorAssignment> existingInstructors = retrieveAllSectionInstructors(null, scheduleId);
        deleteSectionInstructors(existingInstructors, scheduleId, true);
    }

    private void deleteSectionInstructors(List<MCSectionInstructorAssignment> existingInstructors, String scheduleId,
            boolean isScheduleInstructor) throws Exception {
        if (existingInstructors != null && !existingInstructors.isEmpty()) {
            for (MCSectionInstructorAssignment existingInstructor : existingInstructors) {
                if (isScheduleInstructor && scheduleId != null && (existingInstructor.getSectionSchedules() == null)
                        || isScheduleInstructor && existingInstructor.getSectionSchedules().stream()
                        .noneMatch(schedule -> schedule.getId().equals(scheduleId))) {
                    continue;
                }
                RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_DELETE_SECTION_INSTRUCTOR_ASSIGNMENT,
                        existingInstructor.getId());
            }
        }
    }

    private List<MCSectionInstructorAssignment> retrieveAllSectionInstructors(String sectionId, String scheduleId)
            throws Exception {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(new HashMap<>());
        if (sectionId != null) {
            entityRequest.getCriteria().put("section", sectionId);
        }
        if (scheduleId != null) {
            entityRequest.getCriteria().put("sectionSchedule", scheduleId);
        }
        RouteExecutorResult result = RouteSupport.executeRoute(routeExecutor,
                V1_COLLEAGUE_GET_SECTION_INSTRUCTOR_ASSIGNMENTS,
                entityRequest);
        return (List<MCSectionInstructorAssignment>) result.getResults();
    }

    private void createSectionInstructors(String sectionId,
            List<? extends MCSectionInstructorAssignment> sectionInstructors) throws Exception {
        for (MCSectionInstructorAssignment mcSectionInstructorAssignment : sectionInstructors) {
            mcSectionInstructorAssignment.setSectionId(sectionId);
            RouteExecutorResult result = RouteSupport.executeRoute(routeExecutor,
                    V1_COLLEAGUE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT, mcSectionInstructorAssignment);
        }
    }

}
