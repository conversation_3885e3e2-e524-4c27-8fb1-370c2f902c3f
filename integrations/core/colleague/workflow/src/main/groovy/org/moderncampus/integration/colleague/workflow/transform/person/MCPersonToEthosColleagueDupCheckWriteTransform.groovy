package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.colleague.workflow.transform.common.MCCommonColleagueWriteTransform
import org.moderncampus.integration.dto.core.MCPerson
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
@CompileStatic
class MCPersonToEthosColleagueDupCheckWriteTransform  extends BaseTransformer<MCPerson, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Autowired
    MCCommonColleagueWriteTransform commonColleagueWriteTransform

    @Override
    protected String doTransform(TransformContext context, MC<PERSON>erson person) {
        Map<String, ?> request = [:]

        def emailTypesById = commonWriteTransform.resourceMapFromContext(context, ColleagueEthosAPIResource.EMAIL_TYPES)
        def personNameTypesById = commonWriteTransform.resourceMapFromContext(context, ColleagueEthosAPIResource.PERSON_NAME_TYPES)

        commonColleagueWriteTransform.mapRoles(false, request, person.roles)
        commonColleagueWriteTransform.mapCredentials(false, request, person.credentials)
        commonColleagueWriteTransform.mapNames(false, request, person, personNameTypesById)
        commonColleagueWriteTransform.mapEmails(false, request, person.emails, emailTypesById)
        commonColleagueWriteTransform.mapGender(false, request, person)
        commonColleagueWriteTransform.mapDateOfBirth(false, request, person)
        commonColleagueWriteTransform.mapAlternativeCredentials(false, request, person)

        return JsonOutput.toJson(request)
    }

}
