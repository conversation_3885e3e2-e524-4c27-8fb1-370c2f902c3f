package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ColleagueEthosSectionSchedulesAssocHandler extends ColleagueEthosSectionAssociationHandler {

    IRouteExecutor routeExecutor;

    ColleagueEthosSectionInstructorAssocHandler instructorAssocHandler;

    private Map<String, ? extends MCSectionInstructorAssignment> generateSectionInstructorAssignmentAssocByIdMap(
            MCSection sectionCreateRequest,
            String sectionId) {
        return Optional.ofNullable(sectionCreateRequest.getInstructors())
                .map(instructors -> instructors.stream().collect(
                        Collectors.toMap(MCSectionInstructorAssignment::getInstructorId, (instructor) -> {
                            instructor.setSectionId(sectionId);
                            return instructor;
                        })))
                .orElse(new HashMap<>());
    }

    @Override
    protected void handle(String useCase, @NonNull MCSection sectionRequest, @NonNull String sectionId)
            throws Exception {
        List<? extends MCSectionSchedule> sectionSchedules = sectionRequest.getSectionSchedules();
        if (!CollectionUtils.isEmpty(sectionSchedules) && isCreateSection(useCase)) {
            createSectionSchedules(sectionRequest, sectionId);
        } else if (isUpdateSection(useCase) && sectionSchedules != null) {
            List<MCSectionSchedule> existingSchedules = retrieveAllSectionSchedules(sectionId);
            deleteAssociatedInstructors(existingSchedules);
            deleteSectionSchedules(existingSchedules);
            createSectionSchedules(sectionRequest, sectionId);
        }
    }

    private void deleteAssociatedInstructors(List<MCSectionSchedule> existingSchedules) throws Exception {
        if (!CollectionUtils.isEmpty(existingSchedules)) {
            for (MCSectionSchedule existingSchedule : existingSchedules) {
                instructorAssocHandler.deleteSectionInstructorsForSchedule(existingSchedule.getId());
            }
        }
    }

    private void deleteSectionSchedules(List<MCSectionSchedule> existingSchedules) throws Exception {
        if (existingSchedules != null && !existingSchedules.isEmpty()) {
            for (MCSectionSchedule existingSchedule : existingSchedules) {
                RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_DELETE_SECTION_SCHEDULE,
                        existingSchedule.getId());
            }
        }
    }

    protected List<MCSectionSchedule> retrieveAllSectionSchedules(String sectionId) throws Exception {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(Map.of("sectionId", sectionId));
        RouteExecutorResult result = RouteSupport.executeRoute(routeExecutor, V1_COLLEAGUE_GET_SECTION_SCHEDULES,
                entityRequest);
        return (List<MCSectionSchedule>) result.getResults();
    }

    private void createSectionSchedules(MCSection sectionRequest, String sectionId) throws Exception {
        Map<String, ? extends MCSectionInstructorAssignment> sectionInstructorAssocMap = generateSectionInstructorAssignmentAssocByIdMap(
                sectionRequest, sectionId);
        List<MCSectionInstructorAssignment> sectionInstructorAssignments = (List<MCSectionInstructorAssignment>) sectionRequest.getInstructors();
        for (MCSectionSchedule sectionSchedule : sectionRequest.getSectionSchedules()) {
            sectionSchedule.setSection(sectionId);
            RouteExecutorResult result = RouteSupport.executeRoute(routeExecutor,
                    V1_COLLEAGUE_CREATE_SECTION_SCHEDULE, sectionSchedule);
            MCSectionSchedule createdSchedule = (MCSectionSchedule) result.getResults();

            if (!CollectionUtils.isEmpty(sectionSchedule.getInstructors())) {
                for (MCSectionSchedule.MCSectionScheduleInstructor instructor : sectionSchedule.getInstructors()) {
                    if (sectionInstructorAssocMap.containsKey(instructor.getId())) {
                        MCSectionInstructorAssignment sectionInstructor = sectionInstructorAssocMap.get(
                                instructor.getId());
                        addScheduleAssociationToInstructorAssoc(sectionInstructor, createdSchedule.getId());
                    } else {
                        MCSectionInstructorAssignment sectionInstructor = createMcSectionInstructorAssignmentAssoc(
                                instructor,
                                sectionId, createdSchedule.getId());
                        sectionInstructorAssignments.add(sectionInstructor);
                    }
                }
            }
        }
    }

    private MCSectionInstructorAssignment createMcSectionInstructorAssignmentAssoc(
            MCSectionSchedule.MCSectionScheduleInstructor instructor,
            String sectionId, String scheduleId) {
        MCSectionInstructorAssignment sectionInstructor = new MCSectionInstructorAssignment();
        sectionInstructor.setId(instructor.getId());
        sectionInstructor.setSectionId(sectionId);
        addScheduleAssociationToInstructorAssoc(sectionInstructor, scheduleId);
        return sectionInstructor;
    }

    private void addScheduleAssociationToInstructorAssoc(@NonNull MCSectionInstructorAssignment sectionInstructor,
            String scheduleId) {
        MCSectionInstructorAssignment.MCSectionInstructorSchedule scheduleAssoc = new MCSectionInstructorAssignment.MCSectionInstructorSchedule();
        scheduleAssoc.setId(scheduleId);
        List<MCSectionInstructorAssignment.MCSectionInstructorSchedule> instructorSchedulesAssoc = sectionInstructor.getSectionSchedules();
        if (CollectionUtils.isEmpty(instructorSchedulesAssoc)) {
            sectionInstructor.setSectionSchedules(new ArrayList<>() {{
                add(scheduleAssoc);
            }});
        } else if (instructorSchedulesAssoc.stream().noneMatch(
                instructorSchedule -> instructorSchedule.getId()
                        .equals(scheduleId))) {
            instructorSchedulesAssoc.add(scheduleAssoc);
        } //otherwise the association already exists
    }
}
