package org.moderncampus.integration.colleague.workflow.transform.student

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCFinalGrade
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_FINAL_GRADE
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCFinalGradeToEthosColleagueWriteTransform extends BaseTransformer<MCFinalGrade, String> {

    private static final GRADE_TYPE_FINAL_CODE = "FINAL"

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCFinalGrade input) {

        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> sectionGradeStatuses = ethosAssocCacheMap[ColleagueEthosAPIResource.SECTION_GRADE_TYPES] as Map<String, Map>
        Map<String, ?> request = [:]

        if (USE_CASE_UPDATE_FINAL_GRADE == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            commonWriteTransform.mapId(request, input.id)
        } else {
            commonWriteTransform.mapId(request)
        }

        mapSectionRegistration(request, input)
        mapGrade(request, input, sectionGradeStatuses)
        mapLastAttendance(request, input)

        return JsonOutput.toJson(request)
    }

    void mapSectionRegistration(Map<String, Object> request, MCFinalGrade finalGrade) {
        if (isValueExist(finalGrade.getStudentEnrollment())) {
            request.sectionRegistration = ['id': finalGrade.getStudentEnrollment()]
        }
    }

    void mapGrade(Map<String, Object> request, MCFinalGrade finalGrade, Map<String, Map> gradeStatuses) {
        def type = gradeStatuses.find { it.value?['code'] == GRADE_TYPE_FINAL_CODE }
        if (!type) return

        def gradeMap = [type : [id: type.value?['id']],
                        grade: [id: finalGrade.grade.grade]]

        def incompleteGrade = finalGrade?.grade?.incompleteGrade
        if (isValueExist(incompleteGrade)) {
            Map<String, Object> incompleteMap = [:]

            if (isValueExist(incompleteGrade.extensionDate)) {
                incompleteMap.extensionDate = DateTimeFormatter.ISO_LOCAL_DATE.format(incompleteGrade.extensionDate)
            }

            if (isValueExist(incompleteGrade.finalGrade)) {
                incompleteMap.finalGrade = [id: incompleteGrade.finalGrade]
            }

            if (!incompleteMap?.isEmpty()) {
                gradeMap.incompleteGrade = incompleteMap
            }
        }
        request.grade = gradeMap
    }

    void mapLastAttendance(Map<String, Object> request, MCFinalGrade finalGrade) {
        def lastAttendance = finalGrade.lastAttendance
        if (isValueExist(lastAttendance)) {
            def lastAttendanceMap = [:]
            if (isValueExist(lastAttendance.date)) {
                lastAttendanceMap.date = DateTimeFormatter.ISO_LOCAL_DATE.format(lastAttendance.date)
            }

            if (isValueExist(lastAttendance.status)) {
                lastAttendanceMap.status = lastAttendance.status
            }

            if (!lastAttendanceMap.isEmpty()) {
                request.lastAttendance = lastAttendanceMap
            }
        }
    }
}