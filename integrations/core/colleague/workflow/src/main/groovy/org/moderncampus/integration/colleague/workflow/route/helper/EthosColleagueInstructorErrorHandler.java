package org.moderncampus.integration.colleague.workflow.route.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import org.moderncampus.integration.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * Error handler for instructor GET-by-ID endpoint that converts PERSONS 404 errors 
 * to instructor-specific error messages.
 * 
 * This handler intercepts 404 errors from the PERSONS endpoint and converts them
 * to the appropriate instructor error message: "No instructor was found for id '...'"
 * 
 * This ensures consistent error messaging regardless of whether:
 * - The person ID doesn't exist at all
 * - The person exists but is not an instructor
 */
@Component
@Qualifier("instructorErrorHandler")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorErrorHandler implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorErrorHandler.class);

    @Override
    public void process(Exchange exchange) throws Exception {
        // Get the exception that caused the error
        Exception exception = exchange.getProperty(Exchange.EXCEPTION_CAUGHT, Exception.class);
        
        if (exception == null) {
            logger.warn("Error handler called but no exception found in exchange");
            return;
        }

        // Extract the person ID from the exchange
        String personId = getPersonIdFromExchange(exchange);
        
        logger.debug("Handling error for person ID: {}, exception: {}", personId, exception.getMessage());

        // Check if this is a 404 error from PERSONS endpoint
        if (is404Error(exception)) {
            // Convert PERSONS 404 to instructor-specific error message
            String errorMessage = String.format("No instructor was found for id '%s'", personId);
            logger.info("Converting PERSONS 404 to instructor error: {}", errorMessage);
            
            // Create new ApplicationException with instructor-specific message
            ApplicationException instructorException = new ApplicationException(errorMessage, 404);
            
            // Set the new exception in the exchange
            exchange.setProperty(Exchange.EXCEPTION_CAUGHT, instructorException);
            exchange.getIn().setHeader(Exchange.HTTP_RESPONSE_CODE, 404);
            
            // Throw the new exception to be handled by the framework
            throw instructorException;
        }
        
        // For non-404 errors, let them pass through unchanged
        logger.debug("Non-404 error, letting it pass through: {}", exception.getMessage());
        throw exception;
    }
    
    /**
     * Extract person ID from the exchange
     */
    private String getPersonIdFromExchange(Exchange exchange) {
        // Try to get from RESOURCE_ID property first (this is set by the framework)
        String personId = exchange.getProperty("RESOURCE_ID", String.class);

        if (personId != null && !personId.trim().isEmpty()) {
            logger.debug("Found person ID from RESOURCE_ID property: {}", personId);
            return personId.trim();
        }

        // Fallback: try to get from CamelHttpUri header (full request URI)
        String httpUri = exchange.getIn().getHeader("CamelHttpUri", String.class);
        if (httpUri != null && httpUri.contains("/instructors/")) {
            String[] parts = httpUri.split("/instructors/");
            if (parts.length > 1) {
                String idPart = parts[1];
                // Remove query parameters if any
                int queryIndex = idPart.indexOf('?');
                if (queryIndex > 0) {
                    idPart = idPart.substring(0, queryIndex);
                }
                logger.debug("Found person ID from CamelHttpUri: {}", idPart);
                return idPart.trim();
            }
        }

        // Fallback: try to get from CamelHttpPath header
        String httpPath = exchange.getIn().getHeader("CamelHttpPath", String.class);
        if (httpPath != null && httpPath.contains("/instructors/")) {
            String[] parts = httpPath.split("/instructors/");
            if (parts.length > 1) {
                String idPart = parts[1];
                // Remove query parameters if any
                int queryIndex = idPart.indexOf('?');
                if (queryIndex > 0) {
                    idPart = idPart.substring(0, queryIndex);
                }
                logger.debug("Found person ID from CamelHttpPath: {}", idPart);
                return idPart.trim();
            }
        }

        // Fallback: try to get from path parameter header
        personId = exchange.getIn().getHeader("id", String.class);
        if (personId != null && !personId.trim().isEmpty()) {
            logger.debug("Found person ID from id header: {}", personId);
            return personId.trim();
        }

        // Fallback: try to get from exchange body
        personId = exchange.getIn().getBody(String.class);
        if (personId != null && !personId.trim().isEmpty()) {
            logger.debug("Found person ID from body: {}", personId);
            return personId.trim();
        }

        // Debug: log all available headers and properties
        logger.debug("Could not extract person ID. Available headers: {}", exchange.getIn().getHeaders().keySet());
        logger.debug("Available properties: {}", exchange.getProperties().keySet());

        return "unknown";
    }
    
    /**
     * Check if the exception represents a 404 error
     */
    private boolean is404Error(Exception exception) {
        if (exception == null) {
            return false;
        }
        
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        // Check for various 404 indicators
        return message.contains("404") || 
               message.contains("statusCode: 404") ||
               message.contains("HTTP operation failed") ||
               (exception instanceof ApplicationException && 
                ((ApplicationException) exception).getStatusCode() == 404);
    }
}
