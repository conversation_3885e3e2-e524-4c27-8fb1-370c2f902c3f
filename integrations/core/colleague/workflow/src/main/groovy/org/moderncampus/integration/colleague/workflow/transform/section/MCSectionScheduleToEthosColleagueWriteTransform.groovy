package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionSchedule
import org.moderncampus.integration.ellucian.workflow.transform.ethos.MCSectionScheduleToEthosWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist
import static org.moderncampus.integration.transform.support.DateTimeFormatters.toUtcDateTimeString

@Component
@CompileStatic
class MCSectionScheduleToEthosColleagueWriteTransform extends BaseTransformer<MCSectionSchedule, String> {

    @Autowired
    MCSectionScheduleToEthosWriteTransform scheduleWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCSectionSchedule schedule) {

        Map<String, ?> scheduleReq = [:]

        if (USE_CASE_UPDATE_SECTION == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            scheduleReq = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            scheduleWriteTransform.mapId(scheduleReq)
        }

        scheduleWriteTransform.mapSection(scheduleReq, schedule)
        scheduleWriteTransform.mapInstructionalMethod(scheduleReq, schedule)
        scheduleWriteTransform.mapRecurrenceTimePeriod(scheduleReq, schedule)
        scheduleWriteTransform.mapRoom(scheduleReq, schedule)
        scheduleWriteTransform.mapApprovalOverrides(scheduleReq, schedule)

        mapRepeatRule(scheduleReq, schedule)
        mapWorkload(scheduleReq, schedule)

        return JsonOutput.toJson(scheduleReq)
    }

    private void mapRepeatRule(Map<String, Object> req, MCSectionSchedule schedule) {
        if (!isValueExist(schedule.recurrenceType) || !isValueExist(schedule.recurrenceType)) return

        scheduleWriteTransform.createRecurrenceRepeatRule(req)
        def repeatRule = req.recurrence['repeatRule']

        repeatRule['type'] = schedule.recurrenceType
        repeatRule['interval'] = schedule.recurrenceInterval

        if (isValueExist(schedule.recurrenceUntil)) {
            repeatRule['ends'] = ['date': toUtcDateTimeString(schedule.recurrenceUntil, null)]
        }

        switch (schedule.recurrenceType) {
            case 'weekly':
                repeatRule['daysOfWeek'] = scheduleWriteTransform.daysOfWeek(schedule)
                break
            case 'monthly':
                if (isValueExist(schedule.recurrenceByDayOfWk)) {
                    repeatRule['repeatBy'] = ['dayOfWeek': ['occurrence': schedule.recurrenceByDayOfWk, 'day': scheduleWriteTransform.daysOfWeek
                    (schedule)]]
                } else if (isValueExist(schedule.recurrenceByDayOfMo)) {
                    repeatRule['repeatBy'] = ['dayOfMonth': schedule.recurrenceByDayOfMo]
                }
                break
        }

    }

    private void mapWorkload(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.workload)) {
            req.workload = schedule.workload
        }
    }

}
