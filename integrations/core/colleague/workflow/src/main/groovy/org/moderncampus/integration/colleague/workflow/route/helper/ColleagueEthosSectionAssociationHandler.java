package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_CREATE_SECTION;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianCloudCommonAssociationHandler;
import org.moderncampus.integration.route.dto.RouteExecutorResult;

/**
 * Handles associations on the Ethos section that require calls to separate Ethos APIs and are not available for
 * cascading on the section Ethos API
 */
public abstract class ColleagueEthosSectionAssociationHandler extends EllucianCloudCommonAssociationHandler
        implements Processor {

    protected boolean isCreateSection(String useCase) {
        return USE_CASE_CREATE_SECTION.equals(useCase);
    }

    protected boolean isUpdateSection(String useCase) {
        return USE_CASE_UPDATE_SECTION.equals(useCase);
    }

    public void process(Exchange exchange) throws Exception {
        MCSection sectionWriteRequest = getOriginalRequest(exchange, MCSection.class);
        RouteExecutorResult sectionWriteResponse = exchange.getMessage().getBody(RouteExecutorResult.class);
        String useCase = extractUseCase(exchange);
        String sectionId = extractObjectId(isCreateSection(useCase), isUpdateSection(useCase), sectionWriteRequest,
                sectionWriteResponse);
        if (sectionId != null && sectionWriteRequest != null) {
            handle(extractUseCase(exchange), sectionWriteRequest, sectionId);
        }
    }

    protected abstract void handle(String useCase, MCSection request, String sectionId) throws Exception;

}
