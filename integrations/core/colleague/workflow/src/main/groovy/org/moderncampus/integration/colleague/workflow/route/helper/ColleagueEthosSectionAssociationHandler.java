package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianCloudCommonAssociationHandler;
import org.moderncampus.integration.route.dto.RouteExecutorResult;

/**
 * Handles associations on the Ethos section that require calls to separate Ethos APIs and are not available for
 * cascading on the section Ethos API
 */
public abstract class ColleagueEthosSectionAssociationHandler extends EllucianCloudCommonAssociationHandler
        implements Processor {

    protected boolean isCreateSection(String useCase) {
        return USE_CASE_CREATE_SECTION.equals(useCase);
    }

    protected boolean isUpdateSection(String useCase) {
        return USE_CASE_UPDATE_SECTION.equals(useCase);
    }

    protected boolean isGetSection(String useCase) {return USE_CASE_GET_SECTION.equals(useCase);}

    protected boolean isGetSections(String useCase) {return USE_CASE_GET_SECTIONS.equals(useCase);}


    public void process(Exchange exchange) throws Exception {
        MCSection sectionWriteRequest = getOriginalRequest(exchange, MCSection.class);
        RouteExecutorResult sectionWriteResponse = exchange.getMessage().getBody(RouteExecutorResult.class);
        String useCase = extractUseCase(exchange);

        if (isCreateSection(useCase) || isUpdateSection(useCase)) {
            String sectionId = extractObjectId(isCreateSection(useCase), isUpdateSection(useCase), sectionWriteRequest,
                    sectionWriteResponse);
            if (sectionId != null && sectionWriteRequest != null) {
                handle(extractUseCase(exchange), sectionWriteRequest, sectionId);
            }
            return;
        }

        if (isGetSection(useCase)) {
            List<MCSection> mcSection = Optional.ofNullable(sectionWriteResponse.getResults())
                    .map(rawObj -> List.of((MCSection) rawObj)).orElse(Collections.emptyList());
            handleGet(mcSection);
            return;
        }

        if (isGetSections(useCase)) {
            List<MCSection> mcSections = Optional.ofNullable(sectionWriteResponse.getResults())
                    .map(rawList -> (List<MCSection>) rawList).orElse(Collections.emptyList());
            handleGet(mcSections);
        }
    }

    protected abstract void handle(String useCase, MCSection request, String sectionId) throws Exception;

    protected abstract void handleGet(List<MCSection> mcSections) throws Exception;

}
