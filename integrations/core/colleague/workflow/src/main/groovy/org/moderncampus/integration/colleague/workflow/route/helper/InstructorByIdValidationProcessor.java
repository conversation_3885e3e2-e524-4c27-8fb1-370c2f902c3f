package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Simple processor to validate that a person is an instructor for the new /instructors/id={personId} endpoint.
 * 
 * This processor executes BEFORE calling the PERSONS endpoint and validates that the requested person ID 
 * corresponds to an actual instructor. If not, it throws a 404 error.
 */
@Component
@Qualifier("instructorByIdValidationProcessor")
public class InstructorByIdValidationProcessor extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(InstructorByIdValidationProcessor.class);

    public InstructorByIdValidationProcessor(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
        logger.info("InstructorByIdValidationProcessor initialized successfully");
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        // Extract person ID from the exchange - for the new route it will be in the path parameter
        String personId = extractPersonIdFromPath(exchange);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        // Set the person ID in the body for the subsequent processing
        exchange.getIn().setBody(personId);

        logger.debug("InstructorByIdValidationProcessor: Set person ID in exchange body: {}", personId);
    }
    
    /**
     * Extract person ID from the path parameter for the new /instructors/id={personId} route
     */
    private String extractPersonIdFromPath(Exchange exchange) {
        // For the new route /instructors/id={personId}, the ID should be in the path variable
        // Try to get it from the path variable first
        String personId = exchange.getIn().getHeader("id", String.class);

        if (personId != null) {
            return personId.trim();
        }

        // Fallback: try to extract from the HTTP path
        String httpPath = exchange.getIn().getHeader("CamelHttpPath", String.class);
        if (httpPath != null && httpPath.contains("id=")) {
            String[] parts = httpPath.split("id=");
            if (parts.length > 1) {
                return parts[1].trim();
            }
        }

        // Last fallback: try to get from body if available
        return exchange.getIn().getBody(String.class);
    }

}
