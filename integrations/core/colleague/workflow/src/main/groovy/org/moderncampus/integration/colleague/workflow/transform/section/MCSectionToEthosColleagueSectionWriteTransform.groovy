package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSection
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.ellucian.workflow.transform.ethos.MCSectionToEthosSectionWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource.*
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionToEthosColleagueSectionWriteTransform extends BaseTransformer<MCSection, String> {

    @Autowired
    MCSectionToEthosSectionWriteTransform ethosWriteTransform

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCSection section) {
        Map<String, ?> requestRoot = [:]

        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> sectionTitleTypeByIdMap = ethosAssocCacheMap[SECTION_TITLE_TYPES] as Map<String, Map>
        Map<String, Map> sectionDescriptionTypeByIdMap = ethosAssocCacheMap[SECTION_DESCRIPTION_TYPES] as Map<String, Map>
        Map<String, Map> creditCategoriesByIdMap = ethosAssocCacheMap[CREDIT_CATEGORIES] as Map<String, Map>
        Map<String, Map> adminMethodsTypeByIdMap = ethosAssocCacheMap[ADMINISTRATIVE_INSTRUCTIONAL_METHODS] as Map<String, Map>

        if (USE_CASE_UPDATE_SECTION == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            requestRoot = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            commonWriteTransform.mapId(requestRoot)
        }

        ethosWriteTransform.mapStartDate(requestRoot, section)
        ethosWriteTransform.mapEndDate(requestRoot, section)
        ethosWriteTransform.mapCode(requestRoot, section)
        ethosWriteTransform.mapNumber(requestRoot, section)
        ethosWriteTransform.mapAcademicPeriod(requestRoot, section)
        ethosWriteTransform.mapCensusDates(requestRoot, section)
        ethosWriteTransform.mapCourse(requestRoot, section)
        ethosWriteTransform.mapCourseCategories(requestRoot, section)
        ethosWriteTransform.mapSite(requestRoot, section)
        ethosWriteTransform.mapAcademicLevels(requestRoot, section)
        ethosWriteTransform.mapInstructionalMethods(requestRoot, section)
        ethosWriteTransform.mapGradeSchemes(requestRoot, section)
        ethosWriteTransform.mapStatus(requestRoot, section)
        ethosWriteTransform.mapDuration(requestRoot, section)
        ethosWriteTransform.mapEnrollmentSize(requestRoot, section)
        ethosWriteTransform.mapInstructionalDeliveryMethod(requestRoot, section)
        ethosWriteTransform.mapBillingHours(requestRoot, section)
        ethosWriteTransform.mapOwningInstitutionUnits(requestRoot, section)

        mapTitles(requestRoot, section, sectionTitleTypeByIdMap)
        mapDescriptions(requestRoot, section, sectionDescriptionTypeByIdMap)
        mapCrossListed(requestRoot, section)
        mapHours(requestRoot, section, adminMethodsTypeByIdMap)
        mapCredits(requestRoot, section, creditCategoriesByIdMap)
        mapReportingAcademicPeriod(requestRoot, section)
        mapInstructionalPlatform(requestRoot, section)
        mapCourseLevels(requestRoot, section)
        mapBillingMethod(requestRoot, section)
        mapCatalogDisplay(requestRoot, section)
        mapAlternateIds(requestRoot, section)
        commonWriteTransform.mapEthosExtensions(requestRoot, section)

        return JsonOutput.toJson(requestRoot)
    }

    private void mapId(Map<String, Object> requestRoot, MCSection section){
        if (isValueExist(section?.id)) {
            requestRoot['id'] = section.id
        }
    }

    private void mapDescriptions(Map<String, ?> requestRoot, MCSection section, Map<String, Map> descriptionTypeByIdMap) {
        if (isValueExist(section?.description)) {
            def descriptionsArr = []
            def descriptionTypeByCodeMap = descriptionTypeByIdMap.collectEntries {
                def code = it.value['code'] as String
                [(code.toLowerCase()): it.value]
            }
            if (isValueExist(section?.description)) {
                String longId = descriptionTypeByCodeMap['printed']?['id']
                if (isValueExist(longId)) {
                    descriptionsArr.push([
                            "type" : [
                                    "id": longId
                            ],
                            "value": section.description
                    ]) as List<Map<String, ?>>
                }
            }
            if (isValueExist(descriptionsArr)) {
                requestRoot.put("descriptions", descriptionsArr)
            }
        }
    }

    private void mapTitles(Map<String, ?> requestRoot, MCSection section, Map<String, Map> sectionTitleTypeByIdMap) {
        if (isValueExist(section?.shortTitle)) {
            def titlesArr = []
            def sectionTitleTypeByCodeMap = sectionTitleTypeByIdMap.collectEntries {
                def code = it.value['code'] as String
                [(code.toLowerCase()): it.value]
            }
            if (isValueExist(section?.shortTitle)) {
                String shortId = sectionTitleTypeByCodeMap['short']?['id']
                if (isValueExist(shortId)) {
                    titlesArr.push([
                            "type" : [
                                    "id": shortId
                            ],
                            "value": section.shortTitle
                    ]) as List<Map<String, ?>>
                }
            }
            requestRoot.put("titles", titlesArr)
        }
    }

    private void mapHours(Map<String, Object> requestRoot, MCSection section, Map<String, Map> adminInstructionalMethodsTypeByIdMap) {
        if (!isValueExist(section?.instructionalMethods)) return

        def validInstructionalMethods = section.instructionalMethods.findAll { isValueExist(it.hours) }
        if (!validInstructionalMethods) return

        def instructionalIdsMap = adminInstructionalMethodsTypeByIdMap.collectEntries {
            String instructionalMethodId = it.value['instructionalMethod']?['id']
            String adminInstructionalMethodId = it.key
            [instructionalMethodId, adminInstructionalMethodId]
        }

        def hoursList = validInstructionalMethods.collect {
            def adminInstructionalMethodId = instructionalIdsMap[it.id]
            Map<String, Object> elementMap = ['administrativeInstructionalMethod': ['id': adminInstructionalMethodId]] as Map<String, Object>

            // Should we validate this? Colleague is expecting one of day, week, month, term
            if (isValueExist(it.hoursInterval)) elementMap['interval'] = it.hoursInterval
            if (isValueExist(it.hours)) elementMap['minimum'] = it.hours

            return elementMap
        }

        requestRoot['hours'] = hoursList
    }

    private void mapCredits(Map<String, Object> requestRoot, MCSection section, Map<String, Map> creditCategoriesByIdMap) {
        if (!isValueExist(section?.minCredits) && !isValueExist(section?.minCeu)) return

        def creditCat = creditCategoriesByIdMap[section.creditType]
        Map<String, Object> baseMap = [
                'creditCategory': [
                        'creditType': creditCat['creditType'],
                        'detail'    : [
                                'id': section.creditType
                        ]
                ]
        ] as Map<String, Object>

        def creditsList = []
        if (isValueExist(section.minCredits)) {
            creditsList << createCreditsMap(baseMap, section)
        }

        if (isValueExist(section.minCeu)) {
            creditsList << createCeuMap(baseMap, section)
        }

        requestRoot['credits'] = creditsList
    }

    private void mapCrossListed(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.crossListed)) {
            requestRoot['crossListed'] = section.crossListed ? "crossListed" : "notCrossListed"
        }
    }

    private void mapReportingAcademicPeriod(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.reportingAcademicPeriod)) {
            requestRoot['reportingAcademicPeriod'] = ['id': section.reportingAcademicPeriod]
        }
    }

    private void mapInstructionalPlatform(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.instructionalPlatform)) {
            requestRoot['instructionalPlatform'] = ['id': section.instructionalPlatform]
        }
    }

    private void mapCourseLevels(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.courseLevels)) {
            requestRoot['courseLevels'] = section.courseLevels.collect { ['id': it.id] }
        }
    }

    private void mapBillingMethod(Map<String, ?> requestRoot, MCSection section) {
        if (section?.billingMethod) {
            requestRoot.put("chargeAssessmentMethod", ["id": section.billingMethod])
        }
    }

    private void mapCatalogDisplay(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section.catalogDisplay)) {
            requestRoot['catalogDisplay'] = section.catalogDisplay
        }
    }

    private void mapAlternateIds(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.otherIds)) {
            requestRoot['alternateIds'] = section.otherIds.collect{['title': it.type, 'value': it.value]}
        }
    }

    /*
     * Utility methods
     */

    private createCreditsMap(Map<String, Object> baseMap, MCSection section) {
        def creditMap = deepCopyMap(baseMap)

        creditMap['minimum'] = section.minCredits
        creditMap['measure'] = "credit"


        if (isValueExist(section?.maxCredits)) {
            creditMap['maximum'] = section.maxCredits
        }

        if (isValueExist(section?.creditsIncrement)) {
            creditMap['increment'] = section.creditsIncrement
        }

        return creditMap
    }

    private createCeuMap(Map<String, Object> baseMap, MCSection section) {
        def ceuMap = deepCopyMap(baseMap)

        ceuMap['minimum'] = section.minCeu
        ceuMap['measure'] = "ceu"

        return ceuMap
    }

    def deepCopyMap(Map original) {
        def json = JsonOutput.toJson(original)
        return new JsonSlurper().parseText(json)
    }


}
