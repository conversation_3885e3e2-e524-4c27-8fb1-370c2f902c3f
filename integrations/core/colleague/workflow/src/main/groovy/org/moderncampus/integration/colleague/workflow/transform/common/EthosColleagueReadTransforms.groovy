package org.moderncampus.integration.colleague.workflow.transform.common

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.*
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.DateTimeFormatters.mapDate

@Component
@CompileStatic
class EthosColleagueReadTransforms {

    ITransformer<String, MCCourse> mcCourseTransformer = (TransformContext ctx, String body) -> {
        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> courseTitleTypeByIdMap = ethosAssocCacheMap[ColleagueEthosAPIResource.COURSE_TITLE_TYPES]
        Map<String, Map> adminInstructionalMethodsTypeByIdMap = ethosAssocCacheMap[ColleagueEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS]
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def course = new MCCourse()

        course.id = rootNode.id
        course.status = rootNode.status?['id']

        rootNode['titles'].findAll {
            def courseTitleTypeObj = courseTitleTypeByIdMap?[it['type']['id']]
            return courseTitleTypeObj && (courseTitleTypeObj['code'] as String).equalsIgnoreCase("short")
        }.each { titleObj ->
            course.shortTitle = titleObj['value']
        }
        rootNode['titles'].findAll {
            def courseTitleTypeObj = courseTitleTypeByIdMap?[it['type']['id']]
            return courseTitleTypeObj && (courseTitleTypeObj['code'] as String).equalsIgnoreCase("long")
        }.each { titleObj ->
            course.longTitle = titleObj['value']
        }

        rootNode['credits']?.findAll {
            return it?['measure'] == 'credit'
        }?.each { creditsObj ->
            course.creditType = creditsObj['creditCategory']['detail']['id']
            course.creditsMin = creditsObj['minimum'] as Integer
            course.creditsMax = creditsObj['maximum'] ? creditsObj['maximum'] as Integer : null
            course.creditsIncrement = creditsObj['increment'] ? creditsObj['increment'] as Integer : null
        }
        rootNode['credits']?.findAll {
            return it?['measure'] == 'ceu'
        }?.each { creditsObj ->
            course.creditType = creditsObj['creditCategory']['detail']['id']
            course.creditsMin = creditsObj['minimum'] as Integer
            course.creditsMax = null
            course.creditsIncrement = creditsObj['increment'] ? creditsObj['increment'] as Integer : null
        }

        course.gradeSchemes = rootNode.gradeSchemes.collect {
            return it['gradeScheme']?['id'] as String
        }

        course.academicLevels = rootNode.academicLevels.collect {
            return it['id'] as String
        }

        course.number = rootNode.number
        course.instructionalMethods = rootNode.instructionalMethodDetails.collect { instMethodDetail ->
            new MCCourse.MCCourseInstructionalMethod().with {
                id = instMethodDetail['instructionalMethod']['id'] as String
                if (rootNode['hours']) {
                    def hourItem = rootNode['hours'].find { hourItem ->
                        adminInstructionalMethodsTypeByIdMap[hourItem['administrativeInstructionalMethod']['id']]?['instructionalMethod']['id'] == id
                    }
                    if (hourItem) {
                        hours = hourItem['minimum'] as Integer
                    }
                }
                it
            }
        }

        course.courseLevels = rootNode.courseLevels.collect {
            return it['id'] as String
        }

        course.subjects = [rootNode.subject?['id']] as List<String>

        course.departments = rootNode.owningInstitutionUnits.collect { it ->
            def department = new MCCourse.MCCourseDepartments()
            department.id = it['institutionUnit']?['id']
            department.ownershipPercentage = it['ownershipPercentage'] as Integer
            return department
        }

        return course
    }

    ITransformer<String, MCSectionCrossList> mcSectionCrossListTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def sectionCrossList = new MCSectionCrossList()

        sectionCrossList.id = rootNode.id

        sectionCrossList.maxCapacity = rootNode.maxEnrollment as Integer

        sectionCrossList.sections = rootNode.sections.collect {
            def crossListSection = new MCSectionCrossList.CrossListSection()
            crossListSection.setId(it['section']?['id'] as String)
            crossListSection.setPrimary(it['type'] == "primary")
            return crossListSection
        }

        return sectionCrossList
    }

    ITransformer<String, MCSectionInstructorAssignment> mcSectionInstructorAssignmentTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def assignment = new MCSectionInstructorAssignment()

        assignment.id = rootNode.id
        assignment.workloadHrs = rootNode.workLoad as Integer
        assignment.assignmentStartOn = mapDate(rootNode.workStartOn as String)
        assignment.assignmentEndOn = mapDate(rootNode.workEndOn as String)
        assignment.percentageResponsible = rootNode.responsibilityPercentage as Integer
        assignment.instructorRole = rootNode.instructorRole

        assignment.instructorId = rootNode.instructor?['id']
        assignment.sectionId = rootNode.section?['id']
        assignment.instructionalMethod = rootNode.instructionalMethod?['id']

        return assignment
    }

    ITransformer<String, MCAddress> mcAddressTransformer = (TransformContext ctx, String body) -> {

        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def address = new MCAddress(id: rootNode.id)

        def lines = rootNode.addressLines as String[]
        if(lines)
            lines.eachWithIndex{ String line, int i -> if(i<4) address['addressLine'+(i+1)] = line}

        def country = rootNode?.place?['country'] as Map<String,?>
        if(country){
            if(country.locality)
                address.city = country.locality
            if(country.postalCode)
                address.postalCode = country.postalCode


            address.country = new MCCountry(countryCode: country.code, countryName: country.title)

            if(country.region) {
                def region = country.region as Map<String,?>
                address.state = new MCState(stateCode: region.code, stateName: region.title)
            }

            if(country.subRegion) {
                def region = country.subRegion as Map<String,?>
                address.county = new MCCounty(countyCode: region.code, countyName: region.title)
            }
        }

        return address
    }

    ITransformer<String, MCAcademicLevel> mcAcademicLevelTransformer = (ctx, String body) -> {
        MCAcademicLevel academicLevel = new MCAcademicLevel()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        academicLevel.id = rootNode.id
        academicLevel.code = rootNode.code
        academicLevel.title = rootNode.title
        return academicLevel
    }

    ITransformer<String, MCSubject> mcSubjectTransformer = (ctx, String body) -> {
        MCSubject subject = new MCSubject()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        subject.id = rootNode.id
        subject.code = rootNode.abbreviation
        subject.title = rootNode.title
        return subject
    }

    ITransformer<String, MCRoom> mcRoomTransformer = (ctx, String body) -> {
        MCRoom room = new MCRoom()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        room.id = rootNode.id
        room.code = rootNode.number
        room.title = rootNode.title
        room.description = rootNode.description
        room.floor = rootNode.floor
        room.roomTypes = rootNode.roomTypes?.collect { it ->
            return it['type'] as String
        }
        def capacityList = rootNode.occupancies?.collect { it ->
            return it['maxOccupancy'] as Integer
        }
        room.maxCapacity = capacityList.size() == 0 ? 0 : capacityList[0]
        room.location = rootNode?.site?['id']
        room.building = rootNode?.building?['id']
        room.characteristics = rootNode.roomCharacteristics?.collect { it ->
            MCRoomCharacteristic characteristic = new MCRoomCharacteristic()
            characteristic.id = it['id'] as String
            return characteristic
        }
        return room
    }

    ITransformer<String, MCLocation> mcLocationTransformer = (ctx, String body) -> {
        MCLocation location = new MCLocation()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        location.id = rootNode.id
        location.code = rootNode.code
        location.title = rootNode.title
        return location
    }

    ITransformer<String, MCInstructionalMethod> mcInstructionalMethod = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def instructionalMethod = new MCInstructionalMethod()

        instructionalMethod.id = rootNode.id
        instructionalMethod.code = rootNode.abbrevation
        instructionalMethod.title = rootNode.title

        return instructionalMethod
    }

}
