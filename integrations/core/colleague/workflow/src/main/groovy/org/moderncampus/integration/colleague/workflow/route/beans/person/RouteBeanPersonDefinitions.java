package org.moderncampus.integration.colleague.workflow.route.beans.person;

import static org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds.*;
import static org.moderncampus.integration.transform.IExtSystemWriteTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.MulticastDefinition;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosAssocResolver;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosPersonAddressAssociationHandler;
import org.moderncampus.integration.colleague.workflow.route.helper.ColleagueEthosPersonEmergencyContactAssocHandler;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueWriteTransformer;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanPersonDefinitions")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanPersonDefinitions {

    EthosColleagueWriteTransformer writeTransformHelper;
    ColleagueEthosAssocResolver associationResolver;
    ObjectMapper mapper;

    public RouteBeanPersonDefinitions(
            EthosColleagueWriteTransformer writeTransformHelper,
            @Qualifier("colleagueEthosAssocResolver") ColleagueEthosAssocResolver associationResolver,
            ObjectMapper mapper) {
        this.writeTransformHelper = writeTransformHelper;
        this.associationResolver = associationResolver;
        this.mapper = mapper;
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreatePersonEmergencyContact() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_PERSON_EMERGENCY_CONTACT.getId(),
                        ColleagueEthosAPIResource.PERSON_EMERGENCY_CONTACT, writeTransformHelper,
                        METHOD_MAP_TO_PERSON_EMERGENCY_CONTACT_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_PERSON_EMERGENCY_CONTACT_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.EMERGENCY_CONTACT_PHONE_AVAILABILITIES,
                        ColleagueEthosAPIResource.EMERGENCY_CONTACT_TYPES)).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueDeletePersonEmergencyContact() {
        return ColleagueEthosRouteBuilderSupport.deleteByIdBuilder(V1_COLLEAGUE_DELETE_PERSON_EMERGENCY_CONTACT.getId(),
                ColleagueEthosAPIResource.PERSON_EMERGENCY_CONTACT,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCreatePerson(
            ColleagueEthosPersonEmergencyContactAssocHandler emergencyContactAssocHandler) {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CREATE_PERSON.getId(),
                        ColleagueEthosAPIResource.PERSON, writeTransformHelper,
                        METHOD_MAP_TO_PERSON_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_PERSON_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(
                        List.of(ColleagueEthosAPIResource.PHONE_TYPES, ColleagueEthosAPIResource.ADDRESS_TYPES,
                                ColleagueEthosAPIResource.EMAIL_TYPES, ColleagueEthosAPIResource.PERSON_NAME_TYPES,
                                ColleagueEthosAPIResource.CITIZENSHIP_STATUSES))
                .useCase(EllucianConstants.USE_CASE_CREATE_PERSON).postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(emergencyContactAssocHandler);
                })).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueCheckDuplicatePerson() {
        return ColleagueEthosRouteBuilderSupport.createBuilder(V1_COLLEAGUE_CHECK_DUPLICATE_PERSON.getId(),
                        ColleagueEthosAPIResource.CHECK_DUPLICATE_PERSON, writeTransformHelper,
                        METHOD_MAP_TO_CHECK_DUPLICATE_PERSON_WRITE_REQUEST, writeTransformHelper,
                        METHOD_MAP_FROM_PERSON_CREATE_RESPONSE
                ).associationResolver(associationResolver)
                .associationPreFetchList(
                        List.of(ColleagueEthosAPIResource.EMAIL_TYPES, ColleagueEthosAPIResource.PERSON_NAME_TYPES))
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdatePerson(
            ColleagueEthosPersonAddressAssociationHandler personAddressAssociationHandler,
            ColleagueEthosPersonEmergencyContactAssocHandler emergencyContactAssocHandler) {

        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_PERSON.getId(),
                        ColleagueEthosAPIResource.PERSON, writeTransformHelper,
                        METHOD_MAP_TO_PERSON_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(
                        List.of(ColleagueEthosAPIResource.PHONE_TYPES, ColleagueEthosAPIResource.ADDRESS_TYPES,
                                ColleagueEthosAPIResource.EMAIL_TYPES, ColleagueEthosAPIResource.PERSON_NAME_TYPES,
                                ColleagueEthosAPIResource.CITIZENSHIP_STATUSES))
                .useCase(EllucianConstants.USE_CASE_UPDATE_PERSON).postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(personAddressAssociationHandler);
                    multicastDefinition.bean(emergencyContactAssocHandler);
                })).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueUpdatePersonAddress() {
        return ColleagueEthosRouteBuilderSupport.updateByIdBuilder(V1_COLLEAGUE_UPDATE_PERSON_ADDRESS.getId(),
                        ColleagueEthosAPIResource.ADDRESSES,
                        writeTransformHelper,
                        METHOD_MAP_TO_PERSON_ADDRESS_WRITE_REQUEST, mapper).associationResolver(associationResolver)
                .associationPreFetchList(List.of(ColleagueEthosAPIResource.ADDRESS_TYPES)).build();
    }


}
