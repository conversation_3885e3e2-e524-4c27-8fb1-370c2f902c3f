package org.moderncampus.integration.colleague.workflow.route.beans.instructor;

import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_FETCH_INSTRUCTORS;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.colleague.workflow.route.helper.EthosColleagueInstructorAssocResolver;
import org.moderncampus.integration.colleague.workflow.route.helper.EthosColleagueInstructorByIdResolver;
import org.moderncampus.integration.colleague.workflow.route.identifier.CoreColleagueRouteIds;
import org.moderncampus.integration.colleague.workflow.transform.EthosColleagueReadTransformer;
import org.moderncampus.integration.colleague.workflow.transform.instructor.EthosColleaguePersonSearchTransforms;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.colleague.ColleagueEthosRouteBuilderSupport;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("colleagueRouteBeanInstructorDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanInstructorDefinitions {

    public static final CoreColleagueRouteIds V1_COLLEAGUE_GET_INSTRUCTORS = CoreColleagueRouteIds.V1_COLLEAGUE_GET_INSTRUCTORS;
    public static final CoreColleagueRouteIds V1_COLLEAGUE_GET_INSTRUCTOR = CoreColleagueRouteIds.V1_COLLEAGUE_GET_INSTRUCTOR;
    EthosColleagueReadTransformer readTransformer;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetInstructors(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosColleaguePersonSearchTransforms personSearchTransforms,
            @Qualifier("instructorAssocResolver") EthosColleagueInstructorAssocResolver instructorResolver) {
        return ColleagueEthosRouteBuilderSupport.getAllBuilder(V1_COLLEAGUE_GET_INSTRUCTORS.getId(),
                        ColleagueEthosAPIResource.PERSON, readTransformer,
                        "mapToCombinedInstructor", aggregationStrategy
                )
                .preTransformProcessor(instructorResolver)
                .searchCriteriaBuilder(personSearchTransforms)
                .useCase(USE_CASE_FETCH_INSTRUCTORS)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1ColleagueGetInstructor(
            DefaultListAggregationStrategy aggregationStrategy,
            @Qualifier("instructorAssocResolver") EthosColleagueInstructorAssocResolver instructorResolver) {
        return ColleagueEthosRouteBuilderSupport.getByIdBuilder(V1_COLLEAGUE_GET_INSTRUCTOR.getId(),
                        ColleagueEthosAPIResource.PERSON, readTransformer,
                        "mapToInstructor"
                )
                .preTransformProcessor(instructorResolver)
                .build();
    }


}
