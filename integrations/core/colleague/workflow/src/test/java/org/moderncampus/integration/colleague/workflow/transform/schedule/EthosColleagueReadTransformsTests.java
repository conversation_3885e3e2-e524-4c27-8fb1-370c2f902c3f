package org.moderncampus.integration.colleague.workflow.transform.schedule;

import static org.assertj.core.api.Assertions.assertThat;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

import org.junit.jupiter.api.Test;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionScheduleTransforms;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.transform.TransformContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes={MCSectionScheduleTransforms.class})
@ActiveProfiles("dev")
@AutoConfiguration
public class EthosColleagueReadTransformsTests {

    @Test
    void transform_colleague_instructional_event_into_mc_section_schedule_ok_with_weekly_recurrence(
            @Autowired MCSectionScheduleTransforms transforms,
            @Value("classpath:org/moderncampus/integration/colleague/workflow/transform/schedule/InstructionalEventOkWeeklyRecurrence.json") Resource instructionalEvent)
            throws Exception {

        TransformContext context = new TransformContext(Collections.emptyMap());
        MCSectionSchedule result = transforms.getMcSectionScheduleTransformer().transform(context, asString(instructionalEvent));

        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotBlank();
        assertThat(result.getSection()).isNotBlank();
        assertThat(result.getInstructionalMethod()).isNotBlank();
        assertThat(result.getRoom()).isNotBlank();

        assertThat(result.getRecurrenceType()).isEqualTo("weekly");
        assertThat(result.getRecurrenceInterval()).isEqualTo(1 );
        assertThat(result.getDayMonday()).isFalse();
        assertThat(result.getDayTuesday()).isTrue();
        assertThat(result.getDayWednesday()).isFalse();
        assertThat(result.getDayThursday()).isTrue();
        assertThat(result.getDayFriday()).isFalse();
        assertThat(result.getDaySaturday()).isFalse();
        assertThat(result.getDaySunday()).isFalse();

    }

    @Test
    void transform_colleague_instructional_event_into_mc_section_schedule_ok_with_monthly_recurrence_day_of_week(
            @Autowired MCSectionScheduleTransforms transforms,
            @Value("classpath:org/moderncampus/integration/colleague/workflow/transform/schedule/InstructionalEventOkMonthlyRecurrenceDayOfWeek.json") Resource instructionalEvent)
            throws Exception {

        TransformContext context = new TransformContext(Collections.emptyMap());
        MCSectionSchedule result = transforms.getMcSectionScheduleTransformer().transform(context, asString(instructionalEvent));

        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotBlank();
        assertThat(result.getSection()).isNotBlank();
        assertThat(result.getInstructionalMethod()).isNotBlank();
        assertThat(result.getRoom()).isNotBlank();

        assertThat(result.getRecurrenceType()).isEqualTo("monthly");
        assertThat(result.getRecurrenceInterval()).isEqualTo(1 );
        assertThat(result.getRecurrenceByDayOfWk()).isEqualTo(2 );
        assertThat(result.getDayMonday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayTuesday()).isTrue();
        assertThat(result.getDayWednesday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayThursday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayFriday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDaySaturday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDaySunday()).isNotIn(Boolean.TRUE);
    }

    @Test
    void transform_colleague_instructional_event_into_mc_section_schedule_ok_with_monthly_recurrence_day_of_month(
            @Autowired MCSectionScheduleTransforms transforms,
            @Value("classpath:org/moderncampus/integration/colleague/workflow/transform/schedule/InstructionalEventOkMonthlyRecurrenceDayOfMonth.json") Resource instructionalEvent)
            throws Exception {

        TransformContext context = new TransformContext(Collections.emptyMap());
        MCSectionSchedule result = transforms.getMcSectionScheduleTransformer().transform(context, asString(instructionalEvent));

        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotBlank();
        assertThat(result.getSection()).isNotBlank();
        assertThat(result.getInstructionalMethod()).isNotBlank();
        assertThat(result.getRoom()).isNotBlank();

        assertThat(result.getRecurrenceType()).isEqualTo("monthly");
        assertThat(result.getRecurrenceInterval()).isEqualTo(1 );
        assertThat(result.getRecurrenceByDayOfMo()).isEqualTo(15 );
        assertThat(result.getDayMonday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayTuesday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayWednesday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayThursday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDayFriday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDaySaturday()).isNotIn(Boolean.TRUE);
        assertThat(result.getDaySunday()).isNotIn(Boolean.TRUE);
    }

    public static String asString(Resource resource) throws IOException {
        return resource.getContentAsString(StandardCharsets.UTF_8);
    }
}
