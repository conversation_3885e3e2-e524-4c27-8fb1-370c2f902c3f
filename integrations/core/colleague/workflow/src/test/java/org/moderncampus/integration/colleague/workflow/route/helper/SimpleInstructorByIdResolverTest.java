package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.support.DefaultExchange;
import org.junit.jupiter.api.Test;

class SimpleInstructorByIdResolverTest {

    @Test
    void testPersonIdExtractionFromBody() {
        // Given
        Exchange exchange = new DefaultExchange(new DefaultCamelContext());
        String personId = "test-person-id";
        exchange.getIn().setBody(personId);

        // When
        String extractedId = exchange.getIn().getBody(String.class);

        // Then
        assertEquals(personId, extractedId, "Should extract person ID from body");
        System.out.println("DEBUG: Successfully extracted person ID: " + extractedId);
    }

    @Test
    void testPersonIdExtractionFromProperty() {
        // Given
        Exchange exchange = new DefaultExchange(new DefaultCamelContext());
        String personId = "test-person-id";
        exchange.setProperty("resourceId", personId);

        // When
        String extractedId = exchange.getProperty("resourceId", String.class);

        // Then
        assertEquals(personId, extractedId, "Should extract person ID from property");
        System.out.println("DEBUG: Successfully extracted person ID from property: " + extractedId);
    }
}
