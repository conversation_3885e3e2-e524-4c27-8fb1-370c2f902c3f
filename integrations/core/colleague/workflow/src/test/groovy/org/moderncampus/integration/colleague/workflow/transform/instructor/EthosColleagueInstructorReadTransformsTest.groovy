package org.moderncampus.integration.colleague.workflow.transform.instructor

import org.apache.camel.Exchange
import org.apache.camel.impl.DefaultCamelContext
import org.apache.camel.support.DefaultExchange
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test


import static org.junit.jupiter.api.Assertions.*

class EthosColleagueInstructorReadTransformsTest {

    private EthosColleagueInstructorReadTransforms transforms
    private Exchange exchange
    @BeforeEach
    void setUp() {
        transforms = new EthosColleagueInstructorReadTransforms()
        exchange = new DefaultExchange(new DefaultCamelContext())
    }

    @Test
    void testTransformInstructorWithCompleteData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "Dr. <PERSON>",
                firstName: "<PERSON>",
                middleName: "<PERSON>",
                lastName: "<PERSON>",
                title: "Dr.",
                pedigree: "PhD"
            ]],
            dateOfBirth: "1975-05-15",
            gender: "male",
            credentials: [
                [type: "colleaguePersonId", value: "0001234"],
                [type: "ssn", value: "***********"]
            ],
            roles: [[role: "instructor", startOn: "2020-08-15"]]
        ]

        def instructorData = [
            id: "inst-8c005e2e-14fd-47f3-a772-eb88705818ef",
            instructor: [id: "8c005e2e-14fd-47f3-a772-eb88705818ef"],
            institutionalUnits: [
                [
                    department: [id: "dept-001", title: "Computer Science"],
                    percentage: 75.0
                ],
                [
                    department: [id: "dept-002", title: "Mathematics"],
                    percentage: 25.0
                ]
            ],
            tenure: [
                type: [id: "tenure-track"],
                startOn: "2020-08-15"
            ],
            preferences: [
                [type: "preferred", value: "true"],
                [type: "primary", value: "false"]
            ]
        ]

        exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", [instructorData])

        // When
        def result = transforms.transform(exchange, personData)

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)
        assertEquals("John", result.firstName)
        assertEquals("Michael", result.middleName)
        assertEquals("Smith", result.lastName)
        assertEquals("Dr. John Smith", result.fullName)
        assertEquals("Dr.", result.title)
        assertEquals("PhD", result.pedigree)
        assertEquals("1975-05-15", result.dateOfBirth)
        assertEquals("male", result.gender)
        assertEquals("0001234", result.colleaguePersonId)
        assertEquals("***********", result.ssn)
        assertEquals("A", result.status)

        // Verify instructor-specific data
        assertEquals(2, result.instructorDepartments.size())
        assertEquals("dept-001", result.instructorDepartments[0].departmentId)
        assertEquals("Computer Science", result.instructorDepartments[0].departmentTitle)
        assertEquals(75.0, result.instructorDepartments[0].percentage)
        assertEquals("dept-002", result.instructorDepartments[1].departmentId)
        assertEquals("Mathematics", result.instructorDepartments[1].departmentTitle)
        assertEquals(25.0, result.instructorDepartments[1].percentage)

        assertEquals("tenure-track", result.tenure.type)
        assertEquals("2020-08-15", result.tenure.startDate)
        assertTrue(result.preferred)
        assertFalse(result.primary)
        assertEquals("2020-08-15", result.startDate)
    }

    @Test
    void testTransformInstructorWithoutInstructorData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "John Smith",
                firstName: "John",
                lastName: "Smith"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001234"]
            ],
            roles: [[role: "instructor", startOn: "2020-08-15"]]
        ]

        // No instructor data in exchange properties

        // When
        def result = transforms.transform(exchange, personData)

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)
        assertEquals("John", result.firstName)
        assertEquals("Smith", result.lastName)
        assertEquals("John Smith", result.fullName)
        assertEquals("0001234", result.colleaguePersonId)
        assertEquals("A", result.status)

        // Verify default instructor data
        assertTrue(result.instructorDepartments.isEmpty())
        assertNull(result.tenure)
        assertFalse(result.preferred)
        assertFalse(result.primary)
        assertEquals("2020-08-15", result.startDate)
    }

    @Test
    void testTransformInstructorWithPartialInstructorData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "Jane Doe",
                firstName: "Jane",
                lastName: "Doe"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001235"]
            ],
            roles: [[role: "instructor", startOn: "2019-01-15"]]
        ]

        def instructorData = [
            id: "inst-8c005e2e-14fd-47f3-a772-eb88705818ef",
            instructor: [id: "8c005e2e-14fd-47f3-a772-eb88705818ef"],
            institutionalUnits: [
                [
                    department: [id: "dept-003", title: "English Literature"],
                    percentage: 100.0
                ]
            ]
            // No tenure or preferences data
        ]

        exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", [instructorData])

        // When
        def result = transforms.transform(exchange, personData)

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)
        assertEquals("Jane", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("Jane Doe", result.fullName)
        assertEquals("0001235", result.colleaguePersonId)
        assertEquals("A", result.status)

        // Verify partial instructor data
        assertEquals(1, result.instructorDepartments.size())
        assertEquals("dept-003", result.instructorDepartments[0].departmentId)
        assertEquals("English Literature", result.instructorDepartments[0].departmentTitle)
        assertEquals(100.0, result.instructorDepartments[0].percentage)

        assertNull(result.tenure)
        assertFalse(result.preferred)
        assertFalse(result.primary)
        assertEquals("2019-01-15", result.startDate)
    }

    @Test
    void testTransformInstructorWithNonInstructorRole() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "Student User",
                firstName: "Student",
                lastName: "User"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001236"]
            ],
            roles: [[role: "student", startOn: "2020-08-15"]]
        ]

        // When
        def result = transforms.transform(exchange, personData)

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)
        assertEquals("Student", result.firstName)
        assertEquals("User", result.lastName)
        assertEquals("Student User", result.fullName)
        assertEquals("0001236", result.colleaguePersonId)
        assertEquals("I", result.status) // Inactive status for non-instructor

        // Verify default instructor data
        assertTrue(result.instructorDepartments.isEmpty())
        assertNull(result.tenure)
        assertFalse(result.preferred)
        assertFalse(result.primary)
        assertNull(result.startDate) // No instructor start date
    }

    @Test
    void testExtractUniqueValues() {
        // Given
        def data = [
            [department: [id: "dept-001", title: "Computer Science"]],
            [department: [id: "dept-001", title: "Computer Science"]], // Duplicate
            [department: [id: "dept-002", title: "Mathematics"]],
            [department: [id: "dept-003", title: "Physics"]]
        ]

        // When
        def result = transforms.extractUniqueValues(data) { it.department.id }

        // Then
        assertEquals(3, result.size())
        assertTrue(result.contains("dept-001"))
        assertTrue(result.contains("dept-002"))
        assertTrue(result.contains("dept-003"))
    }

    @Test
    void testMapPreferencesToBoolean() {
        // Given
        def preferences = [
            [type: "preferred", value: "true"],
            [type: "primary", value: "false"],
            [type: "other", value: "true"]
        ]

        // When
        def preferredResult = transforms.mapPreferencesToBoolean(preferences, "preferred")
        def primaryResult = transforms.mapPreferencesToBoolean(preferences, "primary")
        def otherResult = transforms.mapPreferencesToBoolean(preferences, "other")
        def nonExistentResult = transforms.mapPreferencesToBoolean(preferences, "nonexistent")

        // Then
        assertTrue(preferredResult)
        assertFalse(primaryResult)
        assertTrue(otherResult)
        assertFalse(nonExistentResult) // Default to false for non-existent preferences
    }
}
