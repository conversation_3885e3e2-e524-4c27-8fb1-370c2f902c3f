openapi: 3.0.0
info:
  title: Section Crosslists
  description: >
    This API exposes and consumes the cross-listing of course sections
    (COURSE.SEC.XLISTS). The API also supports the delete method which removes
    the cross-listing but does not delete the section itself.
  version: "6.0"
  x-source-system: colleague
  x-release-status: ga
  x-api-type: ethos
  x-audience: all
  x-source-domain: Student
servers:
  - url: https://integrate.elluciancloud.com
    description: Ethos Integration API U.S.
  - url: https://integrate.elluciancloud.ca
    description: Ethos Integration API Canada
  - url: https://integrate.elluciancloud.ie
    description: Ethos Integration API Europe
  - url: https://integrate.elluciancloud.com.au
    description: Ethos Integration API Asia-Pacific
  - url: "{server_url}"
    description: Custom server url
    variables:
      server_url:
        default: localhost
paths:
  /api/section-crosslists:
    get:
      summary: Returns resources from COURSE.SEC.XLISTS
      description: >
        Returns a paged listing of COURSE.SEC.XLISTS.


        The sections attribute contains an array of all sections that make up the cross-listing.
      security:
        - EthosIntegrationBearer: []
      tags:
        - section-crosslists
      parameters:
        - name: accept
          in: header
          required: true
          description: >
            The version of the resource requested. Prefer to use only the whole
            MAJOR version.  See the semantic versioning topic in the API
            Standards from more information.

            ```

            application/vnd.hedtech.integration.v6+json

            ```
          schema:
            type: string
        - name: limit
          in: query
          required: false
          description: The maximum number of resources requesting for this result set.
            Default page limit is 500 and upper limit is 500.
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: The 0 based index for a collection of resources for the page
            requested.
          schema:
            type: integer
        - name: section
          in: query
          required: false
          schema:
            type: object
          description: >
            ### Section

            The section for which a cross listing exists. This filter using the GUID for a course section is not defined in the schema but is supported by Colleague.  The following legacy syntax is an example for this filter. 

            ```

            /section-crosslists?section=fa46ddb7-dbda-49ab-bc3b-b2bcba4ef682

            ```
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/schema-section-crosslists.json"
          headers:
            X-Media-Type:
              description: application/vnd.hedtech.integration.v6+json
              schema:
                type: string
            X-Total-Count:
              description: Specifies the total number of resources that satisfy the query.
              schema:
                type: integer
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Permission Denied
        "404":
          description: Resource not found
        "500":
          description: Server error, unexpected configuration or data
    post:
      summary: Create a new resource in COURSE.SEC.XLISTS
      description: >
        POST requests must include a nil GUID value
        ("00000000-0000-0000-0000-000000000000") for the root id property.


        Creates a cross-listing of sections identified in the payload.  Prior to the POST, you must first create the course sections in Colleague UI or through a POST to the sections API.


        There must be at least two sections in the sections array to create a COURSE.SEC.XLISTS.


        If providing the section.type, only one of the sections in the array is permitted to have a section.type of 'primary'.  If section.type is not provided, Colleague will default the first section as 'primary'.
      security:
        - EthosIntegrationBearer: []
      tags:
        - section-crosslists
      parameters:
        - name: accept
          in: header
          required: true
          description: >
            The version of the resource requested. Please refer to the API
            source documentation for detailed support information. Prefer to use
            only the whole MAJOR version. 

            ```

            application/vnd.hedtech.integration.v6+json

            ```
          schema:
            type: string
        - name: content type
          in: header
          required: true
          description: >
            The version of the resource supplied in the request. Please refer to
            the API source documentation for detailed support information.
            Prefer to use only the whole MAJOR version. 

            ```

            application/vnd.hedtech.integration.v6+json

            ```
          schema:
            type: string
      responses:
        "200":
          description: Success
          headers:
            X-Media-Type:
              description: application/vnd.hedtech.integration.v6+json
              schema:
                type: string
          content:
            application/vnd.hedtech.integration.v6+json:
              schema:
                type: object
                $ref: "#/components/schemas/schema-section-crosslists.json"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Permission Denied
        "404":
          description: Resource not found
        "500":
          description: Server error, unexpected configuration or data
      requestBody:
        required: true
        content:
          application/vnd.hedtech.integration+json:
            schema:
              type: object
              $ref: "#/components/schemas/schema-section-crosslists.json"
  /api/section-crosslists/{id}:
    get:
      summary: Return the requested resource from COURSE.SEC.XLISTS
      description: |
        Returns a single COURSE.SEC.XLISTS based on the passed in identifier.
      security:
        - EthosIntegrationBearer: []
      tags:
        - section-crosslists
      parameters:
        - name: id
          in: path
          required: true
          description: A global identifier of the resource for use in all external
            references
          schema:
            type: string
            format: GUID
            minimum: 1
        - name: accept
          in: header
          required: true
          description: >
            The version of the resource requested. Please refer to the API
            source documentation for detailed support information. Prefer to use
            only the whole MAJOR version. 

            ```

            application/vnd.hedtech.integration.v6+json

            ```
          schema:
            type: string
      responses:
        "200":
          description: Success
          headers:
            X-Media-Type:
              description: application/vnd.hedtech.integration.v6+json
              schema:
                type: string
          content:
            application/vnd.hedtech.integration.v6+json:
              schema:
                type: object
                $ref: "#/components/schemas/schema-section-crosslists.json"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Permission Denied
        "404":
          description: Resource not found
        "500":
          description: Server error, unexpected configuration or data
    put:
      summary: Update the requested resource from COURSE.SEC.XLISTS
      description: |
        Updates an existing COURSE.SEC.XLISTS with the requested identifier.
      security:
        - EthosIntegrationBearer: []
      tags:
        - section-crosslists
      parameters:
        - name: id
          in: path
          required: true
          description: A global identifier of Section Crosslists for use in all external
            references
          schema:
            type: string
            format: GUID
            minimum: 1
        - name: accept
          in: header
          required: true
          description: >
            The version of the resource requested. Please refer to the API
            source documentation for detailed support information. Prefer to use
            only the whole MAJOR version. 

            ```

            application/vnd.hedtech.integration.v6+json 

            ```
          schema:
            type: string
        - name: content type
          in: header
          required: true
          description: >
            The version of the resource supplied in the request. Please refer to
            the API source documentation for detailed support information.
            Prefer to use only the whole MAJOR version. 

            ```

            application/vnd.hedtech.integration.v6+json 

            ```
          schema:
            type: string
      responses:
        "200":
          description: Success
          headers:
            X-Media-Type:
              description: application/vnd.hedtech.integration.v6+json
              schema:
                type: string
          content:
            application/vnd.hedtech.integration.v6+json:
              schema:
                type: object
                $ref: "#/components/schemas/schema-section-crosslists.json"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Permission Denied
        "500":
          description: Server error, unexpected configuration or data
      requestBody:
        required: true
        content:
          application/vnd.hedtech.integration+json:
            schema:
              type: object
              $ref: "#/components/schemas/schema-section-crosslists.json"
    delete:
      summary: Removes a resource from COURSE.SEC.XLISTS
      description: |
        Delete the one COURSE.SEC.XLISTS with the passed in identifier.
      security:
        - EthosIntegrationBearer: []
      tags:
        - section-crosslists
      parameters:
        - name: id
          in: path
          required: true
          description: A global identifier of Section Crosslists for use in all external
            references
          schema:
            type: string
            format: GUID
            minimum: 1
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Permission Denied
        "404":
          description: Resource not found
        "500":
          description: Server error, unexpected configuration or data
components:
  securitySchemes:
    EthosIntegrationBearer:
      type: http
      scheme: bearer
  schemas:
    schema-section-crosslists.json:
      title: Section Crosslists
      description: Lists of sections of different courses that meet together, cover
        the same course material, and have an instructor or set of instructors.
      type: object
      properties:
        metadata:
          title: Metadata
          description: Metadata about the JSON payload
          type: object
          properties:
            createdBy:
              title: Created By
              description: The name of the originator (user or system) of the data. This is
                informational only, do not use in business logic!
              type: string
            createdOn:
              title: Created On
              description: The date and time when the entity instance was created
              oneOf:
                - type: string
                  format: date-time
                  pattern: ^(-?(?:[1-9][0-9]*)?[0-9]{4})-(1[0-2]|0[1-9])-(3[0-1]|0[1-9]|[1-2][0-9])T(2[0-3]|[0-1][0-9]):([0-5][0-9]):([0-5][0-9])(\.[0-9]+)?(Z|[+-](?:2[0-3]|[0-1][0-9]):[0-5][0-9])?$
                - type: string
                  maxLength: 0
            modifiedBy:
              title: Modified By
              description: The name of the modifier (user or system) of the data. This is
                informational only, do not use in business logic!
              type: string
            modifiedOn:
              title: Modified On
              description: The date and time when the entity instance was last modified
              oneOf:
                - type: string
                  format: date-time
                  pattern: ^(-?(?:[1-9][0-9]*)?[0-9]{4})-(1[0-2]|0[1-9])-(3[0-1]|0[1-9]|[1-2][0-9])T(2[0-3]|[0-1][0-9]):([0-5][0-9]):([0-5][0-9])(\.[0-9]+)?(Z|[+-](?:2[0-3]|[0-1][0-9]):[0-5][0-9])?$
                - type: string
                  maxLength: 0
          additionalProperties: false
        id:
          title: ID
          description: The global identifier of the Section Crosslists.
          type: string
          format: guid
          pattern: ^[a-f0-9]{8}(?:-[a-f0-9]{4}){3}-[a-f0-9]{12}$
          x-lineageReferenceObject: LDM.GUID.ID(COURSE.SEC.XLISTS)
        code:
          title: Code
          description: The code that identifies the list of cross-listed sections.
          type: string
          x-lineageReferenceObject: COURSE.SEC.XLISTS.ID(COURSE.SEC.XLISTS)
        sections:
          title: Sections
          description: The list of cross-listed sections.
          type: array
          minItems: 1
          items:
            type: object
            properties:
              section:
                title: Section
                description: A section that is cross-listed with other sections.
                type: object
                properties:
                  id:
                    title: ID
                    description: The global identifier for the Section.
                    type: string
                    format: guid
                    pattern: ^[a-f0-9]{8}(?:-[a-f0-9]{4}){3}-[a-f0-9]{12}$
                    x-lineageLookupReferenceObject: sections 
                    x-lineageReferenceObject: CSXL.COURSE.SECTIONS(COURSE.SEC.XLISTS)
                additionalProperties: false
                required:
                  - id
              type:
                title: Type
                description: A indication of the type (primary, secondary) of the section within
                  the group of cross-listed sections.
                oneOf:
                  - type: string
                    enum:
                      - primary
                      - secondary
                    x-lineageReferenceObject: CSXL.PRIMARY.SECTION(COURSE.SEC.XLISTS)
                  - type: string
                    maxLength: 0
            additionalProperties: false
            required:
              - section
        waitlist:
          title: Waitlist
          description: An indicator specifying if all students are placed on the wait-list
            when any of the cross-listed sections has reached its maximum
            enrollment or only when the combined registration has reached the
            specified maximum enrollment of the cross-list.
          oneOf:
            - type: string
              enum:
                - separate
                - combined
              x-lineageReferenceObject: CSXL.WAITLIST.FLAG(COURSE.SEC.XLISTS)
            - type: string
              maxLength: 0
        maxEnrollment:
          title: Maximum Enrollment
          description: The maximum enrollment of the cross-listed section over all
            included sections.
          oneOf:
            - type: integer
              minimum: 0
              format: positiveInteger
              x-lineageReferenceObject: CSXL.CAPACITY(COURSE.SEC.XLISTS)
            - type: string
              nullable: true
        maxWaitlist:
          title: Maximum Waitlist
          description: The maximum number of students allowed in the combined wait-list
            for the cross-listed section.
          oneOf:
            - type: integer
              minimum: 0
              format: positiveInteger
              x-lineageReferenceObject: CSXL.WAITLIST.MAX(COURSE.SEC.XLISTS)
            - type: string
              nullable: true
      required:
        - id
        - sections
      additionalProperties: false

