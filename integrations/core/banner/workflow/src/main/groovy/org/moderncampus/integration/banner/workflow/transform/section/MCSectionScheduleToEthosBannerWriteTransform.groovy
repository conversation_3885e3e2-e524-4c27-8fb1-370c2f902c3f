package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionSchedule
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.moderncampus.integration.ellucian.workflow.transform.ethos.MCSectionScheduleToEthosWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionScheduleToEthosBannerWriteTransform extends BaseTransformer<MCSectionSchedule, String> {

    @Autowired
    MCSectionScheduleToEthosWriteTransform scheduleWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCSectionSchedule schedule) {

        String loadedEntity = ctx.getContextProp(LOADED_ENTITY, String.class)
        Map<String, ?> ethosReq = (loadedEntity)
                ? new JsonSlurper().parseText(loadedEntity) as Map<String, ?>
                : [:]

        ethosReq.id = (!loadedEntity) ? EllucianConstants.NIL_GUID : schedule.id

        scheduleWriteTransform.mapSection(ethosReq, schedule)
        scheduleWriteTransform.mapInstructionalMethod(ethosReq, schedule)
        scheduleWriteTransform.mapRecurrenceTimePeriod(ethosReq, schedule)
        scheduleWriteTransform.mapRoom(ethosReq, schedule)
        scheduleWriteTransform.mapApprovalOverrides(ethosReq, schedule)

        mapRepeatRule(ethosReq, schedule)


        return JsonOutput.toJson(ethosReq)
    }

    private void mapRepeatRule(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.recurrenceType) || isValueExist(schedule.recurrenceInterval)) {
            scheduleWriteTransform.createRecurrenceRepeatRule(req)
            req.recurrence['repeatRule']['type'] = schedule.recurrenceType
            req.recurrence['repeatRule']['interval'] = schedule.recurrenceInterval
            req.recurrence['repeatRule']['daysOfWeek'] = scheduleWriteTransform.daysOfWeek(schedule)
        }
    }


}
