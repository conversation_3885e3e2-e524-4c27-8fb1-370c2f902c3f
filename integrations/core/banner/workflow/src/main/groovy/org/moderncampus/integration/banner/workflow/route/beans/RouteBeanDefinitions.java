package org.moderncampus.integration.banner.workflow.route.beans;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.*;
import static org.moderncampus.integration.banner.workflow.transform.EthosBannerReadTransformer.*;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_FETCH_INSTRUCTORS;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.banner.workflow.route.builder.EthosBannerGetAcademicPeriodsRouteBuilder;
import org.moderncampus.integration.banner.workflow.route.helper.EthosBannerInstructorAssocResolver;
import org.moderncampus.integration.banner.workflow.route.helper.EthosBannerYearsAssocResolver;
import org.moderncampus.integration.banner.workflow.transform.EthosBannerReadTransformer;
import org.moderncampus.integration.banner.workflow.transform.acadperiod.EthosAcademicPeriodSearchTransforms;
import org.moderncampus.integration.banner.workflow.transform.common.search.EthosBannerPersonSearchTransforms;
import org.moderncampus.integration.ellucian.component.BannerCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.banner.BannerEthosRouteBuilderSupport;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("bannerRouteBeanDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanDefinitions {

    EthosBannerReadTransformer readTransformer;

    EllucianEthosReadEndpointPaginator readEPPaginator;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetOrgUnits(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_ORGANIZATIONAL_UNITS.getId(),
                        BannerEthosAPIResource.EDUCATIONAL_INSTITUTION_UNITS, readTransformer,
                        METHOD_MAP_TO_ORGANIZATIONAL_UNIT, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetOrgUnit() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_ORGANIZATIONAL_UNIT.getId(),
                BannerEthosAPIResource.EDUCATIONAL_INSTITUTION_UNITS, readTransformer,
                METHOD_MAP_TO_ORGANIZATIONAL_UNIT).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSubjects(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_SUBJECTS.getId(),
                        BannerEthosAPIResource.SUBJECTS, readTransformer,
                        METHOD_MAP_TO_SUBJECT, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSubject() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_SUBJECT.getId(),
                BannerEthosAPIResource.SUBJECTS, readTransformer,
                METHOD_MAP_TO_SUBJECT
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetInstructionalMethods(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_INSTRUCTIONAL_METHODS.getId(),
                        BannerEthosAPIResource.INSTRUCTIONAL_METHODS, readTransformer,
                        METHOD_MAP_TO_INSTRUCTIONAL_METHOD, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetInstructionalMethod() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_INSTRUCTIONAL_METHOD.getId(),
                BannerEthosAPIResource.INSTRUCTIONAL_METHODS, readTransformer,
                METHOD_MAP_TO_INSTRUCTIONAL_METHOD
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetLocations(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_LOCATIONS.getId(),
                        BannerEthosAPIResource.SITES, readTransformer,
                        METHOD_MAP_TO_LOCATION, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetLocation() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_LOCATION.getId(),
                BannerEthosAPIResource.SITES,
                readTransformer,
                METHOD_MAP_TO_LOCATION
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetAcademicLevels(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_ACADEMIC_LEVELS.getId(),
                        BannerEthosAPIResource.ACADEMIC_LEVELS, readTransformer,
                        METHOD_MAP_TO_ACADEMIC_LEVEL, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetAcademicLevel() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_ACADEMIC_LEVEL.getId(),
                BannerEthosAPIResource.ACADEMIC_LEVELS, readTransformer,
                METHOD_MAP_TO_ACADEMIC_LEVEL
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetRooms(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_ROOMS.getId(),
                        BannerEthosAPIResource.ROOMS, readTransformer,
                        METHOD_MAP_TO_ROOM, aggregationStrategy
                )
                .readEPPaginator(readEPPaginator).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetRoom() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_ROOM.getId(), BannerEthosAPIResource.ROOMS,
                readTransformer, METHOD_MAP_TO_ROOM).build();
    }


    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetAcademicPeriods(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosAcademicPeriodSearchTransforms academicPeriodSearchTransforms,
            EthosBannerYearsAssocResolver assocResolver) {
        return EthosBannerGetAcademicPeriodsRouteBuilder.builder(V1_BANNER_GET_ACADEMIC_PERIODS.getId(),
                        EllucianCloudEndpointType.BANNER_ETHOS_API, BannerEthosAPIResource.ACADEMIC_PERIODS,
                        BannerCloudConnectionConfiguration.class, readTransformer,
                        METHOD_MAP_TO_ACADEMIC_PERIOD, aggregationStrategy
                ).searchCriteriaBuilder(academicPeriodSearchTransforms)
                .readEPPaginator(readEPPaginator).associationResolver(assocResolver).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetAcademicPeriod() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_ACADEMIC_PERIOD.getId(),
                BannerEthosAPIResource.ACADEMIC_PERIODS, readTransformer,
                METHOD_MAP_TO_ACADEMIC_PERIOD).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetInstructors(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosBannerPersonSearchTransforms personSearchTransforms,
            EthosBannerInstructorAssocResolver instructorResolver) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_INSTRUCTORS.getId(),
                        BannerEthosAPIResource.PERSONS, readTransformer,
                        METHOD_MAP_TO_INSTRUCTOR, aggregationStrategy
                )
                .preTransformProcessor(instructorResolver)
                .searchCriteriaBuilder(personSearchTransforms).useCase(USE_CASE_FETCH_INSTRUCTORS).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetInstructor(
            DefaultListAggregationStrategy aggregationStrategy, EthosBannerInstructorAssocResolver instructorResolver) {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_INSTRUCTOR.getId(),
                        BannerEthosAPIResource.PERSONS, readTransformer,
                        METHOD_MAP_TO_INSTRUCTOR
                )
                .preTransformProcessor(instructorResolver).build();
    }

}
