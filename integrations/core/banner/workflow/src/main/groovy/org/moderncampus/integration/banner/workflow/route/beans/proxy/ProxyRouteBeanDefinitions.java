package org.moderncampus.integration.banner.workflow.route.beans.proxy;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.V1_BANNER_PROXY;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.banner.BannerProxyRouteBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("proxyBannerRouteBeanDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProxyRouteBeanDefinitions {

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerProxy() {
        return new BannerProxyRouteBuilder(V1_BANNER_PROXY.getId());
    }
}
