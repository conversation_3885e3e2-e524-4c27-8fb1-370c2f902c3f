package org.moderncampus.integration.banner.workflow.route.builder;

import java.util.List;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Processor;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetAllRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;

public class EthosBannerGetAcademicPeriodsRouteBuilder extends EllucianCloudGetAllRouteBuilder {

    protected EthosBannerGetAcademicPeriodsRouteBuilder(String id, Object transformer, String transformMethod,
            AggregationStrategy aggregationStrategy, Object searchCriteriaBuilder, Object readEPPaginator,
            Processor responsePaginationMapper, Processor responseSplitter,
            Processor preTransformProcessor, String useCase,
            EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass,
            IEllucianAssociationResolver associationResolver,
            List<IEllucianCloudAPIResource> associationPreFetchList) {
        super(id, transformer, transformMethod, aggregationStrategy, searchCriteriaBuilder, readEPPaginator,
                responsePaginationMapper, responseSplitter, preTransformProcessor, useCase, endpointType, apiResource,
                connectionConfigClass, associationResolver, associationPreFetchList);
    }

    @Override
    protected void preFetchAssociations(RouteDefinition routeDefinition) {
        routeDefinition.process((Processor) associationResolver);
    }
}
