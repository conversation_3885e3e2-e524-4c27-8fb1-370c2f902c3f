package org.moderncampus.integration.banner.workflow.route.beans.course;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.*;
import static org.moderncampus.integration.banner.workflow.transform.EthosBannerWriteTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.banner.workflow.route.helper.BannerEthosAssocResolver;
import org.moderncampus.integration.banner.workflow.transform.BPAPIColleagueWriteTransformer;
import org.moderncampus.integration.banner.workflow.transform.EthosBannerReadTransformer;
import org.moderncampus.integration.banner.workflow.transform.EthosBannerWriteTransformer;
import org.moderncampus.integration.ellucian.component.internal.BannerBPAPIResource;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.banner.BannerEthosRouteBuilderSupport;
import org.moderncampus.integration.ellucian.workflow.route.builder.bpapi.BPAPIRouteBuilderSupport;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("bannerRouteBeanCourseDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanCourseDefinitions {

    EthosBannerReadTransformer readTransformer;

    EthosBannerWriteTransformer writeTransformer;

    BPAPIColleagueWriteTransformer bpapiColleagueWriteTransformer;

    BannerEthosAssocResolver bannerEthosAssocResolver;

    EllucianEthosReadEndpointPaginator readEPPaginator;

    ObjectMapper mapper;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerCreateCourse(
            DefaultListAggregationStrategy aggregationStrategy) {

        return BannerEthosRouteBuilderSupport.createBuilder(V1_BANNER_CREATE_COURSE.getId(),
                        BannerEthosAPIResource.COURSES, writeTransformer,
                        METHOD_MAP_TO_COURSE_WRITE_REQUEST,
                        writeTransformer, METHOD_MAP_FROM_COURSE_CREATE_RESPONSE
                ).associationResolver(bannerEthosAssocResolver)
                .associationPreFetchList(List.of(BannerEthosAPIResource.COURSE_TITLE_TYPES,
                        BannerEthosAPIResource.CREDIT_CATEGORIES,
                        BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_CREATE_COURSE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateCourse(
            DefaultListAggregationStrategy aggregationStrategy) {

        return BannerEthosRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_COURSE.getId(),
                        BannerEthosAPIResource.COURSES, writeTransformer, METHOD_MAP_TO_COURSE_WRITE_REQUEST, mapper)
                .associationResolver(bannerEthosAssocResolver)
                .associationPreFetchList(List.of(BannerEthosAPIResource.COURSE_TITLE_TYPES,
                        BannerEthosAPIResource.CREDIT_CATEGORIES,
                        BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_UPDATE_COURSE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateCourseSectionInformation() {
        return BPAPIRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_COURSE_SECTION_INFORMATION.getId(),
                        BannerBPAPIResource.COURSE_SECTION_INFORMATION, bpapiColleagueWriteTransformer,
                        METHOD_MAP_TO_COURSE_SECTION_INFORMATION_WRITE_REQUEST, mapper)
                .isPatchImpl(false)
                .isUpdateById(false)
                .build();
    }
}
