package org.moderncampus.integration.banner.workflow.route.helper;

import static org.moderncampus.integration.banner.workflow.route.Constants.ETHOS_ASSOC_INSTRUCTOR;

import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.banner.workflow.transform.instructor.EthosBannerInstructorSearchTransforms;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.springframework.stereotype.Component;

import groovy.json.JsonSlurper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosBannerInstructorAssocResolver extends BannerEthosAssocResolver implements Processor {

    EthosBannerInstructorSearchTransforms instructorSearchTransforms;

    public EthosBannerInstructorAssocResolver(ProducerTemplate template, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator ellucianEthosReadEndpointPaginator,
            EthosBannerInstructorSearchTransforms instructorSearchTransforms) {
        super(template, camelContext, ellucianEthosReadEndpointPaginator);
        this.instructorSearchTransforms = instructorSearchTransforms;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Exchange newExchange = exchange.copy();
        String personId = extractPersonIdFromEthosPersonResp(newExchange);
        if (personId != null) {
            buildInstructorSearchCriteria(personId, newExchange);
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(BannerEthosAPIResource.INSTRUCTORS,
                    newExchange);
            if (!parsedAssocResponse.isEmpty()) {
                exchange.setProperty(ETHOS_ASSOC_INSTRUCTOR, parsedAssocResponse);
            }
        }
    }

    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(Map.of("personId", personId));
        newExchange.getMessage().setBody(entityRequest);
        instructorSearchTransforms.buildSearchCriteria(newExchange);
    }

    private String extractPersonIdFromEthosPersonResp(Exchange newExchange) {
        String body = newExchange.getMessage().getBody(String.class);
        Map<String, ?> parsedBody = (Map<String, ?>) new JsonSlurper().parseText(body);
        return (String) parsedBody.get("id");
    }
}
