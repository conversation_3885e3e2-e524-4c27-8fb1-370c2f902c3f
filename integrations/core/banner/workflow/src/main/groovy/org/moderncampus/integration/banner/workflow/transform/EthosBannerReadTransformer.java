package org.moderncampus.integration.banner.workflow.transform;

import java.util.List;

import org.apache.camel.Exchange;
import org.moderncampus.integration.banner.workflow.transform.common.EthosBannerReadTransforms;
import org.moderncampus.integration.dto.core.MCAcademicLevel;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCInstructionalMethod;
import org.moderncampus.integration.dto.core.MCInstructor;
import org.moderncampus.integration.dto.core.MCLocation;
import org.moderncampus.integration.dto.core.MCOrganizationalUnit;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCRoom;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCSubject;
import org.moderncampus.integration.transform.IExtSystemReadTransformer;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosBannerReadTransformer implements IExtSystemReadTransformer {

    EthosBannerReadTransforms readTransforms;

    @Override
    public MCOrganizationalUnit mapToOrganizationalUnit(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getCcOrgUnitTransformer(), exchange, body);
    }

    @Override
    public MCSubject mapToSubject(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcSubjectTransformer(), exchange, body);
    }

    @Override
    public MCInstructionalMethod mapToInstructionalMethod(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcInstructionalMethodTransformer(), exchange, body);
    }

    @Override
    public MCLocation mapToLocation(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcLocationTransformer(), exchange, body);
    }

    @Override
    public MCAcademicLevel mapToAcademicLevel(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getCcAcademicLevelTransformer(), exchange, body);
    }

    @Override
    public MCRoom mapToRoom(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcRoomTransformer(), exchange, body);
    }

    @Override
    public MCSection mapToSection(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcSectionTransformer(), exchange, body);
    }

    @Override
    public MCSectionSchedule mapToSectionSchedule(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcSectionScheduleTransformer(), exchange, body);
    }

    @Override
    public MCCourse mapToCourse(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCAcademicPeriod mapToAcademicPeriod(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcAcademicPeriodTransformer(), exchange, body);
    }

    @Override
    public MCInstructor mapToInstructor(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcInstructorTransformer(), exchange, body);
    }

    @Override
    public MCSectionCrossList mapToSectionCrossList(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionInstructorAssignment mapToSectionInstructorAssignment(String body, Exchange exchange)
            throws Exception {
        return invokeTransform(readTransforms.getMcSectionInstructorAssignmentTransformer(), exchange, exchange.getIn().getBody(String.class));
    }

    @Override
    public MCAddress mapToAddress(String body, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCCourseSectionInformation mapToCourseSectionInformation(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getMcCourseSectionInformation(), exchange, body);
    }

    @Override
    public MCSectionCrossListGroup mapToSectionCrossListGroup(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getmCSectionCrossListGroup(), exchange, body);
    }

    @Override
    public List<MCSectionCrossListGroup> mapToSectionCrossListGroups(String body, Exchange exchange) throws Exception {
        return invokeTransform(readTransforms.getmCSectionCrossListGroups(), exchange, body);
    }

    @Override
    public MCPerson mapToPerson(String body, Exchange exchange) throws Exception {
        return null;
    }
}
