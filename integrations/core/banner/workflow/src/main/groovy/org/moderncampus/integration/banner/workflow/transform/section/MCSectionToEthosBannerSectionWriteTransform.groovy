package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.banner.workflow.route.Constants
import org.moderncampus.integration.dto.core.MCSection
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.ellucian.workflow.transform.ethos.MCSectionToEthosSectionWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionToEthosBannerSectionWriteTransform extends BaseTransformer<MCSection, String> {

    @Autowired
    MCSectionToEthosSectionWriteTransform ethosWriteTransform

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCSection section) {
        Map<String, ?> requestRoot = [:]
        Map<BannerEthosAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        Map<String, Map> sectionTitleTypeByIdMap = ethosAssocCacheMap[BannerEthosAPIResource.SECTION_TITLE_TYPES]
        Map<String, Map> sectionDescriptionTypeByIdMap = ethosAssocCacheMap[BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES]
        Map<String, Map> creditCategoriesByIdMap = ethosAssocCacheMap[BannerEthosAPIResource.CREDIT_CATEGORIES]
        Map<String, Map> adminInstructionalMethodsTypeByIdMap = ethosAssocCacheMap[BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS]

        if (USE_CASE_UPDATE_SECTION == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            requestRoot = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            commonWriteTransform.mapId(requestRoot)
        }

        ethosWriteTransform.mapTitles(requestRoot, section, sectionTitleTypeByIdMap)
        ethosWriteTransform.mapDescriptions(requestRoot, section, sectionDescriptionTypeByIdMap)
        ethosWriteTransform.mapStartDate(requestRoot, section)
        ethosWriteTransform.mapEndDate(requestRoot, section)
        ethosWriteTransform.mapCode(requestRoot, section)
        ethosWriteTransform.mapNumber(requestRoot, section)
        ethosWriteTransform.mapAcademicPeriod(requestRoot, section)
        ethosWriteTransform.mapCensusDates(requestRoot, section)
        ethosWriteTransform.mapCourse(requestRoot, section)
        ethosWriteTransform.mapCourseCategories(requestRoot, section)
        ethosWriteTransform.mapSite(requestRoot, section)
        ethosWriteTransform.mapAcademicLevels(requestRoot, section)
        ethosWriteTransform.mapGradeSchemes(requestRoot, section)
        ethosWriteTransform.mapInstructionalMethods(requestRoot, section)
        ethosWriteTransform.mapInstructionalDeliveryMethod(requestRoot, section)
        ethosWriteTransform.mapStatus(requestRoot, section)
        ethosWriteTransform.mapDuration(requestRoot, section)
        ethosWriteTransform.mapEnrollmentSize(requestRoot, section)
        ethosWriteTransform.mapBillingHours(requestRoot, section)
        ethosWriteTransform.mapOwningInstitutionUnits(requestRoot, section)

        mapHours(requestRoot, section, adminInstructionalMethodsTypeByIdMap)
        mapCredits(requestRoot, section, creditCategoriesByIdMap)
        mapCrossListed(requestRoot, section)
        mapAlternateIds(requestRoot, section)

        return JsonOutput.toJson(requestRoot)
    }

    private void mapCredits(Map<String, ?> requestRoot, MCSection section, Map<String, Map> creditCategoriesByIdMap) {
        if (isValueExist(section.minCredits) || isValueExist(section.maxCredits)
                || isValueExist(section.minCeu) || isValueExist(section.maxCeu)) {
            def creditsArr = []
            def creditCategoriesByTypeMap = creditCategoriesByIdMap.collectEntries {
                [(it.value['creditType']): it.value]
            }
            String institutionTypeId = creditCategoriesByTypeMap['institution']?['id']
            String ceTypeId = creditCategoriesByTypeMap['ce']?['id']
            if ((isValueExist(section.minCredits) || isValueExist(section.maxCredits)) && isValueExist(institutionTypeId)) {
                Map<String, ?> creditMap = [
                        "creditCategory": [
                                "creditType": "institution",
                                "detail"    : [
                                        "id": institutionTypeId
                                ]
                        ],
                        "measure"       : "hours",
                ]
                if (isValueExist(section.minCredits)) {
                    creditMap['minimum'] = section.minCredits
                }
                if (isValueExist(section.maxCredits)) {
                    creditMap['maximum'] = section.maxCredits
                }
                creditsArr.push(creditMap)
            }
            if ((isValueExist(section.minCeu) || isValueExist(section.maxCeu)) && isValueExist(ceTypeId)) {
                Map<String, ?> creditMap = [
                        "creditCategory": [
                                "creditType": "ce",
                                "detail"    : [
                                        "id": ceTypeId
                                ]
                        ],
                        "measure"       : "ceu",
                ]
                if (isValueExist(section.minCeu)) {
                    creditMap['minimum'] = section.minCeu
                }
                if (isValueExist(section.maxCeu)) {
                    creditMap['maximum'] = section.maxCeu
                }
                creditsArr.push(creditMap)
            }
            requestRoot.put("credits", creditsArr)
        }
    }

    private void mapHours(Map<String, ?> requestRoot, MCSection section, Map<String, Map> adminInstructionalMethodsByIdMap) {
        if (isValueExist(section.contactHours) || isValueExist(section.labHours)
                || isValueExist(section.lectureHours) || isValueExist(section.otherHours)) {
            def hoursArr = []
            def adminInstructionalMethodsByCodeMap = adminInstructionalMethodsByIdMap.collectEntries {
                def code = it.value['code'] as String
                [(code.toLowerCase()): it.value]
            }
            String contactId = adminInstructionalMethodsByCodeMap['contact']?['id']
            String labId = adminInstructionalMethodsByCodeMap['lab']?['id']
            String lectureId = adminInstructionalMethodsByCodeMap['lecture']?['id']
            String otherId = adminInstructionalMethodsByCodeMap['other']?['id']
            if (isValueExist(section.contactHours)) {
                hoursArr.push([
                        "administrativeInstructionalMethod": [
                                "id": contactId
                        ],
                        "minimum"                          : section.contactHours
                ])
            }
            if (isValueExist(section.labHours)) {
                hoursArr.push([
                        "administrativeInstructionalMethod": [
                                "id": labId
                        ],
                        "minimum"                          : section.labHours
                ])
            }
            if (isValueExist(section.lectureHours)) {
                hoursArr.push([
                        "administrativeInstructionalMethod": [
                                "id": lectureId
                        ],
                        "minimum"                          : section.lectureHours
                ])
            }
            if (isValueExist(section.otherHours)) {
                hoursArr.push([
                        "administrativeInstructionalMethod": [
                                "id": otherId
                        ],
                        "minimum"                          : section.otherHours
                ])
            }
            requestRoot.put("hours", hoursArr)
        }
    }

    private void mapCrossListed(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section.crossListed)) {
            requestRoot.put("crossListed", section.crossListed)
        }
    }

    private void mapAlternateIds(Map<String, Object> requestRoot, MCSection section) {
        if (isValueExist(section?.otherIds)) {
            requestRoot['alternateIds'] = section.otherIds.collect{['title': it.type, 'value': it.value]}
        }
    }

}
