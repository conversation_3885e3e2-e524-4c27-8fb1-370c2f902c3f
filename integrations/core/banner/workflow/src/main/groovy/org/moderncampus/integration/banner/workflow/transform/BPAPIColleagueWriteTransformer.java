package org.moderncampus.integration.banner.workflow.transform;

import org.apache.camel.Exchange;
import org.moderncampus.integration.banner.workflow.transform.section.MCCourseSectionInformationWriteTransform;
import org.moderncampus.integration.banner.workflow.transform.section.MCSectionCrossListGroupWriteTransform;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCFinalGrade;
import org.moderncampus.integration.dto.core.MCOrganization;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCStudentCharge;
import org.moderncampus.integration.dto.core.MCStudentEnrollment;
import org.moderncampus.integration.dto.core.MCStudentPayment;
import org.moderncampus.integration.ellucian.workflow.transform.helper.EthosHelper;
import org.moderncampus.integration.transform.IExtSystemWriteTransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;


@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BPAPIColleagueWriteTransformer implements IExtSystemWriteTransformer {

    MCSectionCrossListGroupWriteTransform sectionCrossListGroupTransform;
    MCCourseSectionInformationWriteTransform courseSectionInformationWriteTransform;

    @Override
    public <T> T mapToCourseWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCCourse mapFromCourseCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToSectionWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSection mapFromSectionCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToSectionScheduleWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionSchedule mapFromSectionScheduleCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToSectionInstructorAssignmentWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionInstructorAssignment mapFromSectionInstructorAssignmentCreateResponse(String resp,
            Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToSectionCrossListWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionCrossList mapFromSectionCrossListCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToPersonEmergencyContactWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCPersonEmergencyContact mapFromPersonEmergencyContactCreateResponse(String resp, Exchange exchange)
            throws Exception {
        return null;
    }

    @Override
    public <T> T mapToPersonWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCPerson mapFromPersonCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToCheckDuplicatePersonWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCStudentCharge mapFromStudentChargeCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToStudentChargeWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCStudentPayment mapFromStudentPaymentCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToStudentPaymentWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCStudentEnrollment mapFromStudentEnrollmentCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToStudentEnrollmentWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToStudentWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToPersonAddressWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCFinalGrade mapFromFinalGradeCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToFinalGradeWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCSectionCrossListGroup mapFromSectionCrossListGroupCreateResponse(String resp, Exchange exchange)
            throws Exception {
        return EthosHelper.ethosSectionCrossListGroupWriteRespTransformer(MCSectionCrossListGroup.class)
                .transform(new TransformContext(exchange.getProperties()), resp);
    }

    @Override
    public String mapToSectionCrossListGroupWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(sectionCrossListGroupTransform, exchange,
                exchange.getMessage().getBody(MCSectionCrossListGroup.class));
    }

    @Override
    public String mapToCourseSectionInformationWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(courseSectionInformationWriteTransform, exchange,
                exchange.getMessage().getBody(MCCourseSectionInformation.class));
    }

    @Override
    public MCOrganization mapFromOrganizationCreateResponse(String resp, Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToOrganizationWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

}
