package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCCourseSectionInformation
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCCourseSectionInformationWriteTransform extends BaseTransformer<MCCourseSectionInformation, String> {

    @Override
    protected String doTransform(TransformContext context, MCCourseSectionInformation input) throws Exception {
        Map<String, ?> requestRoot = [:]

        if (isValueExist(input.crn)) {
            requestRoot.crn = input.crn
        }

        if (isValueExist(input.termCode)) {
            requestRoot.termCode = input.termCode
        }

        if (isValueExist(input.xlstGroup)) {
            requestRoot.xlstGroup = input.xlstGroup
        }

        return JsonOutput.toJson(requestRoot)
    }
}
