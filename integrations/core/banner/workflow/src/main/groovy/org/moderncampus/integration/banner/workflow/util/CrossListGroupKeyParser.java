package org.moderncampus.integration.banner.workflow.util;

import org.moderncampus.integration.banner.workflow.transform.common.CrossListGroupKey;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class CrossListGroupKeyParser {

    private CrossListGroupKeyParser() {
    }

    public static CrossListGroupKey parse(String compositeKey) {
        if (compositeKey == null || compositeKey.isEmpty()) {
            return null;
        }

        int pipeIndex = compositeKey.indexOf('|');
        if (pipeIndex == -1 || pipeIndex != compositeKey.lastIndexOf('|')) {
            log.warn("CrossListGroup Key must contain exactly one pipe separating groupCode and termCode {}",
                    compositeKey);
            return new CrossListGroupKey(null, null);
        }

        String groupCode = compositeKey.substring(0, pipeIndex).trim();
        String termCode = compositeKey.substring(pipeIndex + 1).trim();

        if (groupCode.isEmpty()) {
            log.warn("GroupCode can't be null or empty {}", compositeKey);
            return new CrossListGroupKey(null, null);
        }

        if (termCode.isEmpty()) {
            log.warn("TermCode can't be null or empty {}", compositeKey);
            return new CrossListGroupKey(null, null);
        }

        return new CrossListGroupKey(groupCode, termCode);
    }

}

