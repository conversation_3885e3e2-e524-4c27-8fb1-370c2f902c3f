package org.moderncampus.integration.banner.workflow.route.helper;

import static org.moderncampus.integration.banner.workflow.route.Constants.ETHOS_ASSOC_YEAR;
import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_FILTER_YEARS;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.banner.workflow.transform.acadperiod.EthosAcademicPeriodSearchTransforms;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosBannerYearsAssocResolver extends BannerEthosAssocResolver implements Processor {

    EthosAcademicPeriodSearchTransforms searchTransforms;

    public EthosBannerYearsAssocResolver(ProducerTemplate template, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator ellucianEthosReadEndpointPaginator,
            EthosAcademicPeriodSearchTransforms searchTransforms) {
        super(template, camelContext, ellucianEthosReadEndpointPaginator);
        this.searchTransforms = searchTransforms;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Exchange newExchange = exchange.copy();
        List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(BannerEthosAPIResource.ACADEMIC_PERIODS,
                newExchange);
        exchange.setProperty(ETHOS_ASSOC_YEAR, parsedAssocResponse);
    }

    private void buildYearSearchCriteria(Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(new HashMap<>());
        newExchange.setProperty(INTEGRATION_USE_CASE, USE_CASE_FILTER_YEARS);
        newExchange.getMessage().setBody(entityRequest);
        searchTransforms.buildSearchCriteria(newExchange);
    }
}
