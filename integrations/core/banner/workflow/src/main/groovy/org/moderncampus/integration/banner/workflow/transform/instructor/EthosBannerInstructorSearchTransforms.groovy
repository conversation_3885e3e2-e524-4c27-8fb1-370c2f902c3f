package org.moderncampus.integration.banner.workflow.transform.instructor

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.ellucian.workflow.transform.ethos.BaseEllucianSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosBannerInstructorSearchTransforms extends BaseEllucianSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def personId = searchCriteria["personId"]
        if (personId) {
            queryParams["criteria"] = JsonOutput.toJson(["instructor": personId])
        }
    }
}
