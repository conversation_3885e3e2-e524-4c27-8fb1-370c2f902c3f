package org.moderncampus.integration.banner.workflow.route.helper;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.V1_BANNER_GET_ACADEMIC_PERIOD;
import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.V1_BANNER_UPDATE_COURSE_SECTION_INFORMATION;
import static org.moderncampus.integration.constants.Constants.*;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_CREATE_SECTION;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.banner.workflow.transform.common.CrossListGroupKey;
import org.moderncampus.integration.banner.workflow.util.CrossListGroupKeyParser;
import org.moderncampus.integration.dto.base.BaseDTO;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianCloudCommonAssociationHandler;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BPAPICrossListGroupsAssocHandler extends EllucianCloudCommonAssociationHandler implements Processor {

    IRouteExecutor routeExecutor;
    ObjectMapper mapper;

    @Override
    public void process(Exchange exchange) throws Exception {
        MCSection sectionRequest = getOriginalRequest(exchange, MCSection.class);
        Map sectionResponse = mapper.readValue(exchange.getProperty(DEST_SYS_ENDPOINT_RESPONSE).toString(), Map.class);
        String useCase = exchange.getProperty(INTEGRATION_USE_CASE, String.class);
        MCSection.MCSectionCrossListGroup crossListGroup = sectionRequest.getCrossListGroup();

        if (Objects.equals(USE_CASE_CREATE_SECTION, useCase)) {
            createCrossListGroup(sectionResponse, crossListGroup);
        } else {
            updateCrossListGroup(exchange, sectionResponse, crossListGroup);
        }
    }

    private void updateCrossListGroup(Exchange exchange, Map sectionResponse,
            MCSection.MCSectionCrossListGroup crossListGroup)
            throws Exception {

        if (crossListGroup == null || crossListGroup.getId() == null) {
            return;
        }
        String compositeId = crossListGroup.getId();
        String termCode = getTermCode(crossListGroup, sectionResponse, compositeId);

        // Section code
        Map sectionGetResponse = mapper.readValue(exchange.getProperty(LOADED_ENTITY).toString(), Map.class);
        String sectionCode = (String) sectionGetResponse.get("code");

        String xlistGroup = "";
        if (StringUtils.isNotBlank(compositeId)) {
            CrossListGroupKey key = CrossListGroupKeyParser.parse(compositeId);
            xlistGroup = key.groupCode();
        }

        executeCourseSectionInformation(termCode, sectionCode, xlistGroup);
    }

    private void createCrossListGroup(Map sectionResponse, MCSection.MCSectionCrossListGroup crossListGroup)
            throws Exception {
        String compositeId = Optional.ofNullable(crossListGroup).map(BaseDTO::getId).orElse(null);

        // Get the “section code (CRN)” by looking at the code in the response from the originating POST call,
        String sectionCode = (String) sectionResponse.get("code");

        String termCode = getTermCode(crossListGroup, sectionResponse, compositeId);

        /*
         * To associate the xlistGroup, this is also derived from the composite Id as well.
         *  This is the first token in the composite Id and can be used as is.
         */
        String xlistGroup = null;
        if (StringUtils.isNotBlank(compositeId)) {
            CrossListGroupKey key = CrossListGroupKeyParser.parse(compositeId);
            xlistGroup = key.groupCode();
        }

        executeCourseSectionInformation(termCode, sectionCode, xlistGroup);
    }

    private void executeCourseSectionInformation(String termCode, String sectionCode, String xlistGroup)
            throws Exception {
        if (StringUtils.isNotBlank(termCode) && StringUtils.isNotBlank(sectionCode)) {
            MCCourseSectionInformation information = new MCCourseSectionInformation();
            information.setTermCode(termCode);
            information.setCrn(sectionCode);
            information.setXlstGroup(xlistGroup);
            RouteSupport.executeRoute(routeExecutor, V1_BANNER_UPDATE_COURSE_SECTION_INFORMATION, information);
        }
    }

    private String getTermCode(MCSection.MCSectionCrossListGroup crossListGroup, Map sectionResponse,
            String compositeId) throws Exception {
        String termCode;

        if (StringUtils.isNotBlank(compositeId)) {
            /**
             * When a non-empty string/null crossListGroup.id has been provided: this is obtained in the composite Id of the
             * crossListGroup being specified.  Recall that the Id consists of a <groupCode>|<termCode>.
             * The second element here is what can be used as the term code.
             */
            CrossListGroupKey key = CrossListGroupKeyParser.parse(compositeId);
            termCode = key.termCode();
        } else {
            /**
             * When there is an empty string, the term code must then be derived by making an extra call to
             * GET academic-periods/{id}
             */
            Map reportingAcademicPeriodMap = (Map) sectionResponse.get("reportingAcademicPeriod");
            String reportingAcademicPeriodKey = (String) reportingAcademicPeriodMap.get("id");
            RouteExecutorResult academicPeriodResult = RouteSupport.executeRoute(routeExecutor,
                    V1_BANNER_GET_ACADEMIC_PERIOD, reportingAcademicPeriodKey);
            MCAcademicPeriod academicPeriod = (MCAcademicPeriod) academicPeriodResult.getResults();
            termCode = academicPeriod.getCode();
        }

        return termCode;
    }

}
