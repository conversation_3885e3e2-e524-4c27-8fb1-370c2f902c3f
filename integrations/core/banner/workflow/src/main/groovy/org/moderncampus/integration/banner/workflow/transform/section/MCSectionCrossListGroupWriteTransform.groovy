package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.moderncampus.integration.banner.workflow.util.CrossListGroupKeyParser
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_SECTION_CROSS_LIST_GROUP
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionCrossListGroupWriteTransform extends BaseTransformer<MCSectionCrossListGroup, String> {

    @Override
    protected String doTransform(TransformContext ctx, MCSectionCrossListGroup input) throws Exception {
        Map<String, ?> requestRoot = [:]

        if (USE_CASE_UPDATE_SECTION_CROSS_LIST_GROUP == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)) {
            def key = CrossListGroupKeyParser.parse(input.id)
            input.groupCode = key.groupCode()
            input.termCode = key.termCode()
        }

        if (isValueExist(input.groupCode)) {
            requestRoot.keyblocXlstGroup = input.groupCode
        }

        if (isValueExist(input.termCode)) {
            requestRoot.keyblocTermCode = input.termCode
        }

        if (isValueExist(input.maxEnrollments)) {
            requestRoot.maxEnrl = input.maxEnrollments
        }

        if (isValueExist(input.sectionCode)) {
            requestRoot.crn = input.sectionCode
        }

        return JsonOutput.toJson(requestRoot)
    }

}
