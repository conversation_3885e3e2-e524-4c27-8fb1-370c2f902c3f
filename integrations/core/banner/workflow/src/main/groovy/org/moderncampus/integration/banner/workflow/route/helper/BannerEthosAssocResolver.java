package org.moderncampus.integration.banner.workflow.route.helper;

import org.apache.camel.CamelContext;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.ellucian.component.BannerCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianCloudEthosAssociationResolver;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.springframework.stereotype.Component;

@Component
public class BannerEthosAssocResolver extends EllucianCloudEthosAssociationResolver {

    public BannerEthosAssocResolver(ProducerTemplate template, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator ellucianEthosReadEndpointPaginator) {
        super(template, camelContext, ellucianEthosReadEndpointPaginator);
    }

    @Override
    public EllucianCloudEndpointType getEndpointType() {
        return EllucianCloudEndpointType.BANNER_ETHOS_API;
    }

    @Override
    public Class<? extends IEllucianCloudConnectionConfiguration> getConnectionConfigurationClass() {
        return BannerCloudConnectionConfiguration.class;
    }
}
