package org.moderncampus.integration.banner.component.internal;

import static org.moderncampus.integration.banner.component.internal.APIDeployType.INTEGRATION_API;
import static org.moderncampus.integration.banner.component.internal.APIDeployType.STUDENT_API;
import static org.moderncampus.integration.banner.component.internal.EthosMediaType.*;

import org.springframework.http.MediaType;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PACKAGE)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum APIResource {

    EDUCATIONAL_INSTITUTION_UNITS("educational-institution-units", STUDENT_API),
    SUBJECTS("subjects", STUDENT_API, ETHOS_V6),
    INSTRUCTIONAL_METHODS("instructional-methods", STUDENT_API),
    SITES("sites", INTEGRATION_API),
    ACADEMIC_LEVELS("academic-levels", STUDENT_API),
    ADMINISTRATIVE_PERIODS("administrative-periods", STUDENT_API),
    COURSES("courses", STUDENT_API, ETHOS_V16),
    SECTIONS("sections", STUDENT_API, ETHOS_V16),
    ROOMS("rooms", INTEGRATION_API),
    ACADEMIC_PROGRAMS("academic-programs", STUDENT_API, ETHOS_V15),
    COURSE_TITLE_TYPES("course-title-types", STUDENT_API),
    SECTION_TITLE_TYPES("section-title-types", STUDENT_API),
    SECTION_DESCRIPTION_TYPES("section-description-types", STUDENT_API),
    SECTION_INSTRUCTORS("section-instructors", STUDENT_API),
    CREDIT_CATEGORIES("credit-categories", STUDENT_API),
    ADMINISTRATIVE_INSTRUCTIONAL_METHODS("administrative-instructional-methods", STUDENT_API),
    SECTION_STATUSES("section-statuses", STUDENT_API),
    INSTRUCTIONAL_EVENTS("instructional-events", STUDENT_API, ETHOS_V11),
    ACADEMIC_PERIODS("academic-periods", APIDeployType.STUDENT_API),
    PERSONS("persons", INTEGRATION_API),
    INSTRUCTORS("instructors", STUDENT_API);

    String value;

    APIDeployType deployType;

    MediaType mimeType;

    APIResource(String value, APIDeployType deployType) {
        this(value, deployType, MediaType.APPLICATION_JSON);
    }

    public static APIResource fromValue(String value) {
        for (APIResource operationName : APIResource.values()) {
            if (operationName.value.equals(value)) {
                return operationName;
            }
        }
        throw new IllegalArgumentException(value);
    }
}
