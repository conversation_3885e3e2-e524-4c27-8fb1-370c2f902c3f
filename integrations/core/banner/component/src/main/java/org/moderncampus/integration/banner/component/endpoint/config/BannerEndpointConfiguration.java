package org.moderncampus.integration.banner.component.endpoint.config;

import org.moderncampus.integration.banner.component.IBannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.endpoint.EndpointType;
import org.moderncampus.integration.banner.component.internal.APIResource;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BannerEndpointConfiguration {

    APIResource resource;

    EndpointType endpointType;

    IBannerConnectionConfiguration connectionConfig;

    String resourceId;

    String httpMethod;

    String modelClass;
}
