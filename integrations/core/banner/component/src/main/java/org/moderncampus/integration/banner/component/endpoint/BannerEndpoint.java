package org.moderncampus.integration.banner.component.endpoint;

import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import java.net.MalformedURLException;
import java.util.Map;

import org.apache.camel.Consumer;
import org.apache.camel.Processor;
import org.apache.camel.Producer;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.support.DefaultEndpoint;
import org.moderncampus.integration.banner.component.BannerComponent;
import org.moderncampus.integration.banner.component.IBannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.endpoint.config.BannerEndpointConfiguration;
import org.moderncampus.integration.banner.component.internal.APIResource;
import org.moderncampus.integration.banner.component.producer.BannerBPAPIProducer;
import org.moderncampus.integration.banner.component.producer.BannerEthosAPIProducer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Getter;

@Getter
@Component
@Lazy(value = false)
public class BannerEndpoint extends DefaultEndpoint implements ApplicationContextAware {

    private static ApplicationContext context;

    BannerEndpointConfiguration endpointConfiguration = new BannerEndpointConfiguration();

    APIResource resourceName;

    EndpointType endpointType;

    ProducerTemplate producerTemplate;

    ObjectMapper mapper;

    public BannerEndpoint() {
        super();
    }

    public BannerEndpoint(String uri, BannerComponent component) {
        super(uri, component);
    }

    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String endpointType;
        String resourceName;
        String[] uriParts = remaining.split(":");
        if (uriParts.length != 3) {
            throw new MalformedURLException(
                    String.format("An invalid banner remaining uri: '%s' was provided.",
                            remaining));
        }
        setEndpointType(EndpointType.valueOf(uriParts[0]));
        setResourceName(APIResource.valueOf(uriParts[1]));
        producerTemplate = context.getBean(beanRef(ProducerTemplate.class), ProducerTemplate.class);
        mapper = context.getBean(beanRef(ObjectMapper.class), ObjectMapper.class);
    }

    @Override
    public Producer createProducer() throws Exception {
        return switch (endpointConfiguration.getEndpointType()) {
            case ETHOS_API -> new BannerEthosAPIProducer(this);
            case BP_API -> new BannerBPAPIProducer(this);
            default -> throw new UnsupportedOperationException(
                    String.format("Banner producer %s is not implemented", endpointConfiguration.getEndpointType()));
        };
    }

    @Override
    public Consumer createConsumer(Processor processor) throws Exception {
        throw new UnsupportedOperationException("Banner consumer is not implemented.");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    void setEndpointType(EndpointType endpointType) {
        getEndpointConfiguration().setEndpointType(endpointType);
    }

    void setResourceName(APIResource resourceName) {
        getEndpointConfiguration().setResource(resourceName);
    }

    public void setConnectionConfig(IBannerConnectionConfiguration connectionConfig) {
        getEndpointConfiguration().setConnectionConfig(connectionConfig);
    }

    public void setResourceId(String resourceId) {
        getEndpointConfiguration().setResourceId(resourceId);
    }

    public void setHttpMethod(String httpMethod) {
        getEndpointConfiguration().setHttpMethod(httpMethod);
    }

    public void setModelClass(String modelClass) {
        getEndpointConfiguration().setModelClass(modelClass);
    }
}
