package org.moderncampus.integration.banner.component.producer;

import org.apache.camel.Endpoint;
import org.moderncampus.integration.banner.component.IBannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.internal.APIDeployType;

public class BannerBPAPIProducer extends BannerAPIProducer {

    public BannerBPAPIProducer(Endpoint endpoint) {
        super(endpoint);
    }

    @Override
    protected String getBasePath(IBannerConnectionConfiguration connectionConfiguration, APIDeployType deployType) {
        return connectionConfiguration.getBusinessProcessAPIBasePath();
    }
}
