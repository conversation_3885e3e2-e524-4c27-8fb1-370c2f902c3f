package org.moderncampus.integration.banner.component;

import org.moderncampus.integration.banner.component.auth.BannerAuthConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@ConfigurationProperties(prefix = "banner.integration")
public class BannerConnectionConfiguration implements IBannerConnectionConfiguration {

    String studentAPIBasePath;

    String integrationAPIBasePath;

    String businessProcessAPIBasePath;

    BannerAuthConfiguration auth;

    boolean useHttp;
}
