package org.moderncampus.integration.banner.component.producer;

import static org.apache.camel.component.http.HttpConstants.HTTP_METHOD;
import static org.apache.hc.client5.http.auth.StandardAuthScheme.BASIC;
import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;
import static org.moderncampus.integration.component.constants.Constants.*;
import static org.moderncampus.integration.constants.Constants.QUERY_PARAMS;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Map;
import java.util.Optional;

import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.component.http.HttpMethods;
import org.apache.camel.support.DefaultProducer;
import org.apache.camel.support.ExchangeHelper;
import org.apache.hc.core5.http.ContentType;
import org.moderncampus.integration.banner.component.IBannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.auth.BannerAuthConfiguration;
import org.moderncampus.integration.banner.component.endpoint.BannerEndpoint;
import org.moderncampus.integration.banner.component.endpoint.config.BannerEndpointConfiguration;
import org.moderncampus.integration.banner.component.helper.BannerEndpointHeaderFilterStrategy;
import org.moderncampus.integration.banner.component.internal.APIDeployType;
import org.moderncampus.integration.banner.component.internal.APIResource;
import org.moderncampus.integration.constants.Constants;

public abstract class BannerAPIProducer extends DefaultProducer {

    static final String ACCEPT = "Accept";
    static final String APPLICATION_JSON = ContentType.APPLICATION_JSON.toString();

    public BannerAPIProducer(Endpoint endpoint) {
        super(endpoint);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        BannerEndpoint bannerEndpoint = (BannerEndpoint) getEndpoint();
        ProducerTemplate template = bannerEndpoint.getProducerTemplate();
        BannerEndpointConfiguration endpointConfiguration = bannerEndpoint.getEndpointConfiguration();
        IBannerConnectionConfiguration connectionConfiguration = endpointConfiguration.getConnectionConfig();
        BannerAuthConfiguration authConfiguration = connectionConfiguration.getAuth();
        String modelClassStr = endpointConfiguration.getModelClass();
        Class<?> modelClass = modelClassStr != null ? Class.forName(endpointConfiguration.getModelClass()) : null;
        APIResource resource = endpointConfiguration.getResource();
        APIDeployType deployType = resource.getDeployType();
        Optional<String> httpMethod = Optional.ofNullable(endpointConfiguration.getHttpMethod());
        Message message = exchange.getMessage();
        message.setHeader(ACCEPT, ContentType.APPLICATION_JSON.getMimeType());
        if (exchange.getMessage().getBody() != null) {
            message.setHeader(CONTENT_TYPE, APPLICATION_JSON);
        }
        httpMethod.ifPresent(s -> message.setHeader(HTTP_METHOD, HttpMethods.valueOf(s)));
        String basePath = getBasePath(connectionConfiguration, deployType);
        String resourceId = endpointConfiguration.getResourceId();
        String uri = buildRouteURI(connectionConfiguration.isUseHttp() ? Constants.HTTP : Constants.HTTPS,
                appendResourceToBasePath(basePath, resource, resourceId), routeQueryParamStr(
                        Map.of(AUTH_METHOD, BASIC, AUTH_PASSWORD, authConfiguration.getPassword(), AUTH_USERNAME,
                                authConfiguration.getUsername(), AUTHENTICATION_PREEMPTIVE,
                                Boolean.TRUE.toString(), Constants.HEADER_FILTER_STRATEGY, beanRef(
                                        BannerEndpointHeaderFilterStrategy.class))) + Optional.ofNullable(
                                message.getHeader(QUERY_PARAMS))
                        .map(value -> "&" + value).orElse(""));
        exchange = template.send(uri, exchange);
        if (modelClass != null) {
            String response = exchange.getMessage().getBody(String.class);
            if (String.class.equals(modelClass)) {
                exchange.getMessage().setBody(response);
            } else {
                exchange.getMessage().setBody(bannerEndpoint.getMapper().readValue(response, modelClass));
            }
        }
        ExchangeHelper.prepareOutToIn(exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
    }

    private String appendResourceToBasePath(String baseUrl, APIResource resource, String resourceId) {
        return baseUrl + "/" + resource.getValue() + Optional.ofNullable(resourceId).map(id -> "/" + resourceId)
                .orElse("");
    }

    protected abstract String getBasePath(IBannerConnectionConfiguration connectionConfiguration,
            APIDeployType deployType);
}
