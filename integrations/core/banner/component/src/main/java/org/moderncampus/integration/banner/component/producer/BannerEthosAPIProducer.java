package org.moderncampus.integration.banner.component.producer;

import org.apache.camel.Endpoint;
import org.moderncampus.integration.banner.component.IBannerConnectionConfiguration;
import org.moderncampus.integration.banner.component.internal.APIDeployType;

public class BannerEthosAPIProducer extends Banner<PERSON>IProducer {

    public BannerEthosAPIProducer(Endpoint endpoint) {
        super(endpoint);
    }

    protected String getBasePath(IBannerConnectionConfiguration connectionConfiguration, APIDeployType deployType) {
        if (APIDeployType.STUDENT_API == deployType) {
            return connectionConfiguration.getStudentAPIBasePath();
        } else if (APIDeployType.INTEGRATION_API == deployType) {
            return connectionConfiguration.getIntegrationAPIBasePath();
        }
        throw new UnsupportedOperationException(
                String.format("Unsupported deploy type for Ethos Banner API Call %s", deployType.toString()));
    }
}
