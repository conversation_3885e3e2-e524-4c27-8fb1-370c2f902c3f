package org.moderncampus.integration.banner.component.internal;

import org.springframework.http.MediaType;

public class EthosMediaType {

    public final static MediaType ETHOS_V1 = from("vnd.hedtech.v1");
    public final static MediaType ETHOS_V6 = from("vnd.hedtech.integration.v6");
    public final static MediaType ETHOS_V7 = from("vnd.hedtech.integration.v7");
    public final static MediaType ETHOS_V11 = from("vnd.hedtech.integration.v11");
    public final static MediaType ETHOS_V15 = from("vnd.hedtech.integration.v15");
    public final static MediaType ETHOS_V16 = from("vnd.hedtech.integration.v16");

    private static MediaType from(String vendorVersion) {
        return new MediaType("application", vendorVersion + "+json");
    }
}
