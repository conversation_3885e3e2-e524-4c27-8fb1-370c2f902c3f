package org.moderncampus.integration.banner.component.helper;

import static org.moderncampus.integration.constants.Constants.QUERY_PARAMS;

import org.apache.commons.lang3.ArrayUtils;
import org.moderncampus.integration.helper.DefaultEndpointHeaderFilterStrategy;
import org.springframework.stereotype.Component;

@Component
public class BannerEndpointHeaderFilterStrategy extends DefaultEndpointHeaderFilterStrategy {

    static final String ETHOS = "ethos";

    static final String[] BANNER_HEADER_FILTERS = ArrayUtils.addAll(HEADER_FILTERS,
            new String[]{QUERY_PARAMS, ETHOS});
}
