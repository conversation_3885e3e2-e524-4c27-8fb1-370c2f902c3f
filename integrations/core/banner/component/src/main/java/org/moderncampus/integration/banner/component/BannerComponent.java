package org.moderncampus.integration.banner.component;

import static org.moderncampus.integration.banner.component.constants.Constants.BANNER_COMPONENT_SCHEME;

import java.util.Map;

import org.apache.camel.Endpoint;
import org.apache.camel.spi.annotations.Component;
import org.apache.camel.support.DefaultComponent;
import org.moderncampus.integration.banner.component.endpoint.BannerEndpoint;

@Component(BANNER_COMPONENT_SCHEME)
public class BannerComponent extends DefaultComponent {

    @Override
    protected Endpoint createEndpoint(String uri, String remaining, Map<String, Object> parameters) throws Exception {
        BannerEndpoint endpoint = new BannerEndpoint(uri, this);
        endpoint.parseURI(remaining, parameters);
        setProperties(endpoint, parameters);

        return endpoint;
    }
}
