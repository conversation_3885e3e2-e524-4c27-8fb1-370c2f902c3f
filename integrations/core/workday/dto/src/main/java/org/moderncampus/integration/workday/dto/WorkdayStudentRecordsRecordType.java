package org.moderncampus.integration.workday.dto;

import lombok.Getter;

@Getter
public enum WorkdayStudentRecordsRecordType implements IWorkdayRecordType {

    GET_STUDENT_COURSE("Get_Student_Course"),

    SUBMIT_STUDENT_COURSE("Submit_Student_Course");

    private final String endpointOperation;

    private final String serviceName;

    private final String serviceClass;

    WorkdayStudentRecordsRecordType(String endpointOperation) {
        this.endpointOperation = endpointOperation;
        this.serviceName = "Student_Records";
        this.serviceClass = "workday.com.bsvc.v41.StudentRecordsPort";
    }
}
