package org.moderncampus.integration.workday.dto.course;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkdayCourse extends BaseDTO {

    List<AcademicUnitData> academicUnitData;

    List<CourseListingData> courseListingData;

    String privateNotes;

    String studentCourseRefWID;

    BigDecimal contactHours;

    String courseTitle;

    String abbreviatedTitle;

    String cipCodeRefId;

    BigDecimal maxUnits;

    BigDecimal minUnits;

    String publicNotes;

    String description;

    LocalDate firstAvailable;

    LocalDate lastAvailable;

    LocalDate effectiveDate;

    String academicLevelRefId;

    Boolean sectionOverridesAllowed;

    Boolean repeatable;

    Boolean concurrentlyRepeatable;

    BigDecimal repeatMaxAttempts;

    BigDecimal repeatMaxUnits;

    String unitTypeRefId;

    List<String> educationalTaxCodes;

    List<String> specialTopicTitles;

    List<String> periodsOfferedRefTypeIds;

    List<CourseComponentData> courseComponents;

    List<String> allowedLocationRefIds;

    List<CombinationInstructionalFormat> combinationInstructionalFormats;

    List<String> courseTagRefIds;

    List<GradingBasesData> allowedGradingBases;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CourseComponentData extends BaseDTO {

        String refId;
        List<String> deliveryModeRefIds;
        Boolean controlsGrading;
        BigDecimal instructorLoadPercentage;
        BigDecimal contactHours;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CombinationInstructionalFormat extends BaseDTO {

        String refId;
        BigDecimal contactHours;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class AcademicUnitData extends BaseDTO {

        String refId;
        Boolean courseInventoryOwner;
        Boolean allowedToOffer;
        Integer defaultOfferPercent;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CourseListingData extends BaseDTO {

        String refId;
        String courseNumber;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class GradingBasesData extends BaseDTO {

        String typeId;
        String parentId;
    }
}
