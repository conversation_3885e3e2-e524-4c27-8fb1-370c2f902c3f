package org.moderncampus.integration.workday.dto.course;

import jakarta.validation.constraints.Null;

public class CreateWorkdayCourse extends WorkdayCourse {

    @Override
    @Null(message = "Id value cannot be specified on a create")
    public String getId() {
        return super.getId();
    }

    @Override
    @Null(message = "Id(WID) value cannot be specified on a create")
    public String getStudentCourseRefWID() {
        return super.getStudentCourseRefWID();
    }
}
