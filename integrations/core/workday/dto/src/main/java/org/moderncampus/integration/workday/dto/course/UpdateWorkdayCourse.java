package org.moderncampus.integration.workday.dto.course;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

public class UpdateWorkdayCourse extends WorkdayCourse {

    @Override
    @NotNull(message = "Id value must be specified on an update")
    @Schema(hidden = true)
    public String getId() {
        return super.getId();
    }

    @Override
    @NotNull(message = "Effective Date must be specified on an update")
    public LocalDate getEffectiveDate() {
        return super.getEffectiveDate();
    }
}
