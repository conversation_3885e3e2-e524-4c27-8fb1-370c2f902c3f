package org.moderncampus.integration.workday.dto.dupsearch.cewd.request;

import org.moderncampus.integration.dto.cewd.CewdStudent;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WDStudentDuplicateCheckRequest {

    @NotBlank(message = "CorrelationID cannot be blank")
    String correlationId;

    @Valid
    @NotNull(message = "Student information is required")
    CewdStudent student;
}
