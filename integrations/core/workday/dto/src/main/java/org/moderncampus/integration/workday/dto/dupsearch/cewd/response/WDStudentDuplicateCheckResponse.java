package org.moderncampus.integration.workday.dto.dupsearch.cewd.response;

import java.util.List;

import org.moderncampus.integration.dto.cewd.CewdPerson;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WDStudentDuplicateCheckResponse {

    IntegrationStatus integrationStatus;

    String correlationId;

    String businessProcessId;

    List<CewdPerson> matches;

    List<String> errors;

    List<String> warnings;

    public enum IntegrationStatus {
        SUCCESS, FAILED
    }

}
