package org.moderncampus.integration.workday.dto;

import lombok.Getter;

@Getter
public enum WorkdayHRRecordType implements IWorkdayRecordType {

    GET_CHANGE_OTHER_IDS("Get_Change_Other_IDs"),
    GET_CHANGE_HOME_CONTACT_INFORMATION("Get_Change_Home_Contact_Information"),
    GET_CHANGE_WORK_CONTACT_INFORMATION("Get_Change_Work_Contact_Information"),
    CHANGE_HOME_CONTACT_INFORMATION("Change_Home_Contact_Information"),
    CHANGE_WORK_CONTACT_INFORMATION("Change_Work_Contact_Information"),
    CHANGE_OTHER_IDS("Change_Other_IDs");

    private final String endpointOperation;

    private final String serviceName;

    private final String serviceClass;

    WorkdayHRRecordType(String endpointOperation) {
        this.endpointOperation = endpointOperation;
        this.serviceName = "Human_Resources";
        this.serviceClass = "workday.com.bsvc.v41.HumanResourcesPort";
    }
}
