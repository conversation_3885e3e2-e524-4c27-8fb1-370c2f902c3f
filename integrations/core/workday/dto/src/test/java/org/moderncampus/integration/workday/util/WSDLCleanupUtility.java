package org.moderncampus.integration.workday.util;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.Map;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Comment;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

/*
 * This utility currently only supports parsing Workday (WSDL 1.0) webservice docs by commenting out <xml:complexType> under the <xsd:types> declaration. For each wsdl, a set of typeNames need to be declared that actually need to present as uncommented in the wsdl file. The commented out types are then skipped for class generation by the wsdl2Java utility
 */
public class WSDLCleanupUtility {

    static Set<String> integrationElemTypeNames = Set.of("Launch_Integration_Event_RequestType",
            "Integration_System__Audited_ObjectType", "Integration_System__Audited_ObjectIDType",
            "Integration_Launch_Parameter_DataType", "Launch_ParameterObjectType", "Launch_ParameterObjectIDType",
            "Integration_Abstract_Value_DataType", "InstanceObjectType", "Instance_IDType",
            "Integration_Repository_Document_DataType", "Document_TagObjectType", "Document_TagObjectIDType",
            "Document_TypeObjectType", "Document_TypeObjectIDType", "Mime_TypeObjectType", "Mime_TypeObjectIDType",
            "System_AccountObjectType", "System_AccountObjectIDType", "Launch_Integration_Event_ResponseType",
            "Application_Instance_Exceptions_DataType", "Background_Process_Message_Severity_LevelObjectIDType",
            "Background_Process_Message_Severity_LevelObjectType", "Background_Process_Runtime_StatusObjectIDType",
            "Background_Process_Runtime_StatusObjectType", "Event_Initialization_Documents_DataType",
            "EventObjectIDType", "EventObjectType", "Exception_DataType", "Get_Event_Documents_DataType",
            "IntegratableObjectIDType", "Integration_Enterprise_Event_Records_DataType",
            "Integration_ESB_Invocation__Abstract_ObjectIDType", "Integration_ESB_Invocation__Abstract_ObjectType",
            "Integration_Event__Abstract_ObjectIDType", "Integration_Event__Abstract_ObjectType",
            "Integration_Event_Data_WWSType", "Integration_Event_Reporting_Details_DataType", "Integration_EventType",
            "Integration_Message_Detail_DataType", "Integration_Repository_DocumentObjectIDType",
            "Integration_Repository_DocumentObjectType", "Integration_Repository_DocumentType",
            "Integration_Runtime_Parameter_DataType", "Integration_Sequence_Generator_ServiceObjectIDType",
            "Integration_Sequence_Generator_ServiceObjectType", "Integration_Sequencer_Generated_Sequence_DataType",
            "Integration_SequencerObjectIDType", "Integration_SequencerObjectType",
            "Integration_Service_Generated_Sequence_DataType", "Integration_SystemObjectIDType",
            "Integration_SystemObjectType", "Integration_TemplateObjectIDType", "Integration_TemplateObjectType",
            "Launch_Integration_Event_DataType", "Repository_Document__Abstract_ObjectIDType",
            "Repository_Document__Abstract_ObjectType", "Simple_Work_Data_Runtime_Parameter_NameType",
            "System_UserObjectIDType", "System_UserObjectType", "Text_AttributeType", "Validation_FaultType",
            "Processing_FaultType", "Validation_ErrorType", "Workday_Common_HeaderType", "IntegratableObjectType");

    static Set<String> hrElemTypeNames = Set.of("Get_Change_Other_IDs_RequestType",
            "Get_Change_Other_IDs_ResponseType",
            "Change_Other_IDs_RequestType",
            "Change_Other_IDs_ResponseType",
            "Get_Change_Home_Contact_Information_RequestType",
            "Get_Change_Home_Contact_Information_ResponseType",
            "Change_Home_Contact_Information_RequestType",
            "Change_Home_Contact_Information_ResponseType",
            "Get_Change_Work_Contact_Information_RequestType",
            "Get_Change_Work_Contact_Information_ResponseType",
            "Change_Work_Contact_Information_RequestType",
            "Change_Work_Contact_Information_ResponseType",
            "Address_Core_DataType",
            "Address_Line_Information_DataType",
            "Address_ReferenceObjectIDType",
            "Address_ReferenceObjectType",
            "Business_Process_Attachment_DataType",
            "Business_Process_Comment_DataType",
            "Business_Process_ParametersType",
            "Change_Home_Contact_Information_Business_Process_DataType",
            "Change_Home_Contact_Information_Response_DataType",
            "Change_Home_Contact_Information_Response_WrapperType",
            "Change_Other_ID_Request_CriteriaType",
            "Change_Other_IDs_Business_Process_DataType",
            "Change_Other_IDs_Response_DataType",
            "Change_Other_IDs_Response_WrapperType",
            "Change_Work_Contact_Information_Business_Process_DataType",
            "Change_Work_Contact_Information_Response_DataType",
            "Change_Work_Contact_Information_Response_WrapperType",
            "Communication_Method_Usage_Information_DataType",
            "Communication_Usage_BehaviorObjectIDType",
            "Communication_Usage_BehaviorObjectType",
            "Communication_Usage_Behavior_TenantedObjectIDType",
            "Communication_Usage_Behavior_TenantedObjectType",
            "Communication_Usage_Type_DataType",
            "Communication_Usage_TypeObjectIDType",
            "Communication_Usage_TypeObjectType",
            "Country_CityObjectIDType",
            "Country_CityObjectType",
            "CountryObjectIDType",
            "CountryObjectType",
            "Country_Phone_CodeObjectIDType",
            "Country_Phone_CodeObjectType",
            "Country_RegionObjectIDType",
            "Country_RegionObjectType",
            "Custom_ID_DataType",
            "Custom_Identification_DataType",
            "Custom_Identifier_ReferenceObjectIDType",
            "Custom_Identifier_ReferenceObjectType",
            "Custom_IDType",
            "Custom_ID_TypeObjectIDType",
            "Custom_ID_TypeObjectType",
            "Day_of_the_WeekObjectIDType",
            "Day_of_the_WeekObjectType",
            "Email_Core_DataType",
            "Email_ReferenceObjectIDType",
            "Email_ReferenceObjectType",
            "Event_Attachment_CategoryObjectIDType",
            "Event_Attachment_CategoryObjectType",
            "Get_Change_Home_Contact_Information_Request_CriteriaType",
            "Get_Change_Home_Contact_Information_Request_ReferencesType",
            "Get_Change_Other_IDs_Request_ReferencesType",
            "Get_Change_Work_Contact_Information_Request_CriteriaType",
            "Get_Change_Work_Contact_Information_Request_ReferencesType",
            "Instant_Messenger_Core_DataType",
            "Instant_Messenger_ReferenceObjectIDType",
            "Instant_Messenger_ReferenceObjectType",
            "Instant_Messenger_TypeObjectIDType",
            "Instant_Messenger_TypeObjectType",
            "OrganizationObjectIDType",
            "OrganizationObjectType",
            "Person_Address_DataType",
            "Person_Address_Information_DataType",
            "Person_Contact_Information_DataType",
            "Person_Email_DataType",
            "Person_Email_Information_DataType",
            "Person_Instant_Messenger_DataType",
            "Person_Instant_Messenger_Information_DataType",
            "Person_Phone_DataType",
            "Person_Phone_Information_DataType",
            "Person_Type_CriteriaType",
            "Person_Web_Address_DataType",
            "Person_Web_Address_Information_DataType",
            "Phone_Core_DataType",
            "Phone_Device_TypeObjectIDType",
            "Phone_Device_TypeObjectType",
            "Phone_ReferenceObjectIDType",
            "Phone_ReferenceObjectType",
            "Response_FilterType",
            "Response_ResultsType",
            "RoleObjectIDType",
            "RoleObjectType",
            "Submunicipality_Information_DataType",
            "Subregion_Information_DataType",
            "Unique_IdentifierObjectIDType",
            "Unique_IdentifierObjectType",
            "Universal_IdentifierObjectIDType",
            "Universal_IdentifierObjectType",
            "Web_Address_Core_DataType",
            "Web_Address_ReferenceObjectIDType",
            "Web_Address_ReferenceObjectType",
            "WorkerObjectIDType",
            "WorkerObjectType",
            "Validation_FaultType", "Processing_FaultType", "Validation_ErrorType", "Workday_Common_HeaderType");

    static Set<String> studentRecordsElemTypeNames = Set.of("Get_Student_Courses_RequestType",
            "Get_Student_Courses_ResponseType", "Academic_Curricular_DivisionObjectIDType",
            "Academic_Curricular_DivisionObjectType", "Academic_LevelObjectIDType", "Academic_LevelObjectType",
            "Academic_Period_TypeObjectIDType", "Academic_Period_TypeObjectType",
            "CIP_Code__Workday_Owned_ObjectIDType", "CIP_Code__Workday_Owned_ObjectType", "CompetencyObjectIDType",
            "CompetencyObjectType", "Course_DefinitionObjectIDType", "Course_DefinitionObjectType",
            "Course_Material_Course_Assignment_DataType", "Course_Overridable_FieldObjectIDType",
            "Course_Overridable_FieldObjectType", "Course_SectionObjectIDType", "Course_SectionObjectType",
            "Course_SubjectObjectIDType", "Course_SubjectObjectType", "Delivery_ModeObjectIDType",
            "Delivery_ModeObjectType", "Educational_Taxonomy_CodeObjectIDType", "Educational_Taxonomy_CodeObjectType",
            "Grading_Basis_CategoryObjectIDType", "Grading_Basis_CategoryObjectType",
            "Instructional_FormatObjectIDType", "Instructional_FormatObjectType", "Learning_OutcomeObjectIDType",
            "Learning_OutcomeObjectType", "LocationObjectIDType", "LocationObjectType", "Other_Credit_TypeObjectIDType",
            "Other_Credit_TypeObjectType", "Other_Credit_Type_Value_DataType", "Other_Unit_TypeObjectIDType",
            "Other_Unit_TypeObjectType", "Response_FilterType", "Response_ResultsType",
            "Student_Course_Academic_Unit_DataType",
            "Student_Course_Combination_Instructional_Format_Contact_Hours_DataType",
            "Student_Course_Component_DataType", "Student_Course_Data_DataType",
            "Student_Course_Data_Snapshot_DataType", "Student_Course_DataType", "Student_Course_Listing_DataType",
            "Student_Course_MaterialObjectIDType", "Student_Course_MaterialObjectType",
            "Student_Course_Request_CriteriaType", "Student_Course_Request_ReferencesType",
            "Student_Course_Response_DataType", "Student_Course_Special_Topic_DataType",
            "Student_Course_TagObjectIDType", "Student_Course_TagObjectType", "Student_CourseType",
            "Student_Eligibility_RuleObjectIDType", "Student_Eligibility_RuleObjectType",
            "Unique_IdentifierObjectIDType", "Unique_IdentifierObjectType", "Validation_FaultType",
            "Processing_FaultType", "Validation_ErrorType", "Workday_Common_HeaderType");

    static Map<String, Set<String>> elemTypeNameMap = Map.of("Integrations", integrationElemTypeNames, "HumanResources",
            hrElemTypeNames, "StudentRecords", studentRecordsElemTypeNames);

    public static void main(String[] args) {
        try {
            String wsdlName = args[0];
            String wsdlPath = args[1];
            String serviceName = args[2];
            String absOutputPath = args[3];
            // Load the WSDL file from the resources directory
            InputStream inputStream = WSDLCleanupUtility.class.getResourceAsStream(wsdlPath + wsdlName);

            // Create a DocumentBuilder
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true); // Ensure namespace awareness
            DocumentBuilder builder = factory.newDocumentBuilder();

            // Parse the XML document
            Document document = builder.parse(inputStream);

            // Close the input stream
            inputStream.close();

            // Traverse the XML document
            Element rootElement = document.getDocumentElement();
            System.out.println("Root Element: " + rootElement.getNodeName());

            // If you want to traverse child nodes, you can use getChildNodes() method
            NodeList nodeList = rootElement.getChildNodes();
            Set<String> elemNames = elemTypeNameMap.get(serviceName);
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getNodeName().equals("wsdl:types")) {
                    System.out.println("WSDL:TYPES!");
                    NodeList xsdSchemaNodes = node.getChildNodes().item(1).getChildNodes();
                    for (int j = 0; j < xsdSchemaNodes.getLength(); j++) {
                        Node xsdElement = xsdSchemaNodes.item(j);
                        if (xsdElement.getNodeName().equals("xsd:complexType") && !elemNames.contains(
                                xsdElement.getAttributes().getNamedItem("name").getNodeValue())) {
                            String elementXml = nodeToXml(xsdElement);
                            if (!elementXml.startsWith("<!-")) {
                                Comment comment = document.createComment(elementXml);
                                xsdElement.getParentNode().replaceChild(comment, xsdElement);
                            }
                        }
                    }

                }
            }
            saveDocument(document, absOutputPath + wsdlName);
        } catch (ParserConfigurationException | SAXException | IOException e) {
            e.printStackTrace();
        }
    }

    private static void saveDocument(Document document, String fileName) {
        try {
            // Prepare the XML document for printing
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");

            // Save the XML document to a file
            File file = new File(fileName);
            FileWriter fileWriter = new FileWriter(file);
            transformer.transform(new DOMSource(document), new StreamResult(fileWriter));
            System.out.println("Modified WSDL saved to: " + file.getAbsolutePath());

        } catch (TransformerException | IOException e) {
            e.printStackTrace();
        }
    }

    private static void printDocument(Document document) {
        try {
            // Prepare the XML document for printing
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");

            // Print the XML document to the console
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));
            System.out.println(writer.toString());

        } catch (TransformerException e) {
            e.printStackTrace();
        }
    }

    private static String nodeToXml(Node node) {
        try {
            // Prepare the XML document for printing
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");

            // Print the XML document to a string
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(node), new StreamResult(writer));
            return writer.toString();
        } catch (TransformerException e) {
            e.printStackTrace();
            return "";
        }
    }
}
