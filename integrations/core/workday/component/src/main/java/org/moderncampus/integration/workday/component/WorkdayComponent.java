package org.moderncampus.integration.workday.component;

import java.util.Map;

import org.apache.camel.Endpoint;
import org.apache.camel.spi.annotations.Component;
import org.apache.camel.support.DefaultComponent;
import org.moderncampus.integration.workday.component.constants.Constants;

@Component(Constants.WORKDAY_COMPONENT_SCHEME)
public class WorkdayComponent extends DefaultComponent {

    @Override
    protected Endpoint createEndpoint(String uri, String remaining, Map<String, Object> parameters) throws Exception {
        WorkdayEndpoint endpoint = new WorkdayEndpoint(uri, this);
        endpoint.parseURI(remaining, parameters);
        setProperties(endpoint, parameters);

        return endpoint;
    }

    @Override
    protected void afterConfiguration(String uri, String remaining, Endpoint endpoint, Map<String, Object> parameters)
            throws Exception {
        super.afterConfiguration(uri, remaining, endpoint, parameters);
        WorkdayEndpoint workdayEndpoint = (WorkdayEndpoint) endpoint;
        ((WorkdayEndpoint) endpoint).validateConfigurationParameters();
    }
}
