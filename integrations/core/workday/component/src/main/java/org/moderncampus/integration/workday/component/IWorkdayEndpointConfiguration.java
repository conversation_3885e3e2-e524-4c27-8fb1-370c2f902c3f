package org.moderncampus.integration.workday.component;

import java.util.List;

import org.moderncampus.integration.workday.component.auth.IWorkdayAuthenticator;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;

import jakarta.xml.bind.JAXBElement;

public interface IWorkdayEndpointConfiguration {

    String CONNECTION_CONFIG_PARAM = "connectionConfig";
    String RECORD_TYPE_PARAM = "recordType";
    String RETURN_TYPE_CLASS_PARAM = "returnTypeClass";
    String SOAP_HEADERS = "soapHeaders";

    public enum Entity {
        report,
        soapWS,
        health
    }

    public IWorkdayConnectionConfiguration getConnectionConfig();

    public Entity getEntity();

    public IWorkdayAuthenticator getAuthenticator();

    public IWorkdayRecordType getRecordType();

    public String getReturnTypeClass();

    public String getHttpMethod();

    public List<JAXBElement<?>> getSoapHeaders();
}
