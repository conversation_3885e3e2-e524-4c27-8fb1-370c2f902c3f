package org.moderncampus.integration.workday.component.auth;

import java.util.Date;

import org.apache.camel.health.HealthCheck;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WorkdayAuthenticator implements IWorkdayAuthenticator {

    WorkdayOAuthTokenRetriever oAuthTokenRetriever;

    @CachePut(value = "authTokenCache", key = "#root.args[0].host + '-' + #root.args[0].tenant + '-' + #root.args[0].auth.clientId + '-' + #root.args[0].auth.clientSecret + '-' + #root.args[0].auth.refreshToken", unless = "#result == null")
    public String getAuthToken(IWorkdayConnectionConfiguration endpointConfiguration, boolean forceRefresh)
            throws Exception {
        if (forceRefresh) {
            return oAuthTokenRetriever.retrieveNewOAuthToken(endpointConfiguration);
        }
        String bearerToken = oAuthTokenRetriever.retrieveOAuthToken(endpointConfiguration);
        DecodedJWT decodedJWT = decodedJWT(bearerToken);
        if (decodedJWT != null) {
            long expirationTime = decodedJWT.getExpiresAt().getTime();
            long currentTime = new Date().getTime();
            long timeToExpiry = expirationTime - currentTime;

            if (timeToExpiry > 3000) {
                return bearerToken;
            }
        }
        return oAuthTokenRetriever.retrieveNewOAuthToken(endpointConfiguration);
    }

    public String getAuthToken(IWorkdayConnectionConfiguration endpointConfiguration)
            throws Exception {
        return getAuthToken(endpointConfiguration, false);
    }

    @Override
    public HealthCheck getHealthCheck(IWorkdayConnectionConfiguration endpointConfiguration,
            IntegrationRequestContext requestContext) {
        return new WorkdayAuthHealthCheck(oAuthTokenRetriever, endpointConfiguration, requestContext);
    }

    public DecodedJWT decodedJWT(String jwtToken) {
        return JWT.decode(jwtToken);
    }
}
