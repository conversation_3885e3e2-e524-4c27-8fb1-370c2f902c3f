package org.moderncampus.integration.workday.component;

import java.util.Map;

import org.moderncampus.integration.workday.component.auth.WorkdayAuthConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@ConfigurationProperties(prefix = "workday.integration")
public class WorkdayConnectionConfiguration implements IWorkdayConnectionConfiguration {

    String tenant;
    String host;
    WorkdayAuthConfiguration auth;
    String version;
    String reportUser;
    Map<String, WorkdayReportConfiguration> report;
}
