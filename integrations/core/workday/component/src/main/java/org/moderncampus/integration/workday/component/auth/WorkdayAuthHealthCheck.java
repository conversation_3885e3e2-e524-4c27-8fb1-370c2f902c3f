package org.moderncampus.integration.workday.component.auth;

import java.util.Map;

import org.apache.camel.health.HealthCheckResultBuilder;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.health.IntEndpointHealthCheck;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.springframework.util.StringUtils;

public class WorkdayAuthHealthCheck extends IntEndpointHealthCheck {

    private final WorkdayOAuthTokenRetriever authTokenRetriever;
    private final IWorkdayConnectionConfiguration configuration;

    public WorkdayAuthHealthCheck(final WorkdayOAuthTokenRetriever authTokenRetriever,
            IWorkdayConnectionConfiguration configuration, IntegrationRequestContext requestContext) {
        super("workday", "workday:oauth:authentication", requestContext);
        this.authTokenRetriever = authTokenRetriever;
        this.configuration = configuration;
    }

    @Override
    public boolean isEnabled() {
        return authTokenRetriever != null && configuration != null;
    }

    @Override
    protected void doHealthCheck(HealthCheckResultBuilder builder, Map<String, Object> options) {
        try {
            String authToken = authTokenRetriever.retrieveNewOAuthToken(configuration);
            if (StringUtils.hasText(authToken)) {
                builder.up();
            }
        } catch (Exception e) {
            builder.message("Unable to authenticate with Workday OAuth endpoint: " + e.getMessage());
            builder.error(e);
            builder.down();
            return;
        }
    }
}
