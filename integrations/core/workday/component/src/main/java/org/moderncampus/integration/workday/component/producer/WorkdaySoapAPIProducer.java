package org.moderncampus.integration.workday.component.producer;

import static org.apache.camel.component.cxf.common.message.CxfConstants.*;
import static org.apache.cxf.message.Message.CONNECTION_TIMEOUT;
import static org.apache.cxf.message.Message.RECEIVE_TIMEOUT;
import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.dom.DOMResult;

import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.component.cxf.common.DataFormat;
import org.apache.camel.component.cxf.common.message.CxfConstants;
import org.apache.camel.support.ExchangeHelper;
import org.apache.camel.util.CastUtils;
import org.apache.camel.util.ObjectHelper;
import org.apache.cxf.binding.soap.SoapHeader;
import org.apache.cxf.headers.Header;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayEndpoint;
import org.w3c.dom.Document;

import jakarta.xml.bind.JAXB;
import jakarta.xml.bind.JAXBElement;

public class WorkdaySoapAPIProducer extends BaseWorkdayProducer {

    static final String WORKDAY_SOAP_API_URL_TEMPLATE = "https://%s/ccx/service/%s/%s/%s";

    static final String CXF_COMPONENT = "cxf";

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TRACE_PRE_INVOKE = "Pre-soap call invoke";
    private static final String TRACE_POST_INVOKE = "Post-soap call invoke";

    public WorkdaySoapAPIProducer(WorkdayEndpoint endpoint) {
        super(endpoint);
    }

    @Override
    protected void validateEndpoint(Exchange exchange, WorkdayEndpoint endpoint) {
        ObjectHelper.notNull(endpoint.getEndpointConfiguration().getRecordType(), RECORD_TYPE_PARAM);
        ObjectHelper.notNull(endpoint.getEndpointConfiguration().getRecordType().getServiceClass(), SERVICE_CLASS);
        ObjectHelper.notNull(endpoint.getEndpointConfiguration().getRecordType().getServiceName(), SERVICE_NAME);
    }

    @Override
    protected void processRequest(Exchange exchange, String authToken, WorkdayEndpoint endpoint) throws Exception {
        ProducerTemplate producerTemplate = endpoint.getProducerTemplate();
        IWorkdayEndpointConfiguration endpointConfig = endpoint.getEndpointConfiguration();
        endpoint.getTracingService().trace(TRACE_PRE_INVOKE, exchange);
        boolean rawMode = endpoint.getEndpointConfiguration().getReturnTypeClass() == null;
        Message message = exchange.getMessage();
        List<SoapHeader> headers = null;
        if (!rawMode) {
            if (message.getHeader(Header.HEADER_LIST) == null) {
                message.setHeader(Header.HEADER_LIST, new ArrayList<SoapHeader>());
            }
            headers = CastUtils.cast((List<?>) message.getHeader(Header.HEADER_LIST));
            addSOAPHeader(headers, endpointConfig.getSoapHeaders());
            message.setHeader(OPERATION_NAME, endpointConfig.getRecordType().getEndpointOperation());
        }
        message.setHeader(AUTHORIZATION_HEADER, "Bearer" + " " + authToken);
        setCxfRequestContext(message);
        String cxfURI = buildRouteURI(CXF_COMPONENT, createEndpointURI(endpointConfig), (!rawMode ?
                "serviceClass=" + endpointConfig.getRecordType().getServiceClass() : "dataFormat=" + DataFormat.RAW)
                + "&synchronous=true");
        exchange = producerTemplate.send(cxfURI, exchange);
        ExchangeHelper.prepareOutToIn(exchange);
        endpoint.getTracingService().trace(TRACE_POST_INVOKE, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
        exchange.getMessage()
                .setBody(rawMode ? exchange.getMessage().getBody(InputStream.class)
                        : exchange.getMessage().getBody(Class.forName(endpointConfig.getReturnTypeClass())));
    }

    private String createEndpointURI(IWorkdayEndpointConfiguration endpointConfiguration) {
        IWorkdayConnectionConfiguration connectionConfiguration = endpointConfiguration.getConnectionConfig();
        return String.format(WORKDAY_SOAP_API_URL_TEMPLATE, connectionConfiguration.getHost(),
                connectionConfiguration.getTenant(), endpointConfiguration.getRecordType().getServiceName(),
                endpointConfiguration.getConnectionConfig().getVersion());
    }

    private void addSOAPHeader(List<SoapHeader> headers, List<JAXBElement<?>> soapHeaders) throws Exception {
        if (soapHeaders == null) {
            return;
        }
        for (JAXBElement<?> soapHeaderElem : soapHeaders) {
            Document document =
                    DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
            JAXB.marshal(soapHeaderElem, new DOMResult(document));
            SoapHeader header = new SoapHeader(soapHeaderElem.getName(), document.getDocumentElement());
            header.setDirection(Header.Direction.DIRECTION_OUT);
            headers.add(header);
        }
    }

    private void setCxfRequestContext(Message message) {
        //use the legacy HttpUrlConnection client instead of the newer JDK 11 Java HTTP client. The latter creates a thread pool per client
        //and asynchronously streams the body which creates unnecessary overhead in the current threading model (thread per request)
        message.setHeader(CxfConstants.REQUEST_CONTEXT,
                Map.of("force.urlconnection.http.conduit", true,
                        CONNECTION_TIMEOUT, 180000,//max of 3 minutes to open a connection successfully
                        RECEIVE_TIMEOUT,
                        180000));//max of 3 minutes (socket timeout) connection can be idle between data retrievals
    }
}
