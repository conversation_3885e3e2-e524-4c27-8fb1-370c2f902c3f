package org.moderncampus.integration.workday.component.config;

import java.util.Set;

import org.apache.camel.component.http.HttpEndpointUriFactory;

public class RAASHttpUriFactory extends HttpEndpointUriFactory {

    private static final Set<String> RAAS_PROP_NAMES;

    static final String RESPONSE_TIMEOUT_PROP = "responseTimeout";

    static {
        RAAS_PROP_NAMES = Set.of(RESPONSE_TIMEOUT_PROP);
    }

    @Override
    public Set<String> propertyNames() {
        return RAAS_PROP_NAMES;
    }
}
