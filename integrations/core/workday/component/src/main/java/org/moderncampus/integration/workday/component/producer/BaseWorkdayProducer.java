package org.moderncampus.integration.workday.component.producer;

import org.apache.camel.Exchange;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.apache.camel.support.DefaultProducer;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.WorkdayEndpoint;

public abstract class BaseWorkdayProducer extends DefaultProducer {

    public BaseWorkdayProducer(WorkdayEndpoint endpoint) {
        super(endpoint);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        if (getEndpoint() instanceof WorkdayEndpoint endpoint) {
            IWorkdayEndpointConfiguration endpointConfiguration = endpoint.getEndpointConfiguration();
            IWorkdayConnectionConfiguration connectionConfiguration = endpointConfiguration.getConnectionConfig();
            String authToken = null;
            if (endpointConfiguration.getAuthenticator() != null) {
                authToken = endpointConfiguration.getAuthenticator().getAuthToken(connectionConfiguration);
            }
            validateEndpoint(exchange, endpoint);
            try {
                processRequest(exchange, authToken, endpoint);
            } catch (Exception e) {
                if (e instanceof HttpOperationFailedException httpOperationFailedException &&
                        httpOperationFailedException.getHttpResponseCode() == 401) {//refresh stale session
                    authToken = endpointConfiguration.getAuthenticator()
                            .getAuthToken(connectionConfiguration, true);
                    exchange.setException(null);
                    processRequest(exchange, authToken, endpoint);
                    return;
                }
                throw e;
            }
        }
    }

    protected abstract void validateEndpoint(Exchange exchange, WorkdayEndpoint endpoint);

    protected abstract void processRequest(Exchange exchange, String authToken, WorkdayEndpoint endpoint)
            throws Exception;
}
