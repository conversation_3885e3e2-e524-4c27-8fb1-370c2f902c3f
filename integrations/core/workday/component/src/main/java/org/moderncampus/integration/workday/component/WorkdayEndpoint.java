package org.moderncampus.integration.workday.component;

import static org.moderncampus.integration.Constants.WORKDAY_SYSTEM_ID;
import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.camel.Consumer;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.Producer;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.health.HealthCheck;
import org.apache.camel.support.DefaultEndpoint;
import org.apache.camel.util.ObjectHelper;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.health.DefaultEndpointHostAvailabilityHealthCheck;
import org.moderncampus.integration.health.IIntEndpointHealthCheckInvoker;
import org.moderncampus.integration.route.support.IRouteTracingService;
import org.moderncampus.integration.route.support.RouteTracingService;
import org.moderncampus.integration.workday.component.auth.IWorkdayAuthenticator;
import org.moderncampus.integration.workday.component.auth.WorkdayAuthenticator;
import org.moderncampus.integration.workday.component.producer.WorkdayReportProducer;
import org.moderncampus.integration.workday.component.producer.WorkdaySoapAPIProducer;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.xml.bind.JAXBElement;
import lombok.Getter;

@Getter
@Component
@Lazy(value = false)
public class WorkdayEndpoint extends DefaultEndpoint implements ApplicationContextAware,
        IIntEndpointHealthCheckInvoker {

    private static ApplicationContext context;

    WorkdayEndpointConfiguration endpointConfiguration = new WorkdayEndpointConfiguration();

    ProducerTemplate producerTemplate;

    IRouteTracingService<Exchange> tracingService;

    public WorkdayEndpoint() {
    }

    public WorkdayEndpoint(String uri, WorkdayComponent component) {
        super(uri, component);
    }

    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String entity;
        try {
            URI u = new URI(remaining);
            entity = u.getPath();
        } catch (Exception e) {
            throw new MalformedURLException(
                    String.format("An invalid workday remaining uri: '%s' was provided. Error: '%s'", remaining,
                            e.getMessage()));
        }
        ObjectHelper.notNull(entity, "Entity");
        setEntity(IWorkdayEndpointConfiguration.Entity.valueOf(entity));
        producerTemplate = context.getBean(beanRef(ProducerTemplate.class), ProducerTemplate.class);
        tracingService = context.getBean(beanRef(RouteTracingService.class), IRouteTracingService.class);
        setAuthenticator(
                context.getBean(beanRef(WorkdayAuthenticator.class), IWorkdayAuthenticator.class));
    }

    public Producer createProducer() throws Exception {
        return switch (endpointConfiguration.getEntity()) {
            case report -> new WorkdayReportProducer(this);
            case soapWS -> new WorkdaySoapAPIProducer(this);
            default -> throw new UnsupportedOperationException(
                    String.format("Workday producer %s is not implemented", endpointConfiguration.getEntity()));
        };
    }

    protected void validateConfigurationParameters() {
        ObjectHelper.notNull(endpointConfiguration.getConnectionConfig(),
                IWorkdayEndpointConfiguration.CONNECTION_CONFIG_PARAM);
    }

    public Consumer createConsumer(Processor processor) throws Exception {
        throw new UnsupportedOperationException("Workday consumer is not implemented.");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public void setEntity(IWorkdayEndpointConfiguration.Entity entity) {
        getEndpointConfiguration().setEntity(entity);
    }

    public void setAuthenticator(IWorkdayAuthenticator authTokenRetriever) {
        getEndpointConfiguration().setAuthenticator(authTokenRetriever);
    }

    public void setRecordType(IWorkdayRecordType recordType) {
        getEndpointConfiguration().setRecordType(recordType);
    }

    public void setReturnTypeClass(String returnTypeClass) {
        getEndpointConfiguration().setReturnTypeClass(returnTypeClass);
    }

    public void setHttpMethod(String httpMethod) {
        getEndpointConfiguration().setHttpMethod(httpMethod);
    }

    public void setSoapHeaders(List<JAXBElement<?>> soapHeaders) {
        getEndpointConfiguration().setSoapHeaders(soapHeaders);
    }

    public void setConnectionConfig(IWorkdayConnectionConfiguration connectionConfig) {
        getEndpointConfiguration().setConnectionConfig(connectionConfig);
    }

    @Override
    public Collection<HealthCheck.Result> healthCheck(Exchange exchange) {
        IWorkdayConnectionConfiguration connectionConfig = getEndpointConfiguration().getConnectionConfig();
        IntegrationRequestContext requestContext = context.getBean(IntegrationRequestContext.class);
        HealthCheck authHealthCheck = getEndpointConfiguration().getAuthenticator()
                .getHealthCheck(connectionConfig, requestContext);
        DefaultEndpointHostAvailabilityHealthCheck hostAvailabilityHealthCheck = new DefaultEndpointHostAvailabilityHealthCheck(
                WORKDAY_SYSTEM_ID, WORKDAY_SYSTEM_ID + ":hostAvailability", requestContext, connectionConfig.getHost());
        return List.of(authHealthCheck.call(null), hostAvailabilityHealthCheck.call(null));
    }
}
