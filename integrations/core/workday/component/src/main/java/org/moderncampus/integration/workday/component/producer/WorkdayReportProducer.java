package org.moderncampus.integration.workday.component.producer;

import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;
import static org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration.RECORD_TYPE_PARAM;

import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.converter.jaxb.JaxbConstants;
import org.apache.camel.support.ExchangeHelper;
import org.apache.camel.util.ObjectHelper;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.moderncampus.integration.workday.component.IWorkdayEndpointConfiguration;
import org.moderncampus.integration.workday.component.IWorkdayReportConfiguration;
import org.moderncampus.integration.workday.component.WorkdayEndpoint;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;
import org.springframework.util.ObjectUtils;

public class WorkdayReportProducer extends BaseWorkdayProducer {

    static final String WORKDAY_RAAS_URL_TEMPLATE = "https://%s/ccx/service/customreport2/%s";

    static final String REST_COMPONENT_URI_TEMPLATE = "%s:%s";

    static final String REST_COMPONENT = "rest";

    static final String BINDING_MODE = "bindingMode";

    static final String OUT_TYPE = "outType";

    static final String PRODUCER_COMPONENT_NAME = "producerComponentName";

    static final String RAAS_HTTP = "raas-http";

    static final String RESPONSE_TIMEOUT = "responseTimeout";

    static final String RESPONSE_TIMEOUT_MS = "3300000";//55 minutes

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TRACE_POST_INVOKE = "Post-RAAS call invoke";

    public WorkdayReportProducer(WorkdayEndpoint endpoint) {
        super(endpoint);
    }

    @Override
    protected void validateEndpoint(Exchange exchange, WorkdayEndpoint endpoint) {
        ObjectHelper.notNull(endpoint.getEndpointConfiguration().getRecordType(), RECORD_TYPE_PARAM);
    }

    @Override
    protected void processRequest(Exchange exchange, String authToken, WorkdayEndpoint endpoint) throws Exception {
        ProducerTemplate producerTemplate = endpoint.getProducerTemplate();
        IWorkdayEndpointConfiguration endpointConfig = endpoint.getEndpointConfiguration();
        Message message = exchange.getMessage();
        message.setHeader(AUTHORIZATION_HEADER, "Bearer" + " " + authToken);
        message.setHeader(JaxbConstants.JAXB_PART_CLASS, endpointConfig.getReturnTypeClass());
        String restCmpUri = buildRouteURI(REST_COMPONENT, createRESTURI(endpointConfig),
                "host=" + createEndpointURI(endpointConfig.getConnectionConfig()) + addRestComponentOptions(
                        endpointConfig));
        exchange = producerTemplate.send(restCmpUri, exchange);
        ExchangeHelper.prepareOutToIn(exchange);
        endpoint.getTracingService().trace(TRACE_POST_INVOKE, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
        exchange.getMessage()
                .setBody(endpointConfig.getReturnTypeClass() != null ? exchange.getMessage()
                        .getBody(Class.forName(endpointConfig.getReturnTypeClass()))
                        : exchange.getMessage().getBody(String.class));
    }

    private String createRESTURI(IWorkdayEndpointConfiguration endpointConfig) {
        String path =
                endpointConfig.getConnectionConfig().getReportUser() + "/" + getReportName(endpointConfig);
        return String.format(REST_COMPONENT_URI_TEMPLATE,
                endpointConfig.getHttpMethod() == null ? "get" : endpointConfig.getHttpMethod(), path);
    }

    private String getReportName(IWorkdayEndpointConfiguration endpointConfig) {
        IWorkdayRecordType recordType = endpointConfig.getRecordType();
        return Optional.ofNullable(
                        endpointConfig.getConnectionConfig()
                                .getReport().get(makeConfigLookupKey(recordType)))
                .map((IWorkdayReportConfiguration::getName))
                .filter(reportName -> !ObjectUtils.isEmpty(reportName))
                .orElse(recordType.getEndpointOperation());
    }

    private String makeConfigLookupKey(IWorkdayRecordType recordType) {
        return recordType.name().toLowerCase().replaceAll("_", "");
    }

    private String createEndpointURI(IWorkdayConnectionConfiguration connectionConfiguration) {
        return String.format(WORKDAY_RAAS_URL_TEMPLATE, connectionConfiguration.getHost(),
                connectionConfiguration.getTenant());
    }

    private String addRestComponentOptions(IWorkdayEndpointConfiguration endpointConfig) {
        StringBuilder builder = new StringBuilder();
        if (endpointConfig.getReturnTypeClass() != null) {
            builder.append("&").append(BINDING_MODE).append("=").append("xml").append("&").append(OUT_TYPE).append("=")
                    .append(endpointConfig.getReturnTypeClass()).append("&");
        } else {
            builder.append("&").append(BINDING_MODE).append("=").append("off").append("&");
        }
        builder.append(PRODUCER_COMPONENT_NAME).append("=")
                .append(RAAS_HTTP).append("&").append(RESPONSE_TIMEOUT).append("=").append(RESPONSE_TIMEOUT_MS);
        return builder.toString();
    }
}
