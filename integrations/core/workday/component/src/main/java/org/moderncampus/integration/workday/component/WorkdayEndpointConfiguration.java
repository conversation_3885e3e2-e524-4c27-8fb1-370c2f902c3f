package org.moderncampus.integration.workday.component;

import java.util.List;

import org.moderncampus.integration.workday.component.auth.IWorkdayAuthenticator;
import org.moderncampus.integration.workday.dto.IWorkdayRecordType;

import jakarta.xml.bind.JAXBElement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WorkdayEndpointConfiguration implements IWorkdayEndpointConfiguration {

    IWorkdayConnectionConfiguration connectionConfig;

    Entity entity;

    IWorkdayAuthenticator authenticator;

    IWorkdayRecordType recordType;

    String returnTypeClass;

    String httpMethod;

    List<JAXBElement<?>> soapHeaders;
}
