package org.moderncampus.integration.workday.component;

import java.util.Map;

import org.moderncampus.integration.workday.component.auth.IWorkdayAuthConfiguration;

public interface IWorkdayConnectionConfiguration {

    String getHost();

    String getTenant();

    IWorkdayAuthConfiguration getAuth();

    String getVersion();

    String getReportUser();

    Map<String, ? extends IWorkdayReportConfiguration> getReport();
}
