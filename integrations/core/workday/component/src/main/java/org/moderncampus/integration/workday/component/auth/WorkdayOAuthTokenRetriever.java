package org.moderncampus.integration.workday.component.auth;

import static org.apache.camel.Exchange.HTTP_METHOD;
import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WorkdayOAuthTokenRetriever {

    public static final String BASE_TOKEN_PATH = "%s/ccx/oauth2/%s/token";

    private static final String GRANT_TYPE = "grant_type";

    private static final String REFRESH_TOKEN = "refresh_token";

    private static final String ACCESS_TOKEN = "access_token";

    private static final String CONTENT_TYPE_HEADER = "Content-Type";

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String CONTENT_TYPE = "application/x-www-form-urlencoded";

    CamelContext camelContext;

    ProducerTemplate producerTemplate;

    ObjectMapper mapper;

    @Cacheable(value = "authTokenCache", key = "#root.args[0].host + '-' + #root.args[0].tenant + '-' + #root.args[0].auth.clientId + '-' + #root.args[0].auth.clientSecret + '-' + #root.args[0].auth.refreshToken")
    public String retrieveOAuthToken(IWorkdayConnectionConfiguration endpointConfiguration) throws Exception {
        return retrieveNewOAuthToken(endpointConfiguration);
    }

    public String retrieveNewOAuthToken(IWorkdayConnectionConfiguration endpointConfiguration) throws Exception {
        IWorkdayAuthConfiguration authConfiguration = endpointConfiguration.getAuth();
        String tokenPath = String.format(BASE_TOKEN_PATH, endpointConfiguration.getHost(),
                endpointConfiguration.getTenant());
        String routeURI = buildRouteURI("https", tokenPath, null);
        Endpoint endpoint = camelContext.getEndpoint(routeURI);
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        List<NameValuePair> nvps = new ArrayList<>();
        nvps.add(new BasicNameValuePair(GRANT_TYPE, REFRESH_TOKEN));//Currently only supports refresh token
        nvps.add(new BasicNameValuePair(REFRESH_TOKEN, authConfiguration.getRefreshToken()));
        exchange.getMessage().setHeader(CONTENT_TYPE_HEADER, CONTENT_TYPE);
        exchange.getMessage().setHeader(HTTP_METHOD, "POST");
        exchange.getMessage().setHeader(AUTHORIZATION_HEADER,
                "Basic " + new String(
                        Base64.getEncoder()
                                .encode((authConfiguration.getClientId() + ":"
                                        + authConfiguration.getClientSecret())
                                        .getBytes())));
        exchange.getMessage().setBody(new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8));
        exchange = producerTemplate.send(routeURI, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();//Unhandled exceptions are thrown by default
        }
        return parseResponse(exchange.getMessage().getBody(String.class));
    }

    private String parseResponse(String response) {
        try {
            JsonNode jsonNode = mapper.readTree(response);
            return jsonNode.get(ACCESS_TOKEN).asText();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
