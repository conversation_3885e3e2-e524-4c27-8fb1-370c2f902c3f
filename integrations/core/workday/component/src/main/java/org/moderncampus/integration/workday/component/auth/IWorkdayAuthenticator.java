package org.moderncampus.integration.workday.component.auth;

import org.apache.camel.health.HealthCheck;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.workday.component.IWorkdayConnectionConfiguration;

public interface IWorkdayAuthenticator {

    String getAuthToken(IWorkdayConnectionConfiguration endpointConfiguration) throws Exception;

    String getAuthToken(IWorkdayConnectionConfiguration endpointConfiguration, boolean forceRefresh) throws Exception;

    HealthCheck getHealthCheck(IWorkdayConnectionConfiguration endpointConfiguration,
            IntegrationRequestContext requestContext);
}
