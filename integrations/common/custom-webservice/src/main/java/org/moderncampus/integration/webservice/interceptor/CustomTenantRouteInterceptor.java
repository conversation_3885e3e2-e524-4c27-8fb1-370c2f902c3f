package org.moderncampus.integration.webservice.interceptor;

import static org.springframework.web.servlet.HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomTenantRouteInterceptor implements HandlerInterceptor {

    IIntegrationRequestContext requestContext;

    @Override
    @SuppressWarnings("unchecked")
    public boolean preHandle(HttpServletRequest hsr, HttpServletResponse hsr1, Object handler)
            throws Exception {

        URI uri = new URI(hsr.getRequestURL().toString());
        List<String> pathSegments = List.of(uri.getPath().split("/"));

        Optional.ofNullable(hsr.getAttribute(URI_TEMPLATE_VARIABLES_ATTRIBUTE))
                .map(Map.class::cast)
                .ifPresent(pathParameters -> {
                    if (pathParameters.containsKey(Constants.SCHOOL_ID)) {
                        setRequestContextFields(pathParameters, pathSegments);
                    } else {
                        setRequestContextFields(pathSegments);
                    }
                });
        return true;
    }

    private void setRequestContextFields(List<String> pathSegments) {
        if (pathSegments != null && pathSegments.size() >= 6) {
            String version = pathSegments.get(2);
            String sourceSystemId = pathSegments.get(3);
            String schoolId = pathSegments.get(4);
            String destinationSystemId = pathSegments.get(5);

            setupContext(sourceSystemId, schoolId, destinationSystemId, version);
        }
    }

    private void setRequestContextFields(Map<String, String> pathParameters,
            List<String> pathSegments) {
        String schoolId = pathParameters.get(Constants.SCHOOL_ID);
        String sourceSystemId = pathParameters.getOrDefault(org.moderncampus.integration.Constants.SOURCE_SYSTEM_ID,
                getSourceSystemIdFromSegments(pathSegments, schoolId));
        String destinationSystemId = pathParameters.getOrDefault(org.moderncampus.integration.Constants.DEST_SYSTEM_ID,
                getSISIdFromSegments(pathSegments, schoolId));
        String version = pathParameters.getOrDefault(Constants.VERSION,
                getVersionFromSegments(pathSegments, schoolId));

        setupContext(sourceSystemId, schoolId, destinationSystemId, version);
    }

    private void setupContext(String sourceSystemId, String schoolId, String destinationSystemId, String version) {
        requestContext.setSchool(schoolId);
        requestContext.setSourceSystem(sourceSystemId);
        requestContext.setDestSystem(destinationSystemId);
        requestContext.setVersion(version);
    }

    private String getSourceSystemIdFromSegments(List<String> pathSegments, String schoolId) {
        int schoolIdIdx = pathSegments.indexOf(schoolId);
        if (schoolIdIdx > -1 && schoolIdIdx - 1 > -1) {
            return pathSegments.get(schoolIdIdx - 1);
        }
        return null;
    }

    private String getVersionFromSegments(List<String> pathSegments, String schoolId) {
        int schoolIdIdx = pathSegments.indexOf(schoolId);
        if (schoolIdIdx > -1 && schoolIdIdx - 2 > -1) {
            return pathSegments.get(schoolIdIdx - 2);
        }
        return null;
    }

    private String getSISIdFromSegments(List<String> pathSegments, String schoolId) {
        int schoolIdIdx = pathSegments.indexOf(schoolId);
        if (schoolIdIdx > -1) {
            return pathSegments.get(schoolIdIdx + 1);
        }
        return null;
    }
}
