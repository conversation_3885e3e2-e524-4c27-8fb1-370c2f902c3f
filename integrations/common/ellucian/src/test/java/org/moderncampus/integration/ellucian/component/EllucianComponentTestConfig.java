package org.moderncampus.integration.ellucian.component;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Map;

import org.apache.camel.builder.RouteBuilder;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

@SpringBootApplication
@ComponentScan(lazyInit = true, basePackages = {"org.moderncampus.integration.*"})
public class EllucianComponentTestConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES);
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
//        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    @Bean
    public RouteBuilder testRoute() {
        return new RouteBuilder() {

            @Override
            public void configure() throws Exception {
                from("direct:ellucian-ep-test").setProperty("id", simple("49aef0f9-f017-4cf8-b2c0-d260da026d72"))
                        .toD(buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                                EllucianCloudEndpointType.COLLEAGUE_ETHOS_API + ":" + ColleagueEthosAPIResource.COURSES
                                        + ":"
                                        + "/", routeQueryParamStr(
                                        Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                                parameterBeanRef(ColleagueCloudConnectionConfiguration.class),
                                                Constants.RESOURCE_ID_PARAM,
                                                "${exchangeProperty.id}"))));
            }
        };
    }
}
