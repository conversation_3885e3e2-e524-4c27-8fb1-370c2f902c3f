package org.moderncampus.integration.ellucian.component;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.test.spring.junit5.CamelSpringBootTest;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

@CamelSpringBootTest
@EnableAutoConfiguration
@SpringBootTest(classes = {EllucianComponentTestConfig.class})
@SpringJUnitWebConfig
public class EllucianComponentTest {

    @Autowired
    ProducerTemplate producerTemplate;

    @Autowired
    CamelContext context;

    @Test
    public void testEllucianCloudEP() throws InterruptedException {
        String routeUri = RouteSupport.buildDirectRouteURI("ellucian-ep-test");
        Endpoint endpoint = context.getEndpoint(routeUri);
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        Exchange response = producerTemplate.send(routeUri, exchange);
        System.out.println(response.getMessage().getBody(String.class));
    }
}
