package org.moderncampus.integration.ellucian.component.auth;

import static org.apache.camel.Exchange.HTTP_METHOD;
import static org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION;
import static org.moderncampus.integration.constants.Constants.HTTP;
import static org.moderncampus.integration.constants.Constants.HTTPS;
import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianCloudAuthTokenRetriever {

    public static final String BASE_TOKEN_PATH = "%s/auth";

    CamelContext camelContext;

    ProducerTemplate producerTemplate;

    @Cacheable(value = "ellucianCloudAuthTokenCache", key = "#root.args[0].host + '-' + #root.args[0].auth.authToken")
    public String retrieveAuthToken(IEllucianCloudConnectionConfiguration configuration) throws Exception {
        return retrieveNewAuthToken(configuration);
    }

    public String retrieveNewAuthToken(IEllucianCloudConnectionConfiguration configuration) throws Exception {
        EllucianCloudAuthConfiguration authConfiguration = configuration.getAuth();
        String tokenPath = String.format(BASE_TOKEN_PATH, configuration.getHost());
        String routeURI = buildRouteURI(authConfiguration.isUseHttp() ? HTTP : HTTPS, tokenPath, null);
        Endpoint endpoint = camelContext.getEndpoint(routeURI);
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        exchange.getMessage().setHeader(HTTP_METHOD, "POST");
        exchange.getMessage().setHeader(AUTHORIZATION,
                "Bearer " + authConfiguration.getAuthToken());
        exchange = producerTemplate.send(routeURI, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();//Unhandled exceptions are thrown by default
        }
        return exchange.getMessage().getBody(String.class);
    }

}
