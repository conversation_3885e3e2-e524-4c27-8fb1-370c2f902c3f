package org.moderncampus.integration.ellucian.component.producer;

import static org.apache.camel.component.http.HttpConstants.HTTP_METHOD;
import static org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION;
import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;
import static org.moderncampus.integration.constants.Constants.PROXY_REQUEST;
import static org.moderncampus.integration.constants.Constants.QUERY_PARAMS;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.component.http.HttpMethods;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.apache.camel.support.DefaultProducer;
import org.apache.camel.support.ExchangeHelper;
import org.apache.hc.core5.http.ContentType;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpoint;
import org.moderncampus.integration.ellucian.component.endpoint.config.EllucianCloudEndpointConfiguration;
import org.moderncampus.integration.ellucian.component.helper.EllucianCloudAPIHeaderFilterStrategy;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;

public class BaseEllucianCloudAPIProducer extends DefaultProducer {

    static final String ACCEPT = "Accept";
    static final String APPLICATION_JSON = ContentType.APPLICATION_JSON.toString();
    static final String THROW_EXCEPTION_ON_FAILURE = "throwExceptionOnFailure";
    static final String COPY_HEADERS = "copyHeaders";

    public BaseEllucianCloudAPIProducer(Endpoint endpoint) {
        super(endpoint);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        EllucianCloudEndpoint ellucianCloudEndpoint = (EllucianCloudEndpoint) getEndpoint();
        ProducerTemplate template = ellucianCloudEndpoint.getProducerTemplate();
        EllucianCloudEndpointConfiguration endpointConfiguration = ellucianCloudEndpoint.getEndpointConfiguration();
        IEllucianCloudConnectionConfiguration connectionConfiguration = endpointConfiguration.getConnectionConfig();
        String authToken = null;
        if (endpointConfiguration.getAuthenticator() != null) {
            authToken = endpointConfiguration.getAuthenticator().getAuthToken(connectionConfiguration);
        }
        try {
            invokeEndpoint(exchange, endpointConfiguration, connectionConfiguration, template, authToken,
                    ellucianCloudEndpoint);
        } catch (Exception e) {
            if (e instanceof HttpOperationFailedException httpOperationFailedException &&
                    httpOperationFailedException.getHttpResponseCode() == 401) {//refresh stale session
                authToken = endpointConfiguration.getAuthenticator()
                        .getAuthToken(connectionConfiguration, true);
                exchange.setException(null);
                invokeEndpoint(exchange, endpointConfiguration, connectionConfiguration, template, authToken,
                        ellucianCloudEndpoint);
                return;
            }
            throw e;
        }
    }

    private void invokeEndpoint(Exchange exchange, EllucianCloudEndpointConfiguration endpointConfiguration,
            IEllucianCloudConnectionConfiguration connectionConfiguration, ProducerTemplate template, String authToken,
            EllucianCloudEndpoint ellucianCloudEndpoint) throws Exception {
        String modelClassStr = endpointConfiguration.getModelClass();
        Class<?> modelClass = modelClassStr != null ? Class.forName(endpointConfiguration.getModelClass()) : null;
        IEllucianCloudAPIResource resource = endpointConfiguration.getApiResource();
        Optional<String> httpMethod = Optional.ofNullable(endpointConfiguration.getHttpMethod());
        boolean isProxyRequest = exchange.getProperty(PROXY_REQUEST) != null;
        Message message = exchange.getMessage();
        populateAcceptHeader(message, resource, isProxyRequest);
        populateConceptTypeHeader(exchange, message, resource, isProxyRequest);
        if (resource.sendBodyAsBytes() || (isProxyRequest && exchange.getMessage().getBody() != null)) {
            exchange.getMessage().setBody(exchange.getMessage().getBody(String.class).getBytes(
                    ContentType.APPLICATION_JSON.getCharset()));
        }
        httpMethod.ifPresent(s -> message.setHeader(HTTP_METHOD, HttpMethods.valueOf(s)));
        String host = connectionConfiguration.getHost();
        String resourceId = endpointConfiguration.getResourceId();
        message.setHeader(AUTHORIZATION, "Bearer " + authToken);
        String isProxyBooleanStr = isProxyRequest ? Boolean.FALSE.toString() : Boolean.TRUE.toString();
        exchange = template.send(buildRouteURI(connectionConfiguration.isUseHttp() ? Constants.HTTP : Constants.HTTPS,
                        appendResourceToBasePath(host, resource, resourceId), routeQueryParamStr(
                                Map.of(Constants.HEADER_FILTER_STRATEGY, beanRef(
                                                EllucianCloudAPIHeaderFilterStrategy.class), THROW_EXCEPTION_ON_FAILURE,
                                        isProxyBooleanStr, COPY_HEADERS, isProxyBooleanStr)) + Optional.ofNullable
                                        (message.getHeader(QUERY_PARAMS))
                                .map(value -> "&" + value).orElse("")),
                exchange);
        if (modelClass != null) {
            String response = exchange.getMessage().getBody(String.class);
            if (String.class.equals(modelClass)) {
                exchange.getMessage().setBody(response);
            } else {
                exchange.getMessage().setBody(ellucianCloudEndpoint.getMapper().readValue(response, modelClass));
            }
        }
        ExchangeHelper.prepareOutToIn(exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();
        }
    }

    private void populateConceptTypeHeader(Exchange exchange, Message message, IEllucianCloudAPIResource resource,
            boolean isProxyRequest) {
        if (exchange.getMessage().getBody() != null) {
            if (message.getHeaders().containsKey(CONTENT_TYPE) && isProxyRequest) {
                return;
            }
            setContentTypeHeaders(message, resource);
        }
    }

    private void populateAcceptHeader(Message message, IEllucianCloudAPIResource resource, boolean isProxyRequest) {
        if (message.getHeaders().containsKey(ACCEPT) && isProxyRequest) {
            return;
        }
        message.setHeader(ACCEPT,
                Optional.ofNullable(resource.getAcceptHeaderVersion())
                        .orElse(ContentType.APPLICATION_JSON.getMimeType()));
    }

    private String appendResourceToBasePath(String baseUrl, IEllucianCloudAPIResource resource, String resourceId) {
        return baseUrl + resource.getPathSegment() + Optional.ofNullable(resourceId).map(id -> "/" + resourceId)
                .orElse("");
    }

    private void setContentTypeHeaders(Message message, IEllucianCloudAPIResource resource) {
        String contentType = Stream.of(
                        resource.getContentTypeHeaderVersion(),
                        resource.getAcceptHeaderVersion(),
                        APPLICATION_JSON
                )
                .filter(value -> value != null && !value.isBlank())
                .findFirst()
                .orElse(APPLICATION_JSON);

        message.setHeader(CONTENT_TYPE, contentType);

        if (!APPLICATION_JSON.equals(contentType) && (resource.getContentTypeHeaderVersion() != null
                || resource.getAcceptHeaderVersion() != null) && !resource.sendBodyAsBytes()) {
            message.setHeader(Exchange.CHARSET_NAME, ContentType.APPLICATION_JSON.getCharset().name());
        }
    }

}
