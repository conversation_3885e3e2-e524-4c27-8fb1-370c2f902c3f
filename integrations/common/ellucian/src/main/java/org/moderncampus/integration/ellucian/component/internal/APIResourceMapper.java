package org.moderncampus.integration.ellucian.component.internal;

import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;

public class APIResourceMapper {

    public static IEllucianCloudAPIResource mapAPIResource(EllucianCloudEndpointType endpointType, String resource) {
        return switch (endpointType) {
            case COLLEAGUE_ETHOS_API -> ColleagueEthosAPIResource.valueOf(resource);
            case BANNER_ETHOS_API -> BannerEthosAPIResource.valueOf(resource);
            case BANNER_BP_API -> BannerBPAPIResource.valueOf(resource);
            case null, default -> null;
        };
    }
}
