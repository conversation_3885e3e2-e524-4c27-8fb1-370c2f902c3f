package org.moderncampus.integration.ellucian.component.internal;

import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum BannerBPAPIResource implements IEllucianCloudAPIResource {

    SECTION_CROSS_LIST_GROUP("schedule-cross-list-definition", "application/vnd.hedtech.integration.v1.0.0+json"),
    COURSE_SECTION_INFORMATION("course-section-information", "application/vnd.hedtech.integration.v1.0.0+json"),
    SECTION_CROSS_LIST_QUERY("schedule-cross-list-query");

    BannerBPAPIResource(String value) {
        this(value, null);
    }

    BannerBPAPIResource(String value, String acceptHeaderVersion) {
        this.value = value;
        this.acceptHeaderVersion = acceptHeaderVersion;
        this.contentTypeHeaderVersion = null;
    }

    String value;
    String acceptHeaderVersion;
    String contentTypeHeaderVersion;

    @Override
    public String getPathSegment() {
        return "/api/" + getValue();
    }

    @Override
    public EllucianCloudEndpointType endpointType() {
        return EllucianCloudEndpointType.BANNER_BP_API;
    }

    @Override
    public boolean sendBodyAsBytes() {
        return false;
    }
}
