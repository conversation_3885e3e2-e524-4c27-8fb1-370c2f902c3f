package org.moderncampus.integration.ellucian.component.internal;

import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum ColleagueEthosAPIResource implements IEllucianCloudAPIResource {

    ADDRESSES("addresses", false, "application/vnd.hedtech.integration.v11+json"),
    COURSES("courses", false, "application/vnd.hedtech.integration.v16+json"),
    COURSE_TITLE_TYPES("course-title-types", false),
    CREDIT_CATEGORIES("credit-categories", false),
    ADMINISTRATIVE_INSTRUCTIONAL_METHODS("administrative-instructional-methods", false),
    SECTIONS("sections", false, "application/vnd.hedtech.integration.v16+json"),
    SECTION_TITLE_TYPES("section-title-types", false),
    SECTION_DESCRIPTION_TYPES("section-description-types", false),
    SECTION_SCHEDULES("instructional-events", false, "application/vnd.hedtech.integration.v11+json"),
    SECTION_INSTRUCTOR("section-instructors", false, "application/vnd.hedtech.integration.v10+json"),
    SECTION_CROSS_LIST("section-crosslists", false, "application/vnd.hedtech.integration.v6+json"),
    PERSON_EMERGENCY_CONTACT("person-emergency-contacts", false, "application/vnd.hedtech.integration.v1+json"),
    EMERGENCY_CONTACT_PHONE_AVAILABILITIES("emergency-contact-phone-availabilities", false, "application/vnd.hedtech.integration.v1+json"),
    EMERGENCY_CONTACT_TYPES("emergency-contact-types", false, "application/vnd.hedtech.integration.v1+json"),
    PERSON("persons", false, "application/vnd.hedtech.integration.v12.6.0+json"),
    CHECK_DUPLICATE_PERSON("persons", true, "application/vnd.hedtech.integration.v12+json"),
    PHONE_TYPES("phone-types", false, "application/vnd.hedtech.integration.v6+json"),
    ADDRESS_TYPES("address-types", false, "application/vnd.hedtech.integration.v6+json"),
    EMAIL_TYPES("email-types", false, "application/vnd.hedtech.integration.v6+json"),
    PERSON_NAME_TYPES("person-name-types", false, "application/vnd.hedtech.integration.v12+json"),
    STUDENTS("students", false, "application/vnd.hedtech.integration.v16+json"),
    STUDENT_CHARGE("student-charges", false, "application/vnd.hedtech.integration.v16+json"),
    STUDENT_PAYMENTS("student-payments", false, "application/vnd.hedtech.integration.v16+json"),
    SECTION_REGISTRATION("section-registrations", false, "application/vnd.hedtech.integration.v16+json"),
    SECTION_REGISTRATION_STATUSES("section-registration-statuses", false, "application/vnd.hedtech.integration.v8+json"),
    CITIZENSHIP_STATUSES("citizenship-statuses",false,"application/vnd.hedtech.integration.v6+json"),
    ACADEMIC_LEVELS("academic-levels", false, "application/vnd.hedtech.integration.v6+json"),
    SUBJECTS("subjects", false, "application/vnd.hedtech.integration.v6+json"),
    ROOMS("rooms", false, "application/vnd.hedtech.integration.v10+json"),
    LOCATIONS("sites", false, "application/vnd.hedtech.integration.v6+json"),
    FINAL_GRADES("student-unverified-grades", false, "application/vnd.hedtech.integration.v1+json",
            "application/vnd.hedtech.integration.student-unverified-grades-submissions.v1.0.0+json", true),
    SECTION_GRADE_TYPES("section-grade-types", false, "application/vnd.hedtech.integration.v6+json"),
    INSTRUCTIONAL_METHODS("instructional-methods", false, "application/vnd.hedtech.integration.v6+json"),
    INSTRUCTORS("instructors", false, "application/vnd.hedtech.integration.v9.0.0+json"),
    ORGANIZATIONS("organizations", false, "application/vnd.hedtech.integration.v6+json");

    ColleagueEthosAPIResource(String value, boolean isQAPI) {
        this(value, isQAPI, null);
    }

    ColleagueEthosAPIResource(String value, boolean isQAPI, String requestVersion) {
        this(value, isQAPI, requestVersion, requestVersion, false);
    }

    ColleagueEthosAPIResource(String value, boolean isQAPI, String acceptHeaderVersion, String contentTypeHeaderVersion,
            boolean sendBodyAsBytes) {
        this.value = value;
        this.isQAPI = isQAPI;
        this.acceptHeaderVersion = acceptHeaderVersion;
        this.contentTypeHeaderVersion = contentTypeHeaderVersion;
        this.sendBodyAsBytes = sendBodyAsBytes;
    }

    String value;

    String acceptHeaderVersion;

    String contentTypeHeaderVersion;

    boolean isQAPI;

    boolean sendBodyAsBytes;

    @Override
    public String getPathSegment() {
        return isQAPI() ? "/qapi/" + getValue() : "/api/" + getValue();
    }

    @Override
    public EllucianCloudEndpointType endpointType() {
        return EllucianCloudEndpointType.COLLEAGUE_ETHOS_API;
    }

    @Override
    public boolean sendBodyAsBytes() {
        return sendBodyAsBytes;
    }
}
