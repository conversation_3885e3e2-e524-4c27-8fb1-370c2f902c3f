package org.moderncampus.integration.ellucian.component.endpoint;

import static org.moderncampus.integration.ellucian.component.internal.APIResourceMapper.mapAPIResource;
import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import java.net.MalformedURLException;
import java.util.Map;

import org.apache.camel.Consumer;
import org.apache.camel.Processor;
import org.apache.camel.Producer;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.support.DefaultEndpoint;
import org.moderncampus.integration.ellucian.component.EllucianCloudComponent;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.auth.EllucianCloudAuthenticator;
import org.moderncampus.integration.ellucian.component.endpoint.config.EllucianCloudEndpointConfiguration;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.component.producer.BaseEllucianCloudAPIProducer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Getter;

@Getter
@Component
@Lazy(value = false)
public class EllucianCloudEndpoint extends DefaultEndpoint implements ApplicationContextAware {

    private static ApplicationContext context;

    EllucianCloudEndpointConfiguration endpointConfiguration = new EllucianCloudEndpointConfiguration();
    ProducerTemplate producerTemplate;
    ObjectMapper mapper;


    public EllucianCloudEndpoint() {
        super();
    }

    public EllucianCloudEndpoint(String uri, EllucianCloudComponent component) {
        super(uri, component);
    }

    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String endpointType;
        String resourceName;
        String[] uriParts = remaining.split(":");
        if (uriParts.length != 3) {
            throw new MalformedURLException(
                    String.format("An invalid ellucian cloud remaining uri: '%s' was provided.",
                            remaining));
        }
        setResourceName(mapAPIResource(EllucianCloudEndpointType.valueOf(uriParts[0]), uriParts[1]));
        producerTemplate = context.getBean(beanRef(ProducerTemplate.class), ProducerTemplate.class);
        mapper = context.getBean(beanRef(ObjectMapper.class), ObjectMapper.class);
        getEndpointConfiguration().setAuthenticator(
                context.getBean(beanRef(EllucianCloudAuthenticator.class), EllucianCloudAuthenticator.class));
    }

    void setResourceName(IEllucianCloudAPIResource resourceName) {
        getEndpointConfiguration().setApiResource(resourceName);
    }

    @Override
    public Producer createProducer() throws Exception {
        EllucianCloudEndpointType endpointType = getEndpointConfiguration().getApiResource().endpointType();
        return switch (endpointType) {
            case COLLEAGUE_ETHOS_API, BANNER_ETHOS_API, BANNER_BP_API -> new BaseEllucianCloudAPIProducer(this);
            default -> throw new UnsupportedOperationException(
                    String.format("Ellucian Cloud producer %s is not implemented", endpointType));
        };
    }

    @Override
    public Consumer createConsumer(Processor processor) throws Exception {
        throw new UnsupportedOperationException("Ellucian Cloud Consumer is not implemented.");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public void setConnectionConfig(IEllucianCloudConnectionConfiguration connectionConfig) {
        getEndpointConfiguration().setConnectionConfig(connectionConfig);
    }

    public void setResourceId(String resourceId) {
        getEndpointConfiguration().setResourceId(resourceId);
    }

    public void setHttpMethod(String httpMethod) {
        getEndpointConfiguration().setHttpMethod(httpMethod);
    }

    public void setModelClass(String modelClass) {
        getEndpointConfiguration().setModelClass(modelClass);
    }
}
