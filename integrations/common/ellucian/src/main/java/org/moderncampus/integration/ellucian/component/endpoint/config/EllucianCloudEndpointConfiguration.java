package org.moderncampus.integration.ellucian.component.endpoint.config;

import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.auth.EllucianCloudAuthenticator;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EllucianCloudEndpointConfiguration {

    IEllucianCloudAPIResource apiResource;

    IEllucianCloudConnectionConfiguration connectionConfig;

    EllucianCloudAuthenticator authenticator;

    String resourceId;

    String httpMethod;

    String modelClass;
}
