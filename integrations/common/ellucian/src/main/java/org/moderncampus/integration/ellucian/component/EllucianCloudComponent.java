package org.moderncampus.integration.ellucian.component;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;

import java.util.Map;

import org.apache.camel.Endpoint;
import org.apache.camel.spi.annotations.Component;
import org.apache.camel.support.DefaultComponent;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpoint;

@Component(ELLUCIAN_CLOUD_COMPONENT_SCHEME)
public class EllucianCloudComponent extends DefaultComponent {

    @Override
    protected Endpoint createEndpoint(String uri, String remaining, Map<String, Object> parameters) throws Exception {
        EllucianCloudEndpoint endpoint = new EllucianCloudEndpoint(uri, this);
        endpoint.parseURI(remaining, parameters);
        setProperties(endpoint, parameters);

        return endpoint;
    }
}
