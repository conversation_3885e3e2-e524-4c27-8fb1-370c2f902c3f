package org.moderncampus.integration.ellucian.workflow.route.helper;

import java.util.List;

import org.apache.camel.Exchange;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;

public interface IEllucianAssociationResolver {

    static final String METHOD_RESOLVE_ASSOCIATIONS = "resolveAssociations";

    void resolveAssociations(List<IEllucianCloudAPIResource> APIResources, Exchange exchange) throws Exception;
}
