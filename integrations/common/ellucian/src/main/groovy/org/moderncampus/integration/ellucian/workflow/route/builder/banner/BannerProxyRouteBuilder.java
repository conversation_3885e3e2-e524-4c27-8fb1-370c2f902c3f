package org.moderncampus.integration.ellucian.workflow.route.builder.banner;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.moderncampus.integration.ellucian.component.BannerCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.internal.BannerBPAPIResource;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudProxyRouteBuilder;

public class BannerProxyRouteBuilder extends EllucianCloudProxyRouteBuilder {

    static final Map<String, IEllucianCloudAPIResource> bannerAPIResourceMap = Stream.concat(Arrays.stream(
            BannerEthosAPIResource.values()), Arrays.stream(BannerBPAPIResource.values())).collect(Collectors.toMap(
            IEllucianCloudAPIResource::getPathSegment, Function.identity()));

    public BannerProxyRouteBuilder(String id) {
        super(id);
    }

    @Override
    protected Map<String, IEllucianCloudAPIResource> getAllowedAPIResourceMap() {
        return bannerAPIResourceMap;
    }

    @Override
    protected Class<?> getConnectionConfigClass() {
        return BannerCloudConnectionConfiguration.class;
    }
}
