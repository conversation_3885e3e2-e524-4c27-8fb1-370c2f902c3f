package org.moderncampus.integration.ellucian.workflow.transform.ethos

import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.base.BaseDTO
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EllucianCommonWriteTransform {

    void mapId(Map<String, ?> requestRoot) {
        requestRoot.put("id", EllucianConstants.NIL_GUID)
    }

    void mapId(Map<String, ?> requestRoot, String id) {
        requestRoot.put("id", id)
    }

    void mapEthosExtensions(Map<String, Object> requestRoot, BaseDTO baseDTO) {
        def props = baseDTO?.dynamicProperties
        if (!props) return
        def extensions = props['extensions'] as Map
        requestRoot.putAll(extensions)
    }

    Map<String, Map> resourceMapFromContext(TransformContext ctx, ColleagueEthosAPIResource resource) {
        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        return ethosAssocCacheMap[resource] as Map<String, Map>
    }

    void setNestedValue(Map map, Object value, String... path) {
        if (!path) return

        def current = map
        def lastIndex = path.length - 1

        // Create nested structure
        for (int i = 0; i < lastIndex; i++) {
            current.putAt(path[i], current.get(path[i]) ?: [:])
            current = current.get(path[i])
        }

        // Set the final value
        current.putAt(path[lastIndex], value)
    }

}
