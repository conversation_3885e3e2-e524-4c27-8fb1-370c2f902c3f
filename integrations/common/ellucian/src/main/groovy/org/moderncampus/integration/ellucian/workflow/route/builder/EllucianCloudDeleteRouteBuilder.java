package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.Map;

import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.route.builder.DeleteToExtSystemRouteBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianCloudDeleteRouteBuilder extends DeleteToExtSystemRouteBuilder {

    EllucianCloudEndpointType endpointType;
    IEllucianCloudAPIResource apiResource;
    Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass;

    public EllucianCloudDeleteRouteBuilder(String id, EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, ObjectMapper objectMapper) {
        super(id, objectMapper);
        this.endpointType = endpointType;
        this.apiResource = apiResource;
        this.connectionConfigClass = connectionConfigClass;
    }

    @Override
    protected String getExtSystemDeleteByIdRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":" + "/", routeQueryParamStr(Map.of(
                        Constants.HTTP_METHOD, METHOD_DELETE,
                        Constants.CONNECTION_CONFIG_PARAM, parameterBeanRef(
                                connectionConfigClass),
                        Constants.RESOURCE_ID_PARAM, RESOURCE_ID_EXPR_REF)
                ));
    }
}
