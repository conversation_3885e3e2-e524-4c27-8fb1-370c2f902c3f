package org.moderncampus.integration.ellucian.workflow.route.helper;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianProxyPathMeta {

    String prefix;
    String resource;
    String resourceId;

    public EllucianProxyPathMeta(String[] segments) {
        prefix = segments[1];
        resource = segments[2];
        resourceId = segments.length == 4 ? segments[3] : null;
    }

    public String getResourcePath() {
        return "/" + prefix + "/" + resource;
    }
}
