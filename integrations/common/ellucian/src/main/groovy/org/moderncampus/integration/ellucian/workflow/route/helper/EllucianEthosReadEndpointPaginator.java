package org.moderncampus.integration.ellucian.workflow.route.helper;

import static org.moderncampus.integration.constants.Constants.PAGE_SIZE;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.ETHOS_PAGINATION_LIMIT;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.ETHOS_PAGINATION_OFFSET;
import static org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosCommonFunctions.responsePaginationMapper;
import static org.moderncampus.integration.route.support.RouteSupport.routeQueryParamStr;
import static org.moderncampus.integration.transform.support.CommonFunctions.mapJsonListToStringCollection;

import java.util.Collection;
import java.util.HashMap;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.helper.Pagination;
import org.moderncampus.integration.route.support.ExtReadEndpointPaginator;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianEthosReadEndpointPaginator extends ExtReadEndpointPaginator<String> {


    public EllucianEthosReadEndpointPaginator(ProducerTemplate template,
            CamelContext camelContext) {
        super(template, camelContext, String.class);
    }

    protected Collection<String> mapResponseToCollection(String response) {
        return mapJsonListToStringCollection(response);
    }

    @Override
    protected Pagination getInitialPaginationInst(String routeURI, Exchange exchange) {
        return new Pagination(0, null, 0, true, -1);
    }

    protected void updatePaginationParamsFromResp(Collection<String> response, Exchange exchange, Pagination pagination)
            throws Exception {
        responsePaginationMapper.process(exchange);
        pagination.setPageSize(exchange.getProperty(PAGE_SIZE, Integer.class));
        pagination.setResultsFound(response.size());
    }

    protected void mapPaginationParamsInRequest(Exchange exchange, Pagination pagination) {
        if (pagination.getPageSize() != null || pagination.getResultsFound() >= 0) {
            exchange.getMessage().setHeader(getQueryParamHeaderKey(), routeQueryParamStr(new HashMap<>() {{
                if (pagination.getPageSize() != null) {
                    put(ETHOS_PAGINATION_LIMIT, String.valueOf(pagination.getPageSize()));
                }
                if (pagination.getPageNumber() != null && pagination.getPageSize() != null) {
                    put(ETHOS_PAGINATION_OFFSET,
                            String.valueOf(pagination.getPageNumber() * pagination.getPageSize()));
                }
            }}));
        }
    }

    protected String getQueryParamHeaderKey() {
        return Constants.QUERY_PARAMS;
    }
}
