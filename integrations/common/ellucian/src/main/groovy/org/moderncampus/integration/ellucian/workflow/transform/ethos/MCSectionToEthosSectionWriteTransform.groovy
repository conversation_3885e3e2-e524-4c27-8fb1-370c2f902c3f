package org.moderncampus.integration.ellucian.workflow.transform.ethos

import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSection
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCSectionToEthosSectionWriteTransform {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    void mapDescriptions(Map<String, ?> requestRoot, MCSection section, Map<String, Map> descriptionTypeByIdMap) {
        if (isValueExist(section?.description) || isValueExist(section?.shortDescription)) {
            def descriptionsArr = []
            def descriptionTypeByCodeMap = descriptionTypeByIdMap.collectEntries {
                def code = it.value['code'] as String
                [(code.toLowerCase()): it.value]
            }
            if (isValueExist(section?.description)) {
                String longId = descriptionTypeByCodeMap['long']?['id']
                if (isValueExist(longId)) {
                    descriptionsArr.push([
                            "type" : [
                                    "id": longId
                            ],
                            "value": section.description
                    ]) as List<Map<String, ?>>
                }
            }
            if (isValueExist(section?.shortDescription)) {
                String shortId = descriptionTypeByCodeMap['short']?['id']
                if (isValueExist(shortId)) {
                    descriptionsArr.push([
                            "type" : [
                                    "id": shortId
                            ],
                            "value": section.shortDescription
                    ]) as List<Map<String, ?>>
                }
            }
            if (isValueExist(descriptionsArr)) {
                requestRoot.put("descriptions", descriptionsArr)
            }
        }
    }

    void mapTitles(Map<String, ?> requestRoot, MCSection section, Map<String, Map> sectionTitleTypeByIdMap) {
        if (isValueExist(section?.shortTitle) || isValueExist(section?.longTitle)) {
            def titlesArr = []
            def sectionTitleTypeByCodeMap = sectionTitleTypeByIdMap.collectEntries {
                def code = it.value['code'] as String
                [(code.toLowerCase()): it.value]
            }
            if (isValueExist(section?.shortTitle)) {
                String shortId = sectionTitleTypeByCodeMap['short']?['id']
                if (isValueExist(shortId)) {
                    titlesArr.push([
                            "type" : [
                                    "id": shortId
                            ],
                            "value": section.shortTitle
                    ]) as List<Map<String, ?>>
                }
            }
            if (isValueExist(section?.longTitle)) {
                String longId = sectionTitleTypeByCodeMap['long']?['id']
                if (isValueExist(longId)) {
                    titlesArr.push([
                            "type" : [
                                    "id": longId
                            ],
                            "value": section.longTitle
                    ]) as List<Map<String, ?>>
                }
            }
            requestRoot.put("titles", titlesArr)
        }
    }

    void mapStartDate(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.startOn)) {
            requestRoot.put("startOn", DateTimeFormatter.ISO_LOCAL_DATE.format(section.startOn))
        }
    }

    void mapEndDate(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.endOn)) {
            requestRoot.put("endOn", DateTimeFormatter.ISO_LOCAL_DATE.format(section.endOn))
        }
    }

    void mapCode(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.code)) {
            requestRoot.put("code", section.code)
        }
    }

    void mapNumber(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.number)) {
            requestRoot.put("number", section.number)
        }
    }

    void mapAcademicPeriod(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.academicPeriod)) {
            requestRoot.put("academicPeriod", [
                    "id": section.academicPeriod
            ])
        }
    }

    void mapCensusDates(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.censusDates)) {
            requestRoot.put("censusDates", section.censusDates.collect {
                DateTimeFormatter.ISO_LOCAL_DATE.format(it)
            })
        }
    }

    void mapCourse(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.course)) {
            requestRoot.put("course", ["id": section.course])
        }
    }

    void mapCourseCategories(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.courseCategories)) {
            requestRoot.put("courseCategories", section.courseCategories.collect {
                ["id": it]
            })
        }
    }

    void mapSite(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.site)) {
            requestRoot.put("site", ["id": section.site])
        }
    }

    void mapAcademicLevels(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.academicLevels)) {
            requestRoot.put("academicLevels", section.academicLevels.collect {
                ["id": it]
            })
        }
    }

    void mapInstructionalMethods(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.instructionalMethods)) {
            requestRoot.put("instructionalMethods", section.instructionalMethods.collect {
                ["id": it.id]
            })
        }
    }

    void mapGradeSchemes(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.gradeSchemes)) {
            requestRoot.put("gradeSchemes", section.gradeSchemes.collect {
                ["id": it]
            })
        }
    }

    void mapStatus(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.status)) {
            requestRoot.put("status", ["category": "open", "detail": ["id": section.status]])
        }
    }

    void mapDuration(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.duration) || isValueExist(section?.durationUnits)) {
            requestRoot.put("duration", ["length": section.duration, "unit": section.durationUnits])
        }
    }

    void mapEnrollmentSize(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.maxEnrollment)) {
            requestRoot.put("maxEnrollment", section.maxEnrollment)
        }
    }

    void mapInstructionalDeliveryMethod(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.instructionalDeliveryMethod)) {
            requestRoot.put("instructionalDeliveryMethod", ["id": section.instructionalDeliveryMethod])
        }
    }

    void mapBillingHours(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.billingHours)) {
            requestRoot.put("billing", section.billingHours)
        }
    }

    void mapOwningInstitutionUnits(Map<String, ?> requestRoot, MCSection section) {
        if (isValueExist(section?.owningInstitutionUnits)) {
            requestRoot.put("owningInstitutionUnits", section.owningInstitutionUnits.collect {
                def percentage = it.ownershipPercentage ?: 100
                ["institutionUnit": ["id": it.id], "ownershipPercentage": percentage]
            })
        }
    }

}
