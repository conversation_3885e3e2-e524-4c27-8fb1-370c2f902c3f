package org.moderncampus.integration.ellucian.workflow.transform.helper

import groovy.json.JsonSlurper
import org.moderncampus.integration.dto.base.BaseDTO
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.moderncampus.integration.transform.ITransformer

import static org.moderncampus.integration.constants.Constants.SOURCE_BODY_REQUEST

class EthosHelper {

    static Map<?, Map<String, Map>> expandEthosAssocResultsMap(Map<?, List<Map<String, ?>>> ethosAssocResultsMap) {
        return ethosAssocResultsMap.collectEntries { it ->
            return [it.key, groupEthosResultById(it.value)]
        }
    }

    static Map<String, Map> groupEthosResultById(List<Map<String, ?>> results) {
        def objectMap = [:] as Map<String, Map> // Create an empty map to store id -> object
        results?.each { obj ->
            objectMap[obj['id'] as String] = obj as Map// Add to map, using 'id' as the key
        }
        return objectMap
    }

    static <T extends BaseDTO> ITransformer<String, T> ethosWriteRespTransformer(Class<T> classType) {
        return (ctx, body) -> {
            def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
            Object mcDto = classType.getClassLoader().loadClass(classType.getName())?.getDeclaredConstructor()?.newInstance()
            mcDto.with {
                id = rootNode['id']
            }
            return mcDto
        }
    }

    static <T extends BaseDTO> ITransformer<String, T> ethosSectionCrossListGroupWriteRespTransformer(Class<T> classType) {
        return (ctx, body) -> {
            Object mcDto = classType.getClassLoader().loadClass(classType.getName())?.getDeclaredConstructor()?.newInstance()
            def requestProperties = ctx.getContextProp(SOURCE_BODY_REQUEST, Object.class).properties
            mcDto.id =  requestProperties.groupCode + EllucianConstants.ETHOS_COMPOSITE_KEY_DELIMITER + requestProperties.termCode
            return mcDto
        }
    }
}
