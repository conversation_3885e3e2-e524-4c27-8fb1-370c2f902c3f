package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.apache.camel.builder.Builder.constant;
import static org.moderncampus.integration.constants.Constants.API_RESOURCES_KEY;

import java.util.List;

import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;

public interface IEllucianCloudPreFetchSupport {

    default void preFetchAssociations(IEllucianAssociationResolver associationResolver,
            RouteDefinition routeDefinition, List<IEllucianCloudAPIResource> associationPreFetchList) {
        if (associationResolver != null && associationPreFetchList != null) {
            routeDefinition.setHeader(API_RESOURCES_KEY, constant(associationPreFetchList));
            routeDefinition.bean(associationResolver,
                    String.format("%s(${header.%s},*)", IEllucianAssociationResolver.METHOD_RESOLVE_ASSOCIATIONS,
                            API_RESOURCES_KEY));
        }
    }
}
