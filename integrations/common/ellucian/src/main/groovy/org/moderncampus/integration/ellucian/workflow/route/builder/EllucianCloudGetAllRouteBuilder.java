package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.ELLUCIAN_ETHOS;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Processor;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosCommonFunctions;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.route.builder.GetAllFromExtSystemRouteBuilder;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
public class EllucianCloudGetAllRouteBuilder extends GetAllFromExtSystemRouteBuilder implements
        IEllucianCloudPreFetchSupport {

    EllucianCloudEndpointType endpointType;
    IEllucianCloudAPIResource apiResource;
    IEllucianAssociationResolver associationResolver;
    List<IEllucianCloudAPIResource> associationPreFetchList;
    Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass;
    Consumer<RouteDefinition> postRouteActionBuilder;

    @Builder(builderMethodName = "")
    protected EllucianCloudGetAllRouteBuilder(String id,
            Object transformer,
            String transformMethod,
            AggregationStrategy aggregationStrategy,
            Object searchCriteriaBuilder,
            Object readEPPaginator,
            Processor responsePaginationMapper,
            Processor responseSplitter,
            Processor preTransformProcessor,
            String useCase,
            EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass,
            IEllucianAssociationResolver associationResolver,
            List<IEllucianCloudAPIResource> associationPreFetchList,
            Consumer<RouteDefinition> postRouteActionBuilder)
    {
        super(id, transformer, transformMethod, aggregationStrategy, searchCriteriaBuilder, readEPPaginator,
                responsePaginationMapper, responseSplitter, preTransformProcessor, useCase);
        this.endpointType = endpointType;
        this.apiResource = apiResource;
        this.connectionConfigClass = connectionConfigClass;
        this.associationResolver = associationResolver;
        this.associationPreFetchList = associationPreFetchList;
        this.postRouteActionBuilder = postRouteActionBuilder;
    }

    public static EllucianCloudGetAllRouteBuilderBuilder builder(String id, EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, Object transformer,
            String transformMethod,
            AggregationStrategy aggregationStrategy) {
        return new EllucianCloudGetAllRouteBuilderBuilder().id(id).transformer(transformer)
                .transformMethod(transformMethod).aggregationStrategy(aggregationStrategy).endpointType(endpointType)
                .apiResource(apiResource).connectionConfigClass(connectionConfigClass).responsePaginationMapper(
                        endpointType != null && endpointType.name().contains(ELLUCIAN_ETHOS)
                                ? EllucianEthosCommonFunctions.responsePaginationMapper : null);
    }

    @Override
    protected String getExtSystemGetAllRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(connectionConfigClass))));
    }

    @Override
    protected void preFetchAssociations(RouteDefinition routeDefinition) {
        preFetchAssociations(associationResolver, routeDefinition, associationPreFetchList);
    }

    @Override
    protected void buildPostRouteActions(RouteDefinition routeDefinition) {
        if (postRouteActionBuilder != null) {
            postRouteActionBuilder.accept(routeDefinition);
        }
    }
}
