package org.moderncampus.integration.ellucian.workflow.route.helper;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.constants.Constants;

import groovy.json.JsonSlurper;

public class EllucianEthosCommonFunctions {

    static final String ETHOS_PAGE_SIZE_PARAM = "X-Max-Page-Size";
    static final String ETHOS_PAGE_OFFSET_PARAM = "X-hedtech-pageOffset";
    static final String ETHOS_PAGE_TOTAL_COUNT_PARAM = "X-Total-Count";

    private static <T> Optional<T> extractResponseParamFromHeader(Exchange exchange, String headerName,
            Class<T> headerType) {
        return Optional.ofNullable(exchange.getMessage().getHeader(headerName, () -> null, headerType));
    }

    public static Processor responsePaginationMapper = exchange -> {
        Optional<Integer> pageSize = extractResponseParamFromHeader(exchange, ETHOS_PAGE_SIZE_PARAM, Integer.class);
        Optional<Integer> pageOffset = extractResponseParamFromHeader(exchange, ETHOS_PAGE_OFFSET_PARAM, Integer.class);
        Optional<Integer> totalCount = extractResponseParamFromHeader(exchange, ETHOS_PAGE_TOTAL_COUNT_PARAM,
                Integer.class);

        pageSize.ifPresent(integer -> exchange.setProperty(Constants.PAGE_SIZE, integer));
        pageOffset.ifPresent(integer -> exchange.setProperty(Constants.PAGE_OFFSET, integer));
        totalCount.ifPresent(integer -> exchange.setProperty(Constants.TOTAL_SIZE, integer));
    };

    public static List<Map<String, ?>> parseResponse(List<String> responseList) {
        return responseList.stream().map((resp -> {
            return (Map<String, ?>) new JsonSlurper().parseText(resp);
        })).collect(Collectors.toList());
    }
}
