package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.camel.Processor;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.route.builder.GetByIdFromExtSystemRouteBuilder;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianCloudGetByIdRouteBuilder extends GetByIdFromExtSystemRouteBuilder implements
        IEllucianCloudPreFetchSupport {

    EllucianCloudEndpointType endpointType;
    IEllucianCloudAPIResource apiResource;
    IEllucianAssociationResolver associationResolver;
    List<IEllucianCloudAPIResource> associationPreFetchList;
    Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass;
    Consumer<RouteDefinition> postRouteActionBuilder;

    @Builder(builderMethodName = "")
    protected EllucianCloudGetByIdRouteBuilder(String id, Object transformer, String transformMethod,
            Processor preTransformProcessor, EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass,
            IEllucianAssociationResolver associationResolver, Consumer<RouteDefinition> postRouteActionBuilder,
            List<IEllucianCloudAPIResource> associationPreFetchList,
            String useCase) {
        super(id, transformer, transformMethod, preTransformProcessor,useCase);
        this.endpointType = endpointType;
        this.apiResource = apiResource;
        this.connectionConfigClass = connectionConfigClass;
        this.associationResolver = associationResolver;
        this.associationPreFetchList = associationPreFetchList;
        this.postRouteActionBuilder = postRouteActionBuilder;
    }

    public static EllucianCloudGetByIdRouteBuilderBuilder builder(String id, EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass,
            Object transformer, String transformMethod) {
        return new EllucianCloudGetByIdRouteBuilderBuilder().id(id).transformer(transformer)
                .transformMethod(transformMethod).endpointType(endpointType).apiResource(apiResource)
                .connectionConfigClass(connectionConfigClass);
    }

    @Override
    protected void preFetchAssociations(RouteDefinition routeDefinition) {
        preFetchAssociations(associationResolver, routeDefinition, associationPreFetchList);
    }

    @Override
    protected String getExtSystemGetByIdRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(connectionConfigClass),
                                Constants.RESOURCE_ID_PARAM,
                                RESOURCE_ID_EXPR_REF)));
    }

    @Override
    protected void buildPostRouteActions(RouteDefinition routeDefinition) {
        if (postRouteActionBuilder != null) {
            postRouteActionBuilder.accept(routeDefinition);
        }
    }
}
