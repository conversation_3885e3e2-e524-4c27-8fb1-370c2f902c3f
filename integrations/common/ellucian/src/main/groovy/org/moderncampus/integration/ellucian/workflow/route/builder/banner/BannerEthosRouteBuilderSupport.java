package org.moderncampus.integration.ellucian.workflow.route.builder.banner;

import org.apache.camel.AggregationStrategy;
import org.moderncampus.integration.ellucian.component.BannerCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudCreateRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudDeleteRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetAllRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetByIdRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudUpdateRouteBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;

public class BannerEthosRouteBuilderSupport {

    public static EllucianCloudGetAllRouteBuilder.EllucianCloudGetAllRouteBuilderBuilder getAllBuilder(String id,
            BannerEthosAPIResource apiResource, Object transformer, String transformMethod,
            AggregationStrategy aggregationStrategy) {
        return EllucianCloudGetAllRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_ETHOS_API, apiResource,
                BannerCloudConnectionConfiguration.class, transformer, transformMethod, aggregationStrategy);
    }

    public static EllucianCloudGetByIdRouteBuilder.EllucianCloudGetByIdRouteBuilderBuilder getByIdBuilder(String id,
            BannerEthosAPIResource apiResource,
            Object transformer, String transformMethod) {
        return EllucianCloudGetByIdRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_ETHOS_API, apiResource,
                BannerCloudConnectionConfiguration.class, transformer, transformMethod);
    }

    public static EllucianCloudCreateRouteBuilder.EllucianCloudCreateRouteBuilderBuilder createBuilder(String id,
            BannerEthosAPIResource apiResource, Object outTransformHelper,
            String outTransformMethod,
            Object inTransformHelper, String inTransformMethod) {
        return EllucianCloudCreateRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_ETHOS_API, apiResource,
                BannerCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, inTransformHelper,
                inTransformMethod);
    }

    public static EllucianCloudUpdateRouteBuilder.EllucianCloudUpdateRouteBuilderBuilder updateByIdBuilder(String id,
            BannerEthosAPIResource apiResource, Object outTransformHelper,
            String outTransformMethod, ObjectMapper mapper) {
        return EllucianCloudUpdateRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_ETHOS_API, apiResource,
                BannerCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, mapper);
    }

    public static EllucianCloudDeleteRouteBuilder deleteByIdBuilder(String id, BannerEthosAPIResource apiResource,
            ObjectMapper mapper) {
        return new EllucianCloudDeleteRouteBuilder(id, EllucianCloudEndpointType.BANNER_ETHOS_API, apiResource,
                BannerCloudConnectionConfiguration.class, mapper);
    }

}
