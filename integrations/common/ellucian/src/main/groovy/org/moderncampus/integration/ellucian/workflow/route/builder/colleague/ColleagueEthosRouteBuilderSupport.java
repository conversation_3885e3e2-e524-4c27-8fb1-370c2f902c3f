package org.moderncampus.integration.ellucian.workflow.route.builder.colleague;

import org.apache.camel.AggregationStrategy;
import org.moderncampus.integration.ellucian.component.ColleagueCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudCreateRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudDeleteRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetAllRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetByIdRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudUpdateRouteBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;

public class ColleagueEthosRouteBuilderSupport {

    public static EllucianCloudGetAllRouteBuilder.EllucianCloudGetAllRouteBuilderBuilder getAllBuilder(String id,
            ColleagueEthosAPIResource apiResource, Object transformer, String transformMethod,
            AggregationStrategy aggregationStrategy) {
        return EllucianCloudGetAllRouteBuilder.builder(id, EllucianCloudEndpointType.COLLEAGUE_ETHOS_API, apiResource,
                ColleagueCloudConnectionConfiguration.class, transformer, transformMethod, aggregationStrategy);
    }

    public static EllucianCloudGetByIdRouteBuilder.EllucianCloudGetByIdRouteBuilderBuilder getByIdBuilder(String id,
            ColleagueEthosAPIResource apiResource,
            Object transformer, String transformMethod) {
        return EllucianCloudGetByIdRouteBuilder.builder(id, EllucianCloudEndpointType.COLLEAGUE_ETHOS_API, apiResource,
                ColleagueCloudConnectionConfiguration.class, transformer, transformMethod);
    }

    public static EllucianCloudCreateRouteBuilder.EllucianCloudCreateRouteBuilderBuilder createBuilder(String id,
            ColleagueEthosAPIResource apiResource, Object outTransformHelper,
            String outTransformMethod,
            Object inTransformHelper, String inTransformMethod) {
        return EllucianCloudCreateRouteBuilder.builder(id, EllucianCloudEndpointType.COLLEAGUE_ETHOS_API, apiResource,
                ColleagueCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, inTransformHelper,
                inTransformMethod);
    }

    public static EllucianCloudUpdateRouteBuilder.EllucianCloudUpdateRouteBuilderBuilder updateByIdBuilder(String id,
            ColleagueEthosAPIResource apiResource, Object outTransformHelper,
            String outTransformMethod, ObjectMapper mapper) {
        return EllucianCloudUpdateRouteBuilder.builder(id, EllucianCloudEndpointType.COLLEAGUE_ETHOS_API, apiResource,
                ColleagueCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, mapper);
    }

    public static EllucianCloudDeleteRouteBuilder deleteByIdBuilder(String id, ColleagueEthosAPIResource apiResource,
            ObjectMapper mapper) {
        return new EllucianCloudDeleteRouteBuilder(id, EllucianCloudEndpointType.COLLEAGUE_ETHOS_API, apiResource,
                ColleagueCloudConnectionConfiguration.class, mapper);
    }
}
