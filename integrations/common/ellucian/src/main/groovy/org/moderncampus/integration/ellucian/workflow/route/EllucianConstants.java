package org.moderncampus.integration.ellucian.workflow.route;

public class EllucianConstants {

    public static final String NIL_GUID = "00000000-0000-0000-0000-000000000000";
    public static final String USE_CASE_UPDATE_SECTION = "useCaseUpdateSection";
    public static final String USE_CASE_GET_SECTIONS = "useCaseGetSections";
    public static final String USE_CASE_GET_SECTION = "useCaseGetSection";
    public static final String USE_CASE_CREATE_SECTION = "useCaseCreateSection";
    public static final String USE_CASE_UPDATE_COURSE = "useCaseUpdateCourse";
    public static final String USE_CASE_CREATE_COURSE = "useCaseCreateCourse";
    public static final String USE_CASE_CREATE_SECTION_SCHEDULE = "useCaseCreateSectionSchedule";
    public static final String USE_CASE_UPDATE_SECTION_SCHEDULE = "useCaseUpdateSectionSchedule";
    public static final String USE_CASE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT = "useCaseCreateSectionInstructorAssigment";
    public static final String USE_CASE_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT = "useCaseUpdateSectionInstructorAssignment";
    public static final String USE_CASE_UPDATE_STUDENT_ENROLLMENT = "useCaseUpdateStudentEnrollment";
    public static final String USE_CASE_FETCH_INSTRUCTORS = "useCaseFetchInstructors";
    public static final String USE_CASE_FILTER_YEARS = "useCaseFilterYears";
    public static final String USE_CASE_CREATE_PERSON = "useCaseCreatePerson";
    public static final String USE_CASE_UPDATE_PERSON = "useCaseUpdatePerson";
    public static final String USE_CASE_UPDATE_FINAL_GRADE = "useCaseUpdateFinalGrade";
    public static final String USE_CASE_UPDATE_SECTION_CROSS_LIST = "useCaseUpdateSectionCrossList";
    public static final String USE_CASE_UPDATE_SECTION_CROSS_LIST_GROUP = "useCaseUpdateSectionCrossListGroup";
    public static final String USE_CASE_CREATE_ORGANIZATION = "useCaseCreateOrganization";
    public static final String USE_CASE_UPDATE_ORGANIZATION = "useCaseUpdateOrganization";
    public static final String ELLUCIAN_ETHOS = "ETHOS";
    public static final String ETHOS_PAGINATION_LIMIT = "limit";
    public static final String ETHOS_PAGINATION_OFFSET = "offset";
    public static final String ETHOS_COMPOSITE_KEY_DELIMITER = "|";
}
