package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.route.builder.CreateToExtSystemRouteBuilder;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianCloudCreateRouteBuilder extends CreateToExtSystemRouteBuilder implements
        IEllucianCloudPreFetchSupport {

    EllucianCloudEndpointType endpointType;
    IEllucianCloudAPIResource apiResource;
    IEllucianAssociationResolver associationResolver;
    List<IEllucianCloudAPIResource> associationPreFetchList;
    Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass;
    Consumer<RouteDefinition> postRouteActionBuilder;

    @Builder(builderMethodName = "")
    protected EllucianCloudCreateRouteBuilder(String id, EllucianCloudEndpointType endpointType,
            IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, String useCase,
            Object outTransformHelper,
            String outTransformMethod,
            Object inTransformHelper, String inTransformMethod,
            IEllucianAssociationResolver associationResolver, List<IEllucianCloudAPIResource> associationPreFetchList,
            Consumer<RouteDefinition> postRouteActionBuilder) {
        super(id, outTransformHelper, outTransformMethod, inTransformHelper, inTransformMethod, useCase);
        this.endpointType = endpointType;
        this.apiResource = apiResource;
        this.associationResolver = associationResolver;
        this.connectionConfigClass = connectionConfigClass;
        this.associationPreFetchList = associationPreFetchList;
        this.postRouteActionBuilder = postRouteActionBuilder;
    }

    public static EllucianCloudCreateRouteBuilder.EllucianCloudCreateRouteBuilderBuilder builder(String id,
            EllucianCloudEndpointType endpointType, IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, Object outTransformHelper,
            String outTransformMethod,
            Object inTransformHelper, String inTransformMethod) {
        return new EllucianCloudCreateRouteBuilderBuilder().id(id).outTransformHelper(outTransformHelper)
                .outTransformMethod(outTransformMethod).inTransformHelper(inTransformHelper)
                .inTransformMethod(inTransformMethod).endpointType(endpointType).apiResource(apiResource)
                .connectionConfigClass(connectionConfigClass);
    }

    @Override
    protected void preFetchAssociations(RouteDefinition routeDefinition) {
        preFetchAssociations(associationResolver, routeDefinition, associationPreFetchList);
    }

    @Override
    protected void buildPostRouteActions(RouteDefinition routeDefinition) {
        if (postRouteActionBuilder != null) {
            postRouteActionBuilder.accept(routeDefinition);
        }
    }

    @Override
    protected String getExtSystemCreateRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(Constants.HTTP_METHOD, METHOD_POST,
                                Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(connectionConfigClass))));
    }
}
