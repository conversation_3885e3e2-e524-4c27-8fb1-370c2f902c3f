package org.moderncampus.integration.ellucian.workflow.route.helper;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosCommonFunctions.parseResponse;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.Constants;
import org.moderncampus.integration.ellucian.workflow.transform.helper.EthosHelper;
import org.springframework.util.CollectionUtils;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class EllucianCloudEthosAssociationResolver implements IEllucianAssociationResolver {

    ProducerTemplate template;

    CamelContext camelContext;

    EllucianEthosReadEndpointPaginator ellucianEthosReadEndpointPaginator;

    @Override
    public void resolveAssociations(List<IEllucianCloudAPIResource> apiResources, Exchange exchange) throws Exception {
        if (!CollectionUtils.isEmpty(apiResources)) {
            Map<IEllucianCloudAPIResource, List<Map<String, ?>>> assocResultsMap = new HashMap<>();
            for (IEllucianCloudAPIResource apiResource : apiResources) {
                List<Map<String, ?>> results = invokeEthosReadEndpoint(apiResource, null);
                assocResultsMap.put(apiResource, results);
            }
            cacheResultsMap(exchange, assocResultsMap);
        }
    }

    protected List<Map<String, ?>> invokeEthosReadEndpoint(IEllucianCloudAPIResource apiResource, Exchange exchange)
            throws Exception {
        String routeURI = buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                getEndpointType() + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(org.moderncampus.integration.constants.Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(getConnectionConfigurationClass()))));
        Endpoint endpoint = camelContext.getEndpoint(routeURI);
        Exchange apiExchange = exchange != null ? exchange : endpoint.createExchange(ExchangePattern.InOut);
        return parseResponse(ellucianEthosReadEndpointPaginator.invokeReadAllEndpoint(routeURI, apiExchange, false));
    }


    public abstract EllucianCloudEndpointType getEndpointType();

    public abstract Class<? extends IEllucianCloudConnectionConfiguration> getConnectionConfigurationClass();

    public void cacheResultsMap(Exchange exchange,
            Map<IEllucianCloudAPIResource, List<Map<String, ?>>> assocResultsMap) {
        exchange.setProperty(Constants.ETHOS_ASSOC_CACHE_MAP,
                EthosHelper.expandEthosAssocResultsMap(assocResultsMap));
    }
}
