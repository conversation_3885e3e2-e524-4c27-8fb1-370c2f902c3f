package org.moderncampus.integration.ellucian.workflow.route.helper;

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE;

import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.camel.ExchangePropertyKey;
import org.apache.camel.spi.UnitOfWork;
import org.moderncampus.integration.dto.base.BaseDTO;
import org.moderncampus.integration.route.dto.RouteExecutorResult;

public class EllucianCloudCommonAssociationHandler {

    protected <T> T getOriginalRequest(Exchange exchange, Class<T> requestType) {
        UnitOfWork parentUnitOfWork = Optional.ofNullable(
                exchange.getProperty(ExchangePropertyKey.PARENT_UNIT_OF_WORK, UnitOfWork.class)).orElseThrow();
        return parentUnitOfWork.getOriginalInMessage().getBody(requestType);
    }

    protected String extractUseCase(Exchange exchange) {
        return exchange.getProperty(INTEGRATION_USE_CASE, String.class);
    }

    protected String extractObjectId(boolean isCreate, boolean isUpdate, BaseDTO request,
            RouteExecutorResult objectWriteResponse) {
        if (isCreate) {
            return Optional.ofNullable(objectWriteResponse).flatMap(resp -> Optional.ofNullable(resp.getResults()))
                .map(results -> ((BaseDTO) results).getId()).orElse(null);
        } else if (isUpdate) {
            return request.getId();
        }
        throw new RuntimeException("Unknown use case");
    }

}
