package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.constants.Constants.*;
import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.camel.Exchange;
import org.apache.camel.model.RouteDefinition;
import org.apache.camel.spi.HeaderFilterStrategy;
import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.dto.base.IntegrationProxyRequest;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianProxyPathMeta;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.route.builder.BaseRouteBuilder;

public abstract class EllucianCloudProxyRouteBuilder extends BaseRouteBuilder {

    static final String PROXY_KEY = "PROXY_";
    static final String ROUTE_URI = "routeURI";
    static final String ROUTE_URI_EXPR_REF = "${exchangeProperty." + ROUTE_URI + "}";
    static final HeaderFilterStrategy headerFilterStrategy = new org.apache.camel.http.base.HttpHeaderFilterStrategy();

    public EllucianCloudProxyRouteBuilder(String id) {
        super(id);
    }

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.process((exchange) -> {
            IntegrationProxyRequest request = exchange.getMessage().getBody(IntegrationProxyRequest.class);
            EllucianProxyPathMeta pathMeta = parseProxyPath(request);
            IEllucianCloudAPIResource apiResource = getAllowedAPIResourceMap().get(pathMeta.getResourcePath());
            if (apiResource == null) {
                throw new ApplicationException("Invalid Proxy Path specified: " + request.getProxyPath(), 400);
            }
            if (pathMeta.getResourceId() != null) {
                exchange.setProperty(RESOURCE_ID, pathMeta.getResourceId());
            }
            if (request.getHeaders() != null) {
                request.getHeaders().forEach((key, value) -> {
                    if (key.startsWith(PROXY_KEY.toLowerCase())) {
                        exchange.getMessage().setHeader(key.substring(PROXY_KEY.length()), value);
                    }
                });
            }
            if (request.getQueryParams() != null) {
                exchange.getMessage().setHeader(QUERY_PARAMS, request.getQueryParams());
            }
            exchange.setProperty(ROUTE_URI, constructRouteURI(pathMeta.getResourceId(), apiResource,
                    request.getHttpMethod()));
            exchange.setProperty(PROXY_REQUEST, true);
            exchange.getMessage().setBody(null);
            if (request.getData() != null) {
                exchange.getMessage().setBody(request.getData());
            }
        });
        routeDefinition.toD(ROUTE_URI_EXPR_REF);
        routeDefinition.process((exchange) -> {
            String body = exchange.getMessage().getBody(String.class);
            exchange.setProperty(RESPONSE_STATUS_CODE,
                    (Integer) exchange.getMessage().getHeader(Exchange.HTTP_RESPONSE_CODE));
            exchange.setProperty(RESPONSE_HEADERS, getFilteredHeaders(exchange.getMessage().getHeaders(), exchange));
            exchange.getMessage().setBody(body);
        });
    }

    protected String constructRouteURI(String resourceId, IEllucianCloudAPIResource resource, String httpMethod) {
        Map<String, String> routeQueryParams = new HashMap<>() {{
            put(CONNECTION_CONFIG_PARAM,
                    parameterBeanRef(getConnectionConfigClass()));
        }};
        if (resourceId != null) {
            routeQueryParams.put(RESOURCE_ID_PARAM, resourceId);
        }
        if (httpMethod != null) {
            routeQueryParams.put(HTTP_METHOD_PARAM, httpMethod);
        }
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                resource.endpointType() + ":" + resource + ":"
                        + "/", routeQueryParamStr(routeQueryParams));
    }

    protected abstract Map<String, IEllucianCloudAPIResource> getAllowedAPIResourceMap();

    protected abstract Class<?> getConnectionConfigClass();

    private EllucianProxyPathMeta parseProxyPath(IntegrationProxyRequest request) throws Exception {
        String proxyPath = request.getProxyPath();
        if (StringUtils.isBlank(proxyPath)) {
            throw new ApplicationException("No Proxy path specified", 400);
        }
        proxyPath = proxyPath.startsWith("/") ? proxyPath : "/" + proxyPath;
        String[] segments = proxyPath.split("/");
        if (segments.length == 3 || segments.length == 4) {
            return new EllucianProxyPathMeta(segments);
        }
        throw new ApplicationException("Invalid Proxy Path specified: " + proxyPath, 400);
    }

    protected Map<String, Object> getFilteredHeaders(Map<String, Object> headers, Exchange exchange) {
        return headers.entrySet().stream().filter((entry) -> {
            return !headerFilterStrategy.applyFilterToExternalHeaders(entry.getKey(), entry.getValue(), exchange);
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
