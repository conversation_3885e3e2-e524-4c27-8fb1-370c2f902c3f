package org.moderncampus.integration.ellucian.workflow.transform.ethos

import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCSectionSchedule
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist
import static org.moderncampus.integration.transform.support.DateTimeFormatters.toUtcDateTimeString

@Component
@CompileStatic
class MCSectionScheduleToEthosWriteTransform {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    void mapId(Map<String, Object> req) {
        commonWriteTransform.mapId(req)
    }

    void mapSection(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.section)) {
            req.section = ['id': schedule.section]
        }
    }

    void mapInstructionalMethod(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.instructionalMethod)) {
            req.instructionalMethod = ['id': schedule.instructionalMethod]
        }
    }

    void mapRecurrenceTimePeriod(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.startDate)) {
            createRecurrenceTimePeriod(req)
            req.recurrence['timePeriod']['startOn'] = isValueExist(schedule.startTime) ? toUtcDateTimeString(schedule.startDate, schedule.
            startTime) : schedule.startDate.toString()
        }

        if (isValueExist(schedule.endDate)) {
            createRecurrenceTimePeriod(req)
            req.recurrence['timePeriod']['endOn'] = isValueExist(schedule.endTime) ? toUtcDateTimeString(schedule.endDate, schedule.endTime) :
            schedule.endDate.toString()
        }
    }

    void mapRoom(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule?.room)) {
            req.locations = [
                    ['location': [
                            'type': 'room',
                            'room': ['id': schedule.room]
                    ]
                    ]
            ]
        }
    }

    void mapApprovalOverrides(Map<String, Object> req, MCSectionSchedule schedule) {
        if (isValueExist(schedule.approvalOverrides)) {
            req.approvals = schedule.approvalOverrides.collect { ['approvalType': it.type, 'approvalEntity':'user'] }
        }
    }

    List<String> daysOfWeek(MCSectionSchedule schedule) {
        List<String> days = []
        if (isValueExist(schedule.daySunday)) days.add('sunday')
        if (isValueExist(schedule.dayMonday)) days.add('monday')
        if (isValueExist(schedule.dayTuesday)) days.add('tuesday')
        if (isValueExist(schedule.dayWednesday)) days.add('wednesday')
        if (isValueExist(schedule.dayThursday)) days.add('thursday')
        if (isValueExist(schedule.dayFriday)) days.add('friday')
        if (isValueExist(schedule.daySaturday)) days.add('saturday')
        return days
    }

    void createRecurrenceTimePeriod(Map<String, Object> req) {
        req.recurrence = req.recurrence ?: [:]
        req.recurrence['timePeriod'] = req.recurrence['timePeriod'] ?: [:]
    }

    void createRecurrenceRepeatRule(Map<String, Object> req) {
        req.recurrence = req.recurrence ?: [:]
        req.recurrence['repeatRule'] = req.recurrence['repeatRule'] ?: [:]
    }
}
