package org.moderncampus.integration.ellucian.workflow.route.builder;

import static org.moderncampus.integration.ellucian.component.constants.Constants.ELLUCIAN_CLOUD_COMPONENT_SCHEME;
import static org.moderncampus.integration.route.support.RouteSupport.*;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.ellucian.component.IEllucianCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.IEllucianAssociationResolver;
import org.moderncampus.integration.route.builder.UpdateToExtSystemRouteBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EllucianCloudUpdateRouteBuilder extends UpdateToExtSystemRouteBuilder implements
        IEllucianCloudPreFetchSupport {

    EllucianCloudEndpointType endpointType;
    IEllucianCloudAPIResource apiResource;
    IEllucianAssociationResolver associationResolver;
    List<IEllucianCloudAPIResource> associationPreFetchList;
    Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass;
    Consumer<RouteDefinition> postRouteActionBuilder;

    @Builder(builderMethodName = "")
    protected EllucianCloudUpdateRouteBuilder(String id, Object outTransformHelper, String outTransformMethod,
            ObjectMapper objectMapper, EllucianCloudEndpointType endpointType, IEllucianCloudAPIResource apiResource,
            IEllucianAssociationResolver associationResolver, List<IEllucianCloudAPIResource> associationPreFetchList,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, String useCase,
            Consumer<RouteDefinition> postRouteActionBuilder, Boolean isPatchImpl, Boolean isUpdateById) {
        super(id, outTransformHelper, outTransformMethod, objectMapper, useCase);
        this.isPatchImpl = isPatchImpl == null || isPatchImpl;
        this.isUpdateById = isUpdateById == null || isUpdateById;
        this.endpointType = endpointType;
        this.apiResource = apiResource;
        this.associationResolver = associationResolver;
        this.associationPreFetchList = associationPreFetchList;
        this.connectionConfigClass = connectionConfigClass;
        this.postRouteActionBuilder = postRouteActionBuilder;
    }

    public static EllucianCloudUpdateRouteBuilder.EllucianCloudUpdateRouteBuilderBuilder builder(String id,
            EllucianCloudEndpointType endpointType, IEllucianCloudAPIResource apiResource,
            Class<? extends IEllucianCloudConnectionConfiguration> connectionConfigClass, Object outTransformHelper,
            String outTransformMethod, ObjectMapper mapper) {
        return new EllucianCloudUpdateRouteBuilder.EllucianCloudUpdateRouteBuilderBuilder().id(id)
                .outTransformHelper(outTransformHelper)
                .outTransformMethod(outTransformMethod).endpointType(endpointType).apiResource(apiResource)
                .connectionConfigClass(connectionConfigClass).objectMapper(mapper);
    }

    @Override
    protected String getExtSystemGetByIdRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                    endpointType + ":" + apiResource + ":"
                            + "/", routeQueryParamStr(
                        Map.of(Constants.CONNECTION_CONFIG_PARAM,
                                    parameterBeanRef(connectionConfigClass),
                                Constants.RESOURCE_ID_PARAM,
                                    RESOURCE_ID_EXPR_REF)));
    }

    @Override
    protected String getExtSystemUpdateByIdRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(Constants.HTTP_METHOD, METHOD_PUT,
                                Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(connectionConfigClass),
                                Constants.RESOURCE_ID_PARAM,
                                RESOURCE_ID_EXPR_REF)));
    }

    @Override
    protected String getExtSystemUpdateRouteURI() {
        return buildRouteURI(ELLUCIAN_CLOUD_COMPONENT_SCHEME,
                endpointType + ":" + apiResource + ":"
                        + "/", routeQueryParamStr(
                        Map.of(Constants.HTTP_METHOD, METHOD_PUT,
                                Constants.CONNECTION_CONFIG_PARAM,
                                parameterBeanRef(connectionConfigClass))));
    }

    @Override
    protected void buildPostRouteActions(RouteDefinition routeDefinition) {
        if (postRouteActionBuilder != null) {
            postRouteActionBuilder.accept(routeDefinition);
        }
    }

    @Override
    protected void preFetchAssociations(RouteDefinition routeDefinition) {
        preFetchAssociations(associationResolver, routeDefinition, associationPreFetchList);
    }
}
