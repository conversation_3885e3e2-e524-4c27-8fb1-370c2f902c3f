package org.moderncampus.integration.helper;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.route.support.RouteSupport;

public class RouteSupportTest {

    private String path;
    private String result;

    @Test
    void buildDirectRouteUriTest() {
        path = null;
        result = RouteSupport.buildDirectRouteURI(path);

        Assertions.assertThat(result)
                .isNotEmpty()
                .startsWith("direct");

        path = "path";
        result = RouteSupport.buildDirectRouteURI(path);

        Assertions.assertThat(result)
                .isNotEmpty()
                .startsWith("direct")
                .endsWith(path);
    }

    @Test
    void buildRouteUriTest() {
        path = null;
        String scheme = null;
        result = RouteSupport.buildRouteURI(scheme, path, null);

        Assertions.assertThat(result)
                .isNotEmpty();

        path = "path";
        scheme = "scheme";
        result = RouteSupport.buildRouteURI(scheme, path, null);

        Assertions.assertThat(result)
                .isNotEmpty()
                .isEqualTo(scheme + "://" + path);
    }

    @Test
    void buildRouteUri_withQueryTest() {
        path = "path";
        String scheme = "scheme";
        String query = "query";
        result = RouteSupport.buildRouteURI(scheme, path, query);

        Assertions.assertThat(result)
                .isNotEmpty()
                .isEqualTo(scheme + "://" + path + '?' + query);
    }
}
