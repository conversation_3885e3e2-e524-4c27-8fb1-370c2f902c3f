package org.moderncampus.integration.tenant.route;//package org.moderncampus.integration.tenant.route;
//
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.doReturn;
//import static org.mockito.Mockito.verify;
//import static org.moderncampus.integration.Constants.*;
//
//import org.apache.camel.CamelContext;
//import org.apache.camel.Exchange;
//import org.apache.camel.Route;
//import org.assertj.core.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.params.ParameterizedTest;
//import org.junit.jupiter.params.provider.ValueSource;
//import org.mockito.Mockito;
//import org.moderncampus.integration.route.support.IRouteIdResolver;
//import org.moderncampus.integration.route.support.RouteIdResolver;
//import org.moderncampus.integration.context.tenant.IntegrationRequestContext;
//
//public class TenantRouteIdResolverTest {
//
//    private IRouteIdResolver resolver;
//    private Exchange exchange;
//    private IntegrationRequestContext requestContext;
//    private CamelContext camelContext;
//
//    static final String REQUEST_NOT_ROUTABLE = "Request is not routable. Route identifier not found";
//
//    @BeforeEach
//    void setUp() {
//        camelContext = Mockito.mock(CamelContext.class);
//        requestContext = Mockito.mock(IntegrationRequestContext.class);
//        exchange = Mockito.mock(Exchange.class);
//        resolver = new RouteIdResolver(camelContext, requestContext);
//    }
//
//    @Test
//    void process_withInvalidRouteTest() {
//        assertProcessThrowsException();
//
//        doReturn("version").when(requestContext).getVersion();
//        assertProcessThrowsException();
//
//        doReturn("sourceSystem").when(requestContext).getSourceSystem();
//        assertProcessThrowsException();
//
//        doReturn("schoolId").when(requestContext).getSchool();
//        assertProcessThrowsException();
//
//        doReturn("destSystem").when(requestContext).getDestSystem();
//        assertProcessThrowsException();
//    }
//
//    @Test
//    void process_exchangeIsUpdatedTest() {
//        mockRequestGetters();
//
//        Assertions.assertThatThrownBy(() -> resolver.process(exchange))
//                .isInstanceOf(RuntimeException.class).hasMessage(REQUEST_NOT_ROUTABLE);
//
//        verify(exchange).setProperty(eq(SCHOOL_ID), eq("schoolId"));
//        verify(exchange).setProperty(eq(SOURCE_SYSTEM_ID), eq("sourceSystem"));
//        verify(exchange).setProperty(eq(DEST_SYSTEM_ID), eq("destSystem"));
//        verify(exchange).setProperty(eq(VERSION), eq("version"));
//    }
//
//    @ParameterizedTest
//    @ValueSource(strings = {"version/sourceSystem/schoolId/destSystem/context",
//            "version/schoolId/destSystem/context", "version/sourceSystem/*/destSystem/context",
//            "version/destSystem/context"})
//    void processTest(String key) {
//        mockRequestGetters();
//        Route route = Mockito.mock(Route.class);
//        doReturn(route).when(camelContext).getRoute(eq(key));
//
//        resolver.process(exchange);
//
//        verify(exchange).setProperty(eq(ROUTE_ID), contains(key));
//    }
//
//    private void mockRequestGetters() {
//        doReturn("version").when(requestContext).getVersion();
//        doReturn("sourceSystem").when(requestContext).getSourceSystem();
//        doReturn("schoolId").when(requestContext).getSchool();
//        doReturn("destSystem").when(requestContext).getDestSystem();
//        doReturn("context").when(exchange).getProperty(anyString(), eq(String.class));
//    }
//
//    private void assertProcessThrowsException() {
//        Assertions.assertThatThrownBy(() -> resolver.process(exchange))
//                .isInstanceOf(RuntimeException.class).hasMessage(REQUEST_NOT_ROUTABLE);
//    }
//}
