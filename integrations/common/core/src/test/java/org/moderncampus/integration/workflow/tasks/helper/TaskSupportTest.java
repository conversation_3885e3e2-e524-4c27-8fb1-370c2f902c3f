package org.moderncampus.integration.workflow.tasks.helper;//package org.moderncampus.integration.workflow.tasks.helper;
//
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.apache.camel.Exchange;
//import org.apache.camel.Message;
//import org.assertj.core.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mockito;
//import org.moderncampus.integration.route.helper.tasks.TaskSupport;
//import org.moderncampus.integration.route.Constants;
//import org.moderncampus.integration.workflow.result.WorkflowResult;
//import org.moderncampus.integration.workflow.result.WorkflowResultTypeCode;
//import org.moderncampus.integration.route.api.tasks.ITask;
//import org.moderncampus.integration.route.input.tasks.TaskInput;
//import org.moderncampus.integration.route.result.tasks.TaskOutput;
//
//public class TaskSupportTest {
//
//    private Exchange exchange;
//    private Message message;
//
//    @BeforeEach
//    void setUp() {
//        exchange = Mockito.mock(Exchange.class);
//        message = Mockito.mock(Message.class);
//        doReturn(message)
//                .when(exchange).getMessage();
//    }
//
//    @Test
//    void constructTaskInput_withNullTasksTest() {
//        doReturn(null)
//                .when(exchange).getProperty(anyString(), any());
//
//        TaskSupport.constructTaskInput(exchange);
//
//        verify(exchange).setProperty(eq(Constants.PREVIOUS_TASK_OUTPUTS), eq(new HashMap<>()));
//        verify(exchange).setProperty(eq(Constants.TASK_OUTPUTS), eq(new ArrayList<>()));
//    }
//
//    @Test
//    void constructTaskInputTest() {
//        HashMap<String, Object> previousTasks = new HashMap<>();
//        doReturn(previousTasks)
//                .when(exchange).getProperty(eq(Constants.PREVIOUS_TASK_OUTPUTS), eq(Map.class));
//        doReturn(new ArrayList<>())
//                .when(exchange).getProperty(eq(Constants.TASK_OUTPUTS), eq(List.class));
//
//        TaskInput result = TaskSupport.constructTaskInput(exchange);
//
//        Assertions.assertThat(result.getPreviousTaskOutputs())
//                .isNotNull()
//                .isEqualTo(previousTasks);
//
//        verify(exchange, times(0)).setProperty(eq(Constants.PREVIOUS_TASK_OUTPUTS), eq(previousTasks));
//        verify(exchange, times(0)).setProperty(eq(Constants.TASK_OUTPUTS), eq(new ArrayList<>()));
//        verify(message).getBody(eq(Object.class));
//    }
//
//    @Test
//    void postProcessTaskOutputTest() {
//        TaskOutput output = new TaskOutput();
//        ITask task = Mockito.mock(ITask.class);
//        doReturn("id")
//                .when(task).identifier();
//        doReturn(new HashMap<>())
//                .when(exchange).getProperty(eq(Constants.PREVIOUS_TASK_OUTPUTS), eq(Map.class));
//        doReturn(new ArrayList<>())
//                .when(exchange).getProperty(eq(Constants.TASK_OUTPUTS), eq(List.class));
//
//        TaskSupport.postProcessTaskOutput(task, exchange, output);
//
//        verify(exchange, times(0)).setProperty(anyString(), any());
//        verify(message).setBody(any());
//
//    }
//
//    @Test
//    void constructWorkflowResultTest() {
//        List<TaskOutput> taskOutputs = List.of();
//        Object returnObj = new Object();
//        WorkflowResultTypeCode resultCode = WorkflowResultTypeCode.SUCCESS;
//        String error = "error";
//
//        WorkflowResult result = TaskSupport.constructWorkflowResult(taskOutputs, returnObj,
//                resultCode, error);
//
//        Assertions.assertThat(result.getMeta().getResultTypeCode()).isEqualTo(resultCode);
//        Assertions.assertThat(result.getMeta().getTaskOutputs()).isEqualTo(taskOutputs);
//        Assertions.assertThat(result.getMeta().getErrorMessage()).isEqualTo(error);
//        Assertions.assertThat(result.getResults()).isEqualTo(returnObj);
//    }
//}
