package org.moderncampus.integration.env;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.moderncampus.integration.tenant.env.TenantConfigPropertySource;
import org.springframework.context.ApplicationContext;

public class TenantConfigPropertySourceTest {

    private TenantConfigPropertySource source;

    @BeforeEach
    void setUp() {
        source = new TenantConfigPropertySource();
    }

    @Test
    void getProperty_withInvalidRequestTest() {
        Object result = source.getProperty(null);
        Assertions.assertThat(result).isNull();

        result = source.getProperty("tenant-p");
        Assertions.assertThat(result).isNull();

        source.setApplicationContext(Mockito.mock(ApplicationContext.class));
        result = source.getProperty("-p");
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withExceptionThrown() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        source.setApplicationContext(context);

        Assertions.assertThat(source.getProperty("tenant-p")).isNull();
    }

//    @Test
//    void getPropertyTest() {
//        ApplicationContext context = Mockito.mock(ApplicationContext.class);
//        source.setApplicationContext(context);
//        ITenantConfigMgmtService service = Mockito.mock(ITenantConfigMgmtService.class);
//        doReturn(service)
//                .when(context).getBean(ITenantConfigMgmtService.class);
//
//        source.getProperty("tenant-p");
//
//        verify(service).getConfigValByCompositeKey(eq("tenant-p"));
//    }
}
