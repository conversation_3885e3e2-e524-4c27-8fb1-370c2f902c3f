package org.moderncampus.integration.transform.custom;

import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.POST;
import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.PRE;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.springframework.test.util.ReflectionTestUtils;

public class CustomTransformResolverTest {

    private CustomTransformResolver customTransformResolver;

    @BeforeEach
    public void setUp() {
        customTransformResolver = new CustomTransformResolver(createContext());
        String classLoaderPath = "build/classes/java/test";
        ReflectionTestUtils.setField(customTransformResolver, "classLoaderPath", classLoaderPath);
    }

    @Test
    @SuppressWarnings("DataFlowIssue")
    public void testFindApplicableTransforms() {
        List<CustomTransformMetadata> transforms = customTransformResolver.findApplicableTransforms(
                CustomTestTransformer.class);

        CustomTransformMetadata preTransformMetadata = transforms.stream()
                .filter(metadata -> PRE.equals(metadata.applicabilityType()))
                .findFirst().orElse(null);

        CustomTransformMetadata postTransformMetadata = transforms.stream()
                .filter(metadata -> POST.equals(metadata.applicabilityType()))
                .findFirst().orElse(null);

        Assertions.assertEquals("preTransform", preTransformMetadata.method().getName());
        Assertions.assertEquals("TestTransformCustomization",
                preTransformMetadata.object().getClass().getSimpleName());

        Assertions.assertEquals("postTransform", postTransformMetadata.method().getName());
        Assertions.assertEquals("TestTransformCustomization",
                postTransformMetadata.object().getClass().getSimpleName());
    }

    private IntegrationRequestContext createContext() {
        IntegrationRequestContext context = new IntegrationRequestContext();
        context.setDestSystem("destiny");
        context.setSchool("testschool");
        context.setSourceSystem("source");
        return context;
    }
}
