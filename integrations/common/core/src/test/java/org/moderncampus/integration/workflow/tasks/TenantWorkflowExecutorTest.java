package org.moderncampus.integration.workflow.tasks;//package org.moderncampus.integration.workflow.tasks;
//
//import org.apache.camel.ProducerTemplate;
//import org.assertj.core.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mockito;
//import org.moderncampus.integration.route.tasks.TenantWorkflowExecutor;
//import org.moderncampus.integration.workflow.request.WorkflowRequest;
//
//public class TenantWorkflowExecutorTest {
//
//    private TenantWorkflowExecutor executor;
//    private WorkflowRequest request;
//
//    @BeforeEach
//    void setUp() {
//        ProducerTemplate producerTemplate = Mockito.mock(ProducerTemplate.class);
//        executor = new TenantWorkflowExecutor(producerTemplate);
//    }
//
//    @Test
//    void executeWorkflow_withInvalidMetaPropertiesTest() {
//        Assertions.assertThatThrownBy(() -> executor.execute(new WorkflowRequest()))
//                .isInstanceOf(RuntimeException.class)
//                .hasMessage(TenantWorkflowExecutor.NO_META_PROPERTIES_DEFINED_ERROR);
//    }
//
//    @Test
//    void executeWorkflow_withInvalidMetaPathTest() {
//        request = new WorkflowRequest();
//        request.setRequestMeta(new WorkflowRequest.WorkflowRequestMeta());
//        Assertions.assertThatThrownBy(() -> executor.execute(request))
//                .isInstanceOf(RuntimeException.class)
//                .hasMessage(TenantWorkflowExecutor.NO_META_PROPERTIES_DEFINED_ERROR);
//    }
//}
