package org.moderncampus.integration.workflow.support;

import static org.mockito.Mockito.*;

import java.util.List;

import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;

public class DefaultListAggregationStrategyTest {

    private DefaultListAggregationStrategy strategy;
    private Exchange result;
    private Exchange oldExchange;
    private Exchange newExchange;
    private Message message;

    @BeforeEach
    void setUp() {
        this.strategy = new DefaultListAggregationStrategy();
        newExchange = Mockito.mock(Exchange.class);
        oldExchange = Mockito.mock(Exchange.class);
        message = Mockito.mock(Message.class);
    }

    @Test
    void aggregate_withNullExchangesTest() {
        result = strategy.aggregate(null, null);
        Assertions.assertThat(result).isNull();
    }

    @Test
    void aggregate_withNullOldExchangeAndNullNewExchangeMessageTest() {
        doReturn(null)
                .when(newExchange).getMessage();
        result = strategy.aggregate(null, newExchange);
        Assertions.assertThat(result).isEqualTo(newExchange);
    }

    @Test
    void aggregate_withNullOldExchangeTest() {
        Object body = new Object();
        doReturn(message)
                .when(newExchange).getMessage();
        doReturn(body)
                .when(message).getBody();

        result = strategy.aggregate(null, newExchange);

        Assertions.assertThat(result).isNotNull();
        verify(message).setBody(List.of(body), List.class);
    }

    @Test
    void aggregate_withNullNewExchangeTest() {
        Object body = new Object();
        doReturn(message)
                .when(oldExchange).getMessage();
        doReturn(body)
                .when(message).getBody();

        result = strategy.aggregate(oldExchange, null);

        Assertions.assertThat(result).isNotNull();
        verify(message).setBody(List.of(body));
    }

    @Test
    void aggregate_withNullNewExchangeMessageTest() {
        Object body = new Object();
        doReturn(message)
                .when(oldExchange).getMessage();
        doReturn(null)
                .when(newExchange).getMessage();
        doReturn(body)
                .when(message).getBody();

        result = strategy.aggregate(oldExchange, newExchange);

        Assertions.assertThat(result).isNotNull();
        verify(message).setBody(List.of(body));
    }

    @Test
    void aggregateTest() {
        Object body = new Object();
        Object newBody = new Object();
        Message newMessage = Mockito.mock(Message.class);

        doReturn(message)
                .when(oldExchange).getMessage();
        doReturn(newMessage)
                .when(newExchange).getMessage();
        doReturn(body)
                .when(message).getBody();
        doReturn(newBody)
                .when(newMessage).getBody();

        result = strategy.aggregate(oldExchange, newExchange);

        Assertions.assertThat(result).isNotNull();
        verify(message).setBody(List.of(body, newBody));
        verify(newMessage, times(0)).setBody(anyList());
    }
}
