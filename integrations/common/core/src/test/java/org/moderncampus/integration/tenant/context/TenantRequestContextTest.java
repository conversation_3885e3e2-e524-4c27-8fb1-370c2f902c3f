package org.moderncampus.integration.tenant.context;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.moderncampus.integration.context.IntegrationRequestContext;

public class TenantRequestContextTest {

    private IntegrationRequestContext requestContext;

    @BeforeEach
    void setUp() {
        requestContext = new IntegrationRequestContext();
    }

    @Test
    void setSchoolTest() {
        requestContext.setSchool(null);
        Assertions.assertNull(requestContext.getSchool());

        String upperCaseSchool = "UPPer SchOOl";
        requestContext.setSchool(upperCaseSchool);
        Assertions.assertEquals(upperCaseSchool.toLowerCase(), requestContext.getSchool());
    }

    @Test
    void setSourceSystemTest() {
        requestContext.setSourceSystem(null);
        Assertions.assertNull(requestContext.getSourceSystem());

        String upperCaseSourceSystem = "UPPer SourCE";
        requestContext.setSourceSystem(upperCaseSourceSystem);
        Assertions.assertEquals(upperCaseSourceSystem.toLowerCase(),
                requestContext.getSourceSystem());
    }

    @Test
    void setDestSystemTest() {
        requestContext.setDestSystem(null);
        Assertions.assertNull(requestContext.getDestSystem());

        String upperCaseDestSystem = "UPPer DEST";
        requestContext.setDestSystem(upperCaseDestSystem);
        Assertions.assertEquals(upperCaseDestSystem.toLowerCase(), requestContext.getDestSystem());
    }


}
