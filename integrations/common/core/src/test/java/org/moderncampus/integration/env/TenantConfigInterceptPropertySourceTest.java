package org.moderncampus.integration.env;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.system.IntegrationSystem;
import org.moderncampus.integration.tenant.env.TenantConfigInterceptPropertySource;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;

public class TenantConfigInterceptPropertySourceTest {

    private TenantConfigInterceptPropertySource interceptPropertySource;

    @BeforeEach
    void setUp() {
        interceptPropertySource = new TenantConfigInterceptPropertySource();
    }

    @Test
    void getProperty_withNullContextTest() {
        Object result = interceptPropertySource.getProperty(" ");
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withNullTenantPropPrefixListTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);

        Object result = interceptPropertySource.getProperty(" ");
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withNonTenantPropertyLookupTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);

        Object result = interceptPropertySource.getProperty(null);
        Assertions.assertThat(result).isNull();

        result = interceptPropertySource.getProperty("tenant-PROP");
        Assertions.assertThat(result).isNull();

        result = interceptPropertySource.getProperty("not-some-prop");
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withPropertyNameNeedingParsingTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);

        Object result = interceptPropertySource.getProperty("one:prop");
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withoutTenantContextSchoolTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);
        doReturn(Mockito.mock(IIntegrationRequestContext.class))
                .when(context).getBean((Class<Object>) any());

        Object result = interceptPropertySource.getProperty(IntegrationSystem.WORKDAY.name().toLowerCase());
        Assertions.assertThat(result).isNull();
    }

    @Test
    void getProperty_withoutTenantSourceSystemTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);
        IIntegrationRequestContext tenantContext = Mockito.mock(IIntegrationRequestContext.class);
        doReturn(tenantContext)
                .when(context).getBean(IIntegrationRequestContext.class);
        doReturn(environment)
                .when(context).getBean(Environment.class);
        doReturn("school")
                .when(tenantContext).getSchool();
        doReturn("result")
                .when(environment).getProperty(eq("tenant.school." + IntegrationSystem.WORKDAY.name().toLowerCase()));

        Object result = interceptPropertySource.getProperty(IntegrationSystem.WORKDAY.name().toLowerCase());
        Assertions.assertThat(result)
                .isNotNull()
                .isEqualTo("result");
    }

    @Test
    void getProperty_withTenantSourceSystemTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);
        IIntegrationRequestContext tenantContext = Mockito.mock(IIntegrationRequestContext.class);
        doReturn(tenantContext)
                .when(context).getBean(IIntegrationRequestContext.class);
        doReturn(environment)
                .when(context).getBean(Environment.class);
        doReturn("school")
                .when(tenantContext).getSchool();
        doReturn("source syst")
                .when(tenantContext).getSourceSystem();
        doReturn("value")
                .when(environment)
                .getProperty(eq("tenant.source syst.school." + IntegrationSystem.WORKDAY.name().toLowerCase()));

        Object result = interceptPropertySource.getProperty(IntegrationSystem.WORKDAY.name().toLowerCase());
        Assertions.assertThat(result)
                .isNotNull()
                .isEqualTo("value");
    }

    @Test
    void getProperty_withTenantSourceSystemWithSrcKeyNotFoundTest() {
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Environment environment = Mockito.mock(Environment.class);
        doReturn(environment)
                .when(context).getEnvironment();
        interceptPropertySource.setApplicationContext(context);
        IIntegrationRequestContext tenantContext = Mockito.mock(IIntegrationRequestContext.class);
        doReturn(tenantContext)
                .when(context).getBean(IIntegrationRequestContext.class);
        doReturn(environment)
                .when(context).getBean(Environment.class);
        doReturn("school")
                .when(tenantContext).getSchool();
        doReturn("source syst")
                .when(tenantContext).getSourceSystem();
        doReturn(null)
                .when(environment)
                .getProperty(eq("tenant.source syst.school." + IntegrationSystem.WORKDAY.name().toLowerCase()));
        doReturn("result")
                .when(environment).getProperty(eq("tenant.school." + IntegrationSystem.WORKDAY.name().toLowerCase()));

        Object result = interceptPropertySource.getProperty(IntegrationSystem.WORKDAY.name().toLowerCase());
        Assertions.assertThat(result)
                .isNotNull()
                .isEqualTo("result");
    }


}
