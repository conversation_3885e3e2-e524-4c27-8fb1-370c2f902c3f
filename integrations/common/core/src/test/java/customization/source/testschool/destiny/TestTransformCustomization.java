package customization.source.testschool.destiny;

import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.POST;
import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.PRE;

import org.moderncampus.integration.transform.TransformContext;
import org.moderncampus.integration.transform.custom.CustomTestTransformer;
import org.moderncampus.integration.transform.custom.CustomTransform;

public class TestTransformCustomization {

    @CustomTransform(applyTo = CustomTestTransformer.class, applicability = PRE)
    public void preTransform(TransformContext context, Object input) {
    }

    @CustomTransform(applyTo = CustomTestTransformer.class, applicability = POST)
    public void postTransform(TransformContext context, Object input) {
    }
}
