package org.moderncampus.integration.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import groovy.xml.slurpersupport.GPathResult
import groovy.xml.slurpersupport.NodeChild

import java.util.function.Function

@CompileStatic
class GroovyXMLSupport {

    def static String extractNameSpaceFromNode(Node node) {
        if (node.attributes() == null || node.attributes().isEmpty()) {
            return null
        } else {
            return (node.attributes().keySet()[0] as String).split(":")[1]
        }
    }

    def static boolean nodeDefinedAndNotEmpty(GPathResult result) {
        return result != null && !result.isEmpty()
    }

    def static String extractNodeText(GPathResult pathResult, Closure handleEmpty = null) {
        if (pathResult == null) {
            return null
        }
        return pathResult.isEmpty() ? (handleEmpty != null ? handleEmpty() : null) : pathResult.text()
    }

    def static String extractTrimmedText(String text, Integer trimLength, Integer startIndex = 0) {
        try {
            return text ? text.substring(startIndex, trimLength) : text
        } catch (StringIndexOutOfBoundsException ignored) {
            return text.substring(startIndex, text.length())
        }
    }

    def static String extractSanitizedText(String text, String regex) {
        return text.replaceAll(regex, "")
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def static <T> void updateNodeCollection(def objectReference, def builder, List<T> collection, NodeChild existingNode,
                                             Map<String, List<NodeChild>> existingNodeMap, String nodeCreatorMethodName,
                                             Function<T, String> keyMapper = null) {
        if (builder == null) {
            throw new RuntimeException("No builder defined when mapping collection")
        }
        if (collection) {
            if (existingNode) {
                // find all reference nodes -> Map<typeId, Node>
                // iterate through each input entry
                // check if entry exists reference node entry, if so -> update those (call replaceNode()). Remove this entry
                // if entry does not exist -> call appendNode
                // iterate through remaining entries in (reference nodes) and delete them (replace)
                if (existingNodeMap) {
                    existingNodeMap.remove(null)
                }
                collection.each { item ->
                    {
                        String key = keyMapper ? keyMapper.apply(item) : item as String
                        if (existingNodeMap && existingNodeMap.containsKey(key)) {
                            List<NodeChild> nodes = existingNodeMap.get(key)
                            //When using XMLSlurper there isn't a way to remove existing nodes. So when replacing (remove + add) we use replaceNode
                            // which mimics this function
                            nodes[0].replaceNode {
                                objectReference."$nodeCreatorMethodName"(delegate, item)
                            }
                            existingNodeMap.remove(key)
                        } else {
                            existingNode << {
                                objectReference."$nodeCreatorMethodName"(delegate, item)
                            }
                        }
                    }
                }
                //When using XMLSlurper there isn't a way to remove existing nodes. The line below removes all unmapped nodes by replacing the node
                // to empty
                if (existingNodeMap) {
                    existingNodeMap.each { it -> it.value[0].replaceNode {} }
                }
            } else {
                collection.each { item ->
                    objectReference."$nodeCreatorMethodName"(builder, item) //Using the builder's scope here as needed
                }
            }
        }
    }
}
