package org.moderncampus.integration.helper

import groovy.transform.CompileStatic
import groovy.transform.TypeCheckingMode
import org.moderncampus.integration.dto.base.BaseDTO

import java.util.function.Function

@CompileStatic
class GroovyJsonSupport {

    static enum ASSOC_MODE {
        REPLACE, UPDATE, DELETE
    }

    static enum UPDATE_USE_CASE {
        CREATE, UPDATE
    }

    @CompileStatic(TypeCheckingMode.SKIP)
    def static <T extends BaseDTO> void createOrUpdateExtEntityAssociation(def objectReference, boolean isUpdate, List<? extends T> mwCollection,
    Map<String, ?> extEntity, String propName,
                                                                           Map<String, List<Map<String, ?>>> existingExtEntityMap,
                                                                           String extEntityCreatorMethodName,
                                                                           String extEntityUpdaterMethodName, List methodArgs = null, Function<T,
                                                                           String> keyMapper = null, ASSOC_MODE overrideAssocMode = null) {
        def updateUseCase = isUpdate ? GroovyJsonSupport.UPDATE_USE_CASE.UPDATE : GroovyJsonSupport.UPDATE_USE_CASE.CREATE
        if (mwCollection) {
            if (updateUseCase == UPDATE_USE_CASE.UPDATE) {
                if (existingExtEntityMap) {
                    existingExtEntityMap.remove(null)
                }
                mwCollection.each { mwItem ->
                    {
                        String key = keyMapper ? keyMapper.apply(mwItem) : mwItem as String
                        if (existingExtEntityMap && existingExtEntityMap.containsKey(key)) {
                            Map<String, ?> externalEntity = existingExtEntityMap.get(key)[0]
                            if (overrideAssocMode == ASSOC_MODE.UPDATE || overrideAssocMode == null) {
                                objectReference."$extEntityUpdaterMethodName"(mwItem, isUpdate, externalEntity, *methodArgs)
                            }
                            existingExtEntityMap.remove(key)
                        } else {
                            Map newExtEntity = objectReference."$extEntityCreatorMethodName"(mwItem, isUpdate, null, *methodArgs)
                            addEntityToExtCollection(extEntity, propName, newExtEntity)
                        }
                    }
                }
            } else {
                mwCollection.each { mwItem ->
                    Map newExtEntity = objectReference."$extEntityCreatorMethodName"(mwItem, isUpdate, null, *methodArgs)
                    addEntityToExtCollection(extEntity, propName, newExtEntity)
                }
            }
        }
    }

    private static void addEntityToExtCollection(Map extEntity, String propName, Map newExtEntity) {
        def extEntityCollection = extEntity[propName] as List<Map<String, ?>>
        if (!extEntityCollection) {
            extEntityCollection = []
            extEntity[propName] = extEntityCollection as List<Map<String, ?>>
        }
        extEntityCollection.add(newExtEntity)
    }
}
