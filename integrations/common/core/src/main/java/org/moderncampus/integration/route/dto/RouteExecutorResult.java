package org.moderncampus.integration.route.dto;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonPropertyOrder({"results", "meta"})
@ToString
public class RouteExecutorResult {

    Object results;

    Meta meta;

    @Getter
    @Setter
    @ToString
    public static class Meta {

        Map<String, Object> metaProperties;

        RouteExecResultTypeCode resultTypeCode;

        Exception exception;
    }

}
