package org.moderncampus.integration.route.support;

import static org.moderncampus.integration.route.support.RouteSupport.extractPropertiesForResult;
import static org.moderncampus.integration.route.support.RouteSupport.mapToRouteExecutorMeta;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.route.dto.RouteExecResultTypeCode;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.springframework.stereotype.Component;

@Component
public class DefaultRouteResultMapper implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        Object returnObj = exchange.getMessage().getBody();
        RouteExecutorResult executionResult = new RouteExecutorResult();
        executionResult.setResults(returnObj);
        RouteExecutorResult.Meta meta = mapToRouteExecutorMeta(RouteExecResultTypeCode.SUCCESS,
                extractPropertiesForResult(exchange), null);
        executionResult.setMeta(meta);
        exchange.getMessage().setBody(executionResult);
    }
}
