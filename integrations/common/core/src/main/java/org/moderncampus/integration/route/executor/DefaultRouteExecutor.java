package org.moderncampus.integration.route.executor;

import static org.moderncampus.integration.route.Constants.ROUTE_HEADERS;
import static org.moderncampus.integration.route.Constants.ROUTE_HEADER_PREFIX;
import static org.moderncampus.integration.route.support.RouteSupport.buildDirectRouteURI;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.route.dto.RouteExecutorRequest;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.dto.RouteInstance;
import org.moderncampus.integration.route.support.IRouteIdResolver;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultRouteExecutor implements IRouteExecutor {

    private static final String REQUEST_NOT_ROUTABLE = "Request is not routable. Route identifier not found";

    private static final String INVALID_ROUTE_ID = "Request is not routable. Invalid Route identifier";

    CamelContext context;

    IRouteIdResolver routeIdResolver;

    ProducerTemplate producerTemplate;

    @Override
    public RouteExecutorResult execute(RouteInstance routeInstance) throws Exception {
        if (routeInstance.getId() == null) {
            throw new RuntimeException(REQUEST_NOT_ROUTABLE);
        }
        String routeId = routeIdResolver.resolveRouteId(routeInstance.getId());
        if (routeId == null) {
            throw new RuntimeException(REQUEST_NOT_ROUTABLE);
        }
        RouteExecutorRequest request = routeInstance.getRequest();
        Map<String, Object> metaProperties = Optional.ofNullable(request.getRequestMeta())
                .map(RouteExecutorRequest.Meta::getMetaProperties)
                .orElse(new HashMap<>());

        String routeURI = buildDirectRouteURI(routeId, "timeout=1s");
        Endpoint endpoint = context.getEndpoint(routeURI);
        Exchange exchange = getCurrentRouteExchange(metaProperties).orElse(
                endpoint.createExchange(ExchangePattern.InOut));
        populateExchange(exchange, request.getData(), metaProperties);
        exchange = producerTemplate.send(routeURI, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();//Unhandled exceptions are thrown by default
        }
        return exchange.getMessage().getBody(RouteExecutorResult.class);
    }

    private Optional<Exchange> getCurrentRouteExchange(Map<String, Object> metaProperties) {
        return Optional.ofNullable(metaProperties.get(Constants.CURRENT_EXCHANGE)).map(Exchange.class::cast);
    }

    private void populateExchange(Exchange exchange, Object data, Map<String, Object> metaProperties) {
        exchange.getMessage().setBody(data);
        Map<String, String> routeHeaders = (Map<String, String>) metaProperties.getOrDefault(ROUTE_HEADERS, null);
        Optional.ofNullable(routeHeaders)
                .ifPresent(headers -> headers.forEach(
                        (key, value) -> exchange.getMessage().setHeader(ROUTE_HEADER_PREFIX + key, value)));
        metaProperties.forEach((key, value) -> {
            if (!ROUTE_HEADERS.equals(key)) {
                exchange.setProperty(key, value);
            }
        });
    }
}
