package org.moderncampus.integration.component.endpoint.config;

import org.moderncampus.integration.system.IntegrationSystem;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class MCDestinyAsyncEventEndpointConfig {

    public static final String ASYNC_EVENT_ENDPOINT_CONFIG = "asyncEventEndpointConfig";
    public static final String SOURCE_SYSTEM_PARAM = "asyncEventEndpointConfig.sourceSystem";
    public static final String DESTINY_ENTITY_PARAM = "asyncEventEndpointConfig.destinyEntity";
    public static final String ACTION_PARAM = "asyncEventEndpointConfig.action";
    public static final String EVENT_ID_PARAM = "asyncEventEndpointConfig.eventId";
    IntegrationSystem sourceSystem;

    String destinyEntity;

    String action;

    String eventId;
}
