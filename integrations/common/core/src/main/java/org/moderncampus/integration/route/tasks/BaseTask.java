package org.moderncampus.integration.route.tasks;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.route.tasks.api.ITask;
import org.moderncampus.integration.route.tasks.helper.TaskSupport;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.route.tasks.result.TaskOutput;

public abstract class BaseTask implements ITask, Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        TaskOutput output = execute(TaskSupport.constructTaskInput(exchange));
        TaskSupport.postProcessTaskOutput(this, exchange, output);
    }

    @Override
    public String identifier() {
        return this.getClass().getSimpleName();
    }

    protected TaskOutput taskOutput(Object data) {
        TaskOutput output = new TaskOutput();
        output.setData(data);
        return output;
    }

    protected Exchange getExchange(TaskInput taskInput) {
        return (Exchange) taskInput.getContext();
    }
}
