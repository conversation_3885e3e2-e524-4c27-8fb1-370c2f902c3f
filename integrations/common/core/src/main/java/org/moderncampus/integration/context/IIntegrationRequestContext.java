package org.moderncampus.integration.context;

public interface IIntegrationRequestContext {

    void setSchool(String school);

    void setSourceSystem(String sourceSystem);

    void setDestSystem(String destSystem);

    void setVersion(String version);

    void setContextPath(String contextPath);

    String getSchool();

    String getSourceSystem();

    String getDestSystem();

    String getVersion();

    String getContextPath();

}
