package org.moderncampus.integration.route.support;

import static org.moderncampus.integration.constants.Constants.INTEGRATION_PAGINATION_PARAMS;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.helper.PagedListIterator;
import org.moderncampus.integration.helper.Pagination;
import org.moderncampus.integration.util.CheckedFunction;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class ExtReadEndpointPaginator<T> {

    ProducerTemplate template;
    CamelContext camelContext;
    Class<?> responseClass;

    protected CheckedFunction<Exchange, Iterator<T>> getIteratorFunction(String routeURI) {
        return (endpointExchange) -> {
            Pagination pagination = (Pagination) endpointExchange.getProperty(INTEGRATION_PAGINATION_PARAMS);
            mapPaginationParamsInRequest(endpointExchange, pagination);
            Endpoint endpoint = camelContext.getEndpoint(routeURI);
            endpointExchange = template.send(routeURI, endpointExchange);
            if (endpointExchange.getException() != null) {
                throw endpointExchange.getException();
            }
            T response = (T) endpointExchange.getMessage().getBody(responseClass);
            Collection<T> responseCollection = mapResponseToCollection(response);
            updatePaginationParamsFromResp(responseCollection, endpointExchange, pagination);
            return responseCollection.iterator();
        };
    }

    public List<T> invokeReadAllEndpoint(String routeURI, Exchange exchange, boolean copyExchange)
            throws Exception {
        Exchange newExchange = copyExchange ? exchange.copy() : exchange;
        PagedListIterator<T> iterator = invokePaginatedReadEndpoint(routeURI, newExchange);
        List<T> results = new ArrayList<>();
        while (iterator.hasNext()) {
            results.add(iterator.next());
        }
        return results;
    }

    protected PagedListIterator<T> invokePaginatedReadEndpoint(String routeURI, Exchange exchange)
            throws Exception {
        return new PagedListIterator<>(getInitialPaginationInst(routeURI, exchange), exchange,
                getIteratorFunction(routeURI));
    }

    protected abstract Pagination getInitialPaginationInst(String routeURI, Exchange exchange);

    protected abstract void updatePaginationParamsFromResp(Collection<T> responseCollection, Exchange endpointExchange,
            Pagination pagination) throws Exception;

    protected abstract Collection<T> mapResponseToCollection(T response);

    protected abstract void mapPaginationParamsInRequest(Exchange endpointExchange, Pagination pagination);
}
