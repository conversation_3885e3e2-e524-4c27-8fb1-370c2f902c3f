package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.route.Constants.*;
import static org.moderncampus.integration.route.support.RouteSupport.buildDirectRouteURI;

import java.util.List;
import java.util.Set;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;
import org.moderncampus.integration.route.tasks.BaseTask;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter(AccessLevel.PROTECTED)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BaseRouteBuilder extends RouteBuilder {

    protected static final String BEAN_VALIDATOR_URI = "bean-validator://x";

    protected static final String METHOD_POST = "POST";
    protected static final String METHOD_PUT = "PUT";
    protected static final String METHOD_DELETE = "DELETE";

    protected static final String $_NULL = "${null}";
    protected static final String $_BODY = "${body}";

    protected static final String API_RESOURCES_KEY = "APIResources";
    protected static final String RESOURCE_ID = "resourceId";
    protected static final String METHOD_ASSOCIATION_PRE_FETCH_LIST = "associationPreFetchList";

    protected static final String BODY_ID_EXPR_REF = "${body.id}";
    protected static final String RESOURCE_ID_EXPR_REF = "${exchangeProperty.resourceId}";
    protected static final String ORIGINAL_BODY_EXPR_REF = "${exchange.unitOfWork.originalInMessage.getBody()}";
    protected static final String CONVERT_BODY_STRING_EXPR_REF = "${bodyAs(java.lang.String)}";


    Set<String> ids;

    List<? extends BaseTask> tasks;

    public BaseRouteBuilder(String id, List<? extends BaseTask> tasks) {
        this.ids = Set.of(id);
        this.tasks = tasks;
    }

    public BaseRouteBuilder(String id) {
        this.ids = Set.of(id);
        this.tasks = null;
    }

    protected void buildRouteActions(RouteDefinition routeDefinition) {
        tasks.forEach(routeDefinition::process);
    }

    protected void setupExceptionHandler() {
        onException(Exception.class).bean(DEFAULT_ROUTE_ERROR_HANDLER_BEAN_NAME).handled(true);
    }

    protected void buildPostRouteActions(RouteDefinition routeDefinition) {
    }

    public boolean isValidate() {
        return false;
    }

    protected String useCase() {
        return null;
    }

    @Override
    public void configure() throws Exception {
        setupExceptionHandler();
        for (String id : ids) {
            String routeUri = buildDirectRouteURI(id);
            RouteDefinition routeDefinition = from(routeUri).routeId(id);
            if (isValidate()) {
                routeDefinition.bean(DEFAULT_ROUTE_VALIDATOR_BEAN_NAME);
            }
            buildRouteActions(routeDefinition);
            routeDefinition.bean(DEFAULT_ROUTE_RESULT_MAPPER_BEAN_NAME);
            buildPostRouteActions(routeDefinition);
            routeDefinition.end();
        }
    }
}
