package org.moderncampus.integration.component.endpoint.producer;

import static org.moderncampus.integration.constants.Constants.HTTP;
import static org.moderncampus.integration.constants.Constants.HTTPS;
import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;

import java.util.Optional;

import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.apache.camel.support.DefaultProducer;
import org.apache.camel.util.ObjectHelper;
import org.moderncampus.integration.component.endpoint.MCDestinyEndpoint;
import org.moderncampus.integration.component.endpoint.auth.MCDestinyWSSessionIdRetriever;
import org.moderncampus.integration.component.endpoint.auth.MCDestinyWSSessionInfo;
import org.moderncampus.integration.component.endpoint.config.IMCDestinyConnectionConfiguration;
import org.moderncampus.integration.component.endpoint.config.MCDestinyAsyncEventEndpointConfig;
import org.moderncampus.integration.component.endpoint.config.MCDestinyEndpointConfiguration;
import org.moderncampus.integration.exception.ApplicationException;

public class MCDestinyAsyncEventWSProducer extends DefaultProducer {

    static final String DESTINY_SESSION_HEADER = "sessionId";

    static final String REST_COMPONENT = "rest";

    static final String REST_COMPONENT_URI_TEMPLATE = "%s:%s";

    static final String REST_ENDPOINT_URL_TEMPLATE = "%s://%s/webservice/EventREST";

    static final String ASYNC_RESPONSE_HANDLER_PATH = "/async/events/%s/%s/%s/%s";

    static final String HOST_PARAM = "host=";

    static final String POST = "post";

    static final String AMPERSAND = "&";

    static final String BINDING_MODE_JSON = "bindingMode=json";

    static final String ACCESS_DENIED = "Access denied.";

    static final String SYS_0002 = "SYS0002";

    static final String ERR_INVALID_SESSION_INFO = "Unable to login to Destiny WS: Invalid Destiny Session Info returned";

    public MCDestinyAsyncEventWSProducer(Endpoint endpoint) {
        super(endpoint);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        try {
            processRequest(exchange, false);
        } catch (Exception e) {
            if (e instanceof HttpOperationFailedException httpOperationFailedException
                    && httpOperationFailedException.getHttpResponseCode() == 400
                    && httpOperationFailedException.getResponseBody().contains(ACCESS_DENIED)
                    && httpOperationFailedException.getResponseBody()
                    .contains(SYS_0002)) {//Force refresh potentially stale session
                exchange.setException(null);
                processRequest(exchange, true);
                return;
            }
            throw e;
        }
    }

    private void processRequest(Exchange exchange, boolean forceRefreshSession) throws Exception {
        if (getEndpoint() instanceof MCDestinyEndpoint endpoint) {
            Message message = exchange.getMessage();
            MCDestinyEndpointConfiguration endpointConfiguration = endpoint.getEndpointConfiguration();
            IMCDestinyConnectionConfiguration connectionConfig = endpointConfiguration.getConnectionConfig();
            MCDestinyWSSessionIdRetriever sessionIdRetriever = endpointConfiguration.getSessionIdRetriever();
            MCDestinyAsyncEventEndpointConfig asyncEventEndpointConfig = endpointConfiguration.getAsyncEventEndpointConfig();
            ObjectHelper.notNull(asyncEventEndpointConfig,
                    MCDestinyAsyncEventEndpointConfig.ASYNC_EVENT_ENDPOINT_CONFIG);
            ObjectHelper.notNull(asyncEventEndpointConfig.getEventId(),
                    MCDestinyAsyncEventEndpointConfig.EVENT_ID_PARAM);
            ObjectHelper.notNull(asyncEventEndpointConfig.getDestinyEntity(),
                    MCDestinyAsyncEventEndpointConfig.DESTINY_ENTITY_PARAM);
            ObjectHelper.notNull(asyncEventEndpointConfig.getSourceSystem(),
                    MCDestinyAsyncEventEndpointConfig.SOURCE_SYSTEM_PARAM);
            ObjectHelper.notNull(asyncEventEndpointConfig.getAction(), MCDestinyAsyncEventEndpointConfig.ACTION_PARAM);
            message.setHeader(DESTINY_SESSION_HEADER,
                    getD1SessionId(sessionIdRetriever, connectionConfig, forceRefreshSession));
            message
                    .setHeader(MCDestinyAsyncEventEndpointConfig.EVENT_ID_PARAM, asyncEventEndpointConfig.getEventId());
            String httpMethod = Optional.ofNullable(endpointConfiguration.getHttpMethod()).orElse(POST);
            String path = String.format(ASYNC_RESPONSE_HANDLER_PATH,
                    asyncEventEndpointConfig.getSourceSystem().getName(),
                    asyncEventEndpointConfig.getDestinyEntity(), asyncEventEndpointConfig.getAction(),
                    "{" + MCDestinyAsyncEventEndpointConfig.EVENT_ID_PARAM
                            + "}");//Escape the eventId from incorrect parsing by the REST component via component's path parameter support
            String restComponentUri = buildRouteURI(REST_COMPONENT,
                    String.format(REST_COMPONENT_URI_TEMPLATE, httpMethod,
                            path),
                    HOST_PARAM + String.format(REST_ENDPOINT_URL_TEMPLATE,
                            connectionConfig.isUseHttp() ? HTTP : HTTPS,
                            connectionConfig.getHost()) + AMPERSAND
                            + BINDING_MODE_JSON);
            exchange = endpoint.getProducerTemplate().send(restComponentUri, exchange);
            Exception e = exchange.getException();
            if (e != null) {
                throw e;
            }
        }
    }

    private String getD1SessionId(MCDestinyWSSessionIdRetriever sessionIdRetriever,
            IMCDestinyConnectionConfiguration connectionConfig, boolean forceRefresh) throws Exception {
        return Optional.ofNullable(sessionIdRetriever.getSessionInfo(connectionConfig, forceRefresh)).map(
                MCDestinyWSSessionInfo::getSessionId).orElseThrow(() -> new ApplicationException(
                ERR_INVALID_SESSION_INFO));
    }

}
