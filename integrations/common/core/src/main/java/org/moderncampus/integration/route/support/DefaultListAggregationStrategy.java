package org.moderncampus.integration.route.support;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Exchange;
import org.springframework.stereotype.Component;

@Component
public class DefaultListAggregationStrategy implements AggregationStrategy {

    @Override
    public Exchange aggregate(Exchange oldExchange, Exchange newExchange) {
        if (Objects.isNull(oldExchange)) {
            return getExchange(newExchange);
        }

        List<Object> oldList = buildListFrom(oldExchange);
        List<Object> newList = buildListFrom(newExchange);
        List<Object> combined = Stream.concat(oldList.stream(), newList.stream()).toList();

        if (Objects.nonNull(oldExchange.getMessage())) {
            oldExchange.getMessage().setBody(combined);
        }

        return oldExchange;
    }

    private List<Object> buildListFrom(Exchange exchange) {
        List<Object> list = new ArrayList<>();
        if (isValidExchange(exchange)) {
            Object body = exchange.getMessage().getBody();
            if (body instanceof List) {
                list.addAll((List<Object>) body);
            } else {
                list.add(body);
            }
        }
        return list;
    }

    private Exchange getExchange(Exchange exchange) {
        if (isValidExchange(exchange)) {
            List<Object> list = buildListFrom(exchange);
            exchange.getMessage().setBody(list, List.class);
        }
        return exchange;
    }

    private boolean isValidExchange(Exchange exchange) {
        return Objects.nonNull(exchange) && Objects.nonNull(exchange.getMessage()) && Objects.nonNull(
                exchange.getMessage().getBody());
    }
}
