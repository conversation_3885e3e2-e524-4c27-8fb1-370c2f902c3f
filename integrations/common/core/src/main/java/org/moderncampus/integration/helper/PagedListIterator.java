package org.moderncampus.integration.helper;

import static org.moderncampus.integration.constants.Constants.INTEGRATION_PAGINATION_PARAMS;

import java.util.Iterator;

import org.apache.camel.Exchange;
import org.moderncampus.integration.util.CheckedFunction;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PagedListIterator<T> implements Iterator<T> {

    CheckedFunction<Exchange, Iterator<T>> nextIteratorProvider;

    Iterator<T> currentIterator;

    Pagination currentPage;

    final Exchange exchange;

    public PagedListIterator(Pagination initialPage, Exchange exchange,
            CheckedFunction<Exchange, Iterator<T>> nextIteratorProvider)
            throws Exception {
        this.currentPage = initialPage;
        this.exchange = exchange;
        this.nextIteratorProvider = nextIteratorProvider;
        this.exchange.setProperty(INTEGRATION_PAGINATION_PARAMS, currentPage);
        this.currentIterator = nextIteratorProvider.apply(exchange);
    }

    @Override
    public boolean hasNext() {
        if (currentIterator.hasNext()) {
            return true;
        } else {
            try {
                currentPage = currentPage.next();
                if (currentPage == null) {
                    // stop if reached total pages or maximum page number
                    return false;
                }
                exchange.setProperty(INTEGRATION_PAGINATION_PARAMS, currentPage);
                currentIterator = nextIteratorProvider.apply(exchange);
            } catch (Exception e) {
                log.info("Error occurred while iterating: {}", e.getMessage());
                return false;
            }
            return currentIterator.hasNext();
        }
    }

    @Override
    public T next() {
        return currentIterator.next();
    }
}
