package org.moderncampus.integration.health;

import java.util.Map;

import org.apache.camel.health.HealthCheckResultBuilder;
import org.apache.camel.impl.health.AbstractHealthCheck;
import org.moderncampus.integration.context.IntegrationRequestContext;

public abstract class IntEndpointHealthCheck extends AbstractHealthCheck {

    IntegrationRequestContext requestContext;

    protected IntEndpointHealthCheck(String group, String id, IntegrationRequestContext requestContext) {
        super(group, id);
        this.requestContext = requestContext;
    }

    @Override
    protected void doCall(HealthCheckResultBuilder builder, Map<String, Object> options) {
        setupHealthCheckBuilder(builder);
        doHealthCheck(builder, options);
    }

    protected void setupHealthCheckBuilder(HealthCheckResultBuilder builder) {
        builder.detail("sourceSystem", requestContext.getSourceSystem());
        builder.detail("schoolId", requestContext.getSchool());
    }

    protected abstract void doHealthCheck(HealthCheckResultBuilder builder, Map<String, Object> options);
}
