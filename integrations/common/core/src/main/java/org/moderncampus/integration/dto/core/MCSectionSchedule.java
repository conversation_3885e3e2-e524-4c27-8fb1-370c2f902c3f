package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetTime;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSectionSchedule extends BaseDTO {

    String section;

    String instructionalMethod;

    LocalDate startDate;

    @Schema(type = "string",
            format = "time",
            description = "Time values without timezone offset will be assumed to be UTC")
    OffsetTime startTime;

    LocalDate endDate;

    @Schema(type = "string",
            format = "time",
            description = "Time values without timezone offset will be assumed to be UTC")
    OffsetTime endTime;

    String recurrenceType;

    Integer recurrenceInterval;

    Long recurrenceByDayOfMo;

    Long recurrenceByDayOfWk;

    LocalDate recurrenceUntil;

    Boolean dayMonday;

    Boolean dayTuesday;

    Boolean dayWednesday;

    Boolean dayThursday;

    Boolean dayFriday;

    Boolean daySaturday;

    Boolean daySunday;

    @Schema(type = "number",
            pattern = "[0-9]{1,2}.?[0-9]{0,2}]")
    BigDecimal workload;

    String room;

    List<MCSectionScheduleApproval> approvalOverrides;

    List<MCSectionScheduleInstructor> instructors;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCSectionScheduleApproval extends BaseDTO {

        String type;
        String approver;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCSectionScheduleInstructor extends BaseDTO {

    }

}
