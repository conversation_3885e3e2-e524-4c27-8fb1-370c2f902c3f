package org.moderncampus.integration.dto.core;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCAcademicPeriod extends BaseDTO {

    String code;

    String name;

    LocalDateTime startDate;

    LocalDateTime endDate;

    String partsOfTerm;

    List<LocalDate> censusDates;

    String year;

}
