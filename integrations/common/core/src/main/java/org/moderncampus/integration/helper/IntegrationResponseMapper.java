package org.moderncampus.integration.helper;

import static org.moderncampus.integration.constants.Constants.*;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.route.dto.RouteExecResultTypeCode;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.springframework.stereotype.Component;

@Component
public class IntegrationResponseMapper {

    static Set<String> defaultMappedMetaProps = new HashSet<>() {{
        add(BREADCRUMB_ID);
        add(PAGE_SIZE);
        add(PAGE_NUM);
        add(TOTAL_SIZE);
        add(PAGE_OFFSET);
        add(EXECUTED_ROUTE_RESULTS);
    }};

    protected <T> IntegrationResponse<T> mapRouteResult(RouteExecutorResult result, Set<String> mappedMetaProperties,
            boolean continueOnError) throws Exception {
        IntegrationResponse<T> response = new IntegrationResponse<>();
        response.setData((T) result.getResults());
        RouteExecutorResult.Meta meta = result.getMeta();
        if (mappedMetaProperties == null) {
            mappedMetaProperties = defaultMappedMetaProps;
        } else {
            mappedMetaProperties = new HashSet<>(mappedMetaProperties);
            mappedMetaProperties.addAll(defaultMappedMetaProps);
        }
        if (meta != null && RouteExecResultTypeCode.SUCCESS.equals(meta.getResultTypeCode())
                && mappedMetaProperties != null) {
            mapResponseMeta(mappedMetaProperties, response, meta);
        } else if (meta != null && RouteExecResultTypeCode.FAILURE.equals(meta.getResultTypeCode())) {
            if (meta.getException() != null && !continueOnError) {
                throw meta.getException();
            }
            mapResponseMeta(mappedMetaProperties, response, meta);
        }
        return response;
    }

    public <T> IntegrationResponse<T> mapRouteResult(RouteExecutorResult result, Set<String> mappedMetaProperties)
            throws Exception {
        return mapRouteResult(result, mappedMetaProperties, false);
    }

    public <T> IntegrationResponse<T> mapRouteResult(RouteExecutorResult result) throws Exception {
        return mapRouteResult(result, null);
    }

    private <T> void mapResponseMeta(Set<String> mappedMetaProperties, IntegrationResponse<T> response,
            RouteExecutorResult.Meta meta) {
        IntegrationResponse.IntegrationResponseMeta responseMeta = new IntegrationResponse.IntegrationResponseMeta();
        responseMeta.setMetaProps(Optional.ofNullable(meta.getMetaProperties()).map((props -> {
            props.keySet().removeIf(key -> !mappedMetaProperties.contains(key));
            return props;
        })).orElse(null));
        response.setMeta(responseMeta);
    }
}
