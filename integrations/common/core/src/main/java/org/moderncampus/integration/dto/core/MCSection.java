package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSection extends BaseDTO {

    String shortTitle;

    String longTitle;

    String description;

    String shortDescription;

    LocalDate startOn;

    LocalDate endOn;

    String code;

    String number;

    String academicPeriod;

    List<LocalDate> censusDates;

    String course;

    List<String> courseCategories;

    BigDecimal minCredits;

    BigDecimal maxCredits;

    BigDecimal minCeu;

    BigDecimal maxCeu;

    String site;

    List<String> academicLevels;

    List<String> gradeSchemes;

    List<MCSectionInstructionalMethod> instructionalMethods;

    BigDecimal contactHours;

    BigDecimal labHours;

    BigDecimal lectureHours;

    BigDecimal otherHours;

    String instructionalDeliveryMethod;

    String status;

    BigDecimal duration;

    String durationUnits;

    Integer maxEnrollment;

    Boolean crossListed;

    List<MCInstitutionUnit> owningInstitutionUnits;

    BigDecimal billingHours;

    String creditType;

    BigDecimal creditsIncrement;

    String reportingAcademicPeriod;

    String instructionalPlatform;

    List<MCCourseLevel> courseLevels;

    String billingMethod;

    List<MCAlternateId> otherIds;

    List<? extends MCSectionInstructorAssignment> instructors;

    List<? extends MCSectionCrossList> crossLists;

    List<? extends MCSectionSchedule> sectionSchedules;

    String catalogDisplay;

    MCSectionCrossListGroup crossListGroup;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCSectionInstructionalMethod extends BaseDTO {

        @Override
        @Schema(description = "The global identifier for the Institution Unit.")
        public String getId() {
            return super.getId();
        }

        BigDecimal hours;

        String hoursInterval;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCSectionCrossListGroup extends BaseDTO {

    }
}
