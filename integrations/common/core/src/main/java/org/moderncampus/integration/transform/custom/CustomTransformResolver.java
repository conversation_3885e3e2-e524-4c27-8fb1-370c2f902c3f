package org.moderncampus.integration.transform.custom;

import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.POST;
import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.PRE;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType;
import org.moderncampus.integration.util.DynamicClassLoader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;

@Component
public class CustomTransformResolver {

    public static final String CUSTOMIZATION = "customization";
    private final Map<String, Pair<Object, Long>> customTransformMap = new ConcurrentHashMap<>();
    private final Map<String, CustomTransformMetadata> customTransformMetadataMap = new ConcurrentHashMap<>();

    private final IIntegrationRequestContext requestContext;

    @Value("${customization.transform.classpath:#{null}}")
    private String classLoaderPath;

    public CustomTransformResolver(IIntegrationRequestContext requestContext) {
        this.requestContext = requestContext;
    }

    public List<CustomTransformMetadata> findApplicableTransforms(
            Class<? extends ITransformer<?, ?>> coreTransformClass) {

        return Optional.ofNullable(findCustomTransform())
                .map(customTransform -> listTransformMetadata(coreTransformClass, customTransform))
                .orElse(Collections.emptyList());
    }

    private List<CustomTransformMetadata> listTransformMetadata(
            Class<? extends ITransformer<?, ?>> coreTransformClass, Object customTransform) {

        return Stream.of(PRE, POST)
                .map(applicabilityType -> customTransformMetadataMap.getOrDefault(
                        getMetadataKey(coreTransformClass, applicabilityType),
                        createTransformMetadata(coreTransformClass, applicabilityType,
                                customTransform)))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private CustomTransformMetadata createTransformMetadata(
            Class<? extends ITransformer<?, ?>> coreTransformClass,
            ApplicabilityType applicabilityType,
            Object customTransform) {

        Method customTransformMethod = findApplicableTransformMethod(applicabilityType,
                coreTransformClass, customTransform);
        if (customTransformMethod == null) {
            return null;
        }

        String methodPath = getMetadataKey(coreTransformClass, applicabilityType);
        CustomTransformMetadata transformMetadata = new CustomTransformMetadata(
                customTransform, customTransformMethod, applicabilityType);
        customTransformMetadataMap.putIfAbsent(methodPath, transformMetadata);
        return transformMetadata;
    }

    private Object findCustomTransform() {
        String path = getPackagePath();
        if (shouldLoadCustomTransform(path)) {
            loadCustomTransform(path);
        }
        return Optional.ofNullable(customTransformMap.get(path))
                .map(Pair::getKey)
                .orElse(null);
    }

    private boolean shouldLoadCustomTransform(String path) {
        if (!customTransformMap.containsKey(path)) {
            return true;
        }
        Pair<Object, Long> customTransformEntry = customTransformMap.get(path);
        Long lastModifiedTime = getFolderLastModifiedTime(path);
        return !customTransformEntry.getValue().equals(lastModifiedTime);
    }

    private Long getFolderLastModifiedTime(String path) {
        try {
            BasicFileAttributes fileAttributes = Files.readAttributes(Paths.get(path),
                    BasicFileAttributes.class);
            return fileAttributes.lastModifiedTime().toMillis();
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }

    private void loadCustomTransform(String path) {
        try {
            File folder = new File(path);
            File[] files = folder.listFiles();

            if (files != null && files.length == 1) {
                File classFile = files[0];

                String fileName = classFile.getName();
                String className = getPackageName() + "." + FilenameUtils.removeExtension(fileName);
                DynamicClassLoader classLoader = new DynamicClassLoader(getPackagePath());
                Class<?> loadedClass = classLoader.loadClass(className);

                Object customTransform = loadedClass.getDeclaredConstructor().newInstance();
                Long lastModifiedTime = getFolderLastModifiedTime(path);

                customTransformMap.put(path, Pair.of(customTransform, lastModifiedTime));
                cleanMetadataMap(path);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void cleanMetadataMap(String path) {
        for (String key : customTransformMetadataMap.keySet()) {
            if (key.contains(path)) {
                customTransformMetadataMap.remove(key);
            }
        }
    }

    private Method findApplicableTransformMethod(ApplicabilityType applicabilityType,
            Class<? extends ITransformer<?, ?>> coreTransformer, Object customTransformer) {
        if (customTransformer == null) {
            return null;
        }

        Method[] methods = customTransformer.getClass().getDeclaredMethods();
        return Stream.of(methods)
                .filter(method -> isApplicable(applicabilityType, coreTransformer, method))
                .findFirst().orElse(null);
    }

    private boolean isApplicable(ApplicabilityType applicabilityType,
            Class<? extends ITransformer<?, ?>> coreTransformer, Method method) {

        return Optional.ofNullable(method.getDeclaredAnnotation(CustomTransform.class))
                .filter(metaData -> metaData.applicability().equals(applicabilityType))
                .filter(metaData -> metaData.applyTo().equals(coreTransformer))
                .isPresent();
    }

    private String getPackageName() {
        return Joiner.on(".")
                .join(CUSTOMIZATION, requestContext.getSourceSystem(), requestContext.getSchool(),
                        requestContext.getDestSystem());
    }

    private String getPackagePath() {
        return Joiner.on("/")
                .join(classLoaderPath, CUSTOMIZATION, requestContext.getSourceSystem(),
                        requestContext.getSchool(), requestContext.getDestSystem());
    }

    private String getMetadataKey(Class<? extends ITransformer<?, ?>> coreTransformClass,
            ApplicabilityType applicabilityType) {
        return Joiner.on("/")
                .join(getPackagePath(), coreTransformClass.getName(), applicabilityType);
    }
}
