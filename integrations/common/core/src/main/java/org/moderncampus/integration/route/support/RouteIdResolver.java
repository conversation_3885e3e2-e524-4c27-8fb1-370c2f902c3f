package org.moderncampus.integration.route.support;

import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import java.util.List;

import org.apache.camel.CamelContext;
import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteIdResolver implements IRouteIdResolver {

    CamelContext camelContext;

    IIntegrationRequestContext requestContext;

    @Override
    public String resolveRouteId(IRouteId routeId) {
        String version = requestContext.getVersion();
        String sourceSystem = requestContext.getSourceSystem();
        String schoolId = requestContext.getSchool();
        String destSystem = requestContext.getDestSystem();
        String contextPath = routeId.getContextPath();

        if (isInvalidRoute(version, sourceSystem, schoolId, destSystem, contextPath)) {
            return null;
        }

        List<String> validRouteIds = getValidIds(version, sourceSystem, schoolId, destSystem, contextPath);

        if (!validRouteIds.contains(routeId.getId())) {
            return null;
        }

        return getRouteKey(validRouteIds);
    }

    private static boolean isInvalidRoute(String version, String sourceSystem,
            String schoolId, String destSystem, String contextPath) {
        return StringUtils.isBlank(version) || StringUtils.isBlank(sourceSystem) ||
                StringUtils.isBlank(schoolId) || StringUtils.isBlank(destSystem) ||
                StringUtils.isBlank(contextPath);
    }

    private String getRouteKey(List<String> validRouteIds) {
        String lookUpKey = validRouteIds.get(0);

        for (int i = 1; i < 4; i++) {
            if (hasRouteForKey(lookUpKey)) {
                return lookUpKey;
            }

            lookUpKey = validRouteIds.get(i);
        }

        return hasRouteForKey(lookUpKey) ? lookUpKey : null;
    }

    private List<String> getValidIds(String version, String sourceSystem, String schoolId,
            String destSystem, String contextPath) {
        return List.of(
                generateRouteId(new String[]{version, sourceSystem, schoolId, destSystem, contextPath}),
                generateRouteId(new String[]{version, schoolId, destSystem, contextPath}),
                generateRouteId(new String[]{version, sourceSystem, "*", destSystem, contextPath}),
                generateRouteId(new String[]{version, destSystem, contextPath}));
    }

    private boolean hasRouteForKey(String lookUpKey) {
        return camelContext.getRoute(lookUpKey) != null;
    }
}
