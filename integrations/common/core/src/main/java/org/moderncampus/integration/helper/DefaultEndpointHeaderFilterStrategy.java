package org.moderncampus.integration.helper;

import static org.moderncampus.integration.route.Constants.ROUTE_HEADER_PREFIX;

import org.apache.camel.http.base.HttpHeaderFilterStrategy;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Component;

@Component
public class DefaultEndpointHeaderFilterStrategy extends HttpHeaderFilterStrategy {

    protected static final String[] HEADER_FILTERS = ArrayUtils.addAll(CAMEL_FILTER_STARTS_WITH,
            new String[]{ROUTE_HEADER_PREFIX});

    @Override
    protected void initialize() {
        super.initialize();

        setOutFilterStartsWith(HEADER_FILTERS);
    }
}
