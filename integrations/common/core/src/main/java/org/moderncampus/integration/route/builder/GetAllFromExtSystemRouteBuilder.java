package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE;
import static org.moderncampus.integration.transform.support.CommonFunctions.stringJsonToCollectionConverter;

import java.util.Optional;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Processor;
import org.apache.camel.model.RouteDefinition;
import org.apache.camel.model.SplitDefinition;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class GetAllFromExtSystemRouteBuilder extends BaseRouteBuilder {

    static final String METHOD_READ_ALL_ENDPOINT = "invokeReadAllEndpoint";
    static final String METHOD_BUILD_SEARCH_CRITERIA = "buildSearchCriteria";

    Object transformer;
    String transformMethod;
    AggregationStrategy aggregationStrategy;
    @NonFinal
    String useCase;
    @NonFinal
    Object searchCriteriaBuilder;
    @NonFinal
    boolean getAllResults;
    @NonFinal
    Object readEPPaginator;
    @NonFinal
    Processor responsePaginationMapper;
    @NonFinal
    Processor responseSplitter;
    @NonFinal
    Processor preTransformProcessor;

    protected GetAllFromExtSystemRouteBuilder(String id, Object transformer, String transformMethod,
            AggregationStrategy aggregationStrategy, Object searchCriteriaBuilder, Object readEPPaginator,
            Processor responsePaginationMapper, Processor responseSplitter, Processor preTransformProcessor,
            String useCase) {
        super(id);
        this.transformer = transformer;
        this.transformMethod = transformMethod;
        this.aggregationStrategy = aggregationStrategy;
        this.searchCriteriaBuilder = searchCriteriaBuilder;
        this.getAllResults = readEPPaginator != null;
        this.readEPPaginator = readEPPaginator;
        this.responsePaginationMapper = responsePaginationMapper;
        this.responseSplitter = Optional.ofNullable(responseSplitter).orElse(stringJsonToCollectionConverter);
        this.preTransformProcessor = preTransformProcessor;
        this.useCase = useCase;
    }

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.setProperty(INTEGRATION_USE_CASE, simple(useCase()));
        preFetchAssociations(routeDefinition);
        handleSearchCriteria(routeDefinition);
        routeDefinition.setBody().simple($_NULL);
        if (isGetAllResults()) {
            routeDefinition.bean(getReadEPPaginator(),
                    METHOD_READ_ALL_ENDPOINT + "(" + getExtSystemGetAllRouteURI() + ",*,true)");
        } else {
            routeDefinition.toD(getExtSystemGetAllRouteURI());
            if (responseSplitter != null) {
                routeDefinition.process(responseSplitter);
            }
        }
        SplitDefinition splitDefinition = routeDefinition
                .split(body(), aggregationStrategy)
                .stopOnException(true);
        handlePreTransform(splitDefinition);
        splitDefinition.bean(transformer, transformMethod);
        if (!isGetAllResults()) {
            if (responsePaginationMapper != null) {
                routeDefinition.process(responsePaginationMapper);
            }
        }
    }

    protected abstract String getExtSystemGetAllRouteURI();

    protected abstract void preFetchAssociations(RouteDefinition routeDefinition);

    protected void handleSearchCriteria(RouteDefinition routeDefinition) {
        if (searchCriteriaBuilder != null) {
            routeDefinition.bean(searchCriteriaBuilder, METHOD_BUILD_SEARCH_CRITERIA);
        }
    }

    protected void handlePreTransform(SplitDefinition splitDefinition) {
        if (getPreTransformProcessor() != null) {
            splitDefinition.bean(getPreTransformProcessor());
        }
    }

    @Override
    protected String useCase() {
        return useCase;
    }
}
