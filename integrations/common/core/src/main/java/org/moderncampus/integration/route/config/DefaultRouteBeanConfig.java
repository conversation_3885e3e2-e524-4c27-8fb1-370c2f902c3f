package org.moderncampus.integration.route.config;

import org.apache.camel.CamelContext;
import org.apache.camel.spring.boot.CamelContextConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration
public class DefaultRouteBeanConfig {

    public static final String DEFAULT_TRACING_LOG_FORMAT = "%-4.4s [%-50.50s] [%s]";

    @Bean
    @Lazy(value = false)
    public CamelContextConfiguration contextConfiguration() {
        return new CamelContextConfiguration() {
            @Override
            public void beforeApplicationStart(CamelContext context) {
                context.setAllowUseOriginalMessage(true);
                context.setUseBreadcrumb(true);
                context.setTracing(true);
                context.setTracingLoggingFormat(DEFAULT_TRACING_LOG_FORMAT);
            }

            @Override
            public void afterApplicationStart(CamelContext camelContext) {
            }
        };
    }
}
