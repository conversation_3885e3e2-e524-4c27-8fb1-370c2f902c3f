package org.moderncampus.integration.health;

import java.util.Map;

import org.apache.camel.health.HealthCheckResultBuilder;
import org.moderncampus.integration.context.IntegrationRequestContext;
import org.moderncampus.integration.health.helper.HostAvailabilityCheck;

public class DefaultEndpointHostAvailabilityHealthCheck extends IntEndpointHealthCheck {

    String host;

    public DefaultEndpointHostAvailabilityHealthCheck(String group, String id,
            IntegrationRequestContext requestContext, String host) {
        super(group, id, requestContext);
        this.host = host;
    }

    @Override
    protected void doHealthCheck(HealthCheckResultBuilder builder, Map<String, Object> options) {
        HostAvailabilityCheck.isAvailable(host, 443,
                builder);
    }
}
