package org.moderncampus.integration.transform.custom;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.moderncampus.integration.transform.BaseTransformer;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface CustomTransform {

    Class<? extends BaseTransformer<?, ?>> applyTo();

    ApplicabilityType applicability();

    boolean isSkipCoreMethod() default false;

    enum ApplicabilityType {
        PRE, POST
    }
}
