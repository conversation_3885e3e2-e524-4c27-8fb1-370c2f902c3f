package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.route.Constants.*;
import static org.moderncampus.integration.route.support.RouteSupport.buildDirectRouteURI;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter(AccessLevel.PROTECTED)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BaseHealthCheckRouteBuilder extends RouteBuilder {

    String id;

    protected void setupExceptionHandler() {
        onException(Exception.class).bean(DEFAULT_ROUTE_ERROR_HANDLER_BEAN_NAME).handled(true);
    }

    protected void buildHealthRoute(RouteDefinition routeDefinition) {
    }

    @Override
    public void configure() throws Exception {
        setupExceptionHandler();
        String routeUri = buildDirectRouteURI(id);
        RouteDefinition routeDefinition = from(routeUri).routeId(id);
        buildHealthRoute(routeDefinition);
        routeDefinition.bean(DEFAULT_INT_HEALTH_CHECK_RESULT_MAPPER_BEAN_NAME)
                .bean(DEFAULT_ROUTE_RESULT_MAPPER_BEAN_NAME)
                .end();
    }
}
