package org.moderncampus.integration.dto.core;

import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCOrganization extends BaseDTO {

    MCOrganizationName name;

    List<MCRole> roles;

    List<? extends MCRoleCredential> credentials;

    List<? extends MCAddress> addresses;

    List<? extends MCPhone> phones;

    List<? extends MCEmail> emails;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCOrganizationName {

        String title;

    }

}
