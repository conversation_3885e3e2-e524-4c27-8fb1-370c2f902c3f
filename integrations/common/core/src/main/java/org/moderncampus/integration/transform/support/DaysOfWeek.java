package org.moderncampus.integration.transform.support;

import static java.util.Objects.isNull;

import lombok.Getter;
import lombok.experimental.Accessors;

@Accessors(fluent = true)
public enum DaysOfWeek {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY;


    private final static String FLAG_PROPERTY_PREFIX = "day";

    @Getter
    private final String flagName;

    DaysOfWeek(){
        this.flagName = FLAG_PROPERTY_PREFIX + this.name().charAt(0) + this.name().substring(1).toLowerCase();
    }

    public String lowerName() {
        return name().toLowerCase();
    }

    public static DaysOfWeek from(String day){
        if(isNull(day))
            return null;

        for(DaysOfWeek d : DaysOfWeek.values()){
            if(d.name().equalsIgnoreCase(day) || d.flagName().equalsIgnoreCase(day))
                return d;
        }
        return null;
    }
}
