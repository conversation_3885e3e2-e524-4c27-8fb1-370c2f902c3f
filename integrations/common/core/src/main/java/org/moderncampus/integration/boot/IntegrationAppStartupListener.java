package org.moderncampus.integration.boot;

import org.moderncampus.integration.tenant.env.TenantConfigInterceptPropertySource;
import org.moderncampus.integration.tenant.env.TenantConfigPropertySource;
import org.springframework.boot.context.event.ApplicationContextInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.StandardEnvironment;

public class IntegrationAppStartupListener implements
        ApplicationListener<ApplicationContextInitializedEvent> {

    @Override
    public void onApplicationEvent(ApplicationContextInitializedEvent event) {
        ConfigurableEnvironment environment = event.getApplicationContext().getEnvironment();
        environment.getPropertySources().addLast(new TenantConfigPropertySource());
        environment.getPropertySources()
                .addAfter(StandardEnvironment.SYSTEM_ENVIRONMENT_PROPERTY_SOURCE_NAME,
                        new TenantConfigInterceptPropertySource());
    }
}