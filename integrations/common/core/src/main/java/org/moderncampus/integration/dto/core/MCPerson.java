package org.moderncampus.integration.dto.core;

import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCPerson extends BaseDTO {

    List<MCPersonName> names;

    List<? extends MCEmail> emails;

    List<? extends MCAddress> addresses;

    List<? extends MCPhone> phones;

    LocalDate dateOfBirth;

    String gender;

    String genderIdentity;

    String pronouns;

    List<MCRole> roles;

    MCPersonEthnicity ethnicity;

    List<MCPersonRace> races;

    String veteranStatus;

    List<? extends MCRoleCredential> credentials;

    List<? extends MCPersonAlternativeCredential> alternativeCredentials;

    String citizenshipStatus;

    String citizenshipCountry;

    List<? extends MCPersonEmergencyContact> emergencyContacts;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCPersonName extends BaseDTO {

        Boolean preferred;

        MCCategoryType type;

        String title;

        String fullName;

        String firstName;

        String middleName;

        String lastName;

        String suffix;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCPersonEthnicity extends BaseDTO {

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCPersonRace extends BaseDTO {

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCPersonAlternativeCredential extends BaseDTO {

        String type;

        String value;

    }

}
