package org.moderncampus.integration.dto.cewd;

import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;
import org.moderncampus.integration.validation.IValidationGroups;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CewdPerson extends BaseDTO {

    @NotNull(message = "First name must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String firstName;

    @NotNull(message = "Last name must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String lastName;

    @NotNull(message = "Person Number must be defined", groups = IValidationGroups.IIntPersonUpdate.class)
    String number;

    String middleName;

    String genderCode;

    String salutation;

    LocalDate birthDate;

    String socialSecurityNum;

    @Valid
    @NotNull(message = "Address must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    @Size(min = 1, max = 1, message = "A single address must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    List<CewdAddress> addresses;

    @Valid
    @NotNull(message = "Email must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    @Size(min = 1, max = 1, message = "A single email must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    List<CewdEmail> emails;

    @Valid
    @NotNull(message = "Phone must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    @Size(min = 1, max = 1, message = "A single phone must be specified", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    List<CewdPhone> phones;

    @Getter(onMethod_ = {@Schema(hidden = true)})
    CewdPersonType personType;

    @Override
    @NotNull(message = "ID must be specified", groups = IValidationGroups.IIntPersonUpdate.class)
    public String getId() {
        return super.getId();
    }
}
