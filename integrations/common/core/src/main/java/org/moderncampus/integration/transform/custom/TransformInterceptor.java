package org.moderncampus.integration.transform.custom;

import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.POST;
import static org.moderncampus.integration.transform.custom.CustomTransform.ApplicabilityType.PRE;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

@Aspect
@Component
@RequiredArgsConstructor
public class TransformInterceptor {

    final CustomTransformResolver customTransformResolver;

    @Around("execution(* org.moderncampus.integration.transform.ITransformer.transform(..))")
    public Object interceptTransformMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        // Get the target object
        Object target = joinPoint.getTarget();

        if (!((ITransformer<?, ?>) target).isInterceptable()) {
            return joinPoint.proceed();
        }

        // Get the target class
        Class<? extends ITransformer<?, ?>> targetClass = (Class<? extends ITransformer<?, ?>>) target.getClass();

        List<CustomTransformMetadata> transforms = customTransformResolver.findApplicableTransforms(
                targetClass);
        CustomTransformMetadata preTransformMetadata = transforms.stream()
                .filter(metadata -> PRE.equals(metadata.applicabilityType()))
                .findFirst().orElse(null);

        CustomTransformMetadata postTransformMetadata = transforms.stream()
                .filter(metadata -> POST.equals(metadata.applicabilityType()))
                .findFirst().orElse(null);

        boolean isSkipCoreProcessing = isSkipCoreProcessing(preTransformMetadata,
                postTransformMetadata);

        Object result = invokeTransformMethod(preTransformMetadata, joinPoint, null);

        if (!isSkipCoreProcessing) {
            // Call the original method
            result = joinPoint.proceed();
        }

        result = invokeTransformMethod(postTransformMetadata, joinPoint, result);

        return result;
    }

    private boolean isSkipCoreProcessing(CustomTransformMetadata preTransformMetadata,
            CustomTransformMetadata postTransformMetadata) {

        boolean isSkipCoreProcessing = false;
        if (preTransformMetadata != null) {
            CustomTransform preTransformAnnotation = preTransformMetadata.method()
                    .getAnnotation(CustomTransform.class);
            isSkipCoreProcessing = preTransformAnnotation.isSkipCoreMethod();
        }
        if (postTransformMetadata != null && !isSkipCoreProcessing) {
            CustomTransform postTransformAnnotation =
                    postTransformMetadata.method().getAnnotation(CustomTransform.class);
            isSkipCoreProcessing = postTransformAnnotation.isSkipCoreMethod();
        }
        return isSkipCoreProcessing;
    }

    private Object invokeTransformMethod(CustomTransformMetadata customTransformMethod,
            ProceedingJoinPoint joinPoint, Object result)
            throws InvocationTargetException, IllegalAccessException {
        Object invokedResult = null;

        if (customTransformMethod != null) {
            // Get the method arguments
            Object[] args = joinPoint.getArgs();
            TransformContext transformContext = (TransformContext) args[0];
            if (result != null) {
                if (transformContext == null) {
                    transformContext = new TransformContext(new HashMap<>());
                }
                transformContext.setContextProp(Constants.ORIGINAL_TRANSFORM, result);
            }
            invokedResult = customTransformMethod.method()
                    .invoke(customTransformMethod.object(), transformContext, args[1]);
        }

        return invokedResult != null ? invokedResult : result;
    }

}
