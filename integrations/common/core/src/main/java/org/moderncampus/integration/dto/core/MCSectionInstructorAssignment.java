package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSectionInstructorAssignment extends BaseDTO {

    String sectionId;

    List<MCSectionInstructorSchedule> sectionSchedules;

    String instructorId;

    String instructionalMethod;

    String instructorRole;

    BigDecimal workloadHrs;

    BigDecimal percentageResponsible;

    LocalDate assignmentStartOn;

    LocalDate assignmentEndOn;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCSectionInstructorSchedule extends BaseDTO {

    }

}
