package org.moderncampus.integration.component.endpoint.auth;

import java.util.Date;

import org.moderncampus.integration.component.endpoint.config.IMCDestinyConnectionConfiguration;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MCDestinyWSSessionIdRetriever {

    MCDestinyWSSessionInfoCacheRetriever sessionInfoCacheRetriever;

    @CachePut(value = "d1WSSessionIdCache", key = "#root.args[0].host + '-' + #root.args[0].username", unless = "#result == null")
    public MCDestinyWSSessionInfo getSessionInfo(IMCDestinyConnectionConfiguration connectionConfig,
            boolean forceRefresh) throws Exception {
        if (forceRefresh) {
            return sessionInfoCacheRetriever.retrieveNewSessionInfo(connectionConfig);
        }
        MCDestinyWSSessionInfo sessionIdInfo = sessionInfoCacheRetriever.retrieveSessionInfo(connectionConfig);
        if (sessionIdInfo != null) {
            long expirationTime = sessionIdInfo.getExpiryTime();
            long currentTime = new Date().getTime();
            long timeToExpiry = expirationTime - currentTime;

            if (timeToExpiry > 3000) {
                return sessionIdInfo;
            }
        }
        return sessionInfoCacheRetriever.retrieveNewSessionInfo(connectionConfig);
    }

}
