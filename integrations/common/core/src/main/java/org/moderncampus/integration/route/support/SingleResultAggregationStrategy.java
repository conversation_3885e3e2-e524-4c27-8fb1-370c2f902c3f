package org.moderncampus.integration.route.support;

import java.util.Objects;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Exchange;
import org.springframework.stereotype.Component;

@Component
public class SingleResultAggregationStrategy implements AggregationStrategy {

    @Override
    public Exchange aggregate(Exchange oldExchange, Exchange newExchange) {
        if (Objects.isNull(oldExchange)) {
            return getExchange(newExchange);
        }

        return oldExchange;
    }

    private Exchange getExchange(Exchange exchange) {
        if (isValidExchange(exchange)) {
            Object body = exchange.getMessage().getBody();
            exchange.getMessage().setBody(body);
        }
        return exchange;
    }

    private boolean isValidExchange(Exchange exchange) {
        return Objects.nonNull(exchange) && Objects.nonNull(exchange.getMessage()) && Objects.nonNull(
                exchange.getMessage().getBody());
    }

}
