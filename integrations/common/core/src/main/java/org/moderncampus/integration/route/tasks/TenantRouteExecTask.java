package org.moderncampus.integration.route.tasks;

import static org.moderncampus.integration.constants.Constants.CURRENT_EXCHANGE;

import org.moderncampus.integration.route.Constants;
import org.moderncampus.integration.route.dto.RouteExecutorRequest;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.dto.RouteInstance;
import org.moderncampus.integration.route.executor.DefaultRouteExecutor;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.moderncampus.integration.route.tasks.api.ITask;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.route.tasks.result.TaskOutput;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TenantRouteExecTask extends BaseTask implements ITask {

    private static final String TENANT_ROUTE_EXECUTOR = "TENANT_ROUTE_EXECUTOR";

    DefaultRouteExecutor routeExecutor;

    @Override
    public String identifier() {
        return TENANT_ROUTE_EXECUTOR;
    }

    @Override
    public TaskOutput execute(TaskInput input) throws Exception {
        RouteExecutorRequest request = new RouteExecutorRequest();
        request.setData(input.getInputData());
        request.addMetaProperty(CURRENT_EXCHANGE, input.getContext());
        RouteInstance routeInstance = new RouteInstance();
        routeInstance.setId(input.getProperty(Constants.ROUTE_ID, IRouteId.class));
        routeInstance.setRequest(request);
        RouteExecutorResult result = routeExecutor.execute(routeInstance);
        return taskOutput(result);
    }
}
