package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSubject extends BaseDTO {

    String code;

    String title;

    String description;

    String shortDescription;

    String institution;

    String department;

    LocalDate effectiveDate;

    String effectiveStatus;
}
