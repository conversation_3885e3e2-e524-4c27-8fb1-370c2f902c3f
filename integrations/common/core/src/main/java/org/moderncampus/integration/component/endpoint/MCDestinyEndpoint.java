package org.moderncampus.integration.component.endpoint;

import static org.moderncampus.integration.route.support.RouteSupport.beanRef;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.Map;
import java.util.Objects;

import org.apache.camel.Consumer;
import org.apache.camel.Processor;
import org.apache.camel.Producer;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.util.ObjectHelper;
import org.moderncampus.integration.component.MCComponent;
import org.moderncampus.integration.component.endpoint.auth.MCDestinyWSSessionIdRetriever;
import org.moderncampus.integration.component.endpoint.config.IMCDestinyConnectionConfiguration;
import org.moderncampus.integration.component.endpoint.config.MCDestinyAsyncEventEndpointConfig;
import org.moderncampus.integration.component.endpoint.config.MCDestinyEndpointConfiguration;
import org.moderncampus.integration.component.endpoint.producer.MCDestinyAsyncEventWSProducer;
import org.moderncampus.integration.constants.Constants;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import lombok.Getter;

@Getter
@Component
@Lazy(value = false)
public class MCDestinyEndpoint extends MCEndpoint implements ApplicationContextAware {

    public static final String ENTITY_URI_PARAM = "entity";

    private static ApplicationContext context;

    MCDestinyEndpointConfiguration endpointConfiguration = new MCDestinyEndpointConfiguration();

    ProducerTemplate producerTemplate;

    public MCDestinyEndpoint() {
        super();
    }

    public MCDestinyEndpoint(String uri, MCComponent component) {
        super(uri, component);
    }

    @Override
    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String entity;
        String path;
        try {
            URI u = new URI(remaining);
            entity = u.getScheme();
            path = u.getPath();
        } catch (Exception e) {
            throw new MalformedURLException(
                    String.format("An invalid modern campus destiny one remaining uri: '%s' was provided. Error: '%s'",
                            remaining,
                            e.getMessage()));
        }
        ObjectHelper.notNull(entity, ENTITY_URI_PARAM);
        setEntity(MCDestinyEndpointConfiguration.Entity.valueOf(entity));
        setPath(path);
        producerTemplate = context.getBean(beanRef(ProducerTemplate.class), ProducerTemplate.class);
        setSessionIdRetriever(
                context.getBean(beanRef(MCDestinyWSSessionIdRetriever.class), MCDestinyWSSessionIdRetriever.class));
    }

    @Override
    public Producer createProducer() throws Exception {
        if (Objects.requireNonNull(endpointConfiguration.getEntity())
                == MCDestinyEndpointConfiguration.Entity.asyncEventWS) {
            return new MCDestinyAsyncEventWSProducer(this);
        } else {
            throw new UnsupportedOperationException(
                    String.format("Destiny One producer of type: %s is not implemented",
                            endpointConfiguration.getEntity()));
        }
    }

    @Override
    public Consumer createConsumer(Processor processor) throws Exception {
        throw new UnsupportedOperationException("Destiny One consumer is not implemented.");
    }

    public void validateConfigurationParameters() {
        ObjectHelper.notNull(endpointConfiguration.getConnectionConfig(),
                Constants.CONNECTION_CONFIG_PARAM);
        ObjectHelper.notNull(endpointConfiguration.getEntity(), ENTITY_URI_PARAM);
        ObjectHelper.notNull(endpointConfiguration.getPath(), Constants.PATH_URI_PARAM);
    }

    public void setConnectionConfig(IMCDestinyConnectionConfiguration connectionConfig) {
        getEndpointConfiguration().setConnectionConfig(connectionConfig);
    }

    public void setEntity(MCDestinyEndpointConfiguration.Entity entity) {
        getEndpointConfiguration().setEntity(entity);
    }

    public void setPath(String path) {
        getEndpointConfiguration().setPath(path);
    }

    public void setHttpMethod(String method) {
        getEndpointConfiguration().setHttpMethod(method);
    }

    public void setSessionIdRetriever(MCDestinyWSSessionIdRetriever sessionIdRetriever) {
        getEndpointConfiguration().setSessionIdRetriever(sessionIdRetriever);
    }

    public MCDestinyAsyncEventEndpointConfig getAsyncEventEndpointConfig() {
        return getEndpointConfiguration().getAsyncEventEndpointConfig();
    }

    public void setAsyncEventEndpointConfig(MCDestinyAsyncEventEndpointConfig asyncEventEndpointConfig) {
        getEndpointConfiguration().setAsyncEventEndpointConfig(asyncEventEndpointConfig);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}
