package org.moderncampus.integration.route.builder;

import java.util.List;
import java.util.Set;

import org.moderncampus.integration.route.tasks.BaseTask;

public abstract class BaseCustomRouteBuilder extends BaseRouteBuilder {

    public BaseCustomRouteBuilder(Set<String> ids,
            List<? extends BaseTask> tasks) {
        super(ids, tasks);
    }

    public BaseCustomRouteBuilder(String id, List<? extends BaseTask> tasks) {
        super(id, tasks);
    }

    public BaseCustomRouteBuilder(String id) {
        super(id);
    }

    @Override
    public boolean isValidate() {
        return true;
    }
}
