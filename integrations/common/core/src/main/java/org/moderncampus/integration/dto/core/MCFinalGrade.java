package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCFinalGrade extends BaseDTO {

    private String studentEnrollment;

    private Grade grade;

    private LastAttendance lastAttendance;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Grade {

        private String grade;

        private IncompleteGrade incompleteGrade;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class IncompleteGrade {

        LocalDate extensionDate;

        String finalGrade;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class LastAttendance {

        LocalDate date;

        String status;

    }

}
