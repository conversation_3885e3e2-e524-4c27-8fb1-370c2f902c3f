package org.moderncampus.integration.route.executor;

import static org.moderncampus.integration.constants.Constants.DEST_SYS_ENDPOINT_RESPONSE;
import static org.moderncampus.integration.constants.Constants.EXECUTED_ROUTE_RESULTS;
import static org.moderncampus.integration.route.Constants.ROUTE_ID;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.dto.RouteInstance;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Aspect
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteExecutorInterceptor {

    static final String GET = "get";

    @Value("${route.results.append.log:#{false}}")
    @NonFinal
    boolean routeResultAppendLog;

    static Set<String> mappedMetaProps = new HashSet<>() {{
        add(DEST_SYS_ENDPOINT_RESPONSE);
    }};

    RouteExecutorResultCollector resultCollector;

    @Around("execution(* org.moderncampus.integration.route.executor.IRouteExecutor.execute(..))")
    public Object interceptRouteExecutorMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        // Get the target object
        Object target = joinPoint.getTarget();
        Object[] args = joinPoint.getArgs();
        RouteInstance instance = (RouteInstance) args[0];
        IRouteId routeId = instance.getId();

        RouteExecutorResult originalResult = (RouteExecutorResult) joinPoint.proceed();
        if (!routeResultAppendLog || routeId.getId().contains(GET)) {
            return originalResult;
        }
        RouteExecutorResult loggedResult = new RouteExecutorResult();
        if (originalResult.getMeta() != null) {
            Map<String, Object> metaProps = Optional.ofNullable(originalResult.getMeta().getMetaProperties())
                    .map((resultProps -> {
                        HashMap<String, Object> loggedProps = new HashMap<>(resultProps);
                        loggedProps.keySet().removeIf(key -> !mappedMetaProps.contains(key));
                        return loggedProps;
                    })).orElse(null);
            if (metaProps != null) {
                metaProps.put(ROUTE_ID, routeId.getContextPath());
                RouteExecutorResult.Meta meta = new RouteExecutorResult.Meta();
                meta.setResultTypeCode(originalResult.getMeta().getResultTypeCode());
                meta.setMetaProperties(metaProps);
                loggedResult.setMeta(meta);
                resultCollector.addExecutedRouteResult(loggedResult);
                originalResult.getMeta().getMetaProperties()
                        .put(EXECUTED_ROUTE_RESULTS, resultCollector.getExecutedRouteResults());
            }
        }
        return originalResult;
    }
}
