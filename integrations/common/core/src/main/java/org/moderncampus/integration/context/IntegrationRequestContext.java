package org.moderncampus.integration.context;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Scope(value = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class IntegrationRequestContext implements IIntegrationRequestContext {

    public static final String DEFAULT_TENANT = "default";

    String school = DEFAULT_TENANT;

    String sourceSystem;

    String destSystem;

    String version;

    String contextPath;

    public void setSchool(String school) {
        if (StringUtils.isNotBlank(school)) {
            school = school.toLowerCase();
        }
        this.school = school;
    }

    public void setSourceSystem(String sourceSystem) {
        if (StringUtils.isNotBlank(sourceSystem)) {
            sourceSystem = sourceSystem.toLowerCase();
        }
        this.sourceSystem = sourceSystem;
    }

    @Override
    public void setDestSystem(String destSystem) {
        if (StringUtils.isNotBlank(destSystem)) {
            destSystem = destSystem.toLowerCase();
        }
        this.destSystem = destSystem;
    }
}
