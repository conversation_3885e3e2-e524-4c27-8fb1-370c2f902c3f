package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE;

import org.apache.camel.Processor;
import org.apache.camel.model.RouteDefinition;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class GetByIdFromExtSystemRouteBuilder extends BaseRouteBuilder {

    Object transformer;
    String transformMethod;
    @NonFinal
    Processor preTransformProcessor;
    @NonFinal
    String useCase;

    protected GetByIdFromExtSystemRouteBuilder(String id, Object transformer, String transformMethod,
            Processor preTransformProcessor, String useCase) {
        super(id);
        this.transformer = transformer;
        this.transformMethod = transformMethod;
        this.preTransformProcessor = preTransformProcessor;
        this.useCase = useCase;
    }


    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.setProperty(INTEGRATION_USE_CASE, simple(useCase()));
        preFetchAssociations(routeDefinition);
        routeDefinition.setProperty(RESOURCE_ID, simple($_BODY)).setBody().simple($_NULL);
        routeDefinition.toD(getExtSystemGetByIdRouteURI());
        handlePreTransform(routeDefinition);
        routeDefinition.bean(transformer, transformMethod);
    }

    protected abstract void preFetchAssociations(RouteDefinition routeDefinition);

    protected abstract String getExtSystemGetByIdRouteURI();
    protected void handlePreTransform(RouteDefinition routeDefinition) {
        if (getPreTransformProcessor() != null) {
            routeDefinition.bean(getPreTransformProcessor());
        }
    }

    @Override
    protected String useCase() {
        return useCase;
    }
}
