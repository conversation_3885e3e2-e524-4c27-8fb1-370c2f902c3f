package org.moderncampus.integration.route.tasks.input;

import java.util.Map;
import java.util.function.Supplier;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TaskInput {

    Object inputData;

    Object context;

    @Getter(AccessLevel.PROTECTED)
    Supplier<Map<String, Object>> contextPropertiesSupplier;

    @NonFinal
    Map<String, Object> contextProperties;

    public Object getProperty(String name) {
        return getProperty(name, Object.class);
    }

    public <T> T getProperty(String name, Class<T> type) {
        return (T) contextProperties.get(name);
    }

    public Map<String, Object> getContextProperties() {
        if (contextProperties == null) {
            contextProperties = contextPropertiesSupplier.get();
        }
        return contextProperties;
    }

}
