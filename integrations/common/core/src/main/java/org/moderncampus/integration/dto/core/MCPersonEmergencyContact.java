package org.moderncampus.integration.dto.core;

import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCPersonEmergencyContact extends BaseDTO {

    String person;
    String fullName;
    String firstName;
    String middleName;
    String lastName;
    List<McPhone> phones;
    Boolean isEmerContact;
    Boolean isMissPerContact;
    MCRelationship relationship;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class McPhone {

        MCPhoneAvailability availability;
        String countryCode;
        String number;
        String extension;
    }

    @Getter
    @AllArgsConstructor
    public enum MCPhoneAvailability {
        DAYTIME("DAY"),
        EVENING("EVE"),
        OTHER("OTH");

        private final String code;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCRelationship extends BaseDTO {

        String type;
        String priority;
    }

}
