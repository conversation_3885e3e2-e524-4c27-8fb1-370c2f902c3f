package org.moderncampus.integration.tenant.env;

import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.tenants.service.ITenantConfigMgmtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import com.google.common.base.CaseFormat;

@Component
@Lazy(value = false)
public class TenantConfigPropertySource extends
        PropertySource<ITenantConfigMgmtService> implements ApplicationContextAware {

    public static Logger LOGGER = LoggerFactory.getLogger(TenantConfigPropertySource.class);

    private static final String TENANT_PREFIX = "tenant";

    private static ApplicationContext context;

    public TenantConfigPropertySource() {
        super(TenantConfigPropertySource.class.getSimpleName());
    }

    @Override
    public Object getProperty(String propertyName) {
        if (StringUtils.isBlank(propertyName)
                || !propertyName.startsWith(TENANT_PREFIX) || context == null) {
            return null;
        }
        try {
            if (propertyName.contains("-")) {//special handle hyphenated property segments by converting to camel case
                propertyName = CaseFormat.LOWER_HYPHEN.to(CaseFormat.LOWER_CAMEL, propertyName);
            }
            IIntegrationRequestContext requestContext = context.getBean(IIntegrationRequestContext.class);
            ITenantConfigMgmtService configMgmtService = context.getBean(
                    ITenantConfigMgmtService.class);
            return configMgmtService.getConfigValByCompositeKey(requestContext.getSchool(), propertyName);
        } catch (Exception e) {
            LOGGER.debug("Exception encountered while loading tenant property", e);
            return null;
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}

