package org.moderncampus.integration.health;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import org.apache.camel.health.HealthCheck;
import org.springframework.boot.actuate.health.Health;
import org.springframework.stereotype.Component;

@Component
public class DefaultIntHealthCheckResultMapper {

    public Health mapIntegrationHealthResults(Collection<HealthCheck.Result> healthCheckResults) {
        Health.Builder builder = Health.up();

        for (HealthCheck.Result result : healthCheckResults) {
            Map<String, Object> details = result.getDetails();
            boolean enabled = true;

            builder.withDetail(result.getCheck().getId(), result.getState().name());

            applyHealthDetail(builder, result, "full");

            if (result.getState() == HealthCheck.State.DOWN) {
                builder.down();
            }
        }
        return builder.build();
    }

    private void applyHealthDetail(Health.Builder builder, HealthCheck.Result result, String exposureLevel) {
        if (!exposureLevel.equals("oneline")) {
            HealthCheck check = result.getCheck();
            Set<String> metaKeys = check.getMetaData().keySet();

            final Map<String, String> data = new LinkedHashMap<>();
            result.getDetails().forEach((key, value) -> {
                if (value != null) {
                    if (exposureLevel.equals("full")) {
                        data.put(key, value.toString());
                    } else {
                        // Filter health check metadata to have a less verbose output
                        if (!metaKeys.contains(key)) {
                            data.put(key, value.toString());
                        }
                    }
                }
            });

            result.getError().ifPresent(error -> {
                builder.withDetail("error.message", error.getMessage());
                final StringWriter stackTraceWriter = new StringWriter();
                try (final PrintWriter pw = new PrintWriter(stackTraceWriter, true)) {
                    error.printStackTrace(pw);
                    data.put("error.stacktrace", stackTraceWriter.toString());
                }
            });

            if (!data.isEmpty()) {
                String id = result.getCheck().getId() + ".data";
                builder.withDetail(id, data);
            }
        }
    }
}
