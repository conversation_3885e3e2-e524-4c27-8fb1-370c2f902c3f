package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSectionCrossListGroup extends BaseDTO {

    String groupCode;

    String termCode;

    BigDecimal maxEnrollments;

    String sectionCode;

}

