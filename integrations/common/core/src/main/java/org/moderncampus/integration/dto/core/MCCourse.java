package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCCourse extends BaseDTO {

    @Schema(description = "Short title of the course (one of the titles short/long is required)")
    String shortTitle;

    @Schema(description = "Long title of the course (one of the titles short/long is required)")
    String longTitle;

    @Schema(description = "Description of the course")
    String description;

    @Schema(description = "Course start date")
    LocalDate startOn;

    @Schema(description = "Course end date")
    LocalDate endOn;

    String code;

    @Schema(description = "A numbering scheme that distinguishes courses within a subject. Typically, this is an integer.")
    String number;

    @Schema(description = "The categories to which the course may belong")
    List<String> courseCategories;

    @Schema(description = "The levels of scholarship that apply to a course.")
    List<String> courseLevels;

    @Schema(description = "The ID of a credit type")
    String creditType;

    @Schema(description = "Unit specification that can be awarded for completing a section.")
    BigDecimal creditsMin;

    @Schema(description = "Unit specification that can be awarded for completing a section.")
    BigDecimal creditsMax;

    @Schema(description = "The increment by which the range of values for a course credit can change from the minimum to the maximum")
    BigDecimal creditsIncrement;

    @Schema(description = "Unit specification that can be awarded for completing a section.")
    BigDecimal ceuMin;

    @Schema(description = "Unit specification that can be awarded for completing a section.")
    BigDecimal ceuMax;

    @Schema(description = "The global identifiers for the Subjects.  A branch of knowledge such as 'Mathematics' or 'Biology' associated with a course.")
    List<String> subjects;

    @Schema(description = "The global identifiers for the Topics.")
    List<String> topics;

    @Schema(description = "The global identifiers for the Academic Levels.")
    List<String> academicLevels;

    @Schema(description = "The grading schemes that may be used to award a grade to a student taking this course.")
    List<String> gradeSchemes;

    @Schema(description = "Instructional methods associated to the course")
    List<MCCourseInstructionalMethod> instructionalMethods;

    BigDecimal contactHours;

    BigDecimal labHours;

    BigDecimal lectureHours;

    BigDecimal otherHours;

    @Schema(description = "The global identifier for the Topic.")
    String status;

    List<MCCourseDepartments> departments;

    @Schema(description = "The administrative period associated with a course.")
    String startTerm;

    @Schema(description = "The GUID or string of a CIP code.")
    String cipCode;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCourseDepartments extends BaseDTO {

        @Override
        @Schema(description = "The global identifier for the Course Department.")
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The portion of a course that is owned or allocated to a particular organization.")
        BigDecimal ownershipPercentage;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCourseInstructionalMethod extends BaseDTO {

        @Override
        @Schema(description = "The global identifier for the Institution Unit.")
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The hours that may be assigned to the course by instructional method.")
        BigDecimal hours;
    }
}
