package org.moderncampus.integration.transform.support;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.moderncampus.integration.transform.ITransformer;
import org.moderncampus.integration.transform.TransformContext;

import lombok.AccessLevel;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@SuppressWarnings("rawtypes")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BaseTransformProcessor implements Processor {

    ITransformer transformer;

    @NonFinal
    @Setter
    Class<?> inputBodyType = Object.class;

    LanguageEvaluator languageEvaluator;

    protected TransformContext buildTransformContext(Exchange exchange) {
        TransformContext context = new TransformContext(exchange.getProperties());
        if (languageEvaluator != null) {
            context.setJsonPathEvaluator((jsonPath) -> languageEvaluator.evaluateJsonPathExpr(exchange, jsonPath,
                    String.class));
        }
        return context;
    }

    public BaseTransformProcessor(ITransformer transformer) {
        this.transformer = transformer;
        this.languageEvaluator = null;
    }

    public BaseTransformProcessor(ITransformer transformer, Class<?> inputBodyType) {
        this.transformer = transformer;
        this.languageEvaluator = null;
        this.inputBodyType = inputBodyType;
    }

    public BaseTransformProcessor(ITransformer transformer, LanguageEvaluator languageEvaluator) {
        this.transformer = transformer;
        this.languageEvaluator = languageEvaluator;
    }

    public BaseTransformProcessor(ITransformer transformer, LanguageEvaluator languageEvaluator,
            Class<?> inputBodyType) {
        this.transformer = transformer;
        this.languageEvaluator = languageEvaluator;
        this.inputBodyType = inputBodyType;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        TransformContext context = buildTransformContext(exchange);
        Object data = transformer.transform(context, exchange.getMessage().getBody(inputBodyType));
        exchange.getMessage().setBody(data);
    }
}
