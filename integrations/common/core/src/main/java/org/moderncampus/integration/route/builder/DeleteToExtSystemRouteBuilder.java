package org.moderncampus.integration.route.builder;

import org.apache.camel.model.RouteDefinition;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
public abstract class DeleteToExtSystemRouteBuilder extends BaseRouteBuilder {

    ObjectMapper objectMapper;

    public DeleteToExtSystemRouteBuilder(String id, ObjectMapper objectMapper) {
        super(id);
        this.objectMapper = objectMapper;
    }

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.setProperty(RESOURCE_ID, simple($_BODY)).setBody().simple($_NULL);
        routeDefinition.toD(getExtSystemDeleteByIdRouteURI());
        routeDefinition.process((exchange) -> exchange.getMessage().setBody(objectMapper.createObjectNode()));
    }

    protected abstract String getExtSystemDeleteByIdRouteURI();
}
