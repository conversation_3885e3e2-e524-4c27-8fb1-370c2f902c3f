package org.moderncampus.integration.transform;

import java.util.Map;
import java.util.function.Function;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TransformContext {

    Map<String, Object> contextProps;

    @NonFinal
    @Setter
    Function<String, String> jsonPathEvaluator;

    public void setContextProp(String propKey, Object prop) {
        this.contextProps.put(propKey, prop);
    }

    public <T> T getContextProp(String propKey, Class<T> propType) {
        return (T) contextProps.get(propKey);
    }

}
