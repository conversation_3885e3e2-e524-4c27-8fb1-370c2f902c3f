package org.moderncampus.integration.component;

import static org.moderncampus.integration.component.constants.Constants.MODERN_CAMPUS_COMPONENT_SCHEME;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.Map;
import java.util.Objects;

import org.apache.camel.Endpoint;
import org.apache.camel.spi.annotations.Component;
import org.apache.camel.support.DefaultComponent;
import org.apache.camel.util.StringHelper;
import org.moderncampus.integration.component.endpoint.MCDestinyEndpoint;
import org.moderncampus.integration.component.endpoint.MCEndpoint;

import lombok.Setter;

@Component(MODERN_CAMPUS_COMPONENT_SCHEME)
@Setter
public class MCComponent extends DefaultComponent {

    Product product;

    public enum Product {
        destinyOne
    }

    @Override
    protected Endpoint createEndpoint(String uri, String remaining, Map<String, Object> parameters) throws Exception {
        parseURI(remaining, parameters);
        MCEndpoint mcEndpoint = null;
        if (Objects.requireNonNull(product) == Product.destinyOne) {
            mcEndpoint = new MCDestinyEndpoint(uri, this);
        } else {
            throw new UnsupportedOperationException(
                    String.format("Modern Campus producer for product: %s is not implemented", product));
        }
        mcEndpoint.parseURI(StringHelper.after(remaining, ":"), parameters);
        setProperties(mcEndpoint, parameters);
        return mcEndpoint;
    }

    @Override
    protected void afterConfiguration(String uri, String remaining, Endpoint endpoint, Map<String, Object> parameters)
            throws Exception {
        super.afterConfiguration(uri, remaining, endpoint, parameters);
        MCEndpoint mcEndpoint = (MCEndpoint) endpoint;
        ((MCEndpoint) endpoint).validateConfigurationParameters();
    }

    public void parseURI(String remaining, Map<String, Object> parameters) throws Exception {
        String product;
        try {
            URI u = new URI(remaining);
            product = u.getScheme();
        } catch (Exception e) {
            throw new MalformedURLException(
                    String.format("An invalid modern campus remaining uri: '%s' was provided. Error: '%s'", remaining,
                            e.getMessage()));
        }
        setProduct(Product.valueOf(product));
    }
}
