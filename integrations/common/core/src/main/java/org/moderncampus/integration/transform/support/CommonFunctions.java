package org.moderncampus.integration.transform.support;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.camel.Processor;

import net.minidev.json.JSONArray;
import net.minidev.json.JSONValue;

public class CommonFunctions {

    public static Processor stringJsonToCollectionConverter = (exchange) -> {
        String json = exchange.getMessage().getBody(String.class);
        Collection<String> messageList = mapJsonListToStringCollection(json);
        exchange.getMessage().setBody(messageList);
    };

    public static Collection<String> mapJsonListToStringCollection(String json) {
        Collection<String> messageList = new ArrayList<>();

        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);

        for (Object o : jsonArray) {
            messageList.add(o.toString());
        }
        return messageList;
    }

    public static boolean isValueExist(String value) {
        return isValueExist(value, true);
    }

    public static boolean isValueExist(String value, boolean allowBlanks) {
        return value != null && (allowBlanks || !value.isEmpty());
    }

    public static boolean isValueExist(Collection<?> values) {
        return isValueExist(values, true);
    }

    public static boolean isValueExist(Collection<?> values, boolean allowEmpty) {
        return values != null && (allowEmpty || !values.isEmpty());
    }

    public static boolean isValueExist(Boolean value) {
        if (value == null) {
            return false;
        }
        return value;
    }

    public static boolean isValueExist(Object value) {
        return value != null;
    }
}
