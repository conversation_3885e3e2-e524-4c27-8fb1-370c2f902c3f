package org.moderncampus.integration.component.endpoint.auth;

import static org.apache.camel.Exchange.HTTP_METHOD;
import static org.moderncampus.integration.constants.Constants.HTTP;
import static org.moderncampus.integration.constants.Constants.HTTPS;
import static org.moderncampus.integration.route.support.RouteSupport.buildRouteURI;

import org.apache.camel.CamelContext;
import org.apache.camel.Endpoint;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.ProducerTemplate;
import org.apache.commons.lang3.StringUtils;
import org.moderncampus.integration.component.endpoint.config.IMCDestinyConnectionConfiguration;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MCDestinyWSSessionInfoCacheRetriever {

    private static final String CONTENT_TYPE_HEADER = "Content-Type";
    private static final String CONTENT_TYPE = "application/json";
    public static final String BASE_LOGIN_PATH = "%s/webservice/InternalViewREST/login";

    CamelContext context;

    ProducerTemplate producerTemplate;

    ObjectMapper mapper;

    @Cacheable(value = "d1WSSessionIdCache", key = "#root.args[0].host + '-' + #root.args[0].username + #root.args[0].password")
    public MCDestinyWSSessionInfo retrieveSessionInfo(
            IMCDestinyConnectionConfiguration endpointConfiguration) throws Exception {
        return retrieveNewSessionInfo(endpointConfiguration);
    }

    public MCDestinyWSSessionInfo retrieveNewSessionInfo(IMCDestinyConnectionConfiguration endpointConfiguration)
            throws Exception {
        String loginPath = String.format(BASE_LOGIN_PATH, endpointConfiguration.getHost());
        String routeURI = buildRouteURI(endpointConfiguration.isUseHttp() ? HTTP : HTTPS, loginPath, "_type=json");
        Endpoint endpoint = context.getEndpoint(routeURI);
        Exchange exchange = endpoint.createExchange(ExchangePattern.InOut);
        MCDestinyWSLoginRequest request = new MCDestinyWSLoginRequest(endpointConfiguration.getUsername(),
                endpointConfiguration.getPassword());
        exchange.getMessage().setBody(mapper.writeValueAsString(request));
        exchange.getMessage().setHeader(CONTENT_TYPE_HEADER, CONTENT_TYPE);
        exchange.getMessage().setHeader(HTTP_METHOD, "POST");
        exchange = producerTemplate.send(routeURI, exchange);
        if (exchange.getException() != null) {
            throw exchange.getException();//Unhandled exceptions are thrown by default
        }
        return parseResponse(exchange.getMessage().getBody(String.class));
    }

    private MCDestinyWSSessionInfo parseResponse(String response) {
        try {
            JsonNode responseWrapperNode = mapper.readTree(response);
            JsonNode responseNode = responseWrapperNode.get("loginResponse");
            if (responseNode == null) {
                throw new RuntimeException("Invalid response received from D1 login endpoint");
            }
            String sessionId = responseNode.get("sessionId").asText();
            if (StringUtils.isBlank(sessionId)) {
                throw new RuntimeException("Invalid response received from D1 login endpoint");
            }
            MCDestinyWSSessionInfo sessionInfo = new MCDestinyWSSessionInfo();
            sessionInfo.setSessionId(sessionId);
            sessionInfo.setExpiryTime(System.currentTimeMillis() + (1800 * 1000));
            return sessionInfo;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
