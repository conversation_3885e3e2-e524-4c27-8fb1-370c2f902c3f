package org.moderncampus.integration.route.tasks.helper;

import java.util.HashMap;
import java.util.Optional;

import org.apache.camel.Exchange;
import org.moderncampus.integration.route.tasks.api.ITask;
import org.moderncampus.integration.route.tasks.input.TaskInput;
import org.moderncampus.integration.route.tasks.result.TaskOutput;

public class TaskSupport {

    private TaskSupport() {
    }

    public static TaskInput constructTaskInput(Exchange exchange) {
        return new TaskInput(exchange.getMessage().getBody(Object.class),
                exchange,
                () -> new HashMap<>(exchange.getProperties()));
    }

    public static void postProcessTaskOutput(ITask task, Exchange exchange, TaskOutput output) {
        Optional.ofNullable(output.getContextProps())
                .ifPresent(props -> props.forEach(exchange::setProperty));
        exchange.getMessage().setBody(output.getData());
    }
}
