package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCInstructionalMethod extends BaseDTO {

    @Schema(description = "abbreviated name to the instruction method", requiredMode = Schema.RequiredMode.REQUIRED)
    String code;

    @Schema(description = "the name of the instruction method", requiredMode = Schema.RequiredMode.REQUIRED)
    String title;

    @Schema(description = "description field if one exists separate from the title")
    String description;

    @Schema(description = "additional short description field if one exists separate from the long form description")
    String shortDescription;

    @Schema(description = "field for associating the instruction method to an academic structure")
    String institution;

    @Schema(description = "date from which the record is effective or actionable")
    LocalDate effectiveDate;

    @Schema(description = "status associated with the effective date")
    String effectiveStatus;
}
