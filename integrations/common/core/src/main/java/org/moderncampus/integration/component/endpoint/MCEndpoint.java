package org.moderncampus.integration.component.endpoint;

import java.util.Map;

import org.apache.camel.support.DefaultEndpoint;
import org.moderncampus.integration.component.MCComponent;

public abstract class MCEndpoint extends DefaultEndpoint {

    public MCEndpoint() {
        super();
    }

    public MCEndpoint(String uri, MCComponent component) {
        super(uri, component);
    }

    public abstract void parseURI(String remaining, Map<String, Object> parameters) throws Exception;

    public abstract void validateConfigurationParameters();
}
