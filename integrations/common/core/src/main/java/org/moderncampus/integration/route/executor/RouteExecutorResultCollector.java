package org.moderncampus.integration.route.executor;

import java.util.ArrayList;
import java.util.List;

import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Scope(value = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class RouteExecutorResultCollector {

    List<RouteExecutorResult> executedRouteResults;

    public void addExecutedRouteResult(RouteExecutorResult result) {
        if (executedRouteResults == null) {
            executedRouteResults = new ArrayList<>();
        }
        executedRouteResults.add(result);
    }
}
