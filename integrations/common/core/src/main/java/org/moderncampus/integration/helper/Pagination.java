package org.moderncampus.integration.helper;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Pagination {

    Integer pageNumber;
    Integer pageSize;
    Integer totalPages;
    boolean limitOffSetMode;
    Integer resultsFound;

    public Pagination(Integer pageNumber, Integer pageSize) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }

    public Pagination(Integer pageNumber, Integer pageSize, Integer totalPages, boolean limitOffSetMode,
            Integer resultsFound) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
        this.limitOffSetMode = limitOffSetMode;
        this.resultsFound = resultsFound;
    }

    // Generate the next instance which contains an incremented pageNumber
    public Pagination next() throws Exception {
        Integer pageNumber = getPageNumber();
        pageNumber = pageNumber + 1;
        if (isLimitOffSetMode() && ((getPageSize() == null || getResultsFound() == null) || (getTotalPages() <= 0
                && getResultsFound() < getPageSize()))) {
            return null;
        }
        if (!isLimitOffSetMode() && (getTotalPages() == null || pageNumber > getTotalPages())) {
            return null;
        }
        return new Pagination(pageNumber, getPageSize(), getTotalPages(), isLimitOffSetMode(), getResultsFound());
    }
}
