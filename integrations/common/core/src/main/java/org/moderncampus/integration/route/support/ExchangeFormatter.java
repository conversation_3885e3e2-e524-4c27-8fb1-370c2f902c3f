package org.moderncampus.integration.route.support;

import static org.moderncampus.integration.route.Constants.ROUTE_HEADER_PREFIX;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.apache.camel.support.processor.DefaultExchangeFormatter;

public class ExchangeFormatter extends DefaultExchangeFormatter {

    static Set<String> disallowedKeys = new HashSet<>() {{
        add("Authorization");
        add("ps_token");//peoplesoft token
        add("PS_TOKEN");
        add("ca_ryerson_ps_token");
    }};

    @Override
    protected Map<String, Object> filterHeaderAndProperties(Map<String, Object> map) {
        return Optional.ofNullable(super.filterHeaderAndProperties(map)).map(filteredMap -> {
            Map<String, Object> filteredMapCopy = new HashMap<>(filteredMap);
            filteredMapCopy.entrySet().removeIf((entry -> {
                return disallowedKeys.contains(entry.getKey()) || disallowedKeys.contains(
                        entry.getKey().replace(ROUTE_HEADER_PREFIX, ""));
            }));
            return filteredMapCopy;
        }).orElse(null);
    }
}
