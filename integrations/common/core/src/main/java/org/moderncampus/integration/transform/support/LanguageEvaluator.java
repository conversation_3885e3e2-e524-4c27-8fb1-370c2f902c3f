package org.moderncampus.integration.transform.support;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Expression;
import org.apache.camel.jsonpath.JsonPathLanguage;
import org.apache.camel.spi.Language;
import org.springframework.stereotype.Component;

import com.jayway.jsonpath.Option;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LanguageEvaluator {

    public static final String JSONPATH = "jsonpath";

    CamelContext context;

    public <T> T evaluateJsonPathExpr(Exchange exchange, String expr, Class<T> type) {
        Language lan = context.resolveLanguage(JSONPATH);
        JsonPathLanguage language = (JsonPathLanguage) lan;
        language.setOptions(Option.SUPPRESS_EXCEPTIONS, Option.DEFAULT_PATH_LEAF_TO_NULL);
        Expression exp = lan.createExpression(expr);
        return exp.evaluate((Exchange) exchange, type);
    }
}
