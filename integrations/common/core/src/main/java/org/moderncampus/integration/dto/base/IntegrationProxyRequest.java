package org.moderncampus.integration.dto.base;

import java.util.Map;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntegrationProxyRequest {

    Object data;
    String proxyPath;
    String httpMethod;
    Map<String, String> headers;
    Map<String, String> queryParams;
}
