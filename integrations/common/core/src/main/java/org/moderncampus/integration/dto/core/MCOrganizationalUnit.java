package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCOrganizationalUnit extends BaseDTO {

    String name;

    String code;

    String description;

    String type;

    String status;

    LocalDate effectiveDate;

    String parentId;

}
