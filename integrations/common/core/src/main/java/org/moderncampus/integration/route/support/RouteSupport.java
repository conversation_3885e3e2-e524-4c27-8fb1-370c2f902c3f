package org.moderncampus.integration.route.support;

import static java.util.stream.Collectors.joining;
import static org.moderncampus.integration.constants.Constants.REQUEST_ID;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.camel.Exchange;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.moderncampus.integration.constants.Constants;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.route.dto.RouteExecResultTypeCode;
import org.moderncampus.integration.route.dto.RouteExecutorRequest;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.dto.RouteInstance;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.moderncampus.integration.util.AsyncRequestScopeAttr;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

public class RouteSupport {

    static final String ASYNC_REQUEST_ATTRIBUTES = "ASYNC_REQUEST_ATTRIBUTES";

    private RouteSupport() {
    }

    public static String buildDirectRouteURI(String path) {
        return buildRouteURI("direct", path, null);
    }

    public static String buildDirectRouteURI(String path, String query) {
        return buildRouteURI("direct", path, query);
    }

    public static String buildRouteURI(String scheme, String path, String query) {
        StringBuilder route = new StringBuilder()
                .append(scheme).append("://").append(path);

        if (StringUtils.isNotBlank(query)) {
            if (route.indexOf("?") > 0) {
                route.append("&").append(query);
            } else {
                route.append("?").append(query);
            }
        }

        return route.toString();
    }

    public static RouteExecutorResult.Meta mapToRouteExecutorMeta(RouteExecResultTypeCode resultTypeCode,
            Map<String, Object> contextProps, Exception exception) {
        RouteExecutorResult.Meta meta = new RouteExecutorResult.Meta();
        meta.setResultTypeCode(resultTypeCode);
        meta.setMetaProperties(contextProps);
        meta.setException(exception);
        return meta;
    }

    public static Map<String, Object> extractPropertiesForResult(Exchange exchange) {
        Map<String, Object> propertiesMap = Optional.ofNullable(exchange.getProperties()).orElse(new HashMap<>());
        Optional.ofNullable(exchange.getMessage().getHeader(Constants.BREADCRUMB_ID))
                .ifPresent((breadCrumbId) -> propertiesMap.put(Constants.BREADCRUMB_ID, breadCrumbId));
        return propertiesMap;
    }

    public static String beanRef(Class<?> beanClass) {
        return org.springframework.util.StringUtils.uncapitalizeAsProperty(ClassUtils.getShortName(beanClass));
    }

    public static String parameterBeanRef(Class<?> beanClass) {
        return "#" + beanRef(beanClass);
    }

    public static String parameterBeanRef(String beanId) {
        return "#bean:" + org.springframework.util.StringUtils.uncapitalizeAsProperty(beanId);
    }

    public static String parameterValueRef(Class<?> refType, Object value) {
        return "#" + "valueAs(" + refType.getName() + ")" + ":" + value;
    }

    public static String routeQueryParamStr(Map<String, String> params) {
        return params.entrySet()
                .stream()
                .map(Object::toString)
                .collect(joining("&"));
    }

    public static void setupAsyncRouteInvoke(Exchange exchange) {
        AsyncRequestScopeAttr requestAttributes = cloneRequestAttributes(RequestContextHolder.getRequestAttributes());
        exchange.setProperty(ASYNC_REQUEST_ATTRIBUTES, requestAttributes);
        exchange.setProperty(REQUEST_ID, MDC.get(REQUEST_ID));
    }

    public static void setupPreAsyncRoute(Exchange exchange) {
        RequestAttributes requestAttributes = exchange.getProperty(ASYNC_REQUEST_ATTRIBUTES, RequestAttributes.class);
        Optional.ofNullable(requestAttributes).ifPresent(RequestContextHolder::setRequestAttributes);
        MDC.put(REQUEST_ID, exchange.getProperty(REQUEST_ID));
    }

    public static boolean isAsyncRoute(Exchange exchange) {
        RequestAttributes requestAttributes = exchange.getProperty(ASYNC_REQUEST_ATTRIBUTES, RequestAttributes.class);
        return Optional.ofNullable(requestAttributes).isPresent();
    }

    public static void setupPostAsyncRoute(Exchange exchange) {
        RequestContextHolder.resetRequestAttributes();
    }

    /**
     * This clone method retains reference on request scope objects. This is needed as once the main thread is finished,
     * the object may get garbage collected, even if the async thread is not
     *
     * @param requestAttributes
     * @return
     */
    private static AsyncRequestScopeAttr cloneRequestAttributes(RequestAttributes requestAttributes) {
        AsyncRequestScopeAttr clonedRequestAttribute = null;

        try {
            clonedRequestAttribute = new AsyncRequestScopeAttr();

            for (String name : requestAttributes.getAttributeNames(RequestAttributes.SCOPE_REQUEST)) {
                clonedRequestAttribute.setAttribute(
                        name,
                        Objects.requireNonNull(requestAttributes.getAttribute(name, RequestAttributes.SCOPE_REQUEST)),
                        RequestAttributes.SCOPE_REQUEST
                );
            }
            return clonedRequestAttribute;
        } catch (Exception e) {
            return new AsyncRequestScopeAttr();
        }

    }

    public static RouteExecutorResult executeRoute(IRouteExecutor routeExecutor, IRouteId routeId, Object data)
            throws Exception {
        RouteExecutorRequest request = new RouteExecutorRequest();
        request.setData(data);
        RouteInstance routeInstance = new RouteInstance();
        routeInstance.setId(routeId);
        routeInstance.setRequest(request);
        RouteExecutorResult result = routeExecutor.execute(routeInstance);
        RouteExecutorResult.Meta meta = Optional.ofNullable(result).map(RouteExecutorResult::getMeta).orElse(null);
        if (Optional.ofNullable(meta).isPresent()) {
            if (RouteExecResultTypeCode.FAILURE.equals(meta.getResultTypeCode()) && meta.getException() != null) {
                throw meta.getException();
            }
        }
        return result;
    }

    public static void appendEndpointResponseToException(ApplicationException ex, String endpointResponse) {
        Map<String, Object> exMeta = ex.getMeta();
        if (exMeta == null) {
            exMeta = new HashMap<>();
            ex.setMeta(exMeta);
        }
        exMeta.put(Constants.DEST_SYS_ENDPOINT_RESPONSE, endpointResponse);
    }
}
