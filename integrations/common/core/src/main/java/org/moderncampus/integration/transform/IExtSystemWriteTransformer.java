package org.moderncampus.integration.transform;

import org.apache.camel.Exchange;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCFinalGrade;
import org.moderncampus.integration.dto.core.MCOrganization;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCStudentCharge;
import org.moderncampus.integration.dto.core.MCStudentEnrollment;
import org.moderncampus.integration.dto.core.MCStudentPayment;

public interface IExtSystemWriteTransformer extends ITransformInvoker {

    String METHOD_MAP_TO_COURSE_WRITE_REQUEST = "mapToCourseWriteRequest";
    String METHOD_MAP_FROM_COURSE_CREATE_RESPONSE = "mapFromCourseCreateResponse";
    String METHOD_MAP_TO_SECTION_WRITE_REQUEST = "mapToSectionWriteRequest";
    String METHOD_MAP_FROM_SECTION_CREATE_RESPONSE = "mapFromSectionCreateResponse";
    String METHOD_MAP_TO_SECTION_SCHEDULE_WRITE_REQUEST = "mapToSectionScheduleWriteRequest";
    String METHOD_MAP_FROM_SECTION_SCHEDULE_CREATE_RESPONSE = "mapFromSectionScheduleCreateResponse";
    String METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT_WRITE_REQUEST = "mapToSectionInstructorAssignmentWriteRequest";
    String METHOD_MAP_FROM_SECTION_INSTRUCTOR_ASSIGNMENT_CREATE_RESPONSE = "mapFromSectionInstructorAssignmentCreateResponse";
    String METHOD_MAP_TO_SECTION_CROSS_LIST_WRITE_REQUEST = "mapToSectionCrossListWriteRequest";
    String METHOD_MAP_FROM_SECTION_CROSS_LIST_CREATE_RESPONSE = "mapFromSectionCrossListCreateResponse";
    String METHOD_MAP_TO_PERSON_EMERGENCY_CONTACT_WRITE_REQUEST = "mapToPersonEmergencyContactWriteRequest";
    String METHOD_MAP_FROM_PERSON_EMERGENCY_CONTACT_CREATE_RESPONSE = "mapFromPersonEmergencyContactCreateResponse";
    String METHOD_MAP_TO_PERSON_WRITE_REQUEST = "mapToPersonWriteRequest";
    String METHOD_MAP_FROM_PERSON_CREATE_RESPONSE = "mapFromPersonCreateResponse";
    String METHOD_MAP_TO_CHECK_DUPLICATE_PERSON_WRITE_REQUEST = "mapToCheckDuplicatePersonWriteRequest";
    String METHOD_MAP_TO_STUDENT_CHARGE_WRITE_REQUEST = "mapToStudentChargeWriteRequest";
    String METHOD_MAP_FROM_STUDENT_CHARGE_CREATE_RESPONSE = "mapFromStudentChargeCreateResponse";
    String METHOD_MAP_FROM_STUDENT_PAYMENT_CREATE_RESPONSE = "mapFromStudentPaymentCreateResponse";
    String METHOD_MAP_TO_STUDENT_PAYMENT_WRITE_REQUEST = "mapToStudentPaymentWriteRequest";
    String METHOD_MAP_TO_STUDENT_ENROLLMENT_WRITE_REQUEST = "mapToStudentEnrollmentWriteRequest";
    String METHOD_MAP_FROM_STUDENT_ENROLLMENT_CREATE_RESPONSE = "mapFromStudentEnrollmentCreateResponse";
    String METHOD_MAP_TO_STUDENT_WRITE_REQUEST = "mapToStudentWriteRequest";
    String METHOD_MAP_TO_PERSON_ADDRESS_WRITE_REQUEST = "mapToPersonAddressWriteRequest";
    String METHOD_MAP_FROM_FINAL_GRADE_CREATE_RESPONSE = "mapFromFinalGradeCreateResponse";
    String METHOD_MAP_TO_FINAL_GRADE_WRITE_REQUEST = "mapToFinalGradeWriteRequest";
    String METHOD_MAP_TO_SECTION_CROSS_LIST_GROUP_WRITE_REQUEST = "mapToSectionCrossListGroupWriteRequest";
    String METHOD_MAP_FROM_SECTION_CROSS_LIST_GROUP_CREATE_RESPONSE = "mapFromSectionCrossListGroupCreateResponse";
    String METHOD_MAP_TO_COURSE_SECTION_INFORMATION_WRITE_REQUEST = "mapToCourseSectionInformationWriteRequest";
    String METHOD_MAP_FROM_ORGANIZATION_CREATE_RESPONSE = "mapFromOrganizationCreateResponse";
    String METHOD_MAP_TO_ORGANIZATION_WRITE_REQUEST = "mapToOrganizationWriteRequest";

    <T> T mapToCourseWriteRequest(Exchange exchange) throws Exception;

    MCCourse mapFromCourseCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToSectionWriteRequest(Exchange exchange) throws Exception;

    MCSection mapFromSectionCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToSectionScheduleWriteRequest(Exchange exchange) throws Exception;

    MCSectionSchedule mapFromSectionScheduleCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToSectionInstructorAssignmentWriteRequest(Exchange exchange) throws Exception;

    MCSectionInstructorAssignment mapFromSectionInstructorAssignmentCreateResponse(String resp, Exchange exchange)
            throws Exception;

    <T> T mapToSectionCrossListWriteRequest(Exchange exchange) throws Exception;

    MCSectionCrossList mapFromSectionCrossListCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToPersonEmergencyContactWriteRequest(Exchange exchange) throws Exception;

    MCPersonEmergencyContact mapFromPersonEmergencyContactCreateResponse(String resp, Exchange exchange)
            throws Exception;

    <T> T mapToPersonWriteRequest(Exchange exchange) throws Exception;

    MCPerson mapFromPersonCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToCheckDuplicatePersonWriteRequest(Exchange exchange) throws Exception;

    MCStudentCharge mapFromStudentChargeCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToStudentChargeWriteRequest(Exchange exchange) throws Exception;

    MCStudentPayment mapFromStudentPaymentCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToStudentPaymentWriteRequest(Exchange exchange) throws Exception;

    MCStudentEnrollment mapFromStudentEnrollmentCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToStudentEnrollmentWriteRequest(Exchange exchange) throws Exception;

    <T> T mapToStudentWriteRequest(Exchange exchange) throws Exception;

    <T> T mapToPersonAddressWriteRequest(Exchange exchange) throws Exception;

    MCFinalGrade mapFromFinalGradeCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToFinalGradeWriteRequest(Exchange exchange) throws Exception;

    MCSectionCrossListGroup mapFromSectionCrossListGroupCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToSectionCrossListGroupWriteRequest(Exchange exchange) throws Exception;

    <T> T mapToCourseSectionInformationWriteRequest(Exchange exchange) throws Exception;

    MCOrganization mapFromOrganizationCreateResponse(String resp, Exchange exchange) throws Exception;

    <T> T mapToOrganizationWriteRequest(Exchange exchange) throws Exception;
}
