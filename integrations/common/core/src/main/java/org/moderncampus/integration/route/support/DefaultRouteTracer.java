package org.moderncampus.integration.route.support;

import static org.apache.camel.support.LoggerHelper.getLineNumberLoggerName;
import static org.moderncampus.integration.Constants.PROFILE_PROD;

import java.util.Arrays;

import org.apache.camel.impl.engine.DefaultTracer;
import org.apache.camel.support.processor.DefaultExchangeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class DefaultRouteTracer extends DefaultTracer {

    private static final Logger LOGGER = LoggerFactory.getLogger("route.tracer");

    public DefaultRouteTracer(@Value("${spring.profiles.active:}") String activeProfiles,
            @Value("${route.tracer.log.body:#{false}}") boolean logBody) {
        DefaultExchangeFormatter formatter = new ExchangeFormatter();
        formatter.setShowExchangeId(true);
        formatter.setShowExchangePattern(false);
        formatter.setMultiline(false);
        formatter.setShowHeaders(true);
        formatter.setStyle(DefaultExchangeFormatter.OutputStyle.Default);
        formatter.setMaxChars(20000);
        formatter.setShowBody(isShowBodyApplicable(activeProfiles, logBody));
        setExchangeFormatter(formatter);
    }

    private boolean isShowBodyApplicable(String activeProfiles, boolean logBody) {
        String[] appProfiles = activeProfiles != null ? activeProfiles.split(",") : null;
        if (appProfiles == null || appProfiles.length == 0) {
            return logBody;
        }
        return (Arrays.stream(appProfiles)
                .noneMatch(element -> element.equalsIgnoreCase(PROFILE_PROD)) && logBody);
    }

    @Override
    /*
     * Override needed as base class does not have configuration for log level
     */
    protected void dumpTrace(String out, Object node) {
        String name = getLineNumberLoggerName(node);
        if (name != null) {
            Logger log = LoggerFactory.getLogger(name);
            log.debug(out);//override log levels to debug
        } else {
            LOGGER.debug(out);
        }
    }
}
