package org.moderncampus.integration.route.support;

import org.apache.camel.Converter;
import org.apache.camel.Exchange;
import org.apache.camel.spi.TypeConverterRegistry;
import org.apache.commons.lang3.builder.RecursiveToStringStyle;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Converter
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultTypeConverter {

    static final ToStringStyle RECURSIVE_STYLE = new RecursiveToStringStyle();

    ObjectMapper mapper;

    @Converter(fallback = true)
    public <T> T convertTo(Class<T> type, Exchange exchange, Object value, TypeConverterRegistry registry) {
        if (String.class.isAssignableFrom(type)) {
            try {
                return (T) mapper.writeValueAsString(value);
            } catch (JsonProcessingException e) {
                return (T) ToStringBuilder.reflectionToString(value, RECURSIVE_STYLE);
            }
        }
        return null;
    }

}
