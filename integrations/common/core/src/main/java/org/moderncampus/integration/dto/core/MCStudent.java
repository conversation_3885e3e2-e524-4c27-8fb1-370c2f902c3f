package org.moderncampus.integration.dto.core;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Data
@Schema(description = "Student Supplemental Data")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCStudent extends BaseDTO {

    @NotBlank(message = "Person is mandatory")
    @Schema(description = "The person (student) who incurred the charge")
    String person;

    @Schema(description = "The student types")
    List<MCStudentType> studentTypes;

    @Schema(description = "The student residencies")
    List<MCStudentResidencies> residencies;


    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCStudentType extends BaseDTO {
        @Override
        @Schema(hidden = true)
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The student type is mandatory.")
        String studentType;

        @Schema(description = "The effective start date for the student type is mandatory")
        LocalDate effectiveStart;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCStudentResidencies extends BaseDTO {

        @Override
        @Schema(hidden = true)
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The residency type is mandatory")
        String residencyType;

        @Schema(description = "The effective start date for the residency type.")
        LocalDateTime effectiveStart;

    }

}
