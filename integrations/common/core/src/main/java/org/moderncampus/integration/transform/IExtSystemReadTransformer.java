package org.moderncampus.integration.transform;

import java.util.List;

import org.apache.camel.Exchange;
import org.moderncampus.integration.dto.core.MCAcademicLevel;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCCourseSectionInformation;
import org.moderncampus.integration.dto.core.MCInstructionalMethod;
import org.moderncampus.integration.dto.core.MCInstructor;
import org.moderncampus.integration.dto.core.MCLocation;
import org.moderncampus.integration.dto.core.MCOrganizationalUnit;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCRoom;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCSubject;

public interface IExtSystemReadTransformer extends ITransformInvoker {

    String METHOD_MAP_TO_ORGANIZATIONAL_UNIT = "mapToOrganizationalUnit";
    String METHOD_MAP_TO_SUBJECT = "mapToSubject";
    String METHOD_MAP_TO_INSTRUCTIONAL_METHOD = "mapToInstructionalMethod";
    String METHOD_MAP_TO_LOCATION = "mapToLocation";
    String METHOD_MAP_TO_ACADEMIC_LEVEL = "mapToAcademicLevel";
    String METHOD_MAP_TO_ROOM = "mapToRoom";
    String METHOD_MAP_TO_SECTION = "mapToSection";
    String METHOD_MAP_TO_SECTION_SCHEDULE = "mapToSectionSchedule";
    String METHOD_MAP_TO_COURSE = "mapToCourse";
    String METHOD_MAP_TO_ACADEMIC_PERIOD = "mapToAcademicPeriod";
    String METHOD_MAP_TO_INSTRUCTOR = "mapToInstructor";
    String METHOD_MAP_TO_SECTION_CROSS_LIST = "mapToSectionCrossList";
    String METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT = "mapToSectionInstructorAssignment";
    String METHOD_MAP_TO_ADDRESS = "mapToAddress";
    String METHOD_MAP_TO_COURSE_SECTION_INFORMATION = "mapToCourseSectionInformation";
    String METHOD_MAP_TO_SECTION_CROSS_LIST_GROUP = "mapToSectionCrossListGroup";
    String METHOD_MAP_TO_SECTION_CROSS_LIST_GROUPS = "mapToSectionCrossListGroups";
    String METHOD_MAP_TO_PERSON = "mapToPerson";

    MCOrganizationalUnit mapToOrganizationalUnit(String body, Exchange exchange) throws Exception;

    MCSubject mapToSubject(String body, Exchange exchange) throws Exception;

    MCInstructionalMethod mapToInstructionalMethod(String body, Exchange exchange) throws Exception;

    MCLocation mapToLocation(String body, Exchange exchange) throws Exception;

    MCAcademicLevel mapToAcademicLevel(String body, Exchange exchange) throws Exception;

    MCRoom mapToRoom(String body, Exchange exchange) throws Exception;

    MCSection mapToSection(String body, Exchange exchange) throws Exception;

    MCSectionSchedule mapToSectionSchedule(String body, Exchange exchange) throws Exception;

    MCCourse mapToCourse(String body, Exchange exchange) throws Exception;

    MCAcademicPeriod mapToAcademicPeriod(String body, Exchange exchange) throws Exception;

    MCInstructor mapToInstructor(String body, Exchange exchange) throws Exception;

    MCSectionCrossList mapToSectionCrossList(String body, Exchange exchange) throws Exception;

    MCSectionInstructorAssignment mapToSectionInstructorAssignment(String body, Exchange exchange) throws Exception;

    MCAddress mapToAddress(String body, Exchange exchange) throws Exception;

    MCCourseSectionInformation mapToCourseSectionInformation(String body, Exchange exchange) throws Exception;

    MCSectionCrossListGroup mapToSectionCrossListGroup(String body, Exchange exchange) throws Exception;

    List<MCSectionCrossListGroup> mapToSectionCrossListGroups(String body, Exchange exchange) throws Exception;

    MCPerson mapToPerson(String body, Exchange exchange) throws Exception;

}
