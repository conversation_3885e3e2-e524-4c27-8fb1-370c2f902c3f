package org.moderncampus.integration.tenant.env;

import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import org.apache.commons.lang3.EnumUtils;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.system.IntegrationSystem;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

@Component
@Lazy(value = false)
public class TenantConfigInterceptPropertySource extends PropertySource<Object> implements
        ApplicationContextAware {

    public static final String VALUE_SEPARATOR = ":";
    private static List<String> tenantPropPrefixList;

    private static final String TENANT_PREFIX = "tenant";

    private static ApplicationContext context;

    public TenantConfigInterceptPropertySource() {
        super("This source acts as a filter and adds the tenant information to predefined config properties");
    }

    @Override
    public Object getProperty(String propertyName) {
        if (context == null
                || tenantPropPrefixList == null
                || !isTenantPropertyLookup(propertyName)
                || propertyName.contains(
                VALUE_SEPARATOR)) {  //property key needs to be parsed further
            return null; //We let the lower ordinal config sources decide the prop value
        }

        IIntegrationRequestContext requestContext = context.getBean(IIntegrationRequestContext.class);
        if (requestContext.getSchool() == null) {
            return null;
        }

        Environment environment = context.getBean(Environment.class);
        String propLookupKey = joinString(TENANT_PREFIX,
                requestContext.getSourceSystem().toLowerCase(),
                requestContext.getSchool().toLowerCase(), propertyName);
        return environment.getProperty(propLookupKey);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
        tenantPropPrefixList = EnumUtils.getEnumList(IntegrationSystem.class).stream()
                .map((system -> system.getName().toLowerCase())).collect(
                        Collectors.toList());
    }

    private String joinString(String... parts) {
        StringJoiner strJoiner = new StringJoiner(".");
        Arrays.stream(parts).forEach(strJoiner::add);
        return strJoiner.toString();
    }

    private boolean isTenantPropertyLookup(String propertyName) {
        return propertyName != null && !propertyName.startsWith(TENANT_PREFIX)
                && (tenantPropPrefixList.stream().anyMatch(propertyName::startsWith));
    }
}

