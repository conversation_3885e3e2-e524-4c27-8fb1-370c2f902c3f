package org.moderncampus.integration.transform.support;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public final class DateTimeFormatters {

    private final static DateTimeFormatter utcDateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(
            ZoneOffset.UTC);

    public static LocalDateTime mapDateTime(String dateString) {
        return nonNull(dateString) ? LocalDateTime.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE_TIME) : null;
    }

    public static LocalDateTime mapDateTimeWithOffset(String dateString) {
        return nonNull(dateString) ? LocalDateTime.parse(dateString, DateTimeFormatter.ISO_OFFSET_DATE_TIME) : null;
    }

    public static LocalDate mapDate(String dateString) {
        return nonNull(dateString) ? LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE) : null;
    }

    /**
     * Combines a LocalDate with an OffsetTime to produce an ZonedDateTime which is then formatted to a String in the
     * JSON Schema date-time format
     *
     * @param date a LocalDate
     * @param time an OffsetTime
     * @return a String formatted using the JSON Schema date-time format (ISO-8601). If date is null will return null.
     * If time is null will use the StartofDay (00:00:00Z)
     */
    public static String toUtcDateTimeString(LocalDate date, OffsetTime time) {
        if (isNull(date)) {
            return null;
        }

        ZonedDateTime dttm = isNull(time) ? date.atStartOfDay(ZoneOffset.UTC) : time.atDate(date).toZonedDateTime();

        return utcDateTimeFormatter.format(dttm);
    }

    public static ZonedDateTime fromUtcDateTimeString(String datetime) {
        return isNull(datetime) ? null : ZonedDateTime.parse(datetime, utcDateTimeFormatter);
    }

    public static String toUtcTimeString(OffsetTime time) {
        return isNull(time) ? null : utcDateTimeFormatter.format(time);
    }
}
