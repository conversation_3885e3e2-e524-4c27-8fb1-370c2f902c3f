package org.moderncampus.integration.util;


import org.apache.camel.converter.jaxb.JaxbDataFormat;

import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import jakarta.xml.bind.helpers.DefaultValidationEventHandler;

public class IntegrationJaxbDataFormat extends JaxbDataFormat {

    @Override
    protected Unmarshaller createUnmarshaller() throws JAXBException {
        Unmarshaller unmarshaller = super.createUnmarshaller();
        unmarshaller.setEventHandler(new DefaultValidationEventHandler());
        return unmarshaller;
    }
}
