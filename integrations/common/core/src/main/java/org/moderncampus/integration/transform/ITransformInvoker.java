package org.moderncampus.integration.transform;

import static org.moderncampus.integration.constants.Constants.SOURCE_BODY_REQUEST;

import org.apache.camel.Exchange;

public interface ITransformInvoker {

    default <T, U> U invokeTransform(ITransformer<T, U> transformer, Exchange exchange, T body) throws Exception {
        TransformContext context = new TransformContext(exchange.getProperties());
        exchange.setProperty(SOURCE_BODY_REQUEST, body);
        return transformer.transform(context, body);
    }
}
