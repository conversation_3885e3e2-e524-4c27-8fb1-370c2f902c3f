package org.moderncampus.integration.route.error;

import static org.moderncampus.integration.constants.Constants.BREADCRUMB_ID;
import static org.moderncampus.integration.constants.Constants.EXECUTED_ROUTE_RESULTS;
import static org.moderncampus.integration.route.support.RouteSupport.extractPropertiesForResult;
import static org.moderncampus.integration.route.support.RouteSupport.mapToRouteExecutorMeta;

import java.util.List;
import java.util.Map;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.component.bean.validator.BeanValidationException;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.exception.ValidationException;
import org.moderncampus.integration.exception.ValidationExceptions;
import org.moderncampus.integration.route.dto.RouteExecResultTypeCode;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.RouteExecutorResultCollector;
import org.moderncampus.integration.route.support.IRouteTracingService;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultRouteErrorHandler implements Processor {

    static final String RESPONSE_BODY = "responseBody";
    IRouteTracingService<Exchange> tracingService;

    RouteExecutorResultCollector resultCollector;

    @Override
    public void process(Exchange exchange) throws Exception {
        Exception e = exchange.getProperty(Exchange.EXCEPTION_CAUGHT,
                Exception.class);
        tracingService.trace(ExceptionUtils.getStackTrace(e), exchange);
        e = mapException(e);
        RouteExecutorResult executionResult = new RouteExecutorResult();
        Map<String, Object> metaProps = extractPropertiesForResult(exchange);
        RouteExecutorResult.Meta meta = mapToRouteExecutorMeta(RouteExecResultTypeCode.FAILURE,
                metaProps, e);
        addTraceIdToException(e, metaProps);
        addExecutedRouteResults(e);
        executionResult.setMeta(meta);
        exchange.getMessage().setBody(executionResult);
    }

    public Exception mapException(Exception e) {
        try {
            switch (e) {
                case BeanValidationException beanValidationException ->
                        e = mapToValidationException(beanValidationException);
                case HttpOperationFailedException httpOperationFailedException ->
                        e = mapToApplicationException(httpOperationFailedException);
                case ValidationExceptions validationExceptions -> {
                }
                case null, default -> {
                    e = mapToDefaultApplicationException(e);
                }
            }
        } catch (ClassCastException ignored) {
        }
        return e;
    }

    private void addTraceIdToException(Exception e, Map<String, Object> metaProps) {
        if (e instanceof ApplicationException applicationException) {
            applicationException.setTraceId((String) metaProps.getOrDefault(BREADCRUMB_ID, null));
        }
    }

    private void addExecutedRouteResults(Exception e) {
        if (e instanceof ApplicationException applicationException) {
            applicationException.addMetaProperty(EXECUTED_ROUTE_RESULTS, resultCollector.getExecutedRouteResults());
        }
    }

    private ValidationExceptions mapToValidationException(BeanValidationException beanValidationException) {
        List<ValidationException> exceptionList = beanValidationException.getConstraintViolations().stream()
                .map((violation) -> new ValidationException(violation.getMessage(),
                        violation.getPropertyPath() != null ? violation.getPropertyPath().toString() : null)).toList();
        return new ValidationExceptions(exceptionList);
    }

    private ApplicationException mapToApplicationException(HttpOperationFailedException e) {
        ApplicationException applicationException = new ApplicationException(e, e.getMessage(),
                e.getHttpResponseCode());
        RouteSupport.appendEndpointResponseToException(applicationException, e.getResponseBody());
        return applicationException;
    }

    private Exception mapToDefaultApplicationException(Exception e) {
        if (e instanceof ApplicationException applicationException) {
            return e;
        }
        return new ApplicationException(e, e.getMessage());
    }
}
