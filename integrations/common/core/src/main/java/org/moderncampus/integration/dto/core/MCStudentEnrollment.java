package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCStudentEnrollment extends BaseDTO {

    @NotBlank(message = "Person is mandatory")
    @Schema(description = "A person registered for a section")
    String person;

    @NotBlank(message = "Section is mandatory")
    @Schema(description = "An instance of a course for which a person is registering")
    String section;

    @NotBlank(message = "Academic Level is mandatory")
    @Schema(description = "The academic level at which the student is registering for the course (The level specified should match one of the levels allowed for the section)")
    String academicLevel;

    @NotNull(message = "Original date is mandatory")
    @Schema(description = "The date on which the student originally registered for the section")
    LocalDate originalDate;

    @NotNull(message = "Enrollment status is mandatory")
    @Schema(description = "The status of this person's registration in the section")
    String enrollmentStatus;

    @Schema(description = "The date on which the status was set")
    LocalDate enrollmentStatusDate;

}
