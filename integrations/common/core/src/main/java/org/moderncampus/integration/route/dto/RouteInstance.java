package org.moderncampus.integration.route.dto;

import java.io.Serializable;

import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RouteInstance implements Serializable {

    IRouteId id;
    RouteExecutorRequest request;
}
