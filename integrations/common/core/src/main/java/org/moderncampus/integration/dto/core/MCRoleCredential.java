package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCRoleCredential extends BaseDTO {

    String type;

    String value;

    String country;

    LocalDate effStart;

    LocalDate effEnd;

}
