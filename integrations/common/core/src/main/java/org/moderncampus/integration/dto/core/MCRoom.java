package org.moderncampus.integration.dto.core;

import java.time.LocalDate;
import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCRoom extends BaseDTO {

    String code;

    String title;

    String description;

    String shortDescription;

    List<String> roomTypes;

    Integer maxCapacity;

    List<MCRoomComponent> components;

    String location;

    String building;

    String floor;

    String department;

    List<MCRoomCharacteristic> characteristics;

    LocalDate effectiveDate;

    String effectiveStatus;

}
