package org.moderncampus.integration.route.dto;

import java.util.HashMap;
import java.util.Map;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RouteExecutorRequest {

    Object data;

    RouteExecutorRequest.Meta requestMeta;

    @Getter
    @Setter
    public static class Meta {

        private Map<String, Object> metaProperties;

        public void addMetaProperty(String key, Object value) {
            if (metaProperties == null) {
                metaProperties = new HashMap<>();
            }
            metaProperties.put(key, value);
        }
    }

    public void addMetaProperty(String key, Object value) {
        if (getRequestMeta() == null) {
            setRequestMeta(new Meta());
        }
        getRequestMeta().addMetaProperty(key, value);
    }

}
