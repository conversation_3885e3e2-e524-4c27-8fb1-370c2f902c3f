package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.constants.Constants.DEST_SYS_ENDPOINT_RESPONSE;
import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE;

import org.apache.camel.model.RouteDefinition;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class CreateToExtSystemRouteBuilder extends BaseRouteBuilder {

    Object outTransformHelper;
    String outTransformMethod;
    Object inTransformHelper;
    String inTransformMethod;
    @NonFinal
    String useCase;

    public CreateToExtSystemRouteBuilder(String id, Object outTransformHelper, String outTransformMethod,
            Object inTransformHelper, String inTransformMethod, String useCase) {
        super(id);
        this.outTransformHelper = outTransformHelper;
        this.outTransformMethod = outTransformMethod;
        this.inTransformHelper = inTransformHelper;
        this.inTransformMethod = inTransformMethod;
        this.useCase = useCase;
    }

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.setProperty(INTEGRATION_USE_CASE, simple(useCase()));
        routeDefinition.to(BEAN_VALIDATOR_URI);
        preFetchAssociations(routeDefinition);
        routeDefinition.bean(outTransformHelper, outTransformMethod);
        routeDefinition.toD(getExtSystemCreateRouteURI());
        routeDefinition.setProperty(DEST_SYS_ENDPOINT_RESPONSE, simple(CONVERT_BODY_STRING_EXPR_REF));
        routeDefinition.bean(inTransformHelper, inTransformMethod);
    }

    protected abstract String getExtSystemCreateRouteURI();

    protected abstract void preFetchAssociations(RouteDefinition routeDefinition);

    @Override
    protected String useCase() {
        return useCase;
    }
}
