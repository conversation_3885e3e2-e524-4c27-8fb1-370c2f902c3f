package org.moderncampus.integration.constants;

public class Constants {

    public static final String SCHOOL_ID = "schoolId";
    public static final String VERSION = "version";
    public static final String ORIGINAL_TRANSFORM = "originalTransform";
    public static final String SCHOOL_ID_TMU = "tmu";
    public static final String LOADED_ENTITY = "loadedEntity";
    public static final String UPDATE = "update";
    public static final String ASSOCIATION_MODE = "associationMode";
    public static final String ASSOCIATION_DELETE_MODE = "delete";
    public static final String INTEGRATION_USE_CASE = "integrationUseCase";
    public static final String INTEGRATION_PAGINATION_PARAMS = "integrationPaginationParams";
    public static final String PAGE_SIZE = "pageSize";
    public static final String PAGE_NUM = "pageNum";
    public static final String PAGE_OFFSET = "pageOffset";
    public static final String TOTAL_PAGES = "totalPages";
    public static final String TOTAL_SIZE = "totalSize";
    public static final String HTTPS = "https";
    public static final String HTTP = "http";
    public static final String HEADER_FILTER_STRATEGY = "headerFilterStrategy";
    public static final String HEALTH = "health";
    public static final String EXECUTED_ROUTE_RESULTS = "executedRouteResults";
    public static final String DEST_SYS_ENDPOINT_RESPONSE = "destSysEndpointResponse";
    public static final String API_RESOURCES_KEY = "APIResources";
    public static final String CURRENT_EXCHANGE = "CURRENT_EXCHANGE";
    public static final String BREADCRUMB_ID = "breadcrumbId";
    public static final String REQUEST_ID = "requestId";
    public static final String SOURCE_BODY_REQUEST = "sourceBodyRequest";
    public static final String CONNECTION_CONFIG_PARAM = "connectionConfig";
    public static final String RESOURCE_ID_PARAM = "resourceId";
    public static final String MODEL_CLASS_PARAM = "modelClass";
    public static final String HTTP_METHOD = "httpMethod";
    public static final String QUERY_PARAMS = "queryParams";
    public static final String PATH_URI_PARAM = "path";
    public static final String HTTP_METHOD_PARAM = "httpMethod";
    public static final String RESPONSE_HEADERS = "responseHeaders";
    public static final String RESPONSE_STATUS_CODE = "responseStatusCode";
    public static final String PROXY_REQUEST = "proxyRequest";
}
