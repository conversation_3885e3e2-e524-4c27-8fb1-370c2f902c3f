package org.moderncampus.integration.dto.core;

import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCSectionCrossList extends BaseDTO {

    Long maxCapacity;

    List<CrossListSection> sections;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CrossListSection extends BaseDTO {

        Boolean primary;
    }

}

