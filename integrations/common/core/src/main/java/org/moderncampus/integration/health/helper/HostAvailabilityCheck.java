package org.moderncampus.integration.health.helper;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;

import org.apache.camel.health.HealthCheckResultBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HostAvailabilityCheck {

    static final String ERR_MSG = "Application Health Check Failed due to service unavailability {}";

    public static boolean isAvailable(String hostName, int port, HealthCheckResultBuilder resultBuilder) {
        SocketAddress socketAddress = new InetSocketAddress(hostName, port);

        try (Socket socket = new Socket()) {
            socket.connect(socketAddress, 5000);
        } catch (IOException e) {
            log.error(ERR_MSG, e.getMessage());
            if (resultBuilder != null) {
                resultBuilder.error(e);
                resultBuilder.message(ERR_MSG + e.getMessage());
                resultBuilder.down();
            }
            return false;
        }
        if (resultBuilder != null) {
            resultBuilder.up();
        }
        return true;
    }
}
