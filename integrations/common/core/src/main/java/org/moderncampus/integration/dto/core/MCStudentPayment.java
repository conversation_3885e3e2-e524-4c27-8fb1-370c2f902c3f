package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCStudentPayment extends BaseDTO {

    @NotBlank(message = "Person is mandatory")
    @Schema(description = "The person (student) who incurred the charge")
    String person;

    @NotBlank(message = "Funding source is mandatory")
    @Schema(description = "The accounting code of the funding source associated with the student charge.")
    String fundingSource;

    @Schema(description = "The accounting code of the funding destination associated with the student charge.")
    String fundingDestination;

    @NotBlank(message = "Academic period is mandatory")
    @Schema(description = "The academic period to which the payment is attached.\n")
    String academicPeriod;

    @NotBlank(message = "Payment type is mandatory")
    @Schema(description = "The type of the payment.")
    String paymentType;

    @NotNull(message = "Payment date is mandatory")
    @Schema(description = "The date when the payment was made.")
    LocalDate paymentDate;

    @NotNull(message = "Payment amount is mandatory")
    @Positive(message = "Payment amount must be greater than zero")
    @Schema(description = "The amount of the payment")
    BigDecimal paymentAmount;

    @NotBlank(message = "Currency is mandatory")
    @Schema(description = "The ISO 4217 currency code")
    String currency;

    @Schema(description = "The override description associated with the payment.")
    String description;

    @Schema(description = "The comments associated with the payment.")
    String comments;

    ReportingDetails reportingDetails;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ReportingDetails extends BaseDTO {

        @Override
        @Schema(hidden = true)
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The usage associated with the payment (i.e. tax reporting only).")
        String type;

        @Schema(description = "The date the payment originated for consideration in tax report generation.")
        LocalDate originDate;
    }

}
