package org.moderncampus.integration.health;

import java.util.Collection;
import java.util.List;

import org.apache.camel.health.HealthCheck;
import org.moderncampus.integration.context.IntegrationRequestContext;

public interface IDefaultIntEndpointHealthCheckInvoker {

    String getHealthCheckId();

    IntegrationRequestContext getTenantRequestContext();

    default Collection<HealthCheck.Result> healthCheck(String host) {
        DefaultEndpointHostAvailabilityHealthCheck hostAvailabilityHealthCheck = new DefaultEndpointHostAvailabilityHealthCheck(
                getHealthCheckId(), getHealthCheckId() + ":hostAvailability", getTenantRequestContext(), host);
        return List.of(hostAvailabilityHealthCheck.call());
    }
}
