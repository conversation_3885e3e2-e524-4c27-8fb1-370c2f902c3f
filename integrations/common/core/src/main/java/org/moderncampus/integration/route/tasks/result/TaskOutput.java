package org.moderncampus.integration.route.tasks.result;

import java.util.HashMap;
import java.util.Map;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskOutput {

    Object data;
    Object meta;
    final Map<String, Object> contextProps = new HashMap<>();

    public void setContextProp(String propKey, Object prop) {
        this.contextProps.put(propKey, prop);
    }
}
