package org.moderncampus.integration.component.endpoint.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import lombok.Getter;

@Getter
@Component
@Scope(scopeName = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class MCDestinyConnectionConfiguration implements IMCDestinyConnectionConfiguration {

    @Value("${destiny.integration.host}")
    String host;

    @Value("${destiny.integration.username}")
    String username;

    @Value("${destiny.integration.password}")
    String password;

    @Value("${destiny.integration.useHttp}")
    boolean useHttp;

}