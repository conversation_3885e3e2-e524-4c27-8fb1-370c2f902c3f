package org.moderncampus.integration.route.validator;

import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.moderncampus.integration.exception.ApplicationException;
import org.moderncampus.integration.tenants.validator.TenantValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultRouteValidator {

    public static final String INVALID_REQUEST_CONTEXT = "Invalid request context";
    IIntegrationRequestContext integrationRequestContext;
    TenantValidator tenantValidator;
    String activeProfiles;

    public DefaultRouteValidator(IIntegrationRequestContext integrationRequestContext,
            TenantValidator tenantValidator,
            @Value("${spring.profiles.active:}") String activeProfiles) {
        this.integrationRequestContext = integrationRequestContext;
        this.tenantValidator = tenantValidator;
        this.activeProfiles = activeProfiles;
    }

    public void validate() throws Exception {
        if (!activeProfiles.contains("dev")) {
            if (integrationRequestContext == null) {
                throw new ApplicationException(INVALID_REQUEST_CONTEXT);
            }
            String school = integrationRequestContext.getSchool();
            String sourceSystem = integrationRequestContext.getSourceSystem();
            String destSystem = integrationRequestContext.getDestSystem();
            tenantValidator.validate(school, sourceSystem, destSystem);
        }
    }
}
