package org.moderncampus.integration.route.builder;

import static org.moderncampus.integration.constants.Constants.*;

import org.apache.camel.model.RouteDefinition;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@Getter
@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
public abstract class UpdateToExtSystemRouteBuilder extends BaseRouteBuilder {

    Object outTransformHelper;
    String outTransformMethod;
    ObjectMapper objectMapper;
    @NonFinal
    boolean isPatchImpl = true;
    @NonFinal
    String useCase;
    @NonFinal
    boolean isUpdateById = true;

    public UpdateToExtSystemRouteBuilder(String id, Object outTransformHelper, String outTransformMethod,
            ObjectMapper objectMapper, String useCase) {
        super(id);
        this.outTransformHelper = outTransformHelper;
        this.outTransformMethod = outTransformMethod;
        this.objectMapper = objectMapper;
        this.useCase = useCase;
    }

    @Override
    protected void buildRouteActions(RouteDefinition routeDefinition) {
        routeDefinition.setProperty(INTEGRATION_USE_CASE, simple(useCase()));
        routeDefinition.to(BEAN_VALIDATOR_URI);
        preFetchAssociations(routeDefinition);
        routeDefinition.setProperty(RESOURCE_ID, simple(BODY_ID_EXPR_REF));
        if (isPatchImpl()) {
            routeDefinition.setBody().simple($_NULL);
            routeDefinition.toD(getExtSystemGetByIdRouteURI());
            routeDefinition.setProperty(LOADED_ENTITY, simple(CONVERT_BODY_STRING_EXPR_REF));
            routeDefinition.setBody(simple(ORIGINAL_BODY_EXPR_REF));
        }
        routeDefinition.bean(outTransformHelper, outTransformMethod);
        if (isUpdateById()) {
            routeDefinition.toD(getExtSystemUpdateByIdRouteURI());
        } else {
            routeDefinition.toD(getExtSystemUpdateRouteURI());
        }
        routeDefinition.setProperty(DEST_SYS_ENDPOINT_RESPONSE, simple(CONVERT_BODY_STRING_EXPR_REF));
        routeDefinition.process((exchange) -> exchange.getMessage().setBody(objectMapper.createObjectNode()));
    }

    protected abstract String getExtSystemGetByIdRouteURI();

    protected abstract String getExtSystemUpdateByIdRouteURI();

    protected abstract String getExtSystemUpdateRouteURI();

    protected abstract void preFetchAssociations(RouteDefinition routeDefinition);

    @Override
    protected String useCase() {
        return useCase;
    }
}
