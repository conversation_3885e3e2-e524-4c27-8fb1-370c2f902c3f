package org.moderncampus.integration.dto.core;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Data
@Schema(description = "Student Charge Information")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCStudentCharge extends BaseDTO {

    @NotBlank(message = "Person is mandatory")
    @Schema(description = "The person (student) who incurred the charge")
    String person;

    @Schema(description = "The accounting code of the funding source associated with the student charge")
    String fundingSource;

    @NotBlank(message = "Funding destination is mandatory")
    @Schema(description = "The accounting code of the funding destination associated with the student charge")
    String fundingDestination;

    @Schema(description = "The term in which charges were incurred")
    String academicPeriod;

    @NotNull(message = "Chargeable date is mandatory")
    @Schema(description = "The date when the student becomes liable for the charge")
    LocalDateTime chargeableOn;

    @NotNull(message = "Charge amount is mandatory")
    @Schema(description = "The amount of the charge")
    BigDecimal chargeAmount;

    @NotNull(message = "Currency code is mandatory")
    @Schema(description = "The ISO 4217 currency code")
    String currency;

    @Schema(description = "The override description associated with the charge")
    String description;

    @Schema(description = "The comments associated with the charge")
    String comments;

    MCStudentChargeReportingDetails reportingDetails;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCStudentChargeReportingDetails extends BaseDTO {

        @Override
        @Schema(hidden = true)
        public String getId() {
            return super.getId();
        }

        @Schema(description = "The usage associated with the charge (i.e. tax reporting only)")
        String type;

        @Schema(description = "The date the charge originated for consideration in tax report generation")
        LocalDateTime originDate;

        @Schema(description = "The start date of the activity associated with the charge")
        LocalDate activityStartDate;

        @Schema(description = "The end date of the activity associated with the charge ")
        LocalDate activityEndDate;
    }

}
