package org.moderncampus.integration.util;

import static java.lang.String.format;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DynamicClassLoader extends ClassLoader {

    Set<String> loadedClasses = new HashSet<>();
    Set<String> unavailableClasses = new HashSet<>();

    Function<String, byte[]> loader;

    public DynamicClassLoader(String path) {
        loader = Stream.of(path)
                .map(File::new)
                .map(this::createLoader)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Path does not exists."));
    }

    public Function<String, byte[]> createLoader(File path) {
        return fileName -> Optional.ofNullable(fileName)
                .map(name -> new File(path, name))
                .filter(File::exists)
                .map(this::toByteArray)
                .orElse(null);
    }

    @Override
    public Class<?> loadClass(String name) throws ClassNotFoundException {
        if (loadedClasses.contains(name) || unavailableClasses.contains(name)) {
            return super.loadClass(name); // Use default CL cache
        }

        byte[] newClassData = loader.apply(getFilename(name));
        if (newClassData != null) {
            loadedClasses.add(name);
            return defineClass(name, newClassData, 0, newClassData.length);
        } else {
            unavailableClasses.add(name);
            return getClass().getClassLoader().loadClass(name);
        }
    }

    public String getFilename(String name) {
        return Stream.of(name.split("\\."))
                .reduce((first, second) -> second)
                .map(className -> format("%s.class", className))
                .orElse(null);
    }

    private byte[] toByteArray(File file) {
        try {
            return Files.readAllBytes(file.toPath());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
