package org.moderncampus.integration.route.support;

import org.apache.camel.Exchange;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteTracingService implements IRouteTracingService<Exchange> {

    DefaultRouteTracer tracer;

    public void trace(String traceStr, Exchange exchange) {
        tracer.dumpTrace(traceStr + " | " + tracer.getExchangeFormatter().format(exchange), null);
    }
}
