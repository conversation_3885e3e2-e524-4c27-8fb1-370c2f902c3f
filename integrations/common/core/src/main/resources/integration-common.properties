spring.config.import=common.properties,persistence-common.properties
# camel spring boot properties
camel.dataformat.jaxb.ignore-j-a-x-b-element=true
camel.dataformat.jaxb.filter-non-xml-chars=true
camel.dataformat.jackson.auto-discover-object-mapper=true
camel.springboot.load-type-converters=true
camel.component.http.max-total-connections=250
camel.component.http.connections-per-route=50
# product endpoint properties
destiny.integration.host=
destiny.integration.username=
destiny.integration.password=
destiny.integration.useHttp=false