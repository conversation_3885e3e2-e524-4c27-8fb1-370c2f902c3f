package org.moderncampus.integration.webservice.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntegrationRequestContext {

    @Parameter(in = ParameterIn.PATH)
    String version;

    @Parameter(in = ParameterIn.PATH)
    @NotBlank(message = "schoolId must be defined")
    String schoolId;

    @Parameter(in = ParameterIn.PATH)
    @NotBlank(message = "sourceSystemId must be defined")
    String sourceSystemId;

    @Parameter(in = ParameterIn.PATH)
    String destinationSystemId;

}
