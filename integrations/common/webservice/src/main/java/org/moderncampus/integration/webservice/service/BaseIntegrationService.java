package org.moderncampus.integration.webservice.service;

import static org.moderncampus.integration.constants.Constants.BREADCRUMB_ID;
import static org.moderncampus.integration.route.Constants.ROUTE_HEADERS;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.helper.IntegrationResponseMapper;
import org.moderncampus.integration.route.dto.RouteExecutorRequest;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.dto.RouteInstance;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.identifier.IRouteId;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter(value = AccessLevel.PROTECTED)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BaseIntegrationService {

    static Set<String> defaultMappedMetaProps = new HashSet<>() {{
        add(BREADCRUMB_ID);
    }};

    @Setter(onMethod_ = {@Autowired})
    IRouteExecutor routeExecutor;

    @Setter(onMethod_ = {@Autowired})
    IntegrationResponseMapper responseMapper;


    protected RouteInstance getRouteInstance(Object data, IRouteId routeId, Map<String, String> headers) {
        RouteInstance instance = new RouteInstance();
        instance.setId(routeId);
        RouteExecutorRequest request = new RouteExecutorRequest();
        request.setData(data);
        instance.setRequest(request);
        request.addMetaProperty(ROUTE_HEADERS, headers);
        return instance;
    }

    protected <T> IntegrationResponse<T> executeRoute(IRouteId routeId) throws Exception {
        return executeRoute(routeId, null, null);
    }

    protected <T> IntegrationResponse<T> executeRoute(IRouteId routeId, Object data, Set<String> mappedMetaProperties)
            throws Exception {
        return executeRoute(routeId, data, null, mappedMetaProperties);
    }

    protected <T> IntegrationResponse<T> executeRoute(IRouteId routeId, Object data, Map<String, String> headers,
            Set<String> mappedMetaProperties)
            throws Exception {
        RouteInstance routeInstance = getRouteInstance(data, routeId, headers);
        RouteExecutorResult result = getRouteExecutor().execute(routeInstance);
        return responseMapper.mapRouteResult(result, mappedMetaProperties);
    }

    protected <E> IntegrationResponse<List<E>> searchEntities(BasePaginationRequest requestWS, IRouteId routeId,
            Set<String> metaProps) throws Exception {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setPaginationConstruct(requestWS.getPaginationConstruct());
        return executeRoute(routeId, entityRequest, metaProps);
    }

    protected <E> IntegrationResponse<E> searchEntities(BasePaginationRequest requestWS, IRouteId routeId,
            Map<String, Object> searchCriteria, Set<String> metaProps) throws Exception {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(searchCriteria);
        entityRequest.setPaginationConstruct(requestWS.getPaginationConstruct());
        return executeRoute(routeId, entityRequest, metaProps);
    }

    public IntegrationResponse<Health> getHealthStatus(IRouteId routeId) throws Exception {
        return executeRoute(routeId);
    }
}
