package org.moderncampus.integration.webservice.controller;

import static org.moderncampus.integration.constants.Constants.HEALTH;

import org.moderncampus.integration.route.identifier.IRouteId;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.IntegrationRequestContext;
import org.moderncampus.integration.webservice.service.BaseIntegrationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.GetMapping;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

public interface IHealthEndpointController {

    BaseIntegrationService getIntegrationService();

    IRouteId getHeathRouteId();

    @Operation(summary = "Get Health Endpoint Status", hidden = true)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Health Status"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = HEALTH)
    default Health getHealthStatus(
            @ParameterObject IntegrationRequestContext requestContextWS) throws Exception {
        return getIntegrationService().getHealthStatus(getHeathRouteId()).getData();
    }
}
