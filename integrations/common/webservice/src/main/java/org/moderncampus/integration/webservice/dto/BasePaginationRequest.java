package org.moderncampus.integration.webservice.dto;

import org.moderncampus.integration.dto.base.IPaginationConstruct;
import org.moderncampus.integration.dto.base.PaginationConstruct;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BasePaginationRequest {

    @Parameter(in = ParameterIn.QUERY, hidden = true)
    @Min(value = 0L, message = "Page Number must be a positive number")
    Integer pageNumber;

    @Parameter(in = ParameterIn.QUERY)
    @Min(value = 0L, message = "Page Size must be a positive number")
    Integer pageSize;

    @Parameter(in = ParameterIn.QUERY)
    @Min(value = 0L, message = "Page Offset must be a positive number")
    Integer pageOffset;

    protected IPaginationConstruct createPaginationConstruct() {
        return new PaginationConstruct();
    }

    @Schema(hidden = true)
    public IPaginationConstruct getPaginationConstruct() {
        IPaginationConstruct paginationConstruct = createPaginationConstruct();
        paginationConstruct.setPageSize(getPageSize());
        paginationConstruct.setPageNumber(getPageNumber());
        paginationConstruct.setPageOffset(getPageOffset());
        return paginationConstruct;
    }
}
