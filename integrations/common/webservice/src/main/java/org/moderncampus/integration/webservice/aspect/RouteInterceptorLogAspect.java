package org.moderncampus.integration.webservice.aspect;

import static org.moderncampus.integration.constants.Constants.REQUEST_ID;

import java.util.UUID;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.moderncampus.integration.context.IIntegrationRequestContext;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class RouteInterceptorLogAspect {

    static final String REQUEST_START_TIME = "requestStartTime";
    static final String REQUEST_BEGIN_TEMPLATE = "Request URI: {} | Method: {} | Start Time: {}";
    static final String REQUEST_END_TEMPLATE = "Response Status: {} | Request URI: {} | Duration: {} ms";
    static final String REQUEST_ID_HEADER = "int-request-id";

    IIntegrationRequestContext requestContext;

    @After("execution(* org.springframework.web.servlet.HandlerInterceptor+.preHandle(..))")
    public void preHandle(JoinPoint joinPoint) {
        Object[] requestArgs = joinPoint.getArgs();
        HttpServletRequest request = (HttpServletRequest) requestArgs[0];
        HttpServletResponse response = (HttpServletResponse) requestArgs[1];
        String schoolId = requestContext.getSchool();
        String sourceSystemId = requestContext.getSourceSystem();
        String destinationSystemId = requestContext.getDestSystem();
        String requestId = String.join(":", UUID.randomUUID().toString(), sourceSystemId, schoolId,
                destinationSystemId);
        MDC.put(REQUEST_ID, requestId);
        response.addHeader(REQUEST_ID_HEADER, requestId);
        long startTime = System.currentTimeMillis();
        log.info(REQUEST_BEGIN_TEMPLATE, request.getRequestURI(), request.getMethod(), startTime);
        MDC.put(REQUEST_START_TIME, String.valueOf(startTime));
    }

    @After("execution(* org.springframework.web.servlet.HandlerInterceptor+.afterCompletion(..))")
    public void afterCompletion(JoinPoint joinPoint) {
        Object[] requestArgs = joinPoint.getArgs();
        HttpServletRequest request = (HttpServletRequest) requestArgs[0];
        HttpServletResponse response = (HttpServletResponse) requestArgs[1];
        long endTime = System.currentTimeMillis();
        long duration = endTime - Long.parseLong(MDC.get(REQUEST_START_TIME));
        log.info(REQUEST_END_TEMPLATE, response.getStatus(),
                request.getRequestURI(), duration);
        MDC.remove(REQUEST_START_TIME);
        MDC.remove(REQUEST_ID);
    }

}
