package org.moderncampus.integration.navigate.dto;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NavigateTerm extends BaseDTO {

    String code;

    String name;

    LocalDate startDate;

    LocalDate endDate;
}
