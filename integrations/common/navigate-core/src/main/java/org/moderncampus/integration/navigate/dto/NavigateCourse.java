package org.moderncampus.integration.navigate.dto;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NavigateCourse extends BaseDTO {

    String code;

    String number;

    String title;

    String faculty;

    String description;

    Integer creditWeight;
}
