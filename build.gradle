plugins {
    id 'org.springframework.boot' apply false
}

allprojects {
    group 'com.moderncampus.integration'
    version '1.0-SNAPSHOT'
}

ext {
    libCamel = '4.8.0'
    libGuava = '33.3.0-jre'
    libSpringDoc = '2.6.0'
    libJUnitJupiter = '5.11.0'
    libAWSSDK = '2.28.1'
    libCommonsIo = '2.16.1'
    libJavaJwt = '4.4.0'
    libPhoneNumber = '8.13.45'
    libTestContainers = '1.20.1'
    libCaffeine = '3.1.8'
    libWireMock = '3.9.1'
    libCollections4 = '4.4'
}

subprojects {
    apply plugin: 'java'

    repositories {
        mavenCentral()
        mavenLocal()
    }

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    test {
        useJUnitPlatform()
    }

    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << '-parameters'
    }

    compileTestJava {
        options.encoding = 'UTF-8'
    }

}